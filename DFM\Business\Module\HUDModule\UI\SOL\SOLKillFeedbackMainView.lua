----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



-- SOL积分板
local SOLKillFeedbackMainView = hud("SOLKillFeedbackMainView")
local HudConfig = require "DFM.Business.Module.HUDModule.HUDConfig"
local EGameHUDState = import "GameHUDSate"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local UGameplayStatics = import "GameplayStatics"
local ADFMCharacter = import "DFMCharacter"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local ULAI = import "LAI"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local BattlefieldConfig = require "DFM.Business.Module.HUDModule.UI.BattleField.BattleFieldConfig"
local Queue=require("DFM.YxFramework.Core.Library.deque")
local ESOLScoreName = import("EScoreName")
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

local function log(...)
    --loginfo("[SOLKillFeedbackMainView]", ...)
end

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end

--- 行为状态
local Behavior = {
    ---等待新的的分事件
    Idle = "Idle",
    ---处理一个得分事件
    Pop = "Pop",
    ---展示得分事件
    Show = "Show",

    --检查并选择性pop，用于中间区域
    CheckAndPop = "CheckAndPop",
}

--- icon区域配置
local Config = {
    --- 创建Icon间隔
    CreateIconInterval = 0.2,
    --- 向右移动时间
    MoveTime = 0.2,
    --- 向右移动距离
    MoveDistance = 40,

    --info区域最长显示多久后刷新下一个
    InfoShowingTime = 1.5,
}

function SOLKillFeedbackMainView:Ctor()
    Config.CreateIconInterval = self.CreateIconInterval
    Config.InfoShowingTime = self.InfoShowingTime

    -- 设置显隐规则
    self:_InitInVisibleGameHudState()

    ---icon事件列表
    self.KillEventIconQueue = Queue.new()
    --中间区域的刷新逻辑和上下两区域不同，所以单独出一个队列
    self.KillEventInfoQueue = Queue.new()

    --中间区域的队列弹出类型
    self.IsFlickerForInfoView = false

    ---当前处理的得分事件
    self.CurrentKillEventIcon = nil
    --中间区域刷新逻辑的当前事件
    self.CurrentKillEventInfo = nil

    ---当前状态
    self.CurrentBehaviorStateForIcon = Behavior.Idle
    --中间区域刷新逻辑的当前状态
    self.CurrentBehaviorStateInfo = Behavior.Idle

    ---时间
    self.TimeStamp = 0

    ---Icon列表
    self.IconViewList = {}
    ---上一个IconView
    self.LastIconView = nil
    ---上一个Icon创建时间
    self.LastIconCreateTimeStamp = 0
    self.NeedMoveX = 0
    ---Icon移动速度倍数
    self.MoveSpeedMultiply = 1

    ---SpecialInfo列表
    self.SpecialInfoViewList = {}
    ---上一个SpecialInfoView
    self.LastSpecialInfoView = nil

    ---Icon用的当前得分事件序号
    self.CurrentKillEventIconIndex = 0

    self.Panel = self:Wnd("DFCanvasPanel_0", UIWidgetBase)

    self.SOLKillFeedbackInfoView = self:Wnd("WBP_KillFeedBackDetailed", UIWidgetBase)
    if self.SOLKillFeedbackInfoView then
        self.SOLKillFeedbackInfoView:SetShowKeepTime(self.InfoShowKeepTime)
    end

    -- 以前从HudConfig里配置，但为了方便重构修改位置，挪到了蓝图变量里
    self.PosSetting_PC = 
    {
        IconYOffset = self.IconYOffset_PC,
        InfoYOffset = self.InfoYOffset_PC,
        SpecialInfoYOffset = self.SpecialInfoYOffset_PC,
        ScaleToMobile = self.ScaleToMobile_PC,
        FontScaleToMobile = self.FontScaleToMobile_PC,
    }
    self.PosSetting_Mobile = 
    {
        IconYOffset = self.IconYOffset_Mobile,
        InfoYOffset = self.InfoYOffset_Mobile,
        SpecialInfoYOffset = self.SpecialInfoYOffset_Mobile,
    }

    -- 位置设置
    if DFHD_LUA == 1 then
        self.PosSetting = self.PosSetting_PC
        local scale = self.PosSetting.ScaleToMobile * self.PosSetting.FontScaleToMobile
        self.SOLKillFeedbackInfoView:SetRenderScale(FVector2D(scale, scale))
    else
        self.PosSetting = self.PosSetting_Mobile
    end
    self.SOLKillFeedbackInfoView:SetPosition(FVector2D(0, self.PosSetting.InfoYOffset))

    self.CustomCachedIconViewNum = self.CachedIconViewNum or 6

    -- 预先创建并缓存住
    self.CachedIconViewList = {}
    self.CachedIconViewIndex = 0
    for Idx = 1, self.CustomCachedIconViewNum do
        local CachedIconView = Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.SOLKillFeedbackIconView)
        if isvalid(CachedIconView) then
            CachedIconView:Collapsed()
            table.insert(self.CachedIconViewList, CachedIconView)
        end
    end

    self.CachedSpecialInfoViewList = {}
    self.CachedSpecialInfoViewIndex = 0
    for Idx = 1, self.CachedSpecialInfoViewNum do
        local CachedSpecialInfoView = Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.SOLKillFeedbackSpecialInfoView)
        if isvalid(CachedSpecialInfoView) then
            CachedSpecialInfoView:Collapsed()
            table.insert(self.CachedSpecialInfoViewList, CachedSpecialInfoView)
        end
    end

    --测试按钮
    self.TestBtn1 = self:Wnd("Button_73", UIButton)
    self.TestBtn1:Event("OnClicked", self.OnClickButton, self)

    self.TestBtn2 = self:Wnd("Button", UIButton)
    self.TestBtn2:Event("OnClicked", self.OnKillDownAClick, self)

    self.TestBtn3 = self:Wnd("Button_1", UIButton)
    self.TestBtn3:Event("OnClicked", self.OnKillAClick, self)

    self.TestBtn4 = self:Wnd("Button_2", UIButton)
    self.TestBtn4:Event("OnClicked", self.OnAssistKillDownAClick, self)

    self.TestBtn5 = self:Wnd("Button_3", UIButton)
    self.TestBtn5:Event("OnClicked", self.OnAssistKillAClick, self)

    self.TestBtn6 = self:Wnd("Button_4", UIButton)
    self.TestBtn6:Event("OnClicked", self.OnRescueAClick, self)

    self.TestBtn7 = self:Wnd("Button_7", UIButton)
    self.TestBtn7:Event("OnClicked", self.OnKillDownBClick, self)

    self.TestBtn8 = self:Wnd("Button_5", UIButton)
    self.TestBtn8:Event("OnClicked", self.OnKillBClick, self)

    self.TestBtn9 = self:Wnd("Button_6", UIButton)
    self.TestBtn9:Event("OnClicked", self.OnAssistKillDownBClick, self)

    self.TestBtn10 = self:Wnd("Button_8", UIButton)
    self.TestBtn10:Event("OnClicked", self.OnAssistKillBClick, self)

    self.TestBtn11 = self:Wnd("Button_9", UIButton)
    self.TestBtn11:Event("OnClicked", self.OnRescueBClick, self)

    ---配置不同积分类型的图片和文字
    self.KillEventConfig = {
        [ESOLScoreName.ENormalKnockDown] = { PlayerName = "111", PlayerID = 111, WeaponName = "1111" },
        [ESOLScoreName.EHeadKnockDown] = { PlayerName = "222", PlayerID = 222, WeaponName = "2222" },
        [ESOLScoreName.ENormalKill] = { PlayerName = "333", PlayerID = 333, WeaponName = "3333" },
        [ESOLScoreName.EHeadKill] = { PlayerName = "444", PlayerID = 444, WeaponName = "4444" },
        [ESOLScoreName.EAssistKnockDown] = { PlayerName = "555", PlayerID = 555, WeaponName = "5555" },
        [ESOLScoreName.EAssistKill] = { "666", PlayerID = 666, WeaponName = "6666" },
        [ESOLScoreName.ERescue] = { PlayerName = "777", PlayerID = 777, WeaponName = "7777" },
    }
end

function SOLKillFeedbackMainView:GetUseCached()
    return self.CustomCachedIconViewNum > 0 and self.CachedSpecialInfoViewIndex > 0
end

function SOLKillFeedbackMainView:GetNewCachedIconView()
    if self:GetUseCached() then
        self.CachedIconViewIndex = self.CachedIconViewIndex + 1
        if 1 <= self.CachedIconViewIndex and self.CachedIconViewIndex <= self.CustomCachedIconViewNum then
            return self.CachedIconViewList[self.CachedIconViewIndex]
        else
            self.CachedIconViewIndex = 1
            return self.CachedIconViewList[self.CachedIconViewIndex]
        end
    else
        return Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.SOLKillFeedbackIconView)
    end
end

function SOLKillFeedbackMainView:GetNewCachedSpecialInfoView()
    if self:GetUseCached() then
        self.CachedSpecialInfoViewIndex = self.CachedSpecialInfoViewIndex + 1
        if 1 <= self.CachedSpecialInfoViewIndex and self.CachedSpecialInfoViewIndex <= self.CachedSpecialInfoViewNum then
            return self.CachedSpecialInfoViewList[self.CachedSpecialInfoViewIndex]
        else
            self.CachedSpecialInfoViewIndex = 1
            return self.CachedSpecialInfoViewList[self.CachedSpecialInfoViewIndex]
        end
    else
        return Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.SOLKillFeedbackSpecialInfoView)
    end
end

local PlayerIDA = 123
local PlayerIDB = 456
local PlayerNameA = "张三"
local PlayerNameB = "李四"

local disList = { 5, 6, 11, 20, 50 }

local weaponList = { "AK-突击步枪", "M4A1-1000", "巴雷特-极光", "霰弹枪", "手枪-100"}

local IsKillTeamList = { true, false }

function SOLKillFeedbackMainView:GetRandomDis()
    --math.newrandomseed()
    local idx = math.random(1, #disList)
    return disList[idx]
end

function SOLKillFeedbackMainView:GetRandomWeaponName()
    --math.newrandomseed()
    local idx = math.random(1, #weaponList)
    return weaponList[idx]
end

function SOLKillFeedbackMainView:GetRandomIsKillTeam()
    --math.newrandomseed()
    local idx = math.random(1, #IsKillTeamList)
    return IsKillTeamList[idx]
end

function SOLKillFeedbackMainView:OnKillDownAClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.ENormalKnockDown, self:GetRandomDis(), PlayerNameA, PlayerIDA, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnKillAClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.EHeadKill, self:GetRandomDis(), PlayerNameA, PlayerIDA, self:GetRandomWeaponName(), self:GetRandomIsKillTeam())
end

function SOLKillFeedbackMainView:OnAssistKillDownAClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.EAssistKnockDown, self:GetRandomDis(), PlayerNameA, PlayerIDA, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnAssistKillAClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.EAssistKill, self:GetRandomDis(), PlayerNameA, PlayerIDA, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnRescueAClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.ERescue, self:GetRandomDis(), PlayerNameA, PlayerIDA, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnKillDownBClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.ENormalKnockDown, self:GetRandomDis(), PlayerNameB, PlayerIDB, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnKillBClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.ENormalKill, self:GetRandomDis(), PlayerNameB, PlayerIDB, self:GetRandomWeaponName(), self:GetRandomIsKillTeam())
end

function SOLKillFeedbackMainView:OnAssistKillDownBClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.EAssistKnockDown, self:GetRandomDis(), PlayerNameB, PlayerIDB, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnAssistKillBClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.EAssistKill, self:GetRandomDis(), PlayerNameB, PlayerIDB, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnRescueBClick()
    self:OnSOLStatisticsNtf(ESOLScoreName.ERescue, self:GetRandomDis(), PlayerNameB, PlayerIDB, self:GetRandomWeaponName(), false)
end

function SOLKillFeedbackMainView:OnClickButton()
    for key, value in pairs(self.KillEventConfig) do
        self:OnSOLStatisticsNtf(key, 1234567, value.PlayerName, value.PlayerID, value.WeaponName)
    end
end

function SOLKillFeedbackMainView:OnOpen()
    -- 绑定事件
    log("[muxinzhao] OnOpen")
    self.delegatesHandler = UDFMGameHudDelegates.Get(GetGameInstance())
    self.callbacks = {
        
    }
    self:_InitCallbacks()

    self:RegisterDelegate()

    if self.SOLKillFeedbackInfoView then
        self.SOLKillFeedbackInfoView:Hidden()
    end
end

function SOLKillFeedbackMainView:RegisterDelegate()
    self.DFMGD = UDFMGameplayDelegates.Get(GetWorld())
    self.OnSOLStatisticsNtfEvent = self.DFMGD.SOLShowScoreHud:Add(self.OnSOLStatisticsNtf, self)
end

function SOLKillFeedbackMainView:RemoveDelegate()
    self.DFMGD.SOLShowScoreHud:Remove(self.OnSOLStatisticsNtf, self)
end

function SOLKillFeedbackMainView:_InitInVisibleGameHudState()
    local invisibleStates = {
        EGameHUDState.GHS_Settlement,
        EGameHUDState.GHS_OpenMap,
        EGameHUDState.GHS_PrepareTime,
        EGameHUDState.GHS_Operating3DUI,
        EGameHUDState.GHS_CutScene,
        EGameHUDState.GHS_UseTelescope,
        EGameHUDState.GHS_Monitor,
        EGameHUDState.GHS_Assassinate,
        EGameHUDState.GHS_ObserverModeFreeCamera,
    }

    for Key = 1, #invisibleStates do
        self:AddStateToInVisibleGameHudState(invisibleStates[Key])
    end
end

function SOLKillFeedbackMainView:_InitCallbacks()
    for i = 1, #self.callbacks do
        local callback = self.callbacks[i]
        callback[3] = callback[1]:Add(CreateCallBack(callback[2], self))
    end
end

function SOLKillFeedbackMainView:OnShow()
    --self:StartTick()
end

function SOLKillFeedbackMainView:StartTick()
    LuaTickController:Get():RegisterTick(self)
end

--todo，SOL里可能不需要这个优先级
function SOLKillFeedbackMainView:IsPushLeft(cateGory)
    return true
end

--- 收到新的事件
function SOLKillFeedbackMainView:OnSOLStatisticsNtf(cateGory, distance, playerName, playerID, weaponName, bKillTeam)
    if self == nil then
        log("OnScoreInfoNtf self == nil")
        return
    end

    if cateGory == ESOLScoreName.ENone then
        --隐藏UI
        self:SetVisibility(ESlateVisibility.Hidden)
        return
    end

    --显示
    self:SetVisibility(ESlateVisibility.SelfHitTestInvisible)

    local gameState = GetInGameController():GetGameState()
    if gameState == nil then
        log("OnScoreInfoNtf gameState == nil")
        return
    end

    --先从表中读取
    local DataRow = nil
    local table = Facade.TableManager:GetTable("BattleField/BehaviorScoreTable")
    for _, value in pairs(table) do
        if value.ScoreName == cateGory then
            local find = false
            for _, mode in pairs(value.AvailableMode) do
                if mode == gameState.DFMGamePlayerMode then
                    find = true
                    break
                end
            end
            if find then
                DataRow = value
                break
            end
        end
    end

    if isinvalid(DataRow) then
        return
    end

    -- 内部有过滤，不会重复添加tick方法
    self:StartTick()
    
    local killEvent = { CateGory = cateGory, Sprite = DataRow.Icon, SpriteColor = DataRow.IconColor, Desc = DataRow.ShowText, DescColor = DataRow.ShowTextColor, Audio = DataRow.Audio, Distance = distance, PlayerName = playerName, PlayerNameColor = DataRow.PlayerNameColor, PlayerID = playerID, WeaponName = weaponName, KillTeam = bKillTeam }

    if self:IsPushLeft(cateGory) then
        self.KillEventIconQueue:push_left(killEvent)
    else
        self.KillEventIconQueue:push_right(killEvent)
    end

    self.KillEventInfoQueue:push_left(killEvent)
    
    log("OnSOLStatisticsNtf self.KillEventIconQueue:length():",self.KillEventIconQueue:length())

    --self.MoveSpeedMultiply = 1
end

--中间区域，单独的更新逻辑
function SOLKillFeedbackMainView:UpdateInfo(dt)
    if self.CurrentBehaviorStateInfo == Behavior.Idle then
        self.CurrentBehaviorStateInfo = Behavior.CheckAndPop
    end

    if self.CurrentBehaviorStateInfo == Behavior.CheckAndPop then
        if self.KillEventInfoQueue and self.KillEventInfoQueue:length() > 0 then
            if self.CurrentKillEventInfo == nil then
                self.CurrentKillEventInfo = self.KillEventInfoQueue:pop_left()
                if self.CurrentKillEventInfo then
                    self.CurrentBehaviorStateInfo = Behavior.Show
                    self.IsFlickerForInfoView = false
                end
            elseif self.SOLKillFeedbackInfoView and self.SOLKillFeedbackInfoView:IsInFadeInState() == false then
                --仅在show或fadeout阶段才可以刷新下一个事件
                local nextKillEvent = self.KillEventInfoQueue:peek_left()
                if nextKillEvent then
                    if nextKillEvent.PlayerID == self.CurrentKillEventInfo.PlayerID then
                        --立刻闪动替换
                        self.CurrentKillEventInfo = self.KillEventInfoQueue:pop_left()
                        self.CurrentBehaviorStateInfo = Behavior.Show
                        self.IsFlickerForInfoView = true
                    elseif self.SOLKillFeedbackInfoView:GetShowingTime() > 1.5 then
                        self.CurrentKillEventInfo = self.KillEventInfoQueue:pop_left()
                        self.CurrentBehaviorStateInfo = Behavior.Show
                        self.IsFlickerForInfoView = false
                    end
                else
                    self.KillEventInfoQueue:pop_left()
                end
            end
        end
    end

    if self.CurrentBehaviorStateInfo == Behavior.Show then
        if self.CurrentKillEventInfo then
            self.SOLKillFeedbackInfoView:SetContent(self.CurrentKillEventInfo.CateGory, self.CurrentKillEventInfo.Desc, self.CurrentKillEventInfo.DescColor, self.CurrentKillEventInfo.PlayerName, self.CurrentKillEventInfo.PlayerNameColor, self.CurrentKillEventInfo.WeaponName, self.IsFlickerForInfoView)

            self.CurrentBehaviorStateInfo = Behavior.Idle
        end
    end

    if self.SOLKillFeedbackInfoView then
        self.SOLKillFeedbackInfoView:Update(dt)
    end
end

-- 及时取消tick，有利于性能
function SOLKillFeedbackMainView:CheckStopTick()
    local StopInfoView = false
    if self.CurrentBehaviorStateInfo == Behavior.CheckAndPop and self.KillEventInfoQueue and self.KillEventInfoQueue:length() == 0
    and self.SOLKillFeedbackInfoView and self.SOLKillFeedbackInfoView:IsInIdleState() then
        StopInfoView = true
    end

    local StopIconView = false
    if self.CurrentBehaviorStateForIcon == Behavior.Idle and self.KillEventIconQueue and self.KillEventIconQueue:length() == 0
    and #self.IconViewList == 0 and #self.SpecialInfoViewList == 0 then
        StopIconView = true
    end

    return StopInfoView and StopIconView
end

function SOLKillFeedbackMainView:Update(dt)
    self.TimeStamp = self.TimeStamp + dt

    self:UpdateIcon(dt)

    --中间区域走单独的更新与刷新逻辑
    self:UpdateInfo(dt)

    if self:CheckStopTick() then
        --log("Update, CheckStopTick is true")
        self:StopTick()
    end
end

function SOLKillFeedbackMainView:UpdateIcon(dt)
    --log("SOLKillFeedbackMainView:UpdateIcon")
    
    --检查IconView是否可从队列移除
    for pos = #self.IconViewList, 1, -1 do
        if self.IconViewList[pos]:IsInExitState() then
            self.IconViewList[pos]:Collapsed()
            Facade.UIManager:CloseUI(self.IconViewList[pos])
            table.remove(self.IconViewList, pos)
        end
    end
    
    --FadeOut驱动整体Move
    if #self.IconViewList>=2 then
        if self.IconViewList[1]:IsInFadeOutState() then
            self.NeedMoveX=self.NeedMoveX+40
        end
    end
    if #self.IconViewList>=3 then
        if self.IconViewList[2]:IsInFadeOutState() then
            self.NeedMoveX=self.NeedMoveX+40
        end
    end
    if #self.IconViewList>=4 then
        if self.IconViewList[3]:IsInFadeOutState() then
            self.NeedMoveX=self.NeedMoveX+40
        end
    end
    if #self.IconViewList>=5 then
        if self.IconViewList[4]:IsInFadeOutState() then
            self.NeedMoveX=self.NeedMoveX+40
        end
    end

    --检查SpecialInfoView是否可从队列移除
    for pos = #self.SpecialInfoViewList, 1, -1 do
        if self.SpecialInfoViewList[pos]:IsInExitState() then
            self.SpecialInfoViewList[pos]:Collapsed()
            Facade.UIManager:CloseUI(self.SpecialInfoViewList[pos])
            table.remove(self.SpecialInfoViewList, pos)
        end
    end

    --检查上一个SpecialInfoView正在FadeIn，那么之前的SpecialInfoView都向下移动
    if #self.SpecialInfoViewList > 1 then
        if self.SpecialInfoViewList[#self.SpecialInfoViewList]:IsInFadeInState() then
            for i = #self.SpecialInfoViewList - 1, 1,-1 do
                self.SpecialInfoViewList[i]:Move()
            end
        end
    end

    if self.CurrentBehaviorStateForIcon == Behavior.Idle then
        ---检查是否有未处理事件
        if self.KillEventIconQueue ~= nil and self.KillEventIconQueue:length() > 0 then
            if self.TimeStamp - self.LastIconCreateTimeStamp >= Config.CreateIconInterval then
                self.LastIconCreateTimeStamp = self.TimeStamp
                self.CurrentBehaviorStateForIcon = Behavior.Pop
            end
        end
    end

    if self.CurrentBehaviorStateForIcon == Behavior.Pop then
        --抛出一个Icon事件
        --log("Update Pop self.KillEventIconQueue:length():", self.KillEventIconQueue:length())
        
        self.CurrentKillEventIcon = self.KillEventIconQueue:peek_left()
        if self.CurrentKillEventIcon then
            self.CurrentBehaviorStateForIcon = Behavior.Show
            self.KillEventIconQueue:pop_left()
            --log("Update Pop 1 self.KillEventIconQueue:length():", self.KillEventIconQueue:length())
        end
    end
    
    if self.CurrentBehaviorStateForIcon == Behavior.Show then
        ---创建Icon
        if self.CurrentKillEventIcon.Sprite ~= nil then
            local iconView = self:GetNewCachedIconView()
            if isinvalid(iconView) then
                return
            end
            self.Panel:AddChild(iconView)

            table.insert(self.IconViewList, iconView)

            iconView:InitPos(self.PosSetting_PC, self.PosSetting_Mobile)

            iconView:InitUI(self.CurrentKillEventIcon.CateGory)

            --设置初始位置
            local leftIconViewPosX=0
            if #self.IconViewList >= 2 then
                leftIconViewPosX = self.IconViewList[#self.IconViewList-1]:GetPosX()
                local posX = leftIconViewPosX - Config.MoveDistance * 2
                iconView:SetPosX(posX)
            else
                iconView:SetPosX(0)
            end

            ---新创建Icon驱动整体Move
            if #self.IconViewList >= 2 then
                if self.NeedMoveX == nil then
                    self.NeedMoveX = 0
                end
                local needMoveX = self.NeedMoveX + Config.MoveDistance
                self.NeedMoveX = needMoveX
                
                if #self.IconViewList == 2 then
                    ---创建Icon的同时，前面一个已经处于FadeOut，那么要叠加上FadeOut导致的移动。
                    if self.IconViewList[1]:IsInFadeOutState() or self.IconViewList[1]:IsInFadeOutKeepState() then
                        self.NeedMoveX = self.NeedMoveX + Config.MoveDistance
                    end
                end
            end

            iconView:SetSprite(self.CurrentKillEventIcon.Sprite, self.CurrentKillEventIcon.SpriteColor)
            self.CurrentKillEventIconIndex = self.CurrentKillEventIconIndex + 1
            iconView.Index = self.CurrentKillEventIconIndex

            --音频，复用攻防
            Facade.SoundManager:PlayUIAudioEvent(self.CurrentKillEventIcon.Audio)

            self.LastIconView = iconView

            self.LastIconCreateTimeStamp = self.TimeStamp
        else
            self.LastIconView = nil
        end

        ---创建SpecialInfo
        -- 救援 不显示区域3
        if self.CurrentKillEventIcon.CateGory ~= ESOLScoreName.ERescue then
            local specialInfoView = self:GetNewCachedSpecialInfoView()
            if isinvalid(specialInfoView) then
                return
            end

            self.Panel:AddChild(specialInfoView)

            specialInfoView:InitPos(self.PosSetting_PC, self.PosSetting_Mobile)
    
            specialInfoView:InitUI()
            specialInfoView:SetContent(self.CurrentKillEventIcon.Distance, self.CurrentKillEventIcon.KillTeam)
    
            table.insert(self.SpecialInfoViewList, specialInfoView)

            self.LastSpecialInfoView = specialInfoView
        end

        self.CurrentBehaviorStateForIcon = Behavior.Idle
    end

    ---当右侧开始FadeOut后，FadeOut和FadeIn一起触发2个Move，需要将移动速度提升到2倍
    if #self.IconViewList >= 2 and self.KillEventIconQueue:length() > 0 then
        if self.IconViewList[1]:IsInFadeOutState() then
            self.MoveSpeedMultiply = 2
        end
    end

    if #self.IconViewList == 0 then
        self.MoveSpeedMultiply = 1
    end

    self.MoveSpeedMultiply = math.ceil(self.NeedMoveX / Config.MoveDistance)

    ---IconView向右移动
    if #self.IconViewList >= 1 then
        if self.NeedMoveX > 0 then
            ---当前帧需要移动
            local frameNeedMoveX = self.MoveSpeedMultiply * dt * Config.MoveDistance / Config.MoveTime

            if self.NeedMoveX < frameNeedMoveX and self.KillEventIconQueue:length() == 0 then
                frameNeedMoveX = self.NeedMoveX
                self.NeedMoveX = 0
            else
                self.NeedMoveX = self.NeedMoveX - frameNeedMoveX
            end

            for _, value in pairs(self.IconViewList) do
                value:MoveRight(frameNeedMoveX)
            end
        end
    end

    --驱动Icon Update
    for pos = #self.IconViewList, 1, -1 do
        self.IconViewList[pos]:Update(dt)
    end

    --驱动SpecialInfoView Update
    for pos = #self.SpecialInfoViewList, 1, -1 do
        self.SpecialInfoViewList[pos]:Update(dt)
    end
end

function SOLKillFeedbackMainView:OnClose()
    self:_RemoveCallbacks()
    self:RemoveDelegate()
end

function SOLKillFeedbackMainView:Destroy()

end

function SOLKillFeedbackMainView:OnHide()
    --self:StopTick()
end

function SOLKillFeedbackMainView:StopTick()
    LuaTickController:Get():RemoveTick(self)
end

function SOLKillFeedbackMainView:_RemoveCallbacks()
    for i = 1, #self.callbacks do
        local callback = self.callbacks[i]
        if callback[3] then
            callback[1]:Remove(callback[3])
            callback[3] = nil
        end
    end
end

return SOLKillFeedbackMainView