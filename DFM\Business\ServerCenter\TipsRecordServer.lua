----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSTipsRecord)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 通过key-value的形式往服务器中存储数据。数据一般是服务器不关注的内容，纯客户端使用
-- key：int类型，客户端自定义，不要重复
-- value：底层会转换成string传给后端, 支持number，boolean，string，array(2维,会转化为str)，map(value只能为number，boolean，string)... 
--        array会转化为：{{1, 2, 3}, {4, 5}} -> "1,2,3;4,5"     注意，不要填空表，如{{},{1}}
--                      {1, 2, 3}           -> "1;2;3"
--                      {{1}, {2}, {3}}     -> "1;2;3"
--        map会转化为：  {a=1, b=2}          -> "a:1;b:2"
local TipsRecordServer = class("TipsRecordServer", require("DFM.YxFramework.Managers.Server.ServerBase")) 

local function logerr(...)
    logerror("[TipsRecordServer]", ...)
end

local function log(...)
    loginfo("[TipsRecordServer]", ...)
end

function TipsRecordServer:Ctor()
    -- 1001~1099 预留给引导相关使用
    self.keys = {
        GuideStageStep = 1001,      -- 手游引导进度记录
        GuideStageFinished = 1002,
        IsSetSkipGuideState = 1003,
        GuideGmShowState = 1004,
        GuideInGame3CFinished = 1005,
        GuideStageInGameInfo = 1006,
        GuideLootBtnShowTimes = 1007,
        GuideLootBtnClickedInGame = 1008,   -- 第二局sol中是否点击过loot按钮
        GuideSkipAllGuide = 1009,   -- 此账号是否跳过所有引导
        GuideSolVedioPlayed = 1010,   
        GuideWarehouseOpened = 1011,    -- 是否进入过仓库
        GuideDepartmentOpened = 1012,    -- 是否进入过部门
        GuideFirsetUnlockMap = 1013,    -- 第一个解锁的地图id
        GuideSpecLogSend = 1014,    -- 已经上报过的引导特殊日志
        MpGuideIsFinished = 1015,    -- mp引导是否完成 (主要由cpp调用记录)
        GuideStageStepHD = 1016,    -- pc引导进度记录
        NewMpGuideIsFinished = 1017,    -- 引导进度记录
        GuideIntroFirstDeadBody = 1018,    --  新手关首具尸体
        GuideSettlementVideoPlayed = 1019,    -- 结算视频教学记录
        GuideStageDayPlayed = 1020,    -- 教学当天的播放记录 guideStageId -> count
        GuideSkillDataFlowInfo = 1021,    -- 技能数据流教学的历史数据 skillDataId -> count
        ---------------------------------------------
        NotFirstClickMarkBtn = 1100,   -- 是否首次在局外点击mark按钮
        FirstOpenInsurance = 1101,
        StrategicSelectionUnlockAnim=1102, --战略版记录首次播放解锁动画
        StrategicSelectionRaidUnlock=1103, --战略版记录raid难度红点
        StrategicSelectionMultiPerson=1104, --战略版记录单排三排选择结果
        --安全屋红点（1201~1299）--
        SafeHouseDevice1001 = 1201,
        SafeHouseDevice1002 = 1202,
        SafeHouseDevice1003 = 1203,
        SafeHouseDevice1004 = 1204,
        SafeHouseDevice1005 = 1205,
        SafeHouseDevice1006 = 1206,
        SafeHouseDevice1007 = 1207,
        SafeHouseDevice1008 = 1208,
        SafeHouseDevice1009 = 1209,
        SafeHouseDevice1010 = 1210,
        SafeHouseDevice1011 = 1211,
        SafeHouseDevice1012 = 1212,
        SafeHouseDevice1013 = 1213,
        SafeHouseDevice1014 = 1214,
        SafeHouseDevice1015 = 1215,
        SafeHouseDevice1016 = 1216,
        SafeHouseDevice1017 = 1217,
        SafeHouseDevice1018 = 1218,
        SafeHouseDevice1019 = 1219,
        SafeHouseDevice1020 = 1220,
        --------------------------
        --游玩局数记录
        MPGameNum = 1301,
        SOLGameNum = 1302,
        --MP教学视频首次播放
        MPTeachVideoHasPlayed = 1401,
        -- 兵种道具重塑的二次确认弹窗
        HeroItemReshapeConfirm = 1402,
        -- 兵种道具玄学方案应用的二次确认弹窗
        HeroItemApplyConfirm = 1403,
        -- 兵种道具玄学方案存储槽覆盖的二次确认弹窗
        HeroItemOverrideConfirm = 1404,
        --lobby show skin
        LobbyShowSkin=1501,
        --自动购买sku门票
        SkuAutoPurchase=1502,
        --记录选择的mossAI
        MossAI=1503,
        --地图自动购买sku门票
        SkuAutoPurchaseList=1504,
        --积分赛界面是否打开过
        isTournamentPanelOpenedOnce=1601,
        --积分赛已结算的赛季
        TournamentSettlementedSeasons=1602,
        --排位赛界面是否打开过
        isRankingPanelOpenedOnce=1611,
        --排位赛已结算的赛季
        RankingSettlementedSeasons=1612,

        --大战场模式选择红点展示记录
        TDMMapReddotRecorder=1631,
        --大战场模式地图选择记录
        TDMMapSelectRecorder=1632,

        --是否关闭排位赛，默认开启
        CloseSOLRankMatch=1701,
        --是否关闭积分赛，默认开启
        CloseMPScoreMatch=1702,
        --是否关闭积分赛，默认开启
        ReflowAlreadyPlayUnlockAnim=1703,
        
        --mp曼德尔砖掉落获取完成动画是否已播放
        MPMandelBrickIsPlayGetAnim=1711,

        --BHD教学视频首次播放
        BHDTeachVideoHasPlayed = 2001,
        --BHDBGM首次播放
        BHDBGMFirstPlayed = 2002,

        --回流活动相关
        PlayerReturnLastDayLobbyPopupShown = 2100,  -- 上一次展示大厅签到引导弹窗是哪一天（每日只展示一次)
        PlayerReturnVO = {
            --记录回流活动配音是否播放过
            --每一次触发回流，第一次进入界面播放一次配音
            --为区分是哪一次触发，这里保存的是上次播放VO时的回流结束时间戳
            LobbyPopupFirstDay  = 2101,
            SignIn              = 2102,
            Task                = 2103,
            NewContent          = 2104,
        },
        PlayerReturnClickEntrance=2110,--是否点击过回流入口，mobile

        --军械库，每个物品上次展示时的任务阶段状态
        ArmoryLastStateShown = 2200,
        ArmoryLastActivatedItem = 2201,

        --Shop
        ShopMysteryRaffleHintShownMap = 2300,
        ShopMysteryDoubleConfirm = 2301,

        --CollectionRoom
        CollectionRoomShelveMap = 2400,
        CollectionRoomLevelMap = 2401,

        --赛季任务
        QuestSeasonalTutorial = 2500,

        --首次获得胜者为王段位红点
        FirstVictoryUniteRed = 2501,
        --本周是否打开称号解锁弹窗
        OpenUnlockTitleTime = 2502
    }
    self.events = {
        evtTipsRecordFetchDataFinished = LuaEvent:NewIns("evtTipsRecordFetchDataFinished"),
        evtTipsRecordSetDataSucceed = LuaEvent:NewIns("evtTipsRecordSetDataSucceed"),
    }

    --- Maps
    ---@type table<number, string>
    self._mapCacheData = {}
    self._mapCacheParseData = {} -- 针对map类型的，专门做一下缓存，减少解析消耗

	self:AddListeners()
end
--------------------------------------------------------------------------
--- 生命周期
--------------------------------------------------------------------------
function TipsRecordServer:OnInitServer()
end

function TipsRecordServer:OnDestroyServer()
    self._mapCacheData = {}
    self._mapCacheParseData = {}
    self:RemoveAllLuaEvent()
    --Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
end

--------------------------------------------------------------------------
--- 流程、事件相关
--------------------------------------------------------------------------
function TipsRecordServer:AddListeners()
end

function TipsRecordServer:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
	    --LuaTickController:Get():RegisterTick(self)
    end
end

function TipsRecordServer:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --LuaTickController:Get():RemoveTick(self)
    end
end

--------------------------------------------------------------------------
--- 初始化服务器的key-value数据
--------------------------------------------------------------------------
function TipsRecordServer:FetchServerData()
    self._mapCacheData = {}
    self._mapCacheParseData = {}
    self:_InitData()
end

--------------------------------------------------------------------------
--- private 协议相关
--------------------------------------------------------------------------
function TipsRecordServer:_RealSetData(key, strData)
    self._mapCacheData[key] = strData

    -- 发送给后端
    local fOnSetRecordDataRes = function(res)
        if res.result == 0 then
            log("_RealSetData:", key, strData)
            self.events.evtTipsRecordSetDataSucceed:Invoke(key)
        else
            logerr('_RealSetData 失败！！！')
            if self._mapCacheParseData[key] then
                self._mapCacheParseData[key] = nil
            end
            logbox("TipsRecord RealSetData 失败,", res.result, key, strData)
        end
    end
    local req = pb.CSGuideSetDataReq:New()
    local data = {
        key = key,
        data = strData,
    }
    req.data = {data}
    req:Request(fOnSetRecordDataRes, {bEnableHighFrequency = true})
end

function TipsRecordServer:_InitData()
    ---@param res pb_CSGuideGetDataRes
    local fOnGetRecordDataRes = function(res)
        if res.result == 0 then
            log('拉取tips record成功')
            self._mapCacheData = {}
            for _, guideCustomData in ipairs(res.data) do
                self._mapCacheData[guideCustomData.key] = guideCustomData.data
                log(">>>", guideCustomData.key, guideCustomData.data)
            end
            self.events.evtTipsRecordFetchDataFinished:Invoke()
        elseif res.result > 0 then
            logbox('拉取tips record失败', res.result)
        else
            logerr('拉取tips record失败，等待重新发包', res.result)
            return
            --self:_InitData()
        end
    end
    local req = pb.CSGuideGetDataReq:New()
    req.get_all = true
    req:Request(fOnGetRecordDataRes, {bNeedResendAfterReconnected = true, bEnableHighFrequency = true})
end

--------------------------------------------------------------------------
--- public 读取数据
--------------------------------------------------------------------------
function TipsRecordServer:SetNumber(key, numberValue)
    self:_RealSetData(key, tostring(numberValue))
end

function TipsRecordServer:SetBoolean(key, booleanValue)
    if self:GetBoolean(key) == booleanValue then
        -- loginfo("TipsRecordServer:SetBoolean, value repeat", booleanValue)
        return
    end
    self:_RealSetData(key, booleanValue and "1" or "0")
end

function TipsRecordServer:SetString(key, strValue)
    self:_RealSetData(key, strValue)
end

function TipsRecordServer:SetArray(key, arrValue)
    local ret = ""
    for idx, data in ipairs(arrValue) do
        local dataType = type(data)
        if dataType == "table" then
            arrValue[idx] = table.concat(data, ",")
        elseif dataType == "string" then
        elseif dataType == "number" then
            arrValue[idx] = tostring(data)
        elseif dataType == "boolean" then
            arrValue[idx] = data and "1" or "0"
        else
            logwarning("TipsRecordServer:SetArray fail, data:", data)
            return
        end
    end
    ret = table.concat(arrValue, ";")
    loginfo("TipsRecordServer:SetArray:", ret)
    self:_RealSetData(key, ret)
end

function TipsRecordServer:SetMap(key, mapValue)
    local ret = ""
    local arrValue = {}
    local typeStr = "1%s"
    local typeNum = "2%d"
    local typeBool = "3%s"
    for k, data in pairs(mapValue) do
        local dataType = type(data)
        local strData = ""
        if dataType == "string" then
            strData = string.format(typeStr, data)
        elseif dataType == "number" then
            strData = tostring(data)
            strData = string.format(typeNum, strData)
        elseif dataType == "boolean" then
            strData = data and "1" or "0"
            strData = string.format(typeBool, strData)
        else
            logwarning("TipsRecordServer:SetArray fail, data:", data)
            return
        end
        table.insert(arrValue, string.format("%s:%s", k, strData))
    end
    ret = table.concat(arrValue, ";")
    loginfo("TipsRecordServer:SetMap:", key, ret)
    self:_RealSetData(key, ret)
    self._mapCacheParseData[key] = mapValue
end

function TipsRecordServer:GetNumber(key)
    local value = self._mapCacheData[key]
    if value == nil then
        loginfo("TipsRecordServer:GetNumber is nil, key:", key)
        return 0
    end
    return tonumber(value)
end

function TipsRecordServer:GetBoolean(key)
    local value = self._mapCacheData[key]
    if value == nil or value == "0" then
        return false
    end
    return true
end

function TipsRecordServer:GetString(key)
    local value = self._mapCacheData[key]
    if value == nil then
        return ""
    end
    return value
end

--dimension:数组维度。默认2维
function TipsRecordServer:GetArray(key,dimension)
    local ret = {}
    local arrValue = self._mapCacheData[key]
    if not dimension then
        dimension=2
    end

    if arrValue == nil then
        return ret
    end

    local argsList = StringUtil.StringSplit(arrValue, ";")
    if not argsList then
        return ret
    end
    if dimension==2 then
       for _, argStr in ipairs(argsList) do
           local tempTable = StringUtil.StringSplit(argStr, ",")
           if tempTable then
              table.insert(ret, tempTable)
           else
              table.insert(ret, {})
           end
       end
       return ret
    else
        return argsList
    end
end

function TipsRecordServer:GetMap(key)
    local ret = self._mapCacheParseData[key]
    if ret then
        return ret
    end

    ret = {}
    local mapValue = self._mapCacheData[key]

    if mapValue == nil then
        return ret
    end

    local argsList = StringUtil.StringSplit(mapValue, ";")
    if not argsList then
        return ret
    end

    local typeStr = "1"
    local typeNum = "2"
    local typeBool = "3"
    for _, argStr in ipairs(argsList) do
        local tempTable = StringUtil.StringSplit(argStr, ":")
        if tempTable and #tempTable == 2 then
            local dataType = type(tempTable[2])
            local value = tempTable[2]
            local prefix = string.sub(value, 1, 1)
            local value = string.sub(value, 2)
            if prefix == typeNum then
                value = tonumber(value)
            elseif prefix == typeBool then
                if value == "0" then
                    value = false
                else
                    value = true
                end
            end
            ret[tempTable[1]] = value
        else
            err("AnalysisArgs, mapValue wrong, str:", mapValue)
        end
    end

    self._mapCacheParseData[key] = ret
    return ret
end

function TipsRecordServer:GetDailyMap(key, resetClock )
    -- 默认每天4点重置一次
    resetClock = resetClock or 4

    local map = self:GetMap(key)
    local date = map["__date"]
    local currentTimestamp = Facade.ClockManager:GetLocalTimestamp()
    
    -- Calculate today's date (as a string in format YYYYMMDD)
    local todayStr = os.date("%Y%m%d", currentTimestamp)
    
    -- Check if we need to reset the map
    if date == nil or date ~= todayStr then
        -- Calculate if we passed today's reset time
        local currentTime = os.date("*t", currentTimestamp)
        local currentHour = currentTime.hour
        
        -- If it's a new day or we've passed the reset hour, reset the map
        if date == nil or date ~= todayStr or currentHour >= resetClock then
            map = {}
            map["__date"] = todayStr
            self:SetMap(key, map)
            log("Another day and  Daily map reset for key:", key)
        end
    end

    return map
end

return TipsRecordServer
