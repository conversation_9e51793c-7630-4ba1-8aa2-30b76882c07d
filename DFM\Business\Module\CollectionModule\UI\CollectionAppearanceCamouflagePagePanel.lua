----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionAppearanceCamouflagePagePanel = ui("CollectionAppearanceCamouflagePagePanel")
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local ItemDetailTitleComp = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailTitleComp"
local EIVSlotPos = Module.CommonWidget.Config.EIVSlotPos
local EIVCompOrder = Module.CommonWidget.Config.EIVCompOrder
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
local EDescendantScrollDestination = import "EDescendantScrollDestination"
-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

function CollectionAppearanceCamouflagePagePanel:Ctor()
    self._wtCurrentCamouflageItemView = self:Wnd("wtCurrentCamouflageItemView", IVCommonItemTemplate)
    self._wtWeaponSkinGroupWaterFallBox = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinGroupWaterFallBox", self._OnGetWeaponSkinGroupCount, self._OnProcessWeaponSkinGroupWidget)
    self._wtItemDetailTitle = self:Wnd("wtItemDetailTitle", ItemDetailTitleComp)
    self._wtItemDetailTitle:SetDetailBtnVisible(false)
    self._wtUnlockedNumTxt = self:Wnd("wtUnlockedNumTxt", UITextBlock)
    self._wtTaskScrollBox = UIUtil.WndScrollBox(self, "wtTaskScrollBox", self._OnGetTaskCount, self._OnProcessTaskWidget)
    self._wtDetailBtn_Mobile = self:Wnd("wtDetailBtn_Mobile", DFCommonButtonOnly)
    self._wtDetailBtn_Mobile:Event("OnClicked", self._JumpToSkinDetailPage, self)
    self._wtDetailBtn_PC = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)
    self._wtDetailBtn_PC:Event("OnClicked", self._JumpToSkinDetailPage, self)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtTipAnchor:Collapsed()
    self._wtActionBtn = self:Wnd("wtActionBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnActionBtnClicked, self)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self._selectedGroupIndex = -1
    self._selectedSkinIndex = -1
    self._selectedCell = nil
    self._weaponSkinGroups = {}
    self._selectedSkinTasks = {}
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end


function CollectionAppearanceCamouflagePagePanel:OnInitExtraData()
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionAppearanceCamouflagePagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionAppearanceCamouflagePagePanel:OnClose()
    self:RemoveAllLuaEvent()
    self._selectedCell = nil
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
end

function CollectionAppearanceCamouflagePagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionAppearanceCamouflagePagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionAppearanceCamouflagePagePanel:OnShow()
    if not self._weaponSkinScrollStopHandle then
        self._weaponSkinScrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtWeaponSkinGroupWaterFallBox, self)  
    end
    if not self._taskScrollStopHandle then
        self._taskScrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtTaskScrollBox, self)    
    end
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionAppearanceCamouflagePagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtActionBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    self._wtDetailBtn_PC:SetDisplayInputAction("GunSkin_ShowDetail", true, nil, true)    
    if not self._wtDetailBtnHandle then
        self._wtDetailBtnHandle = self:AddInputActionBinding("GunSkin_ShowDetail", EInputEvent.IE_Pressed, self._JumpToSkinDetailPage, self, EDisplayInputActionPriority.UI_Stack)
    end
    if not self._wtNavGroupSkinList then 
        self._wtNavGroupSkinList = WidgetUtil.RegisterNavigationGroup(self._wtWeaponSkinGroupWaterFallBox, self, "Hittest")
        if self._wtNavGroupSkinList then
            self._wtNavGroupSkinList:AddNavWidgetToArray(self._wtWeaponSkinGroupWaterFallBox)
            self._wtNavGroupSkinList:SetScrollRecipient(self._wtWeaponSkinGroupWaterFallBox)
            self._wtNavGroupSkinList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    if not self._wtNavGroupTaskList then 
        self._wtNavGroupTaskList = WidgetUtil.RegisterNavigationGroup(self._wtTaskScrollBox, self, "Hittest")
        if self._wtNavGroupTaskList then
            self._wtNavGroupTaskList:AddNavWidgetToArray(self._wtTaskScrollBox)
            self._wtNavGroupTaskList:SetScrollRecipient(self._wtTaskScrollBox)
        end
    end 
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionAppearanceCamouflagePagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RemoveInputActionForActionBtn()
    if self._wtDetailBtnHandle then
        self:RemoveInputActionBinding(self._wtDetailBtnHandle)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._wtActionBtnHandle  = nil 
    self._wtDetailBtnHandle = nil 
    self._NavConfigHandler = nil
    self._wtNavGroupSkinList = nil
    self._wtNavGroupTaskList = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionAppearanceCamouflagePagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupSkinList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupSkinList)
    end
end

function CollectionAppearanceCamouflagePagePanel:_DisableGamepadA(FocusedNavGroup)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then 
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionAppearanceCamouflagePagePanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionAppearanceCamouflagePagePanel:_AddInputActionForActionBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._bEnableActionBtn and not self._wtActionBtnHandle then
        self._wtActionBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnActionBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionAppearanceCamouflagePagePanel:_RemoveInputActionForActionBtn()
    if  self._wtActionBtnHandle then
        self:RemoveInputActionBinding(self._wtActionBtnHandle)
        self._wtActionBtnHandle = nil 
    end
end
-- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionAppearanceCamouflagePagePanel:OnHide()
    if self._weaponSkinScrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._weaponSkinScrollStopHandle)
		self._weaponSkinScrollStopHandle = nil
	end
    if self._taskScrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._taskScrollStopHandle)
		self._taskScrollStopHandle = nil
	end
end

function CollectionAppearanceCamouflagePagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_Camouflage_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

function CollectionAppearanceCamouflagePagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        if bFullReset == true then
            self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCamouflageStatusInfo, self._OnLoadCamouflageStatusInfo, self)
            self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionActivateSkinPattern, self._OnLoadCamouflageStatusInfo, self)
            self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
            self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
            self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        end
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self._selectedCell = nil
            self:RemoveLuaEvent(Server.CollectionServer.Events.evtFetchCamouflageStatusInfo, self._OnLoadCamouflageStatusInfo, self)
            self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionActivateSkinPattern, self._OnLoadCamouflageStatusInfo, self)
            self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
            self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
            self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
        end
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self._bHideUI = nil
    end
end

function CollectionAppearanceCamouflagePagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    if bResetTab then
        self:_ShowUI()
    end
    self._activatedCamouflageInfo = Server.CollectionServer:GetActivatedCamouflageInfo(false)
    if self._activatedCamouflageInfo then
        self:_OnLoadCamouflageStatusInfo()
    else
        self:HideUI(true)
        self:OnRefreshModel(ESubStage.HallCollectionNew)
        self:UpdateBackground()
        Server.CollectionServer:FetchCamouflageStatusInfo() 
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnRefreshWeaponSkinItems(bResetList)
    if bResetList then
        self._selectedCell = nil
        self._selectedGroupIndex = -1
        self._selectedSkinIndex = -1
        local targetPropId = Module.Collection.Field:GetSelectedPropId()
        Module.Collection.Field:SetSelectedPropId()
        if targetPropId then
            local targetIndex
            for groupIndex, skinGroup in ipairs(self._weaponSkinGroups) do
                for skinIndex, skinItem in ipairs(skinGroup) do
                    if skinItem.id == targetPropId then
                        targetIndex = groupIndex
                        self._selectedGroupIndex = groupIndex
                        self._selectedSkinIndex = skinIndex
                        self._wtWeaponSkinGroupWaterFallBox:RemoveEvent('OnProcessItemsUpdateFinished')
                        self._wtWeaponSkinGroupWaterFallBox:Event('OnProcessItemsUpdateFinished', self._OnProcessWidgetUpdateFinished, self)
                        break
                    end
                end
                if targetIndex then
                    break
                end
            end
        elseif self._weaponSkinGroups and self._weaponSkinGroups[1] and self._weaponSkinGroups[1][1] then
            self._selectedGroupIndex = 1
            self._selectedSkinIndex = 1
        end
        self._wtWeaponSkinGroupWaterFallBox:RefreshAllItems()
    else
        if #self._weaponSkinGroups == 0 then
            self._wtWeaponSkinGroupWaterFallBox:RefreshAllItems()
        else
            self._wtWeaponSkinGroupWaterFallBox:RefreshVisibleItems()
        end
    end
    self:_RefreshItemUI(bResetList)
end

function CollectionAppearanceCamouflagePagePanel:_RefreshCurrentChallenge()
    if not hasdestroy(self._wtUnlockedNumTxt) then
        self._wtUnlockedNumTxt:SetText(tostring(self._activatedCamouflageInfo.unlockedNum).."/"..tostring(#self._activatedCamouflageInfo.skinItems))
    end
    if not hasdestroy(self._wtCurrentCamouflageItemView) then
        local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", self._activatedCamouflageInfo.patternId)
        local fClickCb = CreateCallBack(self._JumpToSwitchChallengePage, self)
        self._wtCurrentCamouflageItemView:BindCustomOnClicked(fClickCb)
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            local taskRedDotsMap = Server.CollectionServer:GetTaskRedDots()
            for taskId, value in pairs(taskRedDotsMap) do
                if taskId < 20000 and value == true then
                    return true
                end
            end
            local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
            for taskId, value in pairs(newArrivedTaskRedDotsMap) do
                if taskId < 20000 and value == true then
                    return true
                end
            end
            return false
        end,self)
        if isvalid(self._redDotInsMap[self._wtCurrentCamouflageItemView]) and self._redDotInsMap[self._wtCurrentCamouflageItemView]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(self._wtCurrentCamouflageItemView, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[self._wtCurrentCamouflageItemView], fCheckFunc, nil)
        else
            self._redDotInsMap[self._wtCurrentCamouflageItemView] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,self._wtCurrentCamouflageItemView, {reddotType=EReddotType.Normal, zOrder=10})
        end
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_SetUp_Icon_0006.CommonHall_SetUp_Icon_0006"
        local qualityColor = ItemConfigTool.GetItemQualityLinearColor(0)
        local name = ""
        if camouflageDataRow then
            if camouflageDataRow.SkinIconPath.AssetPathName ~= "None" then
                iconPath = camouflageDataRow.SkinIconPath
            end
            qualityColor = ItemConfigTool.GetItemQualityLinearColor(camouflageDataRow.SkinQuality, 0.25)
            name = camouflageDataRow.SkinName
        end
        local actualImgComponent = self._wtCurrentCamouflageItemView._wtItemIcon:GetActualIconImg()
        if actualImgComponent then
            actualImgComponent:AsyncSetImagePath(iconPath)
        end
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.ItemIcon, true)
        local nameComp = self._wtCurrentCamouflageItemView:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
        nameComp:SetBaseWeaponName(true)
        nameComp:ClearLinkageType()
        nameComp:SetMainTxt(name)
        nameComp:RefreshComponent()
        nameComp:SetQualityColor(qualityColor)
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.TopRightItemNameText, true)
        local iconTextComp = self._wtCurrentCamouflageItemView:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
        iconTextComp:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Switch.Common_De_Switch")
        iconTextComp:SetCornerStyle() 
        iconTextComp:RefreshComponent()
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.BottomRightIconText, true)
    end
end

function CollectionAppearanceCamouflagePagePanel:_JumpToSwitchChallengePage()
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    local baseWeaponId = 0
    if skinItem then
        baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionAppearanceSwitchChallengePage, nil, self, false, baseWeaponId)
end

function CollectionAppearanceCamouflagePagePanel:_OnGetWeaponSkinGroupCount()
    return #self._weaponSkinGroups
end

function CollectionAppearanceCamouflagePagePanel:_OnProcessWeaponSkinGroupWidget(position, groupItemWidget)
    local groupIndex = position
    local group = self._weaponSkinGroups[groupIndex]
    if group then
        local bSetTitle = false
        local name
        groupItemWidget:ClearItemWidgets()
        for skinIndex, skinItem in ipairs(group) do
            if not bSetTitle then
                name = ItemConfig.MapWeaponItemType2Name[skinItem.itemSubType] or CollectionConfig.Loc.UnknownWeapon
                bSetTitle = true
                groupItemWidget:SetTitle(name)
                if groupIndex == #self._weaponSkinGroups then
                    local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", self._activatedCamouflageInfo.patternId)
                    if camouflageDataRow and camouflageDataRow.bHasMore then
                        groupItemWidget:SetExtraHint(StringUtil.Key2StrFormat(CollectionConfig.Loc.MoreCamouflageSkinHint,
                        {["PatternName"] = camouflageDataRow.SkinName}))
                    end
                end
            end
            local skinItemWidget = groupItemWidget:CreateItemWidget()
            if not hasdestroy(skinItemWidget) then
                skinItemWidget:StopIVAnimation("WBP_CommonItemTemplate_in_special")
                if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) then
                    skinItemWidget:SetAlreadyGetState()
                else
                    local taskInfo = Server.CollectionServer:GetSkinTaskInfoBySkinId(skinItem.id)
                    if taskInfo.MP_TaskInfo.bFinished or taskInfo.SOL_TaskInfo.bFinished then
                        skinItemWidget:UnsetAlreadyGetState()
                        skinItemWidget:PlayIVAnimation("WBP_CommonItemTemplate_in_special", 0, EUMGSequencePlayMode.Forward, 1, false)
                    else
                        skinItemWidget:UnsetAlreadyGetState()
                    end
                end
                skinItemWidget:SetRootSize(IsHD() and 192 or 140, IsHD() and 192 or 140)
                skinItemWidget:InitItem(skinItem)
                local fClickCb = CreateCallBack(self._OnWeaponSkinItemClick, self, skinItemWidget, groupIndex, skinIndex)
                skinItemWidget:BindCustomOnClicked(fClickCb)
                if self._selectedGroupIndex == groupIndex and self._selectedSkinIndex == skinIndex then
                    self._selectedCell = skinItemWidget
                    WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
                end
                skinItemWidget:SetSelected(skinItem, self._selectedGroupIndex == groupIndex and self._selectedSkinIndex == skinIndex)
            end
        end
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnProcessWidgetUpdateFinished()
    self._wtWeaponSkinGroupWaterFallBox:RemoveEvent('OnProcessItemsUpdateFinished')
    Timer.DelayCall(0.2, function ()
        self._wtWeaponSkinGroupWaterFallBox:ScrollToIndexIfNotInScreen(self._selectedGroupIndex, true)
        local offset = 0
        local itemIndex = 0
        for groupIndex, group in ipairs(self._weaponSkinGroups) do
            for skinIndex, skinItem in ipairs(group) do
                itemIndex = itemIndex + 1
                if groupIndex == self._selectedGroupIndex and skinIndex == self._selectedSkinIndex then
                    offset = (math.floor(itemIndex/3)-1)*(IsHD() and 236 or 184)
                    break
                end
            end
            if offset > 0 then
                self._wtWeaponSkinGroupWaterFallBox:SetScrollOffset(offset)
                break
            end
        end
    end, self)
end

function CollectionAppearanceCamouflagePagePanel:_OnGetTaskCount()
    return #self._selectedSkinTasks
end

function CollectionAppearanceCamouflagePagePanel:_OnProcessTaskWidget(position, taskItemWidget)
    local taskIndex = position + 1
    local taskInfo = self._selectedSkinTasks[taskIndex]
    if isvalid(taskInfo) then
        local goalConfigRow = Facade.TableManager:GetRowByKey("ActivityTaskGoals", taskInfo.goalId)
        local taskDesc = goalConfigRow and goalConfigRow.Remark or Module.CommonWidget.Config.Loc.UnknownMission
        taskDesc = string.isempty(taskDesc) and Module.CommonWidget.Config.Loc.UnknownMission or taskDesc
        taskItemWidget:SetTaskDesc(StringUtil.Key2StrFormat(CollectionConfig.Loc.TaskDescWithProgress,
                {["currentNum"] = tostring(taskInfo.curValue),
                 ["maxNum"] = tostring(taskInfo.maxValue),
                 ["targetDesc"] = taskDesc}))
        local type = 1
        if taskInfo.bIsPreTask or self._selectedCurrentModeSkinTaskInfo.bUnlocked then
            type = taskInfo.curValue < taskInfo.maxValue and 2 or 0
        end
        taskItemWidget:SetCurrentType(type)
        taskItemWidget:SetLineVisible(taskIndex < #self._selectedSkinTasks)
    end
end

function CollectionAppearanceCamouflagePagePanel:OnRefreshModel(curSubStageType)
    local currentStackUI = Facade.UIManager:GetCurrentStackUI()
    if currentStackUI.UINavID == UIName2ID.CollectionMainPanel and (not curSubStageType or curSubStageType == ESubStage.HallCollectionNew) then
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
        if isvalid(skinItem) then
            local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(skinItem.id)
            if weaponDesc == nil then
                weaponDesc = skinItem:GetRawDescObj()
            end
            if isvalid(weaponDesc) then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, false)
            end
        end
    end
end


function CollectionAppearanceCamouflagePagePanel:_OnWeaponSkinItemClick(skinItemWidget, groupIndex, skinIndex)
    if self._selectedGroupIndex ~= groupIndex or self._selectedSkinIndex ~= skinIndex then
        if self._selectedCell then
            self._selectedCell:SetSelected(nil, false)
            if IsHD() and WidgetUtil.IsGamepad() then
                self._selectedCell:SetCppValue("bHandleClick" , true)
            end
        end
        self._selectedCell = skinItemWidget
        self._selectedGroupIndex = groupIndex
        self._selectedSkinIndex = skinIndex
        if IsHD() and WidgetUtil.IsGamepad() then
            self._selectedCell:SetCppValue("bHandleClick" , false)
        end
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        if skinItem then
            self._selectedCell:SetSelected(skinItem, true)
        end
        self:_RefreshItemUI(true)
    end
end



function CollectionAppearanceCamouflagePagePanel:_RefreshItemUI(bReset)
    self._wtTaskScrollBox:Collapsed()
    self._bEnableActionBtn = false
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    if bReset or #self._weaponSkinGroups == 0 then
        if isvalid(skinItem) then
            self._wtItemDetailTitle:SelfHitTestInvisible()
            if IsHD() then
                self._wtDetailBtn_PC:Visible()
                self._wtDetailBtn_Mobile:Collapsed()
                self:_DownLoadResources()
            else
                self._wtDetailBtn_Mobile:Visible()
                self._wtDetailBtn_PC:Collapsed()
            end
            self._wtQuestionHintBox:SelfHitTestInvisible()
            if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) then
                self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.Unlocked)
                self._wtActionBtn:SetIsEnabled(false)
            end
        else
            self._wtItemDetailTitle:Collapsed()
            self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
            self._wtActionBtn:SetIsEnabled(false)
            self._wtTipCheckBtn:Collapsed()
            self._wtDetailBtn_Mobile:Collapsed()
            self._wtDetailBtn_PC:Collapsed()
            self._wtQuestionHintBox:Collapsed()
        end
    else
        self._wtTaskScrollBox:RefreshVisibleItems()
    end
    if isvalid(skinItem) then
        local modeTitle
        if CollectionLogic.IsInMp() then
            modeTitle = "<dfmrichtext type=\"img\" width=\"64\" height=\"64\" id=\"Icon_MP\" align=\"0\"/>"..Module.IrisSafeHouse.Config.Loc.BFModeTitle
        else
            modeTitle = "<dfmrichtext type=\"img\" width=\"64\" height=\"64\" id=\"Icon_SOL\" align=\"0\"/>"..Module.IrisSafeHouse.Config.Loc.IrisModeTitle
        end
        self._wtItemDetailTitle:SetInfo(
                skinItem.name,
                modeTitle,
                skinItem.quality
        )
        local weaponSkinProgressCallback = CreateCallBack(self._OnLoadWeaponSkinUnlockProgress, self)
        Server.CollectionServer:LoadWeaponSkinProgress(skinItem.id, weaponSkinProgressCallback)
    end
    self._shotcutList = {}
    if IsHD() then
        if self._bHideUI then
            table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        if isvalid(skinItem) then
            table.insert(self._shotcutList, {actionName = "Collection_Tip_Gamepad",func = self._ShowWeaponSkinTaskProgressPop, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        table.insert(self._shotcutList, {actionName = "Collection_SwitchChallenge_Gamepad",func = self._JumpToSwitchChallengePage, caller = self ,bUIOnly = false, bHideIcon = false}) 
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
    self:_RefreshDownloadBtn()
    self:OnRefreshModel(ESubStage.HallCollectionNew)
    self:UpdateBackground()
end

function CollectionAppearanceCamouflagePagePanel:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:Visible()
        logerror("[v_dzhanshen] CollectionAppearanceCamouflagePagePanel:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        logerror("[v_dzhanshen] CollectionAppearanceCamouflagePagePanel:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
    logerror("[v_dzhanshen] CollectionAppearanceCamouflagePagePanel:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtWeaponSkinGridBox:RefreshVisibleItems()
        self:OnRefreshModel(ESubStage.HallCollectionNew)
        self:_RefreshDownloadBtn()
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionAppearanceCamouflagePagePanel:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        local bDownloaded =  Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionAppearanceCamouflagePagePanel:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(skinItem) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnLoadCamouflageStatusInfo()
    self._activatedCamouflageInfo = Server.CollectionServer:GetActivatedCamouflageInfo(false)
    if self._activatedCamouflageInfo then
        self._weaponSkinItems = self._activatedCamouflageInfo.skinItems
        self:HideUI(false)
        self:_RefreshCurrentChallenge()
        self:_CreateWeaponSkinGroups()
        self:_OnRefreshWeaponSkinItems(true)
    end
end

function CollectionAppearanceCamouflagePagePanel:_CreateWeaponSkinGroups()
    table.sort(self._weaponSkinItems, CollectionLogic.WeaponSkinInCamouflageSort) 
    self._weaponSkinGroups = {}
    local weaponTypeMap = {}
    for index, skinItem in ipairs(self._weaponSkinItems) do
        if not weaponTypeMap[skinItem.itemSubType] then
            declare_if_nil(weaponTypeMap, skinItem.itemSubType, {})
            table.insert(self._weaponSkinGroups, weaponTypeMap[skinItem.itemSubType])
        end
        table.insert(weaponTypeMap[skinItem.itemSubType], skinItem)
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnLoadWeaponSkinUnlockProgress(res, skinId, skinTaskInfo)
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    if skinItem and skinItem.id == skinId then
        self._selectedSkinTaskInfo = skinTaskInfo
        self._selectedCurrentModeSkinTaskInfo = CollectionLogic.IsInMp() and self._selectedSkinTaskInfo.MP_TaskInfo or self._selectedSkinTaskInfo.SOL_TaskInfo
        self._selectedSkinTasks = {}
        for index, taskInfo in ipairs(self._selectedCurrentModeSkinTaskInfo.pre_Tasks) do
            table.insert(self._selectedSkinTasks, taskInfo)
        end
        for index, taskInfo in ipairs(self._selectedCurrentModeSkinTaskInfo.tasks) do
            table.insert(self._selectedSkinTasks, taskInfo)
        end
        --table.sort(self._selectedSkinTasks, CollectionLogic.WeaponSkinTaskSort) 
        --self._wtTaskScrollBox:ScrollToStart()
        self._wtTaskScrollBox:SelfHitTestInvisible()
        self._wtTaskScrollBox:RefreshAllItems()
        local bCanRetrieve = false
        if self._selectedSkinTaskInfo then
            bCanRetrieve = self._selectedSkinTaskInfo.MP_TaskInfo.bFinished == true or self._selectedSkinTaskInfo.SOL_TaskInfo.bFinished == true
            self._wtTipCheckBtn:Visible()
        end
        self._bCanRetrieveSkinId = nil
        if bCanRetrieve == true then
            self._bCanRetrieveSkinId = skinId
        end
        if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) then
            self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.Unlocked)
            self._wtActionBtn:SetIsEnabled(false)
            self._bEnableActionBtn = false
        else
            self._wtActionBtn:SetMainTitle(bCanRetrieve == true and CollectionConfig.Loc.RetrieveWeaponSkin or CollectionConfig.Loc.NotUnlocked)
            self._wtActionBtn:SetIsEnabled(bCanRetrieve)
            self._bEnableActionBtn = bCanRetrieve
        end
    else
        self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
        self._wtActionBtn:SetIsEnabled(false)
    end
    if self._bEnableActionBtn then
        self:_AddInputActionForActionBtn()
    else
        self:_RemoveInputActionForActionBtn()
    end
end

function CollectionAppearanceCamouflagePagePanel:_OnActionBtnClicked()
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    if skinItem then
        Server.CollectionServer:RetrieveWeaponSkin(skinItem.id)
    end
end

function CollectionAppearanceCamouflagePagePanel:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    else
        self:_HideUI()
    end
end

function CollectionAppearanceCamouflagePagePanel:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionAppearanceCamouflagePagePanel:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
        if isvalid(skinItem) then
            table.insert(self._shotcutList, {actionName = "Collection_Tip_Gamepad",func = self._ShowWeaponSkinTaskProgressPop, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        table.insert(self._shotcutList, {actionName = "Collection_SwitchChallenge_Gamepad",func = self._JumpToSwitchChallengePage, caller = self ,bUIOnly = false, bHideIcon = false}) 
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end

function CollectionAppearanceCamouflagePagePanel:_JumpToSkinDetailPage()
    local skinItem = self:_GetSkinItem(self._selectedGroupIndex, self._selectedSkinIndex)
    if isvalid(skinItem) then
        Module.Collection:ShowWeaponSkinDetailPage(skinItem)
    end
end

function CollectionAppearanceCamouflagePagePanel:ClosePopup()
end

function CollectionAppearanceCamouflagePagePanel:_ShowWeaponSkinTaskProgressPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.CommonWeaponSkinMissionOverview, nil, self, self._selectedSkinTaskInfo)
end

function CollectionAppearanceCamouflagePagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if bChecked then
        self:_ShowWeaponSkinTaskProgressPop()
    end
    self._wtTipCheckBtn:SetSelectState(false, false)
end

function CollectionAppearanceCamouflagePagePanel:OnInputTypeChanged(inputType)
    self._wtWeaponSkinGroupWaterFallBox:RefreshVisibleItems()
end

function CollectionAppearanceCamouflagePagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionAppearanceCamouflagePagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

function CollectionAppearanceCamouflagePagePanel:_GetSkinItem(groupIndex, skinIndex)
    groupIndex = groupIndex or self._selectedGroupIndex
    skinIndex = skinIndex or self._selectedSkinIndex
    local group = self._weaponSkinGroups[groupIndex]
    if group then
        return group[skinIndex]
    end
    return nil
end

return CollectionAppearanceCamouflagePagePanel
