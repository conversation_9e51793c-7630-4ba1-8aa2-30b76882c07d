----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------


local GunsmithUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithUI"
local GunsmithUnlockPathItemUI = require "DFM.Business.Module.GunsmithModule.UI.WeaponUpgrade.GunsmithUnlockPathItemUI"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"

local ItemUIDataContainer = require "DFM.Business.DataStruct.UIDataStruct.ItemUIDataContainer"
local GunsmithUnlockPathItemUIData = require "DFM.Business.Module.GunsmithModule.Data.WeaponUpgrade.GunsmithUnlockPathItemUIData"
local EGunsmithPartMPUnlockMethod = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithPartMPUnlockMethod"

local GunsmithPartUnlockLogic = require "DFM.Business.Module.GunsmithModule.Logic.Unlock.GunsmithPartUnlockLogic"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithPartMainLogic = require "DFM.Business.Module.GunsmithModule.Logic.Part.GunsmithPartMainLogic"

local ECheckBoxState = import"ECheckBoxState"

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class GunsmithUnlockPathUI : GunsmithUI
local GunsmithUnlockPathUI = ui("GunsmithUnlockPathUI", GunsmithUI)

function GunsmithUnlockPathUI:Ctor()
    self._wtCommonPopWindowsV2 = self:Wnd("WBP_CommonPopWindowsV2_409", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._CloseTipsUI, self)
    self._wtCommonPopWindowsV2:BindCloseCallBack(fCallbackIns)

    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self._wtCommonPopWindowsV2:OverrideGamepadSetting("Common_ButtonLeft", nil, WidgetUtil.EUINavDynamicType.Default)
        -- self._wtCanvasPanel = self:Wnd("DFCanvasPanel_51", UIWidgetBase)
    end
    -- END MODIFICATION
    local fOnCloseButtonClicked = CreateCallBack(self._CloseTipsUI, self)
    self._wtCommonPopWindowsV2:BindCloseCallBack(fOnCloseButtonClicked)
    local dfCommonButtons = self._wtCommonPopWindowsV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
		{btnText = Module.Gunsmith.Config.Loc.GunsmithUnlockPathUIConfirmBtnText, fClickCallback = self._OnConfirmClicked, caller = self, bNeedClose = false}
    })
    if dfCommonButtons then
        self._wtConfirmBtn = dfCommonButtons[CommonPopWindows.EHandleBtnType.Confirm]
    end

    self._wtDFCommonCheckBoxWithText = self:Wnd("WBP_DFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtDFCommonCheckBoxWithText:Event("OnCheckStateChanged", self._OnCheckBoxWithTextCheckStateChanged, self)

    self._wt_WaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget, UIName2ID.GunsmithUnlockPathItemUI)
    local path = UIName2ID.GetBPFullPathByID(UIName2ID.GunsmithUnlockPathItemUI)
    self._wt_WaterFallList:RelinkTemplateWidget(path)

    self._wtTips = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtTips:Collapsed()

    self._itemUIdataContainer = ItemUIDataContainer:NewIns(GunsmithUnlockPathItemUIData)
end

function GunsmithUnlockPathUI:OnInitExtraData()
end

function GunsmithUnlockPathUI:Destroy()
    releaseobject(self._itemUIdataContainer)
    self._itemUIdataContainer = nil
end

function GunsmithUnlockPathUI:OnNavBack()
    self:_CloseTipsUI()
    return true
end

function GunsmithUnlockPathUI:OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
    -- END MODIFICATION
end

function GunsmithUnlockPathUI:OnShow()
    GunsmithUI.OnShow(self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtOnGunsmithUnlockPathItemUIUnlockClicked, self._OnProcessGunsmithUnlockPathItemUIUnlockClicked, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithUnlockPathItemStateChanged, self._OnProcessGunsmithUnlockPathItemStateChanged, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate, self._OnProcessServerDataUpdated, self)

    self:OnProcessUIUpdate(true)
end

function GunsmithUnlockPathUI:OnHideBegin()
    GunsmithUI.OnHideBegin(self)
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_EnableGamepadFeature(false)
    end
    -- END MODIFICATION
end

function GunsmithUnlockPathUI:OnHide()
    GunsmithUI.OnHide(self)

    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtOnGunsmithUnlockPathItemUIUnlockClicked)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithUnlockPathItemStateChanged)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate)
end

function GunsmithUnlockPathUI:_CloseTipsUI()
    Facade.UIManager:CloseUI(self)
end

function GunsmithUnlockPathUI:_OnConfirmClicked()
    GunsmithUIContextLogic.SyncPart2ServerFromFrontend()
end

function GunsmithUnlockPathUI:_OnCheckBoxWithTextCheckStateChanged(bChecked)
    local count = self._itemUIdataContainer:GetCount()
    for i = 1, count do
        local data = self._itemUIdataContainer:Get(i)
        local bCanSelect = data:GetCanSelect()
        if bCanSelect then
            data:SetSelect(bChecked)
        end
    end
    self:OnProcessUIUpdate(false, false)
end

function GunsmithUnlockPathUI:_OnGetItemCount()
    if self._itemUIdataContainer == nil then
        return 0
    end
    local count = self._itemUIdataContainer:GetCount()
    return count
end

function GunsmithUnlockPathUI:_OnProcessItemWidget(position, widget)
    local itemUIData = self._itemUIdataContainer:Get(position)
    if widget and widget.UpdateUI then
        widget:UpdateUI(itemUIData)
    end
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        local _wtUnlockBtn= widget._wtUnlockBtn
        if _wtUnlockBtn then
            _wtUnlockBtn:SetCppValue("bIsFocusable", true)
        end
        -- if self._WaterFallListNavGroup and widget._wt_WaterFallList then
        --     self._WaterFallListNavGroup:SetScrollRecipient(widget._wt_WaterFallList)
        -- end
    end
    -- END MODIFICATION
end

function GunsmithUnlockPathUI:_OnProcessGunsmithUnlockPathItemUIUnlockClicked(itemUIData)
    if itemUIData == nil then
        return
    end

    local unlockMethod = itemUIData:GetMPUnlockMethod() or EGunsmithPartMPUnlockMethod.Unknown
    local jumpID = itemUIData:GetMPUnlockjumpID()
    if unlockMethod == EGunsmithPartMPUnlockMethod.Level then
        local weaponGUID = GunsmithUIContextLogic.GetGUID()
        GunsmithPartUnlockLogic.ProcessUnlockMP(weaponGUID)
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Activity then
        GunsmithUIContextLogic.JumpByJumpID(jumpID)
    elseif unlockMethod == EGunsmithPartMPUnlockMethod.Armory then
        GunsmithPartUnlockLogic.JumpArmoryModule()
    end
    self:_CloseTipsUI()
end

function GunsmithUnlockPathUI:_OnProcessGunsmithUnlockPathItemStateChanged()
    self:OnProcessUIUpdate(false, false)
end

function GunsmithUnlockPathUI:OnForceProcessUI(...)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GunsmithPartUnlockLogic.GetUnlockPathItemUIDatas(self._itemUIdataContainer, weaponDescription)
end

function GunsmithUnlockPathUI:OnProcessUI(bRefreshAllItems)
    bRefreshAllItems = setdefault(bRefreshAllItems, true)

    if bRefreshAllItems then
        self._wt_WaterFallList:RefreshAllItems()
    else
        self._wt_WaterFallList:RefreshVisibleItem()
    end
    self:_InternalUpdateCheckBoxUI()
    self:_InternalUpdateButtonUI()
    self:_InternalUpdateTipsUI()
end

function GunsmithUnlockPathUI:_InternalUpdateCheckBoxUI()
    local bCanBeChecked = self:_CheckCanBeChecked()
    if not bCanBeChecked then
        self._wtDFCommonCheckBoxWithText:SelfHitTestInvisible()
        self._wtDFCommonCheckBoxWithText:SetCheckedState(ECheckBoxState.Undetermined)
        return
    end
    self._wtDFCommonCheckBoxWithText:Visible()
    local bSelectedAll = self:GetIsSelectedAll()
    self._wtDFCommonCheckBoxWithText:SetIsChecked(bSelectedAll)
end

function GunsmithUnlockPathUI:_InternalUpdateButtonUI()
    local bEnabled = self:GetBtnEnabled()
    bEnabled = bEnabled or GunsmithUIContextLogic.GetIsRangeToGunsmith()
    self._wtConfirmBtn:SetBtnEnable(bEnabled)
end

function GunsmithUnlockPathUI:_InternalUpdateTipsUI()
    local count = self._itemUIdataContainer:GetCount()
    local bShow = (count == 0) and GunsmithUIContextLogic.GetIsRangeToGunsmith()
    self._wtTips:SetActive(bShow)
    if not bShow then
        -- self._wtTips:Collapsed()
        return
    end
    -- self._wtTips:Collapsed()
    self._wtTips:SetText(Module.Gunsmith.Config.Loc.GunsmithUnlockPathUITips)
end

function GunsmithUnlockPathUI:GetBtnEnabled()
    local count = self._itemUIdataContainer:GetCount()
    for i = 1, count do
        local data = self._itemUIdataContainer:Get(i)
        local bCanSelect = data:GetCanSelect()
        local bSelect = data:GetSelect()
        if bCanSelect and bSelect then
            return true
        end
    end
    return false
end

function GunsmithUnlockPathUI:GetIsSelectedAll()
    local count = self._itemUIdataContainer:GetCount()
    for i = 1, count do
        local data = self._itemUIdataContainer:Get(i)
        local bCanSelect = data:GetCanSelect()
        local bSelect = data:GetSelect()
        if bCanSelect and not bSelect then
            return false
        end
    end
    return true
end

function GunsmithUnlockPathUI:_CheckCanBeChecked()
    local count = self._itemUIdataContainer:GetCount()
    for i = 1, count do
        local data = self._itemUIdataContainer:Get(i)
        local bCanSelect = data:GetCanSelect()
        if bCanSelect then
            return true
        end
    end
    return false
end

function GunsmithUnlockPathUI:_OnProcessServerDataUpdated()
    local _, locks = GunsmithUIContextLogic.GetIDsContainedFromWeaponDescription(weaponDescription)
    local count = table.getkeynum(locks)
    if count == 0 then
        Facade.UIManager:CloseUI(self)
        GunsmithUIContextLogic.SetIsRangeToGunsmith(false)
        Module.Gunsmith.Config.Events.evtGunsmithUnlockPathSuccessCallback:Invoke()
        return
    end
    self:OnProcessUIUpdate(true)
end

-- BEGIN MODIFICATION @ VIRTUOS
function GunsmithUnlockPathUI:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end
    
    if bEnable then
        -- Initialize Navigation
        if not self._WaterFallListNavGroup then
            self._WaterFallListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wt_WaterFallList, self, "Hittest")
            if self._WaterFallListNavGroup then
                self._WaterFallListNavGroup:AddNavWidgetToArray(self._wt_WaterFallList)
                self._WaterFallListNavGroup:SetScrollRecipient(self._wt_WaterFallList)
            end
            WidgetUtil.BuildGroupTree(self._wtCommonPopWindowsV2._wtCanvasPanel)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._WaterFallListNavGroup)
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)

            -- Override common pop window confirm action.
            self._wtCommonPopWindowsV2:OverrideGamepadSetting(nil, nil, WidgetUtil.EUINavDynamicType.Default, true)
        end
        self:_InitShortcuts()
    else
        -- Disable Navigation
        if self._WaterFallListNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._WaterFallListNavGroup = nil
        end
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self:_RemoveShortcuts()
    end
end
-- END MODIFICATION

-- BEGIN MODIFICATION @ VIRTUOS :
function GunsmithUnlockPathUI:_InitShortcuts()
    if not IsHD() then
        return
    end
    self:_RemoveShortcuts()

    local SelectAll_Action = nil
    if self:_CheckCanBeChecked() then
        local bIsCheched = self._wtDFCommonCheckBoxWithText:GetIsChecked()
        SelectAll_Action = bIsCheched and "ComparePrice_UnselectAll" or "ComparePrice_SelectAll"
        self._SelectAll = self:AddInputActionBinding(SelectAll_Action, EInputEvent.IE_Pressed, self._wtDFCommonCheckBoxWithText.SelfClick, self._wtDFCommonCheckBoxWithText, EDisplayInputActionPriority.UI_Pop)
    end
    local bIsValid = SelectAll_Action ~= nil
    if bIsValid then
        self._wtCommonPopWindowsV2:AddSummaries({"Select", SelectAll_Action})
    else
        self._wtCommonPopWindowsV2:AddSummaries({"Select"})
    end
end

function GunsmithUnlockPathUI:_RemoveShortcuts()
    if self._SelectAll then
        self:RemoveInputActionBinding(self._SelectAll)
        self._SelectAll= nil
    end
end
-- END MODIFICATION

return GunsmithUnlockPathUI
