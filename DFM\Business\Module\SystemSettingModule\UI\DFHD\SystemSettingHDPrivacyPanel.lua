----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDPrivacyPanel : LuaUIBaseView
local SystemSettingHDPrivacyPanel = ui("SystemSettingHDPrivacyPanel")
local PrivacySettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.PrivacySettingLogicHD"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local UGPUINavigationManager = import("GPUINavigationManager")
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"

local function _switchVisbility(item,bVisible)
    if not item then
        return
    end
    if bVisible then
        item:SetVisibility(ESlateVisibility.Visible)
    else
        item:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function SystemSettingHDPrivacyPanel:Ctor()

    local bBuildRegionCN = IsBuildRegionCN() and not VersionUtil.IsGameChannelSteam() -- 国服，并且排除steam 国区

    _switchVisbility(self.CN_ListTitle, true)
    _switchVisbility(self.CN_History_Check, true)
    _switchVisbility(self.CN_RankingInformation, true)
    _switchVisbility(self.CN_PersonalInformation, true)
    _switchVisbility(self.CN_StrangerPrivateChat, true)
    _switchVisbility(self.CN_StrangerApplyFriends, true)
    _switchVisbility(self.PrivacyAssistant, true)
    _switchVisbility(self.CN_InvisibleState, true)


    --BEGIN MODIFICATION @ VIRTUOS : 韩国家长监护游戏月报入口
    if IsConsole() and not bBuildRegionCN then
        local bShowKRParentalControl = Server.SDKInfoServer:IsRegionKorea() and Server.SDKInfoServer:GetPlayerAdultState() == -1
        _switchVisbility(self.KR_ParentalControl, bShowKRParentalControl)
    end
    --END MODIFICATION

    -- 仅海外
    _switchVisbility(self.Oversea_TermsOfUse, not bBuildRegionCN)
    _switchVisbility(self.Oversea_PrivacyPolicy, not bBuildRegionCN)

    --BEGIN MODIFICATION @ VIRTUOS : Add bind account functions
    if IsConsole() then
        _switchVisbility(self.Oversea_ListTitle, true)
        local bHasBoundLiPass = Module.Login.Field:GetHasBoundLiPass()
        _switchVisbility(self.Oversea_BindAccount, not bHasBoundLiPass)
        _switchVisbility(self.Oversea_UnbindAccount, bHasBoundLiPass)
    end
    --END MODIFICATION

    -- 仅国服
    _switchVisbility(self.CN_Contract, bBuildRegionCN)
    _switchVisbility(self.CN_Privacy, bBuildRegionCN)
    _switchVisbility(self.CN_Credit, bBuildRegionCN)
    _switchVisbility(self.CN_Children_Privacy, bBuildRegionCN)
    _switchVisbility(self.CN_Privacy_SDK, bBuildRegionCN)

    -- Steam 国区隐藏此选项
    if DFHD_LUA == 1 then
        local bBuildRegionCN_Steam = IsBuildRegionCN() and VersionUtil.IsGameChannelSteam()
        _switchVisbility(self.CN_DeleteAccount, not bBuildRegionCN_Steam)
    else
        switchVisbility(self.CN_DeleteAccount, true)
    end

    self._privacyList = {}

    self._privacyBtnList = {

        [1] = self.CN_History_Check,

        [5] = self.CN_RankingInformation,

        [6] = self.CN_StrangerPrivateChat,

        [7] = self.CN_StrangerApplyFriends,

        [8] = self.CN_InvisibleState,

    }
    self._privacySetFunc = {

        [1] =  Module.SystemSetting.Field.SetIsShowHistoricalRecords,

        [5] =  Module.SystemSetting.Field.SetIsShowRankingInformation,

        [6] =  Module.SystemSetting.Field.SetIsPermitStrangerPrivateChat,

        [7] =  Module.SystemSetting.Field.SetIsPermitStrangerApplyFriends,

        [8] =  Module.SystemSetting.Field.SetInvisibleState,

    }

    --BEGIN MODIFICATION @ VIRTUOS : Add bind account functions
    if IsHD() then
        self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)

        self._wtItemGPUCrashDebugging = self:Wnd("_wtItemGPUCrashDebugging", UIWidgetBase)

        if IsConsole() then
            -- 隐藏GPU调试选项
            if self._wtItemGPUCrashDebugging.SetVisibility ~= nil then
                self._wtItemGPUCrashDebugging:SetVisibility(ESlateVisibility.Collapsed)
            end
            
            -- 隐藏开发设置的分区Header
            local _wtDevSettingHeader = self:Wnd("WBP_SetUpComponent_ListTitle_Dev", UIWidgetBase)
            if _wtDevSettingHeader ~= nil and _wtDevSettingHeader.SetVisibility ~= nil then
                _wtDevSettingHeader:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    end
    --END MODIFICATION
end


--==================================================
--region Life function

function SystemSettingHDPrivacyPanel:BindEvent()
    self:AddLuaEvent(Server.SystemSettingServer.Events.evtInitPlayerPrivacySetting, self.RefreshAllPrivacyCheckBoxes, self)
    self.CN_History_Check:Event("OnStageChanged",self._OnStateChange,self,1)
    self.CN_RankingInformation:Event("OnStageChanged",self._OnStateChange,self,5)
    self.CN_StrangerPrivateChat:Event("OnStageChanged",self._OnStateChange,self,6)
    self.CN_StrangerApplyFriends:Event("OnStageChanged",self._OnStateChange,self,7)
    self.CN_InvisibleState:Event("OnStageChanged",self._OnStateChange,self,8)

    if IsConsole() == false then
        self:AddLuaEvent(self._wtItemGPUCrashDebugging.evtOnStateChanged, self._OnGPUCrashDebuggingStateChanged, self)
    end

    --BEGIN MODIFICATION @ VIRTUOS : Add bind account functions
    if IsConsole() then
        local UGameLogin = import("DFMGameLogin")
        local gameLoginIns = UGameLogin.Get(GetGameInstance())
        if gameLoginIns then
            gameLoginIns.OnBindAccountRetDelegate:Add(CreateCPlusCallBack(self._OnBindAccountRet, self))
        end
    end
    --END MODIFICATION
end

function SystemSettingHDPrivacyPanel:OnOpen()
    self:BindEvent()
    Server.SystemSettingServer:GePlayerPrivacyData()
end

function SystemSettingHDPrivacyPanel:OnClose()
    self:RemoveAllLuaEvent()
    self._privacyBtnList = {}
end

function SystemSettingHDPrivacyPanel:OnShow()

end

function SystemSettingHDPrivacyPanel:OnHide()
    Server.SystemSettingServer:SePlayerPrivacyData(self._privacyList)

    -- BEGIN MODIFICATION @ VIRTUOS : Set LI pass root UI to nullptr after close UI.
    if IsConsole() then
        logerror("[SystemSettingHDPrivacyPanel][OnHide]...SetLIUIRoot nil")
        Module.Login:SetLIUIRoot(nil)
    end
    -- END MODIFICATION   
end

function SystemSettingHDPrivacyPanel:OnInitExtraData()

end

function SystemSettingHDPrivacyPanel:OnShowBegin()
    Module.CommonBar:SetBottomBarTempInputSummaryList(
        {
            -- BEGIN MODIFICATION @ VIRTUOS : 增加确认按键提示
            {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
            -- END MODIFICATION            
            {
                actionName = "Reset",
                func = self._OnReset,
                caller = self
            }
        },
        false
    )
    --尝试打开信息界面
    Module.SystemSetting:OpenRolesNews(true)
    --BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        local bInprivacyPlaySettingPanel = Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.PrivacySetting
        if bInprivacyPlaySettingPanel then
            Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
            Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
        end
        self:_RegisterNavGroup()
    end
    --END MODIFICATION
end

function SystemSettingHDPrivacyPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    --BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self:_RemoveNavGroup()
        CommonSettingLogicHD.RemoveDesc()
        Module.SystemSetting.Field:SetDescRootPanelHD(nil)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
    end
    --END MODIFICATION
end
--endregion
--==================================================


--==================================================
--region Public API

function SystemSettingHDPrivacyPanel:RefreshAllPrivacyCheckBoxes(privacyList)
    if not privacyList then
        return
    end
    self._privacyList = privacyList
    for _, PlayerPrivacyData in pairs(self._privacyList) do
        self:InitPrivacySetting(PlayerPrivacyData)
    end 
end

function SystemSettingHDPrivacyPanel:_OnStateChange(privacyType,bIsEnabled)
    self:PrivacyListDataSet(privacyType,bIsEnabled)
end

function SystemSettingHDPrivacyPanel:InitPrivacySetting(PlayerPrivacyData)
    if not self._privacyBtnList[PlayerPrivacyData.type] then
        return
    end
    self._privacyBtnList[PlayerPrivacyData.type]:BP_SetIsEnabled(not PlayerPrivacyData.is_open)
    self._privacySetFunc[PlayerPrivacyData.type](Module.SystemSetting.Field,not PlayerPrivacyData.is_open)
end

function SystemSettingHDPrivacyPanel:PrivacyListDataSet(privacyType, bOpen)

    for index , PlayerPrivacyData in pairs(self._privacyList) do
        if PlayerPrivacyData.type == privacyType then
           self._privacyList[index].is_open = not bOpen --服务器开关逻辑与客户端相反
        end
   end

    --Server.SystemSettingServer:SePlayerPrivacyData(self._privacyList)
    self._privacySetFunc[privacyType](Module.SystemSetting.Field,bOpen)
    -- if privacyType == 1 then
    --     Module.SystemSetting.Field:SetIsShowHistoricalRecords(bOpen)
    -- end

end

function SystemSettingHDPrivacyPanel:_OnReset()
    local fReset = function()
        self:PrivacyListDataSet(1,true)
        self:PrivacyListDataSet(5,false)
        self:PrivacyListDataSet(6,true)
        self:PrivacyListDataSet(7,true)
        self:PrivacyListDataSet(8,true)
        self.CN_History_Check:BP_SetIsEnabled(true)
        self.CN_RankingInformation:BP_SetIsEnabled(false)
        self.CN_StrangerPrivateChat:BP_SetIsEnabled(true)
        self.CN_StrangerApplyFriends:BP_SetIsEnabled(true)
        self.CN_InvisibleState:BP_SetIsEnabled(true)
        SettingLogicHD.ResetCurrentSettings()
    end
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetPrivacyTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    Module.CommonTips:ShowConfirmWindow(resetInputTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDPrivacyPanel:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    local wtScrollBox = self:Wnd("wtRootScrollBox", UIWidgetBase)
    
    if wtScrollBox and not self._NavGroup then
	    self._NavGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Hittest")
    end

	if self._NavGroup then
		self._NavGroup:AddNavWidgetToArray(wtScrollBox)
        self._NavGroup:SetScrollRecipient(wtScrollBox)
        self._NavGroup:MarkIsStackControlGroup()
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDPrivacyPanel:_RemoveNavGroup()
    if not IsHD() then
        return
    end

	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end

-- BEGIN MODIFICATION @ VIRTUOS: 目前只增加一个高亮的重新设置
function SystemSettingHDPrivacyPanel:_OnBindAccountRet(ret)
    UGPUINavigationManager.Get(GetGameInstance()):RegisterAnalogCursor()
    UDFNavigationSelectorBase.SetHighlight_DirectlyOrNavigation(false)
    self._NavGroup:RestoreFocus()
end
-- END MODIFICATION

--END MODIFICATION

function SystemSettingHDPrivacyPanel:_OnGPUCrashDebuggingStateChanged(bEnable)
    if (bEnable and not VideoSettingLogicHD.IsGPUCrashDebuggingRealEnabled()) then
        local tipText = Module.SystemSetting.Config.Loc.HDSetting.GPUCrashDebuggingRebootTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.GPUCrashDebuggingRebootConfirmTxt
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(tipText, nil, confirmText)
    end
end

--endregion
--==================================================


--==================================================
--region Private API

--endregion
--==================================================


return SystemSettingHDPrivacyPanel