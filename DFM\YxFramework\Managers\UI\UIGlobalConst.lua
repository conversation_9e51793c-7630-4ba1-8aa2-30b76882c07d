----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------



local fRegisterUISetting = import("LuaUIBaseView").RegisterUISetting
local LuaBpCfgTable = Facade.TableManager:GetTable("LuaAsset/LuaBpAssetConfig")
local LuaBpCfgDebugTable
if IsInEditor() then
    LuaBpCfgDebugTable = Facade.TableManager:GetTable("LuaAsset/LuaBpAssetConfigDebug")
end
local LuaBpAssetConfigPCSplit = nil
if DFHD_LUA == 1 then
    LuaBpAssetConfigPCSplit = Facade.TableManager:GetTable("LuaAsset/LuaBpAssetConfigPCSplit")
else
    --- TODO
    --- LuaBpAssetConfigPS5plit = Facade.TableManager:GetTable("LuaAsset/LuaBpAssetConfigPS5Split", false)
end

local EBPConfigSplitFlag = 
{
    PlatfromCommon                    = 0,                            --- 平台通用
    PlatfromSplit_PC_PS5              = 1 | 1 << 1,                   --- PC和PS5均有分裂
    PlatfromSplit_PC                  = 1 ,                           --- PC有分裂
    PlatfromSplit_PS5                 = 1 << 1 ,                      --- PS5有分裂
}

---@class ESplitConfig
---@field public Common number 
---@field public PCSplit number 
---@field public PS5Split number
ESplitConfig = 
{
    Common               = 0,
    PCSplit              = 1,
    PS5Split             = 2,
}

DummyUINavID = -1
DummyUISettings = {}
UIBPCls2LuaPath = {}
UIBPCls2ID = {}
UIBPPath2Cls = {}

local bNeedPlanB = true
local bSplitMuted = false

--------------------------------------------------------------------------
--- UI配置全局表
--------------------------------------------------------------------------
UIMapId2Name = {}
---@class UIName2ID
UIName2ID =
    setmetatable(
    {
        _index = 0,
        GetBPPathByID = function(id)
            local uiSettings = UITable[id]
            if uiSettings then
                if uiSettings.BPPath then
                    -- 统一BPKey配置，BPPath在配置时已自动生成
                    return uiSettings.BPPath
                else
                    -- 如果一个UIName2ID重复配置多个UITable，会导致不触发__newIndex元方法而无法生成BPPath、FullPath
                    logbox(string.format("No found of BPPath. Check whether multiple UITable objects with the same UIName2ID are defined, causing data loss. BPkey:[%s] LuaPath:[%s]", uiSettings.BPKey, uiSettings.LuaPath))
                    return nil
                end
            else
                logerror("ERROR:UISettings is nil, Not found BPPath for " .. id, UIName2ID.GetNameByID(id))
                return nil
            end
        end,
        GetBPFullPathByID = function(id)
            if id == nil then
                logerror("ERROR:UISettings is nil, id is nil")
                return nil
            end
            local uiSettings = UITable[id]
            if uiSettings then
                if uiSettings.FullPath then
                    -- 统一BPKey配置，FullPath在配置时已自动生成
                    return uiSettings.FullPath
                else
                    -- 如果一个UIName2ID重复配置多个UITable，会导致不触发__newIndex元方法而无法生成BPPath、FullPath
                    logbox(string.format("No found of FullPath. Check whether multiple UITable objects with the same UIName2ID are defined, causing data loss. BPkey:[%s] LuaPath:[%s]", uiSettings.BPKey, uiSettings.LuaPath))
                    return nil
                end
            else
                logerror("ERROR:UISettings is nil, Not found FullPath for " .. id, UIName2ID.GetNameByID(id))
                return nil
            end
        end,
        GetNameByID = function(id)
            if id and UIMapId2Name then
                return UIMapId2Name[id]
            end
        end,
        RevertToPlanB = function(id)
            if bNeedPlanB then
                local uiSettings = UITable[id]
                if uiSettings then
                    local planBConfig = uiSettings.CommonPlanBConfig
                    if planBConfig and not planBConfig.bReverted then
                        uiSettings.UINavID = id
                        uiSettings.BPPath = planBConfig.BPPath
                        uiSettings.FullPath = planBConfig.FullPath
                        uiSettings.ResSplitConfig = ESplitConfig.Common
                        uiSettings.CommonPlanBConfig = {
                            bReverted = true,
                        }
                        local bpConfigRow = LuaBpCfgTable[uiSettings.BPKey]
                        if bpConfigRow then
                            -- uiSettings.BPPath = bpConfigRow.BPPath
                            -- uiSettings.FullPath = bpConfigRow.FullPath
                            -- uiSettings.ResSplitConfig = ESplitConfig.Common
                            local bpClassName = bpConfigRow.BPClassName
                            uiSettings.BPClassName = bpClassName
                            if bpClassName then
                                UIBPCls2LuaPath[bpClassName] = uiSettings.LuaPath
                                UIBPCls2ID[bpClassName] = id
                                UIBPPath2Cls[uiSettings.BPPath] = bpClassName
            
                                fRegisterUISetting(bpClassName, uiSettings) -- register uisetting to cpp
                            else
                                logbox(string.format('[Error] : BPClassName [%s] is not in LuaBpAssetConfig Table', bpClassName))
                            end
                            return true
                        end
                    end
                end
            end
            return false
        end
        
    },
    {
        __index = function(tb, k)
            tb._index = tb._index + 1
            rawset(tb, k, tb._index)
            -- if IsInEditor() then
                UIMapId2Name[tb._index] = k
            -- end
            return tb._index
        end
    }
)

---@class CommonPlanBConfig
---@field public BPPath string 
---@field public FullPath string

---@class ReConfig
---@field public IsPoolEnable boolean 
---@field public MaxPoolLength number 栈UI时此参数无效（因为栈UI仅唯一实例）

---@class FFrameStackTransitionRow
---@field public MinTime number 
---@field public AniSpeedScale number

---@class UISettings
---@field public UILayer EUILayer
---@field public LuaPath string
---@field public BPPath string
---@field public BPKey string
---@field public FullPath string
---@field public Anim table
---@field public SubUIs table
---@field public IsModal boolean
---@field public ReConfig ReConfig
---@field public LinkSubStage ESubStage 仅栈UI生效
---@field public ResSplitConfig ESplitConfig
---@field public CommonPlanBConfig CommonPlanBConfig

---@type table<UIName2ID, UISettings>
UITable =
    setmetatable(
    {
    },
    {
        __newindex = function(tb, k, v)
            if not IsLuaFileAvaliableOnThisPlatform(v.LuaPath) then
                rawset(tb, k, DummyUISettings)
                loginfo("Platform UITable ignored", v.LuaPath)
                return
            end
		    if v.BPKey then
                local bpConfigRow = LuaBpCfgTable[v.BPKey]
                if bpConfigRow then
                    --- 初始化
                    v.UINavID = k
                    v.BPPath = bpConfigRow.BPPath
                    v.FullPath = bpConfigRow.FullPath
                    v.ResSplitConfig = ESplitConfig.Common
                    v.IsAnimBlock = setdefault(v.IsAnimBlock, true)
                    v.IsTransitionEnable = setdefault(v.IsTransitionEnable, true)

                    --- 先识别（蓝图动画名不需要因为分裂而改变）
                    local inBPName = string.match(v.BPPath, ".*%.(.*)")
                    if inBPName then
                        v.AnimInName = inBPName.."_in"
                        v.AnimOutName = inBPName.."_out"
                    else
                        logbox(string.format('[Error] : BPPath [%s] is not in LuaBpAssetConfig Table', v.BPPath))
                    end

                    --- 后分裂
                    local bSplit = false
                    if not bSplitMuted and bpConfigRow.LuaBPConfigSplit ~= 0 then
                        if DFHD_LUA == 1 then
                            if bpConfigRow.LuaBPConfigSplit == EBPConfigSplitFlag.PlatfromSplit_PC_PS5 
                                or bpConfigRow.LuaBPConfigSplit == EBPConfigSplitFlag.PlatfromSplit_PC then
                                local bpConfigRowPCSplit = LuaBpAssetConfigPCSplit[v.BPKey]
                                if not bpConfigRowPCSplit then
                                    local bpKeyFindCurTable = tostring(v.BPKey).."_PC_S"
                                    bpConfigRowPCSplit = LuaBpCfgTable[bpKeyFindCurTable]
                                end
                                if bpConfigRowPCSplit then
                                    v.UINavID = k
                                    v.BPPath = bpConfigRowPCSplit.BPPath
                                    v.FullPath = bpConfigRowPCSplit.FullPath
                                    v.ResSplitConfig = ESplitConfig.PCSplit
                                    if bNeedPlanB then
                                        v.CommonPlanBConfig = {
                                            BPPath = bpConfigRow.BPPath,
                                            FullPath = bpConfigRow.FullPath,
                                            bReverted = false,
                                        }
                                    end

                                    local bpClassName = bpConfigRowPCSplit.BPClassName
                                    v.BPClassName = bpClassName
                                    if bpClassName then
                                        UIBPCls2LuaPath[bpClassName] = v.LuaPath
                                        UIBPCls2ID[bpClassName] = k
                                        UIBPPath2Cls[v.BPPath] = bpClassName

                                        fRegisterUISetting(bpClassName, v) -- register uisetting to cpp
                                        bSplit = true
                                    else
                                        logbox(string.format('[Error] : BPClassName [%s] is not in LuaBpAssetConfig Table', bpClassName))
                                    end
                                end
                            end
                        end
                    end

                    --- 未分裂的走默认
                    if not bSplit then
                        --- 最后Cache一些备用数据
                        local bpClassName = bpConfigRow.BPClassName
                        v.BPClassName = bpClassName
                        if bpClassName then
                            UIBPCls2LuaPath[bpClassName] = v.LuaPath
                            UIBPCls2ID[bpClassName] = k
                            UIBPPath2Cls[v.BPPath] = bpClassName

                            fRegisterUISetting(bpClassName, v) -- register uisetting to cpp
                        else
                            logbox(string.format('[Error] : BPClassName [%s] is not in LuaBpAssetConfig Table', bpClassName))
                        end
                    end
                    local layerType = v.UILayer
                    if _WITH_EDITOR == 1 then
                        if layerType == EUILayer.HUD_ScreenEffect
                            or layerType == EUILayer.HUD_Mark
                            or layerType == EUILayer.HUD_Hint
                            or layerType == EUILayer.HUD_Common
                            or layerType == EUILayer.HUD_Feedback
                            or layerType == EUILayer.HUD_Touch
                            or layerType == EUILayer.HUD_Popup
                            or layerType == EUILayer.HUD_LargePopup then
                            logerror("UIName2ID: " .. k .. " should change LayerType to EUILayer.HUD, Please use 'ZOrderBase = EHUDBaseZorder.LargePopup,' instead")
                        end
                    end
                    v.bNeedHUDSafeZone = not(layerType == EUILayer.HUD and v.bInSafeZone == false)
                    if v.ReConfig then
                        v.ReConfig.IsPoolEnable = setdefault(v.ReConfig.IsPoolEnable, false)
                        if v.ReConfig.IsPoolEnable then
                            v.ReConfig.IsPoolEnableLow = setdefault(v.ReConfig.IsPoolEnableLow, true)
                        else
                            v.ReConfig.IsPoolEnableLow = setdefault(v.ReConfig.IsPoolEnableLow, false)
                        end
                    end
                    v.IsReleaseClearRef = true
                else
                    if IsInEditor() then
                        LuaBpCfgDebugTable = Facade.TableManager:GetTable("LuaAsset/LuaBpAssetConfigDebug", false)
                        local bpDebugConfigRow = LuaBpCfgDebugTable[v.BPKey]
                        if bpDebugConfigRow then
                            --- 初始化
                            v.UINavID = k
                            v.BPPath = bpDebugConfigRow.BPPath
                            v.FullPath = bpDebugConfigRow.FullPath
                            v.ResSplitConfig = ESplitConfig.Common
                            v.IsAnimBlock = setdefault(v.IsAnimBlock, true)
                            v.IsTransitionEnable = setdefault(v.IsTransitionEnable, true)
                            
                            --- 先识别（蓝图动画名不需要因为分裂而改变）
                            if v.BPPath then
                                local inBPName = string.match(v.BPPath, ".*%.(.*)")
                                if inBPName then
                                    v.AnimInName = inBPName.."_in"
                                    v.AnimOutName = inBPName.."_out"
                                else
                                    logbox(string.format('[Error] : BPPath [%s] is not in LuaBpAssetConfig Table', v.BPPath))
                                end
                                local bpClassName = bpDebugConfigRow.BPClassName
                                v.BPClassName = bpClassName
                                if bpClassName then
                                    UIBPCls2LuaPath[bpClassName] = v.LuaPath
                                    UIBPCls2ID[bpClassName] = k
                                    UIBPPath2Cls[v.BPPath] = bpClassName

                                    fRegisterUISetting(bpClassName, v) -- register uisetting to cpp
                                else
                                    logbox(string.format('[Error] : BPClassName [%s] is not in LuaBpAssetConfig Table', bpClassName))
                                end

                                local layerType = v.UILayer
                                if _WITH_EDITOR == 1 then
                                    if layerType == EUILayer.HUD_ScreenEffect
                                        or layerType == EUILayer.HUD_Mark
                                        or layerType == EUILayer.HUD_Hint
                                        or layerType == EUILayer.HUD_Common
                                        or layerType == EUILayer.HUD_Feedback
                                        or layerType == EUILayer.HUD_Touch
                                        or layerType == EUILayer.HUD_Popup
                                        or layerType == EUILayer.HUD_LargePopup then
                                        logerror("UIName2ID: " .. k .. " should change LayerType to EUILayer.HUD, Please use 'ZOrderBase = EHUDBaseZorder.LargePopup,' instead")
                                    end
                                end
                                v.bNeedHUDSafeZone = not(layerType == EUILayer.HUD and v.bInSafeZone == false)
                                if v.ReConfig then
                                    v.ReConfig.IsPoolEnable = setdefault(v.ReConfig.IsPoolEnable, false)
                                    if v.ReConfig.IsPoolEnable then
                                        v.ReConfig.IsPoolEnableLow = setdefault(v.ReConfig.IsPoolEnableLow, true)
                                    else
                                        v.ReConfig.IsPoolEnableLow = setdefault(v.ReConfig.IsPoolEnableLow, false)
                                    end
                                end
                                v.IsReleaseClearRef = true
                            else
                                logerror('LuaBpCfgDebugTable.BPPath is nil',  v.BPKey, v.BPPath)
                            end
                        else
                            logbox(string.format('[Error] : BPKey [%s] is not in LuaBpAssetConfig Table', v.BPKey))
                        end
                    else
                        logbox(string.format('[Error] : BPKey [%s] is not in LuaBpAssetConfig Table', v.BPKey))
                    end
                end
            else
                logbox("The BPPath configuration is obsolete. Please use BPKey")
            end
            rawset(tb, k, v)
        end
    }
)

--------------------------------------------------------------------------
--- HUD配置全局表 (HUD映射到的是UI列表)
--------------------------------------------------------------------------
---@class HUDName2ID
HUDName2ID =
    setmetatable(
    {_index = 0},
    {
        __index = function(tb, k)
            tb._index = tb._index + 1
            rawset(tb, k, tb._index)
            return tb._index
        end
    }
)

---@class HUDInfo
---@field HUDName string
---@field ZOrderBase EHUDBaseZorder
---@field ZOrderOffset number

---@class HUDSettings
---@field public HUDTablePath HUDDataTable
---@field public HUDConfigIDs table<UINavID, HUDInfo>
local HUDSettings = nil

---@type table<HUDName2ID, HUDSettings>
HUDTable = 
    setmetatable(
    {},
    {
        __newindex = function(tb, k, v)
            rawset(tb, k, v)
        end
    }
)
--------------------------------------------------------------------------
--- UI相关的框架级枚举
--------------------------------------------------------------------------
---UI默认分层Layer（由上到下层级依次升高）
---@enum EUILayer
EUILayer = {
    Sub = 0,               ---子UI不加入现有层级，需要自己手动AddChild
    HUD = 10,               ---HUD逻辑层，不对应viewport上的根节点
    HUD_ScreenEffect = 11,  ---HUD层 屏幕效果
    HUD_Mark = 12,          ---HUD层 标记追踪游标
    HUD_Common = 13,        ---HUD层 常驻基础控件
    HUD_Hint = 14,          ---HUD层 触发式控件、玩法相关提示
    HUD_Feedback = 15,      ---HUD层 3C相关提示
    HUD_Touch = 16,         ---HUD层 手游触屏按键
    HUD_Popup = 17,         ---HUD层 HUD 弹窗类
    HUD_LargePopup = 18,    ---HUD层 HUD 更高层级的弹窗类
    BackRoot = 21,          ---BackRoot层UI，可用于CutScene、背景，不受安全区影响
    Scene = 22,             ---与场景中有坐标转换有关的2DUI（非WidgetComponent)
    Root = 23,              ---根面板UI，作为垫底于StackUI之下的默认常驻UI
    Stack = 24,             ---栈管理的窗口式UI，彼此为互斥关系
    Top = 25,               ---置顶UI，如TopSideBar、资源飞行物（动画）
    Loading = 26,           ---Loading层UI
    Pop = 27,               ---非栈管理的弹窗UI，如Confirm窗口、弹出详情页
    Tip = 28,               ---Tips提示UI
    Mask = 29,              ---遮罩层UI，用于新手引导、剧情演绎、对话等悬浮于所有层级之上的UI
    Admin = 30,             ---Admin级别UI，用于无法打断的流程
    Watermark = 31,         ---水印层特殊处理，占单独一层, debug时期常驻，后续扩展为不可见的水印层
}

ELuaUILayer = import("ELuaUILayer")

---@enum EHUDBaseZorder
EHUDBaseZorder = {
    None = 0,
    ScreenEffect = 1,   --屏幕效果
    Mark = 30,          --标记追踪游标
    Hint = 60,          --触发式控件、玩法相关提示
    Feedback = 90,     --3C相关提示
    Common = 120,        --常驻基础控件
    Touch = 150,        --手游触屏按键
    Popup = 180,        --小型交互ui
    LargePopup = 210,   --全屏界面
}

---栈操作
---@enum EStackAction
EStackAction = {
    Push = 1, ---入栈
    Pop = 2, ---出栈
}

---@enum EUIPlaceDirection
EUIPlaceDirection =
{
    Left = 1,
    Right = 2,
    Up = 3,
    Down = 4
}

UI_MANAGER_LAYER_PRIORITY_ENABLE = 0

--------------------------------------------------------------------------
--- UI Layer 相关静态常量
--------------------------------------------------------------------------
UITable[UIName2ID.BACKROOTLAYER_BGUI] =
{
    UILayer = EUILayer.BackRoot,
    BPKey = ResourcePath.BACKROOTLAYER_BGUI,
    LuaPath = ResourcePath.LUAPATH_BGUI,
    SubUIs = {
        UIName2ID.BACKROOTLAYER_IMAGEUI,
        UIName2ID.BACKROOTLAYER_CDNIMAGEUI,
        UIName2ID.BACKROOTLAYER_MEDIAUI,
        UIName2ID.BACKROOTLAYER_BLURUI
    },
    ReConfig = {
        IsPoolEnable = true,
        IsPoolEnableLow = true, 
        MaxPoolLength = 2
    },
    IsReleaseClearRef = true,
}

UITable[UIName2ID.BACKROOTLAYER_IMAGEUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.BACKROOTLAYER_IMAGEUI,
    LuaPath = ResourcePath.LUAPATH_IMAGEUI,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 2
    }
}

UITable[UIName2ID.BACKROOTLAYER_CDNIMAGEUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.BACKROOTLAYER_CDNIMAGEUI,
    LuaPath = ResourcePath.LUAPATH_CDNIMAGEUI,
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1
    -- }
}

UITable[UIName2ID.BACKROOTLAYER_MEDIAUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.BACKROOTLAYER_MEDIAUI,
    LuaPath = ResourcePath.LUAPATH_MEDIAUI,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 2
    }
}

UITable[UIName2ID.BACKROOTLAYER_BLURUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.BACKROOTLAYER_BLURUI,
    LuaPath = ResourcePath.LUAPATH_BLURUI,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 2
    }
}


UITable[UIName2ID.MASKLAYER_TRANSITIONUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.MASKLAYER_TRANSITIONUI,
    LuaPath = ResourcePath.LUAPATH_TRANSITIONUI,
}

UITable[UIName2ID.VIEWPORT_PERSISTENTROOTUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.VIEWPORT_PERSISTENTROOTUI,
    LuaPath = ResourcePath.LUAPATH_RPERSISTENTROOTUI,
}

UITable[UIName2ID.VIEWPORT_ROOTUI] =
{
    UILayer = EUILayer.Sub,
    BPKey = ResourcePath.VIEWPORT_ROOTUI,
    LuaPath = ResourcePath.LUAPATH_ROOTUI,
    SubUIs = {
        UIName2ID.MASKLAYER_TRANSITIONUI
    }
}

is_ui_invalid = function (uiIns)
    return uiIns == nil or hasdestroy(uiIns) or isinvalid(uiIns) or uiIns.__cppinst == nil
end

DEFAULT_ZORDER_BASE = 999
DEFAULT_LIFEANI_SPEED = 1.0
DEFAULT_MAX_TRANS_LIMIT = 3.0
DEFAULT_CPLUS_LAYER = true
DEFAULT_SUBPACK_NEW = true
DEFAULT_SUBPACK_WEAK_REUSED = true
DEFAULT_OVERLAP_NEW = true
ALIVE_COUNT_CLEAR_SUBUI = true
ALIVE_COUNT_CLEAR_SUBUI_LOG = true
FINAL_CLOSE_CLEAR_REF = true
DEFAULT_CLEAR_RES_TIME = 0.02

LUA_PENDING_KILL_ENABLE = true

--------------------------------------------------------------------------
--- 各层级对应的LayerController
--- 1.DefaultLayerController管理Sub、Scene、Top、Tip、Mask、Watermark
--- 2.HUD、BackRoot、Root、Stack、Pop拥有独立层级管理器
--------------------------------------------------------------------------
UILayer2ControllerPath = {
    [EUILayer.HUD] = "DFM.YxFramework.Managers.UI.Layer.HUD.HUDLayerController",
    [EUILayer.BackRoot] = "DFM.YxFramework.Managers.UI.Layer.BackRoot.BackRootLayerController",
    [EUILayer.Root] = "DFM.YxFramework.Managers.UI.Layer.Root.RootLayerController",
    [EUILayer.Stack] = "DFM.YxFramework.Managers.UI.Layer.Stack.StackLayerController",
    [EUILayer.Pop] = "DFM.YxFramework.Managers.UI.Layer.Pop.PopLayerController",
}

MapUILayer2Name = {
    [EUILayer.Sub] = 'Sub',
    [EUILayer.HUD] = 'HUD',
    [EUILayer.HUD_ScreenEffect] = 'HUD_ScreenEffect',
    [EUILayer.HUD_Mark] = 'HUD_Mark',
    [EUILayer.HUD_Common] = 'HUD_Common',
    [EUILayer.HUD_Hint] = 'HUD_Hint',
    [EUILayer.HUD_Feedback] = 'HUD_Feedback',
    [EUILayer.HUD_Touch] = 'HUD_Touch',
    [EUILayer.HUD_Popup] = 'HUD_Popup',
    [EUILayer.HUD_LargePopup] = 'HUD_LargePopup',
    [EUILayer.BackRoot] = 'BackRoot',
    [EUILayer.Scene] = 'Scene',
    [EUILayer.Root] = 'Root',
    [EUILayer.Stack] = 'Stack',
    [EUILayer.Top] = 'Top',
    [EUILayer.Loading] = 'Loading',
    [EUILayer.Pop] = 'Pop',
    [EUILayer.Tip] = 'Tip',
    [EUILayer.Mask] = 'Mask',
    [EUILayer.Admin] = 'Admin',
    [EUILayer.Watermark] = 'Watermark',
}

UILayer2ZOrderOS = {
    [EUILayer.Sub] = 0,
    [EUILayer.HUD] = 0,
    [EUILayer.HUD_ScreenEffect] = 1,
    [EUILayer.HUD_Mark] = 2,
    [EUILayer.HUD_Common] = 3,
    [EUILayer.HUD_Hint] = 4,
    [EUILayer.HUD_Feedback] = 5,
    [EUILayer.HUD_Touch] = 6,
    
    [EUILayer.BackRoot] = 50,
    [EUILayer.Scene] = 100,
    [EUILayer.Root] = 200,
    [EUILayer.Stack] = 300,
    [EUILayer.Top] = 350,
    [EUILayer.Loading] = 375,

    [EUILayer.HUD_Popup] = 380,
    [EUILayer.HUD_LargePopup] = 390,
    [EUILayer.Pop] = 400,
    [EUILayer.Tip] = 500,
    [EUILayer.Mask] =  600,
    [EUILayer.Admin] =  700,
    [EUILayer.Watermark] = 800,
}


UILayer2ZOrder = {
    [EUILayer.Sub] = 0,
    [EUILayer.HUD] = 0,
    [EUILayer.HUD_ScreenEffect] = 0,
    [EUILayer.HUD_Mark] = 0,
    [EUILayer.HUD_Common] = 0,
    [EUILayer.HUD_Hint] = 0,
    [EUILayer.HUD_Feedback] = 0,
    [EUILayer.HUD_Touch] = 0,

    [EUILayer.BackRoot] = 0,
    [EUILayer.Scene] = 0,
    [EUILayer.Root] = 0,
    [EUILayer.Stack] = 0,
    [EUILayer.Top] = 0,
    [EUILayer.Loading] = 0,

    [EUILayer.HUD_Popup] = 0,
    [EUILayer.HUD_LargePopup] = 0,
    [EUILayer.Pop] = 0,
    [EUILayer.Tip] = 0,
    [EUILayer.Mask] =  0,
    [EUILayer.Admin] =  0,
    [EUILayer.Watermark] = 0,
}


---@enum EDisplayInputActionPriority
EDisplayInputActionPriority = {
    Game = 0,
    UI_HUD = 1,
    UI_Stack = 2,
    UI_Loading = 3,
    UI_Chat = 4,
    UI_Pop = 5,
    UI_Tip = 6,
    UI_Guide = 7,
    Always = 8,
    Debug = 100
}

MapLayer2InputActionPriority = {
    [EUILayer.HUD] = EDisplayInputActionPriority.UI_HUD,
    [EUILayer.Stack] = EDisplayInputActionPriority.UI_Stack,
    [EUILayer.Loading] = EDisplayInputActionPriority.UI_Loading,
    [EUILayer.Pop] = EDisplayInputActionPriority.UI_Pop,
    [EUILayer.Top] = EDisplayInputActionPriority.UI_Tip,
    [EUILayer.Tip] = EDisplayInputActionPriority.UI_Tip,
    [EUILayer.Admin] =  EDisplayInputActionPriority.UI_Tip,
}

---@class EInputChangeReason
---@field FrameRoot number
---@field ProcessSleeping number
---@field ProtoWaiting number
---@field BatchOperating number
---@field AssetLoading number
---@field UIProducing number
---@field AnimPlaying number
---@field BusinessPending number
EInputChangeReason = {
    FrameRoot = 0,
    ProcessSleeping = 1,
    ProtoWaiting = 2,
    BatchOperating = 3,
    AssetLoading = 4,
    UIProducing = 5,
    AnimPlaying = 6,
    BusinessPending = 7,
    None = 999,
}

---@enum ELayerRuleChangeReason
ELayerRuleChangeReason = {
    Superuser = 0,
    ProcessSleeping = 1,
    ProtoWaiting = 2,
    GMCommand = 3,
    GuideProcessing = 4,
    StackPush = 5,
    StackPop = 6,
    PopFullScreen = 7,
    BusinessPending = 8,
    None = 999,
}

---@class ETransitionChangeReason
---@field FrameRoot number
---@field ProcessSleeping number
---@field ProtoWaiting number
---@field BatchOperating number
---@field AssetLoading number
---@field UIProducing number
---@field AnimPlaying number
---@field BusinessPending number
ETransitionChangeReason = {
    FrameRoot = 0,
    ProcessSleeping = 1,
    ProtoWaiting = 2,
    BatchOperating = 3,
    AssetLoading = 4,
    UIProducing = 5,
    AnimPlaying = 6,
    BusinessPending = 7,
    None = 999,
}


--------------------------------------------------------------------------
--- UI UMG 枚举注释
--------------------------------------------------------------------------
--- UMG动画播放时序
---@class EUMGSequencePlayMode
---@field Forward number
---@field Reverse number
---@field PingPong number
EUMGSequencePlayMode = import"EUMGSequencePlayMode"

--- UMG可视性
---@class ESlateVisibility
---@field Visible number
---@field Collapsed number
---@field Hidden number
---@field HitTestInvisible number
---@field HitTestSelfOnly number
---@field SelfHitTestInvisible number
ESlateVisibility = import "ESlateVisibility"

---@class EStretch
---@field None integer
---@field Fill integer
---@field ScaleToFit integer
---@field ScaleToFitX integer
---@field ScaleToFitY integer
---@field ScaleToFill integer
---@field ScaleBySafeZone integer
---@field UserSpecified integer
EStretch = import "EStretch"

EFrameAnimType = {
    RoomIn = 1,
    RoomOut = 2,
    FadeIn = 3,
    FadeOut = 4,
    Loop = 5,
}

if DFHD_LUA == 1 then
    MapFrameAnimType2Name = {
        [EFrameAnimType.RoomIn] = 'WBP_PersistentRootHD_RoomIn',
        [EFrameAnimType.RoomOut] = 'WBP_PersistentRootHD_RoomOut',
        [EFrameAnimType.FadeIn] = 'WBP_PersistentRootHD_FadeIn',
        [EFrameAnimType.FadeOut] = 'WBP_PersistentRootHD_FadeOut',
    }    
else
    MapFrameAnimType2Name = {
        [EFrameAnimType.RoomIn] = 'WBP_PersistentRoot_RoomIn',
        [EFrameAnimType.RoomOut] = 'WBP_PersistentRoot_RoomOut',
        [EFrameAnimType.FadeIn] = 'WBP_PersistentRoot_FadeIn',
        [EFrameAnimType.FadeOut] = 'WBP_PersistentRoot_FadeOut',
    }
end

--------------------------------------------------------------------------
--- UI Input 枚举注释
--------------------------------------------------------------------------
---@class EInputMode enum
---@field EIM_None number
---@field EIM_GameOnly number Setup an input mode that allows only player input / player controller to respond to user input.
---@field EIM_GameAndUI number Setup an input mode that allows only the UI to respond to user input, and if the UI doesn't handle it player input / player controller gets a chance.
---@field EIM_UIOnly number Setup an input mode that allows only the UI to respond to user input.
EInputMode = import "EInputMode"

---@class EGPInputModeType enum
---@field None number
---@field GameOnly number Setup an input mode that allows only player input / player controller to respond to user input.
---@field UIOnly number Setup an input mode that allows only the UI to respond to user input, and if the UI doesn't handle it player input / player controller gets a chance.
---@field GameAndUI number Setup an input mode that allows only the UI to respond to user input.
---@field ForceUIOnly number
---@field Count number
EGPInputModeType = import "EGPInputModeType"
MapGPInputModeType2Name = {
    [EGPInputModeType.None] = 'None',
    [EGPInputModeType.GameOnly] = 'GameOnly',
    [EGPInputModeType.UIOnly] = 'UIOnly',
    [EGPInputModeType.GameAndUI] = 'GameAndUI',
    [EGPInputModeType.ForceUIOnly] = 'ForceUIOnly',
}

---@class EMouseLockMode enum
---@field DoNotLock number Do not lock the mouse cursor to the viewport
---@field LockOnCapture number Only lock the mouse cursor to the viewport when the mouse is captured
---@field LockAlways number Always lock the mouse cursor to the viewport
---@field LockInFullscreen number Lock the cursor if we're in fullscreen
EMouseLockMode = import "EMouseLockMode"

---@class EInputEvent enum
---@field IE_Pressed number
---@field IE_Released number
---@field IE_Repeat number
---@field IE_DoubleClick number
---@field IE_Axis number
---@field IE_MAX number
EInputEvent = import "EInputEvent"


---@class ETextCommit enum
---@field Default number
---@field OnEnter number
---@field OnUserMovedFocus number
---@field OnCleared number
ETextCommit = import "ETextCommit"


---------------------------------------------------------------------------------
--- UI对象复用配置
---------------------------------------------------------------------------------
ERePoolType = {
    Unique = 1,
    Multiple = 2,
}

MapLayer2RePoolType = {
    [EUILayer.Stack] = ERePoolType.Unique,
    [EUILayer.Tip] = ERePoolType.Multiple,
    [EUILayer.Pop] = ERePoolType.Multiple,
    [EUILayer.Sub] = ERePoolType.Multiple,
    [EUILayer.BackRoot] = ERePoolType.Multiple,
}

---------------------------------------------------------------------------------
--- 性能配置
---------------------------------------------------------------------------------
MapApmLv2PerformanceConfig = {
    [1] = {
        stackPoolLength = 2,
        stackPoolLengthLow = 1,
        stubSizeRuntimeMb = 3,
        stubSizeImageMb = 10,
        stackLruDuration = 30,
        stackLruDurationLow = 10,
        performanceLv = "EQuality_VeryLow"
    },
    [2] = {
        stackPoolLength = 5,
        stackPoolLengthLow = 2,
        stubSizeRuntimeMb = 5,
        stubSizeImageMb = 15,
        stackLruDuration = 30,
        stackLruDurationLow = 20,
        performanceLv = "EQuality_Low"
    },
    [3] = {
        stackPoolLength = 5,
        stackPoolLengthLow = 2,
        stubSizeRuntimeMb = 10,
        stubSizeImageMb = 20,
        stackLruDuration = 180,
        stackLruDurationLow = 30,
        performanceLv = "EQuality_Medium"
    },
    [4] = {
        stackPoolLength = 10,
        stackPoolLengthLow = 5,
        stubSizeRuntimeMb = 15,
        stubSizeImageMb = 30,
        stackLruDuration = 180,
        stackLruDurationLow = 60,
        performanceLv = "EQuality_High"
    },
    [5] = {
        stackPoolLength = 20,
        stackPoolLengthLow = 5,
        stubSizeRuntimeMb = 20,
        stubSizeImageMb = 40,
        stackLruDuration = 180,
        stackLruDurationLow = 60,
        performanceLv = "EQuality_VeryHigh"
    },
}

EApplyThemeIDChangeReason = {
    Superuser = 0,
    ItemSelection = 1,
    TabSelection = 2,
    StackDefault = 3,
    None = 999,
}

EThemeIDType = {
    NoTheme = "0",
    CrossOver = "1",
}

-- EVehicleQualityColor = {
--     [0] = {Brightness = 3,Color = FLinearColor(1,1,1,1)},                                        --白色
--     [2] = {Brightness = 3,Color = FLinearColor(0,1,0,0), Light = "VehicleSceneLight_Green"},     --绿色
--     [3] = {Brightness = 3,Color = FLinearColor(0,0.5,1,0),Light = "VehicleSceneLight_Blue"},     --蓝色
--     [4] = {Brightness = 3,Color = FLinearColor(0.3,0,0.5,0),Light = "VehicleSceneLight_Purple"}, --紫色
--     [5] = {Brightness = 3,Color = FLinearColor(1,0.25,0,0),Light= "VehicleSceneLight_Orange"},   --橙色
--     [6] = {Brightness = 3,Color = FLinearColor(1,0,0,0), Light = "VehicleSceneLight_Red"},       --红色
-- }
--2025.6.14 策划要求4级品质以下为白色灯光
EVehicleQualityColor = {
    [0] = {Brightness = 3,Color = FLinearColor(1,1,1,1)},                                        --白色
    [2] = {Brightness = 3,Color = FLinearColor(1,1,1,1)},     --绿色
    [3] = {Brightness = 3,Color = FLinearColor(1,1,1,1)},     --蓝色
    [4] = {Brightness = 3,Color = FLinearColor(1,1,1,1)},     --紫色
    [5] = {Brightness = 3,Color = FLinearColor(1,0.25,0,0),Light= "VehicleSceneLight_Orange"},   --橙色
    [6] = {Brightness = 3,Color = FLinearColor(1,0,0,0), Light = "VehicleSceneLight_Red"},       --红色
}

NONE_THEME_ID = EThemeIDType.NoTheme