----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class WinnerItem : LuaUIBaseView
local WinnerItem = ui("WinnerItem")


function WinnerItem:Ctor()
    self._wtTypeName = self:Wnd("DFTextBlock_30", UITextBlock) -- 胜者为王
    self._wtName = self:Wnd("DFTextBlock", UITextBlock) -- 军团指挥官
    self._wtStarNum = self:Wnd("DFTextBlock_1", UITextBlock) -- 军团指挥官
    self._wtRankIconItem = self:Wnd("WBP_Common_BigFlatRank", UIWidgetBase)
    self._wtSelect = self:Wnd("WBP_SlotCompSelected_1", UIWidgetBase)
    self._wtUse = self:Wnd("WBP_SlotCompUsing", UIWidgetBase)

    self._wtBtn = self:Wnd("DFButton", UIButton)
    self._wtBtn:Event("OnClicked", self.OnClick, self)

    self.type = 0
end

function WinnerItem:OnOpen()
end

function WinnerItem:Refresh(type, score, select)
    self.type = type

    local str = ""
    if type == 0 then
        self._wtRankIconItem:SetTournamentIconByScore(score) -- 晋升之路 
        str = Module.RoleInfo.Config.Loc.PathToPromotion
    elseif type == 1 then
        str = Module.RoleInfo.Config.Loc.WinnerTakesAll
        self._wtRankIconItem:SetCommanderIconByScore(score) -- 胜者为王
    end
    self._wtTypeName:SetText(str)

    if select == type then
        self._wtSelect:SelfHitTestInvisible()
    else
        self._wtSelect:Collapsed()
    end

    local curMode = 0

    local basicInfo = Server.RoleInfoServer:GetPlayerBasicInfo()
    if basicInfo then
        curMode = basicInfo.show_commander_rank_points
    end

    if curMode == type then
        self._wtUse:SelfHitTestInvisible()
    else
        self._wtUse:Collapsed()
    end

    local TourModule = Module.Tournament

    local rankInfo = type == 0 and TourModule:GetRankDataByScore(score, nil) or
        TourModule:GetCommanderRankDataByScore(score, nil)


    if rankInfo then
        -- self._wtRankScore:SetText(self._solData.rank_score_max)
        self._wtName:SetText(rankInfo.Name)
        self._wtStarNum:SetText(TourModule:GetStarNumByScore(score) or "?")
    end
end

function WinnerItem:OnClose()
    self:RemoveAllLuaEvent()
end

function WinnerItem:OnClick()
    Server.RoleInfoServer.Events.evtCommanderRankModeSelect:Invoke(self.type)
end

return WinnerItem
