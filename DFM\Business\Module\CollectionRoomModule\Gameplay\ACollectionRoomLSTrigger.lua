----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local UKismetSystemLibrary = import "KismetSystemLibrary"

---@class ACollectionRoomLSTrigger : ACollectionRoomInteractorBase
local ACollectionRoomLSTrigger = class("ACollectionRoomLSTrigger", ACollectionRoomInteractorBase)

function ACollectionRoomLSTrigger:Ctor()
    self:log("Ctor")
end

function ACollectionRoomLSTrigger:TriggerStart()
    if IsHD() and Module.CollectionRoom.Field.bFirstEnterAfterLogin then
        if CollectionRoomConfig.bPlayWholeSequenceOneTime then
            if not Module.CollectionRoom.Field.bHasPlayedWholeLevelSequence then
                Module.CollectionRoom.Field.bHasPlayedWholeLevelSequence = true
                CollectionRoomLogic.PlayLevelSequence(nil, nil, true)
            end
        else
            local objectName = UKismetSystemLibrary.GetObjectName(self)
            if not table.contains(Module.CollectionRoom.Field.playedTriggerNames, objectName) then
                table.insert(Module.CollectionRoom.Field.playedTriggerNames, objectName)
                CollectionRoomLogic.PlayLevelSequence(self.StartLabel, self.StopLabel)
            end
        end
    end
end

function ACollectionRoomLSTrigger:TriggerStop()

end

return ACollectionRoomLSTrigger