----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLitePackage)
----- LOG FUNCTION AUTO GENERATE END -----------



require "DFM.YxFramework.Core.Class.CppObject"
---@class UIWidgetBase : CppObject
LiteDownloadManager = class("LiteDownloadManager", CppObject)
local LitePackageWrapper = import "LitePackageWrapper"
local litePackageMgr = LitePackageWrapper.GetInstance(GetGameInstance())
local UGameSDKManager = import "GameSDKManager"
local gameSDKManager = UGameSDKManager.Get(GetGameInstance())
local UFlibPakHelper = import "FlibPakHelper"
local LitePackageLogic = require "DFM.Business.Module.LitePackageModule.Logic.LitePackageLogic"
local LiteDownloadQuest = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadQuest"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local UGameVersionUtils = import "GameVersionUtils"
local LitePackageDownloadTableItem = require "DFM.Business.DataStruct.LitePackageStruct.LitePackageDownloadTableItem"
local ULuaExtension = import("LuaExtension")
local UDFMGameGPM = import "DFMGameGPM"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
--BEGIN VIRTUOS MODE @Zhang Yingqi
local UDFMInitialChunkManager = import "DFMInitialChunkManager"
local DFMInitialChunkManager = UDFMInitialChunkManager.Get(GetWorld())
--END VIRTUOS MODE
local AssetPackerFunctionLibrary = import "AssetPackerBlueprintFunctionLibrary"
local DFPermissionType = import "EDFPermissionType"
local UDFMGameMaple = import "DFMGameMaple"

local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local Json = JsonFactory.createJson()

local LiteDownloadDataTable = nil
local bNeedInit = true

-----UI data cach-----
local QuestIDAndModuleNameMap = {}
local ModuleNameAndQuestIDMap = {}
local ModuleNameToRelatedHDQuest = {}
local ModuleNameShow = {}

local MapIDToModuleNameMap = {}
local ModuleNameToMapIDsMap = {}

--DownloadInfo--
local DownloadInfoMap = {}

-- [moduleName:0-3] 0:needCheck 1:downloading 2:waiting 3:finished
local ModuleDownloadState = {}
local ModuleDownloaded = {}
local ModuleNowSize = {}
local ModuleTotalSize = {}
local ModuleTotalSizeLocal = {}
local ModuleAllInfo = {}

local ModuleLastCachSize = {}

local LocalModuleAndPakName = {}
local LocalPakNameFileSize = {}
local LocalPakNameFileMD5 = {}

local HDDownloadFlagStr = "LiteDownloadHDFlag" -- 是否已经到达高清状态的标志位
local bHDDownloadStatus = false  -- 是否是高清下载的状态
local sHDDownloadCollection = nil  -- 高清大合集Module名
local tHDDownloadCollectionModuleList = {}  -- 高清大合集包含的包体
local tModuleName2HDRuntimeName = {}  -- 低清Module对应的高清合集名
local tHDRuntimeModuleName2LowModuleName = {}  -- 高清合集名对应的低清模块名
local tHDRuntimeModuleName2HDModuleName = {}  -- 高清合集名对应的高清模块名

--QUEST--
local curdownlaodQuest;
local downloadQuestQueueNew = {}

local curdownlaodQuestName;
local downloadQuestQueue = {}

--PERCENT--
local AsyncCheckQuest = {}
local bHasbeenCheckedQuest = false
local bBeginCheckAllModuleAsync = false

--local dictMapPaks = {"Iris_Entry", "SpaceCenter", "IceLand", "Dam_Iris"}
-- local dictMapPakIds = {90, 91, 92, 93, 96, 97, 98, 99, 100, 101}
local dictMapPakIds = {90, 91, 96, 97, 98, 99, 100, 101} --for test
local dictMpaPakFullNames = {}

--var
local bIsFullPackage = false
local bSimulateLitePack = false
local bSimulateLitePackDownloadBeforeLogin = false

--res fix
local FAILED_MOUNT_PAKS = {}

--review
local dictMapModule = {}

local LiteDownloadMBNum = 1024 * 1024
local LiteDownloadGBNum = 1024 * 1024 * 1024

local WIFI_DOWNLOAD_SPACE_GB = 3
local WIFI_DOWNLOAD_TRIGGER_STYLE = 2
local LITE_HD_WIFI_LOBBY_DOWNLOAD_MODEL_LEVEL = 2
--debug
local DebugPakFix = false
local ENABLE_PAK_FIX_CHECK = true
local ENABLE_PAK_COMBINE = true
local OPEN_CHECK_PAK_HAS_PATCH = false
local LITE_APMLevel = -1

local FLAG_LITE_DELETE_PAKS_MODULE = "DeletePaksByModuleNames"
local FLAG_LITE_DELETE_HD = "LitePackDeleteHDFlag"
local FLAG_LITE_ALL_PAK_FIX_DEEP = "LitePackPakFixDeep"
local DICT_COULD_DELETE_PAKS_IDS = {"90", "91", "92", "93", "96", "97", "98", "99", "100", "101", "125", "126", "127", "128", "129", "89", "137", "67"}
local FLAG_LITE_PAK_MD5_CHECKED = "PakMD5Checked"


local BaseModule = "ShaderLibExt"
local BaseModule_SOL = "ShaderLib_SOL"
local BaseModule_MP = "ShaderLib_MP"
local MPModuleName = "MPOnly"
local SOLModuleName = "SOLOnly"

local CN_LIMITE_DOWNLOAD_SPEED = 1
local GLOBAL_LIMITE_DOWNLOAD_SPEED = 1

--下载
EMountGameMode = {
    None = 0, --默认值
    SOL = 1, --SOL
    MP = 2, --MP
}


--下载
EDownloadStyle = {
    None = 0, --默认值
    Manual = 1, --手动
    Auto = 2, --自动
}

EDownloadTrigger = {
    None = 0, --默认值
    ManagerPop = 1, --下载管理弹窗
    DownloadCenter = 2, --下载中心
    BattlePass = 3, --通行证
    Store = 4, --商城
    GunSmith = 5, --改枪台
    Vehicle = 6, --载具
    DownloadAll = 98, --点击下载全部按钮
    WIFTAuto = 99, --Wifi自动下载
}

EDownloadSystem = {
    None = 0, --默认值
    SafeHouse = 1, --特勤处
    MusicPlayer = 2, --音乐播放器
    SOLModule = 3, --SOL模式资源
    MPModule = 4, --MP模式资源
}

function LiteDownloadManager:Init()
    if bNeedInit == true then
        self:ResetData()
        -- self:ResetEvent()
        --self:MountDownloadedPak()
        self:ReloadLiteConfig()
        bNeedInit = false
        bIsFullPackage = litePackageMgr:IsMobileFullPackage()
    end
    --BEGIN VIRTUOS MODE @Zhang Yingqi
    if IsPS5() then
        self:AddPlatformChunksDownloadFinishedEvent()
    end
    --END VIRTUOS MODE
end

function LiteDownloadManager:ReInit()
    bNeedInit = true
    self:Init()

    loginfo("[LiteDownloadManager] ReInit")
end

function LiteDownloadManager:GetAllPakNames()
    return litePackageMgr:GetAllPakNames()
end

local GAME_MODE_PAKIDS_SOL = {"73"}
local GAME_MODE_PAKIDS_MP = {"74"}
local dictMountedPaks = {}

local bUseUnmountMPSOL = false
---@param mountGameMode EMountGameMode gamemode
function LiteDownloadManager:SetMountGameMode(mountGameMode)
    if bUseUnmountMPSOL == true then
        if PLATFORM_ANDROID or PLATFORM_IOS then
            loginfo("[LiteDownloadManager] SetMountGameMode mountGameMode:"..mountGameMode)
            local allVersionPaks = litePackageMgr:GetAllPakNames()
            -- local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)

            local solpaks = {}
            local mppaks = {}
            for _, newestPakName in pairs(allVersionPaks) do
                if string.find(newestPakName, "%.pak$") then -- 只处理pak文件
                    local pakID = newestPakName:match("pakchunk(%d+)")
                    if table.contains(GAME_MODE_PAKIDS_SOL, pakID) then
                        table.insert(solpaks, newestPakName)
                    elseif table.contains(GAME_MODE_PAKIDS_MP, pakID) then
                        table.insert(mppaks, newestPakName)
                    end
                    -- loginfo("[LiteDownloadManager] SetMountGameMode pakID:"..pakID)
                end
            end

            self:SetMountGameModeByArray(solpaks, mountGameMode, EMountGameMode.SOL)
            self:SetMountGameModeByArray(mppaks, mountGameMode, EMountGameMode.MP)
        end
    end
end


function LiteDownloadManager:SetMountGameModeByArray(paks, mountGameMode, nowprocessMode)
    if paks == nil or #paks <= 0 then
        loginfo("[LiteDownloadManager] SetMountGameModeByArray paks == nil or #paks <=0")
        return
    end

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    for index, pakName in ipairs(paks) do
        loginfo("[LiteDownloadManager] SetMountGameModeByArray paks pakname:"..pakName)

        local pakPath = pufferPath.."/"..pakName
        local bMounted = false
        local pos = 0
        for k, name in ipairs(dictMountedPaks) do
            if name == pakPath then
                bMounted = true
                pos = k
                break
            end
        end

        if mountGameMode == nowprocessMode then
            if bMounted == false then
                local bReady, errorInfo = self:CheckPakFileReady(pakName, pakPath)
                if bReady then
                    local ret = UFlibPakHelper.MountPak(pakPath, 5, "")
                    if ret then
                        table.insert(dictMountedPaks, pakPath)
                    else
                        table.insert(FAILED_MOUNT_PAKS, pakPath)
                        loginfo("[LiteDownloadManager] SetMountGameModeByArray MountDownloadedPak >> self:ReportPakMountFailed"..pakName)
                    end
                    loginfo("[LiteDownloadManager] SetMountGameModeByArray MountDownloadedPak >> MountPak ExtenPak:"..pakPath..", result :"..tostring(ret))
                end
            end
        else
            if bMounted == true then
                local ret = UFlibPakHelper.UnMountPak(pakPath)
                if pos > 0 then
                    table.remove(dictMountedPaks, pos)
                end

                loginfo("[LiteDownloadManager] SetMountGameModeByArray UnMountDownloadedPak >> UnMountPak ExtenPak:"..pakPath..", result :"..tostring(ret)..", pos:"..pos)
            end
        end
    end
end

function LiteDownloadManager:CheckPakFileReady(pakName, pakPath)
    local bFileReady = true
    local checkError = ""
    --> check total size
    local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(pakName)
    local localSize = ULuaExtension.Ext_GetFileSize(pakPath)
    local correctlyFileSize = self:GetPakFileCorrectlySize(pakName)
    if correctlyFileSize == nil then
        correctlyFileSize = 0
        logerror("[LiteDownloadManager] config has no filesize: "..pakName)
    end

    if fileState == true then
        local checkinfo = string.format("[LiteDownloadManager] CheckPakFileReady Puffer filename:%s, correctlyFileSize:%s, localSize:%s", pakName, tostring(correctlyFileSize), tostring(localSize))
        loginfo(checkinfo)
        if correctlyFileSize > 0 and localSize ~= correctlyFileSize then
            bFileReady = false
            table.insert(FAILED_MOUNT_PAKS, pakPath)
            checkError = "file size not match"
            logerror("[LiteDownloadManager] CheckPakFileReady Puffer fileState == true but file size not match: "..pakName)
        end
    else
        loginfo("[LiteDownloadManager] CheckPakFileReady Puffer file not ready by puffer")
        local bIsIncombined = self:CheckPakNameIsInCombined(pak_file_name)
        if bIsIncombined == true then
            loginfo("[LiteDownloadManager] CheckPakFileReady CheckPakNameIsInCombined in combined")
        else
            bFileReady = false
            loginfo("[LiteDownloadManager] CheckPakFileReady CheckPakNameIsInCombined not in combined")
        end
    end

    return bFileReady, checkError
end

function LiteDownloadManager:MountDownloadedPak()
    local allVersionPaks = litePackageMgr:GetAllPakNames()
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
    dictMpaPakFullNames = {}
    for _, value in pairs(pakFiles) do
        if value then
            local _, _, build_id = string.find(value, "%d+%.%d+.%d+.%d+.(%d+)_")
            local MoundOrder = build_id or 5

            local pak_file_name = string.match(value, "([^/]+)$")
            local bNeedMountNow = true
            local pakID = pak_file_name:match("pakchunk(%d+)")
            if allVersionPaks ~= nil and #allVersionPaks > 0 then
                -- loginfo("[LiteDownloadManager] MountDownloadedPak process mount versionpakname:" .. pak_file_name)
                local bInVersion = table.contains(allVersionPaks, pak_file_name)
                bNeedMountNow = bInVersion

                if bInVersion then
                    --> check total size
                    local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(pak_file_name)
                    local localSize = ULuaExtension.Ext_GetFileSize(value)
                    local correctlyFileSize = self:GetPakFileCorrectlySize(pak_file_name)
                    if correctlyFileSize == nil then
                        correctlyFileSize = 0
                        logerror("[LiteDownloadManager] config has no filesize: "..pak_file_name)
                    end

                    if fileState == true then
                        local checkinfo = string.format("[LiteDownloadManager] Puffer filename:%s, correctlyFileSize:%s, localSize:%s", pak_file_name, tostring(correctlyFileSize), tostring(localSize))
                        loginfo(checkinfo)
                        if correctlyFileSize > 0 and localSize ~= correctlyFileSize then
                            bNeedMountNow = false
                            table.insert(FAILED_MOUNT_PAKS, value)
                            self:ReportPakMountFailed(pak_file_name, "file size not match")
                            logerror("[LiteDownloadManager] Puffer fileState == true but file size not match: "..pak_file_name)
                        end
                    else
                        loginfo("[LiteDownloadManager] Puffer file not ready by puffer")
                        local bIsIncombined = self:CheckPakNameIsInCombined(pak_file_name)
                        if bIsIncombined == true then
                            loginfo("[LiteDownloadManager] CheckPakNameIsInCombined in combined pak_file_name:"..pak_file_name)
                        else
                            bNeedMountNow = false
                            loginfo("[LiteDownloadManager] CheckPakNameIsInCombined not in combined pak_file_name:"..pak_file_name)
                        end
                        -- self:ReportPakMountFailed(pak_file_name, "file not ready by puffer")
                    end
                end
            end

            if bNeedMountNow then
                local bMounted = false
                for _, name in ipairs(dictMountedPaks) do
                    if name == value then
                        bMounted = true
                        break
                    end
                end

                if bMounted == false then
                    local ret = UFlibPakHelper.MountPak(value, MoundOrder, "")
                    if DebugPakFix then
                        local number = pak_file_name:match("pakchunk(%d+)")
                        if number == "88" then
                            ret = false
                        end
                    end

                    if ret then
                        table.insert(dictMountedPaks, value)
                    else
                        table.insert(FAILED_MOUNT_PAKS, value)
                        --report
                        self:ReportPakMountFailed(pak_file_name, "mount failed")
                        loginfo("[LiteDownloadManager] Puffer MountDownloadedPak >> self:ReportPakMountFailed"..pak_file_name)
                    end
                    loginfo("[LiteDownloadManager] Puffer MountDownloadedPak >> MountPak ExtenPak:", value, ", result :"
                        , ret)
                end
            else
                -- loginfo("[LiteDownloadManager] Puffer MountDownloadedPak >> MountPak ExtenPak:", value, ", but bNeedMountNow == false")
            end
        end
    end
end

function LiteDownloadManager:MountMapPaks()
    if not LitePackageLogic:IsOpenMountMapLazy() then
        return
    end

    for _, value in ipairs(dictMpaPakFullNames) do
        local bMounted = false
        for k, name in ipairs(dictMountedPaks) do
            if name == value then
                bMounted = true
                break
            end
        end

        if bMounted == false then
            local _, _, build_id = string.find(value, "%d+%.%d+.%d+.%d+.(%d+)_")
            local MoundOrder = build_id or 5
            local ret = UFlibPakHelper.MountPak(value, MoundOrder, "")
            if ret then
                table.insert(dictMountedPaks, value)
            end
            loginfo("[LiteDownloadManager] MountMapPaks Puffer MountDownloadedPak >> MountPak ExtenPak maps:", value, ", result :",
                ret)
        end
    end
end

function LiteDownloadManager:ReportPakDeleteToCrashSight(info, reason)
    local UDFMCrashSight = import "DFMCrashSight"
    -- @param type type 3-cocoa 4-c# 5-JS 6-Lua
    -- @param name name
    -- @param reason reason
    -- @param stackTrace stackTrace， json数组序列化字符串
    -- @param extras extras, json对象序列化字符串
    -- @param quit quit
    UDFMCrashSight.ReportException(6,"PakDelete", reason , info or "","{\"tracelyl\":\"errorlyl\"}",false)
end

function LiteDownloadManager:ReportPakDownloadMD5ToCrashSight(info, reason)
    local UDFMCrashSight = import "DFMCrashSight"
    -- @param type type 3-cocoa 4-c# 5-JS 6-Lua
    -- @param name name
    -- @param reason reason
    -- @param stackTrace stackTrace， json数组序列化字符串
    -- @param extras extras, json对象序列化字符串
    -- @param quit quit
    UDFMCrashSight.ReportException(6,"PufferDownloadMD5", reason, info or "","{\"tracelyl\":\"errorlyl\"}",false)
end

function LiteDownloadManager:ReportPakFixInfoToCrashSight(info)
    local UDFMCrashSight = import "DFMCrashSight"
    -- @param type type 3-cocoa 4-c# 5-JS 6-Lua
    -- @param name name
    -- @param reason reason
    -- @param stackTrace stackTrace， json数组序列化字符串
    -- @param extras extras, json对象序列化字符串
    -- @param quit quit
    UDFMCrashSight.ReportException(6,"PakFixCheck", "reason", info or "","{\"tracelyl\":\"errorlyl\"}",false)
end

function LiteDownloadManager:ReportPakMountFailed(info, reason)
    if reason == nil then
        reason = "reason"
    end

    local UDFMCrashSight = import "DFMCrashSight"
    -- @param type type 3-cocoa 4-c# 5-JS 6-Lua
    -- @param name name
    -- @param reason reason
    -- @param stackTrace stackTrace， json数组序列化字符串
    -- @param extras extras, json对象序列化字符串
    -- @param quit quit
    UDFMCrashSight.ReportException(6,"PakMountFailed", reason , info or "","{\"tracelyl\":\"errorlyl\"}",false)
end

function LiteDownloadManager:CheckFailedMountPaks()
    if ENABLE_PAK_FIX_CHECK == false then
        return false
    end

    if PLATFORM_ANDROID or PLATFORM_IOS or _WITH_EDITOR then
        if ULuaExtension == nil then
            loginfo("[LiteDownloadManager] CheckFailedMountPaks ULuaExtension is nil")
            return
        end

        if self.bResetPakFileSizeAndMD5 ~= true then
            self:ResetPaksFileSizeAndMD5()
            self.bResetPakFileSizeAndMD5 = true
        end

        local bHasFileMD5Disaccord = false
        local needFixPaks = {}
        for k, pakPath in ipairs(FAILED_MOUNT_PAKS) do
            local pakFileName = string.match(pakPath, "([^/]+)$")
            local correctlyMD5 = LocalPakNameFileMD5[pakFileName]
            if correctlyMD5 ~= nil or DebugPakFix then
                local localMD5 = ULuaExtension.CalcFileMD5(pakPath)
                local checkinfo = string.format("[LiteDownloadManager] CheckFailedMountPaks pakFileName:%s, correctlyMD5:%s, localMD5:%s", pakFileName, correctlyMD5, localMD5)
                loginfo(checkinfo)
                if localMD5 ~= correctlyMD5 then
                    bHasFileMD5Disaccord = true
                    table.insert(needFixPaks, pakPath)
                end
            else
                loginfo("[LiteDownloadManager] CheckFailedMountPaks has no correctlyMD5, pakFileName:"..pakFileName)
            end
        end

        if bHasFileMD5Disaccord then
            self:SetNeedFixPak(needFixPaks)
        end

        return bHasFileMD5Disaccord
    end

    return false
end

---@param downloadSystem EDownloadSystem 需要下载的系统类型
function LiteDownloadManager:GetSystemDownloadModuleName(downloadSystem)
    local bNeedDownloadHD = self:IsHDDownloadStatus()
    local retModuleName = nil
    if downloadSystem == EDownloadSystem.SafeHouse then
        retModuleName = "SafeHouseOnly"
    elseif downloadSystem == EDownloadSystem.SOLModule then
        retModuleName = "SOLOnly"
    elseif downloadSystem == EDownloadSystem.MPModule then
        retModuleName = "MPOnly"
    elseif downloadSystem == EDownloadSystem.MusicPlayer then
        retModuleName = "LobbyMusicAudio"
    end

    if retModuleName ~= nil and bNeedDownloadHD then
        retModuleName = self:GetHDRuntimeNameByModuleName(retModuleName)
    end
    return retModuleName
end

function LiteDownloadManager:GetPakFileCorrectlySize(pakName)
    local fileSize = nil
    if LocalPakNameFileSize ~= nil then
        fileSize = LocalPakNameFileSize[pakName]
    end

    return fileSize
end

function LiteDownloadManager:GetPakFileCorrectlyMD5(pakName)
    local correctlyMD5 = nil
    if PLATFORM_ANDROID or PLATFORM_IOS or _WITH_EDITOR then
        if self.bResetPakFileSizeAndMD5 ~= true then
            self:ResetPaksFileSizeAndMD5()
            self.bResetPakFileSizeAndMD5 = true
        end

        correctlyMD5 = LocalPakNameFileMD5[pakName]
    end

    return correctlyMD5
end

function LiteDownloadManager:GetDebugPakFix()
    return DebugPakFix
end



function LiteDownloadManager:GetNeedFixPaks()
    if ENABLE_PAK_FIX_CHECK == false then
        return nil
    end

    local paksStr = Facade.ConfigManager:GetString("NeedFixPaks", "")
    loginfo("[LiteDownloadManager] GetNeedFixPaks:"..paksStr)
    local result = {}
    local delimiter = ","
    for match in string.gmatch(paksStr, "([^" .. delimiter .. "]+)") do
        table.insert(result, match:match("^%s*(.-)%s*$"))
    end

    return result
end

function LiteDownloadManager:SetNeedFixPak(needFixPaks)
    local info = ""
    for k, pakName in ipairs(needFixPaks) do
        if info == "" then
            info = pakName
        else
            info = info..","..pakName
        end
    end

    loginfo("[LiteDownloadManager] SetNeedFixPak:"..info)
    Facade.ConfigManager:SetString("NeedFixPaks", info or "")
end

function LiteDownloadManager:SetNeedFixPakFinished()
    DebugPakFix = false
    Facade.ConfigManager:SetString("NeedFixPaks", "")
end

function LiteDownloadManager:SetLaunchFixPak()
    Facade.ConfigManager:SetBoolean("LaunchFixPak", true)
end

function LiteDownloadManager:GetLaunchFixPak()
    return Facade.ConfigManager:GetBoolean("LaunchFixPak", false)
end

function LiteDownloadManager:SetLaunchFixPakFinished()
    Facade.ConfigManager:SetBoolean("LaunchFixPak", false)
end

function LiteDownloadManager:UnMountMapPaks()
    if not LitePackageLogic:IsOpenMountMapLazy() then
        return
    end

    for _, value in ipairs(dictMpaPakFullNames) do
        local ret = UFlibPakHelper.UnMountPak(value)
        local pos = 0
        for k, name in ipairs(dictMountedPaks) do
            if name == value then
                pos = k
                break
            end
        end
        if pos > 0 then
            table.remove(dictMountedPaks, pos)
        end
        loginfo("[LiteDownloadManager] MountMapPaks Puffer MountDownloadedPak >> UnMountPak ExtenPak maps:", value, ", result :", ret)
    end
end

---return 0: need check , 1:ready, 2:not ready
function LiteDownloadManager:GetModuleStateByModuleName(moduleName)
    return litePackageMgr:GetModuleStateByModuleName(moduleName)
end

function LiteDownloadManager:SetModuleStateByModuleName(moduleName, state)
    return litePackageMgr:SetModuleStateByModuleName(moduleName, state)
end

function LiteDownloadManager:AddGameEvent()
    self:AddLuaEvent(Server.MatchServer.Events.evtStartMatching, self.OnStartMatching, self)
end

function LiteDownloadManager:OnStartMatching()
    self:MountMapPaks()
    loginfo("[LiteDownloadManager] OnStartMatching MountMapPaks.")
end

function LiteDownloadManager:ResetEvent()
    litePackageMgr.OnLiteDownloadProgress:Add(CreateCPlusCallBack(self.OnLiteDownloadProgress, self))
    litePackageMgr.OnLiteDownloadReturn:Add(CreateCPlusCallBack(self.OnLiteDownloadReturn, self))
    litePackageMgr.OnLiteModuleCheckResult:Add(CreateCPlusCallBack(self.OnLiteModuleCheckResult, self))

    self:AddLuaEvent(Module.LitePackage.Config.evtCurrentDownloadQuestNeedMoveNext, self.OnCurrentDownloadQuestNeedMoveNext,self)

end

function LiteDownloadManager:RemoveEvent()
    litePackageMgr.OnLiteDownloadProgress:Clear()
    litePackageMgr.OnLiteDownloadReturn:Clear()
    litePackageMgr.OnLiteModuleCheckResult:Clear()
end

function LiteDownloadManager:ResetData()
    self.SortedDownloadTable = {}
    self.SortedInMPDownloadTable = {}
    local i = 0
    LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload", true)
    local bIsShipping = UGameVersionUtils.IsShipping()
    if LiteDownloadDataTable then
        for k, v in pairs(LiteDownloadDataTable) do
            local bAdd = true
            if bIsShipping and v.MapOrModeDownload == 3 then
                bAdd = false
            end

            if bAdd then
                i = i + 1
                local inst = LitePackageDownloadTableItem:New(v)
                self.SortedDownloadTable[i] = inst
                self.SortedInMPDownloadTable[i] = inst
                QuestIDAndModuleNameMap[v.QuestID] = v.ModuleKey
                ModuleNameAndQuestIDMap[v.ModuleKey] = v.QuestID
                ModuleDownloadState[v.ModuleKey] = 0
                ModuleNowSize[v.ModuleKey] = 0
                ModuleLastCachSize[v.ModuleKey] = 0
                ModuleTotalSize[v.ModuleKey] = 0
    
                DownloadInfoMap[v.ModuleKey] = {}
    
                if v.MapOrModeDownload == 1 or v.MapOrModeDownload == 2 then
                    table.insert(dictMapModule, v.ModuleKey)
    
                    ModuleNameToMapIDsMap[v.ModuleKey] = {}
                    table.insert(ModuleNameToMapIDsMap[v.ModuleKey], v.MapOrModeParams)
                    MapIDToModuleNameMap[v.MapOrModeParams] = v.ModuleKey
                    local arr = {}
                    for mapid in string.gmatch(v.MapOrModeParams, "[^,]+") do
                        table.insert(arr, mapid)
                    end
    
                    for i, mapid in ipairs(arr) do
                        MapIDToModuleNameMap[mapid] = v.ModuleKey
                        --loginfo("[LiteDownloadManager] ResetData add mapid:", mapid, " to module:", v.ModuleKey)
                    end
    
                    -- register map base paks
                    -- self:RegisterRuntimeMultiModuleByChildModule(v.ModuleKey, { v.ModuleKey, "MapOtherMap" }, true)
                end

                -- 是否有关联的高清模块
                if v.RelatedHDQuest and v.RelatedHDQuest ~= "" then
                    local tRelatedHDQuestStr = self:split(v.RelatedHDQuest, ",")
                    local tRelatedHDQuest = {}
                    for index, questID in pairs(tRelatedHDQuestStr) do
                        table.insert(tRelatedHDQuest, tonumber(questID))
                    end 
                    ModuleNameToRelatedHDQuest[v.ModuleKey] = tRelatedHDQuest
                end

                -- 是否展示在下载中
                if v.IsShow and v.IsShow == 1 then
                    table.insert(ModuleNameShow, v.ModuleKey)
                end
            end
        end
    end

    table.sort(self.SortedDownloadTable, function(a, b)
        return a.Priority > b.Priority
    end)

    table.sort(self.SortedInMPDownloadTable, function(a, b)
        if a.PriorityInMP == nil or b.PriorityInMP == nil then
            return a.Priority > b.Priority
        end

        return a.PriorityInMP > b.PriorityInMP
    end)
end

function LiteDownloadManager:ReloadLiteConfig()
    litePackageMgr:ReloadLiteConfig()
    loginfo("[LiteDownloadManager] LiteDownloadManager:ReloadLiteConfig()")
end

function LiteDownloadManager:GenerateAllModulePaksForWholePackage()
    litePackageMgr:GenerateAllModulePaksForWholePackage()
end

function LiteDownloadManager:GetFileSize(path)
    local f, err = io.open(path, "rb")
    if not f then
        return 0
    end

    local size = f:seek("end")
    f:close()
    return size
end

function LiteDownloadManager:ResetMapleMaxSpeed(speed)
    litePackageMgr:SetDownloadMaxSpeed(speed)
end

function LiteDownloadManager:SetImmDLMaxTask(maxTask)
    litePackageMgr:SetImmDLMaxTask(maxTask)
end

function LiteDownloadManager:SetImmDLMaxDownloadsPerTask(maxDownloadsPerTask)
    litePackageMgr:SetImmDLMaxDownloadsPerTask(maxDownloadsPerTask)
end

function LiteDownloadManager:ProcessPakDebug()
    local pakFiles = UFlibPakHelper.ScanExtenPakFiles("PakDebug")
    for _, value in pairs(pakFiles) do
        if value then
            local ret = UFlibPakHelper.MountPak(value, 100, "")
            loginfo("[LiteDownloadManager] ProcessPakDebug >> MountPak ExtenPak:", value, ", result :", ret)
        end
    end
end

local bCheckPredownloadMD5 = true
function LiteDownloadManager:CheckPredownloadMD5()
    if bCheckPredownloadMD5 then
        if ULuaExtension == nil then
            loginfo("[LiteDownloadManager] CheckPredownloadMD5 md5 is nil")
        else
            loginfo("[LiteDownloadManager] CheckPredownloadMD5 begin check")
            local bIsShipping = UGameVersionUtils.IsShipping()
            local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
            local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
            for _, value in pairs(pakFiles) do
                if value then
                    local md5 = ULuaExtension.CalcFileMD5(value)
                    loginfo("[LiteDownloadManager:CheckPredownloadMD5] file:"..value..", md5:"..md5)
                end
            end
        end
    end
end

function LiteDownloadManager:CheckAndSetWholePackageStatesByFileNames()
    local LocalFiles = {}
    local LocalFileSize = {}

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)

    local dfFile = pufferPath.."//puffer_set_file_state.df"
    local bExists = ULuaExtension.Ext_FileExists(dfFile)
    if dfFile ~= nil and bExists then
        for line in io.lines(dfFile) do
            if line ~= nil then
                local fileName, fileSize
                local pattern = "(.-)=(.*)"
                for k, v in string.gmatch(line, pattern) do
                    fileName = k
                    fileSize = string.gsub(v, "%s+", "")
                    break
                end

                local localSize = 0

                for _, value in pairs(pakFiles) do
                    if value then
                        local escapedKeyword = string.gsub(fileName, "[%(%)%.%%%+%-%*%?%[%^%$%]]", "%%%0") -- 对包含在 fileName 中的特殊字符加上转义符号
                        if string.find(value, escapedKeyword) ~= nil then
                            localSize = UFlibPakHelper.GetExtenFileSize(value)
                        end
                    end
                end

                local strLocalSize = tostring(localSize)
                if fileSize == strLocalSize then
                    table.insert(LocalFiles, fileName)
                    table.insert(LocalFileSize, localSize)
                    loginfo("[LiteDownloadManager] CheckAndSetWholePackageStatesByFileNames PakName:", fileName,
                        ", fileSize:", fileSize, ", localSize:", localSize, " set file is ready")
                else
                    loginfo("[LiteDownloadManager] CheckAndSetWholePackageStatesByFileNames PakName:", fileName,
                        ", fileSize:", fileSize, ", localSize:", localSize, " file need download")
                end
            end
        end

        self:DeleteFileByPakName(dfFile)
    else
        loginfo("[LiteDownloadManager] CheckAndSetWholePackageStatesByFileNames puffer_set_file_state is not exists, skip")
    end

    local needSetPaks = {}
    local needSetPaksMd5 = {}
    for key, value in pairs(LocalFiles) do
        local md5 = self:GetPakFileCorrectlyMD5(value)
        if md5 ~= nil then
            table.insert(needSetPaks, value)
            local md5upper = string.upper(md5)
            table.insert(needSetPaksMd5, md5upper)
            litePackageMgr:SetCachFileStatusByFileName(value, md5upper)
            loginfo("LiteDownloadManager:CheckAndSetWholePackageStatesByFileNames pakName:"..value.." md5:"..md5upper)
        end
    end

    -- litePackageMgr:CheckAndSetWholePackageStatesByFileNames(LocalFiles)
end

function LiteDownloadManager:StartCheckModuleInfosAsync()
    if self:IsSupportLitePackage() == false then
        return
    end

    if self.SortedDownloadTable == nil then
        return
    end

    if bBeginCheckAllModuleAsync then
        return
    end

    loginfo("[LiteDownloadManager] StartCheckModuleInfosAsync")

    AsyncCheckQuest = {}
    bBeginCheckAllModuleAsync = true
    for k, v in pairs(self.SortedDownloadTable) do
        table.insert(AsyncCheckQuest, v.ModuleKey)
    end
    for k, v in pairs(tModuleName2HDRuntimeName) do
        table.insert(AsyncCheckQuest, v)
    end
    if sHDDownloadCollection then
        table.insert(AsyncCheckQuest, sHDDownloadCollection)
    end

    --insert "Weapon" "OtherWeapon" to cach
    table.insert(AsyncCheckQuest, "Weapon")
    -- table.insert(AsyncCheckQuest, "OtherWeapon")
    table.insert(AsyncCheckQuest, "BeforeLoginHD")
    table.insert(AsyncCheckQuest, "OtherBeforeLoginHD")

    local allChildModuleName = self:GetAllChildModuleName()
    for k, v in pairs(allChildModuleName) do
        table.insert(AsyncCheckQuest, v)
    end
    -- for k, moduleKey in pairs(AsyncCheckQuest) do
    --     self:CheckModuleAsync(moduleKey)
    -- end

    local nowModuleKey = AsyncCheckQuest[1]
    self:CheckModuleAsync(nowModuleKey)
end

function LiteDownloadManager:CheckAllChildModuleInfosAsync()
    if self:IsSupportLitePackage() == false then
        return
    end

    loginfo("[LiteDownloadManager] CheckAllChildModuleInfosAsync")

    AsyncCheckQuest = {}
    local asyncCount = #AsyncCheckQuest
    --insert "Weapon" "OtherWeapon" to cach
    table.insert(AsyncCheckQuest, "Weapon")
    table.insert(AsyncCheckQuest, "OtherWeapon")
    table.insert(AsyncCheckQuest, "BeforeLoginHD")
    table.insert(AsyncCheckQuest, "OtherBeforeLoginHD")

    local allChildModuleName = self:GetAllChildModuleName()
    for k, v in pairs(allChildModuleName) do
        table.insert(AsyncCheckQuest, v)
    end
    -- for k, moduleKey in pairs(AsyncCheckQuest) do
    --     self:CheckModuleAsync(moduleKey)
    -- end
    for k, v in pairs(tModuleName2HDRuntimeName) do
        table.insert(AsyncCheckQuest, v)
    end
    if sHDDownloadCollection then
        table.insert(AsyncCheckQuest, sHDDownloadCollection)
    end

    if asyncCount <= 0 then
        local nowModuleKey = AsyncCheckQuest[1]
        self:CheckModuleAsync(nowModuleKey)
    end
end

function LiteDownloadManager:GetAllChildModuleName()
    return litePackageMgr:GetAllChildModuleName()
end

function LiteDownloadManager:CheckModuleAsync(moduleName)
    if moduleName == nil then
        return
    end

    litePackageMgr:CheckModuleState(moduleName)
end

local CheckModuleAsyncQueue = {}
function LiteDownloadManager:CheckModuleAsyncWithQueue(moduleName)
    if moduleName == nil then
        return
    end

    if table.contains(CheckModuleAsyncQueue, moduleName) == true then
        return
    end

    table.insert(CheckModuleAsyncQueue, moduleName)
    litePackageMgr:CheckModuleState(moduleName)
end

---public 函数
function LiteDownloadManager:ReportPreDownloadEvent(EnablePredownload, ErrCode, ErrStage, ExtractFilecount, FindPatchfile, OpenPatch, Patchfile, PatchfileMd5, PredownloadType, TotalFilecount, PatchMatchMd5, Sdcard)
    litePackageMgr:ReportPreDownloadEvent(EnablePredownload, ErrCode, ErrStage, ExtractFilecount, FindPatchfile, OpenPatch, Patchfile, PatchfileMd5, PredownloadType, TotalFilecount, PatchMatchMd5, Sdcard)
end

function LiteDownloadManager:ReportPreDownloadEventTGPA(EnablePredownload, ErrCode, ErrStage, ExtractFilecount, FindPatchfile, OpenPatch, Patchfile, PatchfileMd5, PredownloadType, TotalFilecount, PatchMatchMd5, Sdcard)
    litePackageMgr:ReportPreDownloadEventTGPA(EnablePredownload, ErrCode, ErrStage, ExtractFilecount, FindPatchfile, OpenPatch, Patchfile, PatchfileMd5, PredownloadType, TotalFilecount, PatchMatchMd5, Sdcard)
end

function LiteDownloadManager:GetPreDownloadState()
    return litePackageMgr:GetPreDownloadState()
end

function LiteDownloadManager:GetPreDownloadStateProgress()
    return litePackageMgr:GetPreDownloadStateProgress()
end

function LiteDownloadManager:RegisterTgpaCallBack()
    litePackageMgr:RegisterTgpaCallBack()
end

function LiteDownloadManager:UnRegisterTgpaCallBack()
    litePackageMgr:UnRegisterTgpaCallBack()
end

function LiteDownloadManager:LiteGetDataFromTGPA(key, value)
    return litePackageMgr:LiteGetDataFromTGPA(key, value)
end

function LiteDownloadManager:CheckAndSetPreDownload()
    local LocalFiles = {}
    local LocalFileSize = {}

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)

    local dfFile = pufferPath.."//puffer_set_file_state.df"
    if dfFile ~= nil then
        for line in io.lines(dfFile) do
            if line ~= nil then
                local fileName, fileSize
                local pattern = "(.-)=(.*)"
                for k, v in string.gmatch(line, pattern) do
                    fileName = k
                    fileSize = string.gsub(v, "%s+", "")
                    break
                end

                local localSize = 0

                for _, value in pairs(pakFiles) do
                    if value then
                        local escapedKeyword = string.gsub(fileName, "[%(%)%.%%%+%-%*%?%[%^%$%]]", "%%%0") -- 对包含在 fileName 中的特殊字符加上转义符号
                        if string.find(value, escapedKeyword) ~= nil then
                            localSize = UFlibPakHelper.GetExtenFileSize(value)
                        end
                    end
                end

                local strLocalSize = tostring(localSize)
                if fileSize == strLocalSize then
                    table.insert(LocalFiles, fileName)
                    table.insert(LocalFileSize, localSize)
                    loginfo("[LiteDownloadManager] CheckAndSetPreDownload PakName:", fileName,
                        ", fileSize:", fileSize, ", localSize:", localSize, " set file is ready")
                else
                    loginfo("[LiteDownloadManager] CheckAndSetPreDownload PakName:", fileName,
                        ", fileSize:", fileSize, ", localSize:", localSize, " file need download")
                end
            end
        end

        -- os.remove(dfFile)
    else
        loginfo("[LiteDownloadManager] CheckAndSetPreDownload puffer_set_file_state is not exists, skip")
    end

    litePackageMgr:CheckAndSetWholePackageStatesByFileNames(LocalFiles)
end

function LiteDownloadManager:IsMobileFullPackage()
    return bIsFullPackage --litePackageMgr:IsMobileFullPackage()
end

function LiteDownloadManager:IsSimulateLitePackDownloadBeforeLogin()
    return _WITH_EDITOR == 1 and bSimulateLitePackDownloadBeforeLogin
end

function LiteDownloadManager:IsSupportLitePackage()
    return litePackageMgr:IsSupportLitePackage()
end

function LiteDownloadManager:IsSimulateLitePack()
    return _WITH_EDITOR == 1 and bSimulateLitePack
end

function LiteDownloadManager:SetSimulateVersion(simulateVersion)
    litePackageMgr:SetSimulateVersion(simulateVersion)
end

---@return number freeSpace
function LiteDownloadManager:GetDeviceFreeSpace()
    return litePackageMgr:GetDeviceFreeSpace()
end

---@return number 0:unknow 1:wifi 2:WWAN 2G~5G
function LiteDownloadManager:GetNetworkState()
    return litePackageMgr:GetNetworkState()
end

---@return number 0:unknow 1:wifi 2:WWAN 2G~5G
function LiteDownloadManager:IsTickFromBGD()
    return litePackageMgr:IsTickFromBGD()
end

---@return number errorcode
function LiteDownloadManager:GetPufferInitErrorCode()
    return litePackageMgr:GetPufferInitErrorCode()
end

function LiteDownloadManager:GMSwicthEditorLitePackage(bOpen)
    litePackageMgr:GMSwicthEditorLitePackage(bOpen)
end

function LiteDownloadManager:GMEnableEditorDownload(bEnable)
    litePackageMgr:GMEnableEditorDownload(bEnable)
end

function LiteDownloadManager:CreateMgrTimer()
    litePackageMgr:CreateMgrTimer()
end

function LiteDownloadManager:ClearMgrTimer()
    litePackageMgr:ClearMgrTimer()
end

function LiteDownloadManager:IsPufferInitSucceed()
    return litePackageMgr:IsPufferInitSucceed()
end

function LiteDownloadManager:InitPufferDownloader()
    litePackageMgr:InitPufferDownloader()
end

function LiteDownloadManager:ReleasePufferDownloader()
    litePackageMgr:ReleasePufferDownloader()
end

function LiteDownloadManager:DeleteFileByModuleName(moduleName)
    -- local tModuleConf = self:GetDownloadConfigByModuleName(moduleName)
    -- if tModuleConf == nil then
    --     logerror("module", moduleName, "conf not exist")
    --     return
    -- end

    -- if tModuleConf.IsDeletable == nil then
    --     logerror("module", moduleName, "conf IsDeletable exist")
    --     return
    -- end

    -- loginfo("module", moduleName, "IsDeletable:", tModuleConf.IsDeletable)

    -- -- 只有可以被删除的才继续处理，否则返回
    -- if tModuleConf.IsDeletable == 0 then
    --     return false
    -- end

    -- local bResult = litePackageMgr:DeleteFileByModuleName(moduleName)
    -- if not bResult then
    --     return false
    -- else
    --     self:CheckModuleAsync(moduleName)
    --     return true
    -- end
end

function LiteDownloadManager:DeleteFileByPakName(pakName)
    -- check pakName mounted?
    local bCloudBeDelete = true
    for k, pakPath in ipairs(dictMountedPaks) do
        local bFound = string.find(pakPath, pakName)
        if bFound ~= nil then
            bCloudBeDelete = false
        end
    end

    if bCloudBeDelete == false then
        self:ReportPakDeleteToCrashSight(pakName, "PakMounted")
        return false
    end

    return litePackageMgr:DeleteFileByPakName(pakName)
end

function LiteDownloadManager:IsAllQuestDownloaded()
    if self:ISQuestsHasBeenChecked() == false then
        return false
    end

    local checkMoudles = {}
    for k, v in pairs(self.SortedDownloadTable) do
        local bIsShow = self:IsModuleShow(v.ModuleKey)
        if bIsShow then
            table.insert(checkMoudles, v.ModuleKey)
        end
    end

    for k, moduleKey in pairs(checkMoudles) do
        if self:IsDownloadedByModuleName(moduleKey) == false then
            return false
        end
    end

    return true
end

function LiteDownloadManager:IsMapResoureReady(mapid)
    for k, v in pairs(MapIDToModuleNameMap) do
        if k == mapid then
            --check map module is ready
        end
    end

    return true
end

function LiteDownloadManager:GetDownloadConfigByModuleName(moduleName)
    local bHasHDRuntime = string.find(moduleName, "HDRuntime")
    if bHasHDRuntime ~= nil then
        logerror("[LiteDownloadManager] GetDownloadConfigByModuleName:"..moduleName)
        moduleName = self:GetLowModuleName(moduleName)
    end

    for k, v in pairs(self.SortedDownloadTable) do
        if v.ModuleKey == moduleName then
            return v
        end
    end

    return nil
end

function LiteDownloadManager:GetDownloadedQuestIDs()
    local bSupportLitePack = self:IsSupportLitePackage()
    local isIgnorePlatform = false

    if DFHD_LUA == 1 or self:IsMobileFullPackage() == true then
        if self:IsSimulateLitePack() == false then
            isIgnorePlatform = true
        end
    end

    if IsInEditor() then
        isIgnorePlatform = true
    end
    
    local downloadedQuestIDs = {}
    --- BEGIN MODIFICATION @ Peng Bofeng
    --- FOR Play GO, downloaded Quest Filter
    if IsPS5() then
        for k, v in pairs(QuestIDAndModuleNameMap) do
            if self:IsDownloadedByQuestID(k) then
                table.insert(downloadedQuestIDs, k)
            end
        end
    else
        for k, v in pairs(QuestIDAndModuleNameMap) do
            if bSupportLitePack == false or isIgnorePlatform == true or self:IsDownloadedByQuestID(k) then
                table.insert(downloadedQuestIDs, k)
            end
        end
    end
    --- END MODIFICATION

    return downloadedQuestIDs
end

--BEGIN VIRTUOS MODE @Zhang Yingqi & PengBofeng
function LiteDownloadManager:CheckModuleNameInPlayGoInitialChunk(name)
    if name == nil then
        return true
    end
    local InitializeMapArray = {"MapCrackedOnly"}
    local start, finish = string.find(name, "Map")
    if start == 1 then
        if table.contains(InitializeMapArray, name) then
            return true
        else
            return false
        end
    else 
        return true
    end
end

function LiteDownloadManager:CheckMapInPlayGoInitialChunk(mapid)
    if self:IsPlayGoFinished() then
        return true
    end
    local moduleName = self:GetModuleNameByMapId(mapid)

    if moduleName == nil then
        local InitializeMapIds = { 2201 , 2202 } --空ModuleName的Map只允许白名单
        return table.contains(InitializeMapIds, mapid)
    else
        return self:CheckModuleNameInPlayGoInitialChunk(moduleName)
    end
end

function LiteDownloadManager:IsPlayGoFinished()
    return DFMInitialChunkManager:IsPlatformAllChunksDownloaded() 
end

function LiteDownloadManager:AddPlatformChunksDownloadFinishedEvent()
    DFMInitialChunkManager.OnPlatformChunksDownloadCompleteDelegate:Add(self.OnPlatformChunksDownloadFinished, self)
end

function LiteDownloadManager:OnPlatformChunksDownloadFinished()
    loginfo("[LiteDownloadManager] OnPlatformChunksDownloadFinished")
    self:ResetAllDownloadedQuestIDs()
    if Server.TeamServer:IsInTeam() then --资源下载完成，上报服务器
        Server.TeamServer:ReqUpdatePackQuest(nil,TeamUpdatePackQuestType.TeamUpdatePackQuestFinish,nil)
    end
end
--END VIRTUOS MODE

function LiteDownloadManager:GetModuleNameByMapId(mapid)
    for k, v in pairs(MapIDToModuleNameMap) do
        if k == tostring(mapid) then
            if self:IsHDDownloadStatus() then
                if tModuleName2HDRuntimeName[v] ~= nil then
                    loginfo("[LiteDownloadManager] GetModuleNameByMapId:", mapid, ", hd collection modulename:", tModuleName2HDRuntimeName[v])
                    return tModuleName2HDRuntimeName[v]
                end
            end
            
            loginfo("[LiteDownloadManager] GetModuleNameByMapId:", mapid, ", modulename:", v)
            return v
        end
    end

    loginfo("[LiteDownloadManager] GetModuleNameByMapId:", mapid, ", MapIDToModuleNameMap has no content.")
    return nil
end

function LiteDownloadManager:RegisterRuntimeMultiModule(multiModuleName, files, isReSet)
    litePackageMgr:RegisterRuntimeMultiModule(multiModuleName, files, isReSet)
end

function LiteDownloadManager:RegisterRuntimeMultiModuleByChildModule(multiModuleName, childModules, isReSet)
    if self.runtimeModuleInfos == nil then
        self.runtimeModuleInfos = {}
    end

    if table.contains(self.runtimeModuleInfos, multiModuleName) == false then
        self.runtimeModuleInfos[multiModuleName] = {}
    end

    for index, childModule in ipairs(childModules) do
        table.insert(self.runtimeModuleInfos[multiModuleName], childModule)
        loginfo("[LiteDownloadManager] RegisterRuntimeMultiModuleByChildModule multiModuleName:", multiModuleName, ", childModule:", childModule)
    end

    litePackageMgr:RegisterRuntimeMultiModuleByChildModule(multiModuleName, childModules, isReSet)
end


function LiteDownloadManager:GetRegisterRuntimeChildModules(multiModuleName)
    local retChildModules = nil
    if self.runtimeModuleInfos == nil then
        self.runtimeModuleInfos = {}
    end

    if self.runtimeModuleInfos[multiModuleName] ~= nil then
        retChildModules = self.runtimeModuleInfos[multiModuleName]
    else
        loginfo("[LiteDownloadManager] GetRegisterRuntimeChildModules multiModuleName not contains:", multiModuleName)
    end

    return retChildModules
end

function LiteDownloadManager:CheckReviewVersion(moduleName)
    if PLATFORM_IOS or IsInEditor() then
        local bisInReview = UGameVersionUtils.IsInReview()
        local bIsShipping = UGameVersionUtils.IsShipping()
        if bisInReview and bIsShipping then
            --shipping and in review
            if table.contains(dictMapModule, moduleName) then
                local downloadBeforeLoginMoudle = "BeforeLogin"
                local bCommonResReady = self:IsDownloadedByModuleName(downloadBeforeLoginMoudle)
                if bCommonResReady == false then
                    loginfo("[LiteDownloadManager] LiteDownloadManager:CheckReviewVersion, register common res.")
                    self:RegisterRuntimeMultiModuleByChildModule(moduleName, {moduleName, downloadBeforeLoginMoudle}, false)
                end
            end
        end
    end
end


function LiteDownloadManager:CheckBaseModuleState(moduleName)
    local notSpecialModule = self:CheckModuleNameNotRumtimeSpecial(moduleName)
    if notSpecialModule == true then
        local modulestate = self:GetModuleStateByModuleName(BaseModule)
        local modulestate_SOL = self:GetModuleStateByModuleName(BaseModule_SOL)
        local modulestate_MP = self:GetModuleStateByModuleName(BaseModule_MP)
        if modulestate ~= 1 or modulestate_SOL ~= 1 or modulestate_MP ~= 1 then
            local needDownloadModule = {BaseModule, BaseModule_SOL, BaseModule_MP}
            if moduleName ~= "" then
                table.insert(needDownloadModule, moduleName)
            end

            logerror("[LiteDownloadManager] CheckBaseModuleState download BaseModule:"..BaseModule)
            self:CheckAndDownloadAll(needDownloadModule, EDownloadTrigger.None, EDownloadStyle.Auto)
            return false
        end
    end

    return true
end

function LiteDownloadManager:DownloadByModuleName(moduleName, bUseCombined, downloadTrigger, downloadStyle)
    local bHasBeenDownload = self:CheckBaseModuleState(moduleName)
    if bHasBeenDownload == false then
        logerror("[LiteDownloadManager] DownloadByModuleName  CheckBaseModuleState = false")
        return
    end

    if bUseCombined == nil then
        bUseCombined = true
    end

    if downloadTrigger == nil then
        downloadTrigger = EDownloadTrigger.None
    end
    if downloadStyle == nil then
        downloadStyle = EDownloadStyle.Manual
    end

    if moduleName ~= nil then
        if self:IsDownloadedByModuleName(moduleName) == true then
            return
        end

        if curdownlaodQuest ~= nil and curdownlaodQuest.ModuleName == moduleName then
            loginfo("module " .. moduleName .. " already downloading")
            return
        end

        local quest = LiteDownloadQuest:New(self, moduleName)
        quest:SetQuestCombine(bUseCombined)
        quest:SetDownloadTriggle(downloadTrigger)
        quest:SetDownloadStyle(downloadStyle)

        if curdownlaodQuest ~= nil then
            table.insert(downloadQuestQueueNew, 1, quest)

            local questNow = LiteDownloadQuest:New(self, curdownlaodQuest.ModuleName)
            questNow:SetDownloadTriggle(curdownlaodQuest.DownloadTrigger)
            questNow:SetDownloadStyle(curdownlaodQuest.DownloadStyle)
            questNow:SetQuestCombine(curdownlaodQuest.OpenCombineToggle)
            table.insert(downloadQuestQueueNew, 2, questNow)
            curdownlaodQuest:SetStop()
            -- litePackageMgr:StopDownload(curdownlaodQuestName)
            ModuleDownloadState[curdownlaodQuest.ModuleName] = 2
            litePackageMgr:StopAll()
            loginfo("[LiteDownloadManager] drmdbg :curdownlaodQuestName ~= nil stopmodulename:" .. curdownlaodQuest.ModuleName)
        else
            curdownlaodQuest = quest
            curdownlaodQuest:SetOldVersionPakNames(self.dictLastVersionPaks)
            curdownlaodQuest:SetPatchPakNames(self.dickCombineMainfestPaks)
            curdownlaodQuest:SetCombinePath(self.combinePath)
            curdownlaodQuest:MoveNext()

            self:CheckModuleSizeInfo(moduleName)
            litePackageMgr:StartDownload(moduleName)
            ModuleDownloadState[moduleName] = 1
            loginfo("[LiteDownloadManager] drmdbg :DownloadCombineByModuleName:" .. moduleName)

            self:DoReportBeginDownloadEvent(curdownlaodQuest)
        end
    end
end


function LiteDownloadManager:CheckModuleSizeInfo(moduleName)
    if moduleName ~= nil then
        local isIn = false
        for key, value in pairs(ModuleNowSize) do
            if key == moduleName then
                isIn = true
                break
            end
        end

        if isIn == false then
            ModuleNowSize[moduleName] = 0
            loginfo("[LiteDownloadManager] CheckModuleSizeInfo ModuleNowSize 0 moduleName:"..moduleName)

            ModuleTotalSize[moduleName] = 0
            loginfo("[LiteDownloadManager] CheckModuleSizeInfo ModuleTotalSize 0 moduleName:"..moduleName)
        end
    end
end

function LiteDownloadManager:DownloadForceByModuleName(moduleName, bforce)
    if moduleName ~= nil then
        self:CheckModuleSizeInfo(moduleName)
        litePackageMgr:StartDownloadForce(moduleName, bforce)
        ModuleDownloadState[moduleName] = 1
    end
end

function LiteDownloadManager:DownloadLazyByModuleName(moduleName)

end

function LiteDownloadManager:CancelByModuleName(moduleName)
    if moduleName ~= nil then
        if curdownlaodQuest ~= nil and moduleName == curdownlaodQuest.ModuleName then
            -- litePackageMgr:StopDownload(moduleName)
            curdownlaodQuest:SetStop()
            litePackageMgr:StopAll()
            -- curdownlaodQuestName = nil
            ModuleDownloadState[moduleName] = 0
        else
            local hasQuest = -1
            for i = 1, #downloadQuestQueueNew do
                if downloadQuestQueueNew[i].ModuleName == moduleName then
                    hasQuest = i
                end
            end

            if hasQuest > 0 then
                table.remove(downloadQuestQueueNew, hasQuest)
            end

            ModuleDownloadState[moduleName] = 0
        end
    end
end

function LiteDownloadManager:DownloadByQuestID(questID)
    local moduleName = QuestIDAndModuleNameMap[questID]
    if moduleName ~= nil then
        if self:IsDownloadedByQuestID(questID) == false then
            litePackageMgr:StartDownload(moduleName)
            ModuleDownloadState[moduleName] = 1
        end
    end
end

function LiteDownloadManager:CancelByQuestID(questID)
    local moduleName = QuestIDAndModuleNameMap[questID]
    if moduleName ~= nil then
        -- litePackageMgr:StopDownload(moduleName)
        litePackageMgr:StopAll()
        ModuleDownloadState[moduleName] = 0
    end
end

function LiteDownloadManager:CancelAll()
    if curdownlaodQuest ~= nil then
        curdownlaodQuest:SetStop()
        ModuleDownloadState[curdownlaodQuest.ModuleName] = 0
    end

    for k, v in pairs(ModuleDownloadState) do
        ModuleDownloadState[k] = 0
    end

    table.empty(downloadQuestQueueNew)
    litePackageMgr:StopAll()
    curdownlaodQuest = nil
end

function LiteDownloadManager:ClearQuestToDownload()
    self.CachModuleToDownload = nil
end

function LiteDownloadManager:CachQuestToDownload()
    if self.CachModuleToDownload ~= nil and #self.CachModuleToDownload > 0 then
        return
    end

    self.CachModuleToDownload = {}

    if curdownlaodQuest ~= nil then
        if curdownlaodQuest.DownloadStyle == EDownloadStyle.Manual then
            table.insert(self.CachModuleToDownload, curdownlaodQuest.ModuleName)
            logerror("[LiteDownloadManager] CachQuestToDownload, add curdownlaodQuest, moduleName:"..curdownlaodQuest.ModuleName)

        end
    end

    if downloadQuestQueueNew ~= nil and #downloadQuestQueueNew>0 then
        for key, quest in pairs(downloadQuestQueueNew) do
            if quest.DownloadStyle == EDownloadStyle.Manual then
                table.insert(self.CachModuleToDownload, quest.ModuleName)
                logerror("[LiteDownloadManager] CachQuestToDownload, add moduleName:"..quest.ModuleName)
            end
        end
    end
end

function LiteDownloadManager:CheckWIFIDownloadQuests(trigger)
    local need_download_modules = {}
    if self.CachModuleToDownload ~= nil and #self.CachModuleToDownload > 0 then
        for key, cachModuleName in pairs(self.CachModuleToDownload) do
            table.insert(need_download_modules, cachModuleName)
            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests, add CachModuleToDownload, moduleName:"..cachModuleName)
        end

        -- self.CachModuleToDownload = nil
    end

    local bDownloadType = LitePackageLogic.GetDownloadType()
    if bDownloadType ~= 3 then
        local bUseWifiDownload = true
        local openWifiDownloadByLauncher = UGameVersionUtils.GetLauncherParamsByKey("openWIFIDownload")
        if openWifiDownloadByLauncher == "yes" then
            loginfo("[LiteDownloadManager] CheckWIFIDownloadQuests , openWifiDownloadByLauncher.")
        elseif openWifiDownloadByLauncher == "no" then
            bUseWifiDownload = false
        else
            local bIsShipping = UGameVersionUtils.IsShipping()
            -- close wifi download in test
            if bIsShipping == false then
                -- bUseWifiDownload = false
            end
        end

        local netState = self:GetNetworkState()
        -- check wifi
        if netState ~= 1 then
            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , not wifi.")
            bUseWifiDownload = false
        end

        local freeSpace = litePackageMgr:GetDeviceFreeSpace()
        local spaceGB = freeSpace / LiteDownloadGBNum
        if spaceGB < WIFI_DOWNLOAD_SPACE_GB then
            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , freespace check failed. spaceGB:"..spaceGB)
            bUseWifiDownload = false
        end

        if LITE_APMLevel == -1 then
            UDFMGameGPM.Get(GetWorld())
            if PLATFORM_IOS then
                LITE_APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
            elseif PLATFORM_ANDROID then
                LITE_APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
            else
                LITE_APMLevel = 4
            end
        end

        logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , LITE_APMLevel:"..LITE_APMLevel)

        if LITE_APMLevel <= LITE_HD_WIFI_LOBBY_DOWNLOAD_MODEL_LEVEL then
            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , LITE_APMLevel >= LITE_HD_WIFI_LOBBY_DOWNLOAD_MODEL_LEVEL.")
            bUseWifiDownload = false
        end

        if bUseWifiDownload then
            local needDownloadRoleID = 0
            local nowUseDownloadTable = nil

            if trigger == 1 then
                nowUseDownloadTable = self.SortedDownloadTable
                needDownloadRoleID = tonumber(Server.HeroServer:GetCurUsedHeroFashionId())
                logerror("[LiteDownloadManager] CheckWIFIDownloadQuests use sol, needDownloadRoleID:"..needDownloadRoleID)
            else
                nowUseDownloadTable = self.SortedInMPDownloadTable
                needDownloadRoleID = tonumber(Server.HeroServer:GetCurUsedHeroFashionId())
                logerror("[LiteDownloadManager] CheckWIFIDownloadQuests use mp, needDownloadRoleID:"..needDownloadRoleID)
            end

            local nowNowSize = 0
            local nowTotalSize = 0
            for k, v in pairs(nowUseDownloadTable) do
                if v.TriggerStyle == WIFI_DOWNLOAD_TRIGGER_STYLE then
                    local bDownloaded = false
                    local moduleName = v.ModuleKey
                    if self:IsHDDownloadStatus() then
                        moduleName = self:GetHDRuntimeNameByModuleName(v.ModuleKey)
                        bDownloaded = self:IsDownloadedByModuleName(moduleName)
                    else
                        bDownloaded = self:IsDownloadedByModuleName(v.ModuleKey)
                    end
                    local bIsShow = self:IsModuleShow(v.ModuleKey)

                    if v.ModuleKey == MPModuleName or  v.ModuleKey == SOLModuleName then
                        bIsShow = true
                    end

                    local bTrigger = v.AutoDownloadTrigger ~= nil and (v.AutoDownloadTrigger == 0 or v.AutoDownloadTrigger == trigger)
                    logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , check modulename:"..v.ModuleKey..", v.AutoDownloadTrigge:"..tostring(v.AutoDownloadTrigger))

                    if bDownloaded == false and bIsShow and bTrigger then
                        -- check space > 3G
                        local nowSize = self:GetNowSizeByModuleName(moduleName)
                        local totalSize = self:GetTotalSizeByModuleName(moduleName)
                        nowNowSize = nowNowSize + nowSize
                        nowTotalSize = nowTotalSize + totalSize
                        local questSize = (nowTotalSize - nowNowSize) / LiteDownloadGBNum
                        if spaceGB > WIFI_DOWNLOAD_SPACE_GB + questSize then
                            table.insert(need_download_modules, moduleName)
                            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , check modulename:"..v.ModuleKey)
                        else
                            logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , space not enough")
                        end
                    end
                end
            end

            if IsBuildRegionCN() then
                if needDownloadRoleID ~= nil and needDownloadRoleID > 0 then
                    local roleModuleName = self:GetDownloadCategary(needDownloadRoleID)
                    logerror("[LiteDownloadManager] CheckWIFIDownloadQuests add roleModuleName:"..roleModuleName)
                    table.insert(need_download_modules, 1, roleModuleName)
                end
            end

            local bNeedAutoDownloadHD = Facade.ConfigManager:GetBoolean("AutoBeginDownloadHD", false)
            if bNeedAutoDownloadHD then
                logerror("[LiteDownloadManager] CheckWIFIDownloadQuests bNeedAutoDownloadHD:"..sHDDownloadCollection)
                table.insert(need_download_modules, 1, sHDDownloadCollection)
            end
        end
    end

    local needDownloadCount = #need_download_modules
    logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , need_download_modules.count:"..needDownloadCount)
    -- if self:GetCurrDownloadModuleName() ~= nil and self:GetCurrDownloadModuleName() == self:GetHDCollectionModuleName() then
    --     logerror("[LiteDownloadManager] CheckWIFIDownloadQuests , download the HD collection already, don't download auto by wifi")
    --     return
    -- end

    if needDownloadCount > 0 then
        -- local curdownlaodQuestName = nil
        -- if curdownlaodQuest ~= nil then
        --     curdownlaodQuestName = curdownlaodQuest.ModuleName
        -- end

        -- if curdownlaodQuestName ~= nil then
        --     table.insert(need_download_modules, 1, curdownlaodQuestName)
        -- end

        self:CheckAndDownloadAll(need_download_modules, EDownloadTrigger.WIFTAuto, EDownloadStyle.Auto)
    end
end

function LiteDownloadManager:CheckAndDownloadAll(moduleNames, downloadTrigger, downloadStyle)
    if moduleNames == nil or #moduleNames <= 0 then
        return
    end

    if downloadTrigger == nil then
        downloadTrigger = EDownloadTrigger.None
    end
    if downloadStyle == nil then
        downloadStyle = EDownloadStyle.Manual
    end

    table.empty(downloadQuestQueueNew)

    if curdownlaodQuest ~= nil then
        curdownlaodQuestName = curdownlaodQuest.ModuleName
    else
        curdownlaodQuestName = nil
    end

    for k, v in pairs(ModuleDownloadState) do
        if k ~= curdownlaodQuestName then
            ModuleDownloadState[k] = 0
        end
    end

    local modulestate = self:GetModuleStateByModuleName(BaseModule)
    if modulestate ~= 1 then
        table.insert(moduleNames, 1, BaseModule)
        logerror("[LiteDownloadManager] CheckAndDownloadAll add BaseModule:"..BaseModule)
    end

    local modulestate_SOL = self:GetModuleStateByModuleName(BaseModule_SOL)
    if modulestate_SOL ~= 1 then
        table.insert(moduleNames, 1, BaseModule_SOL)
        logerror("[LiteDownloadManager] CheckAndDownloadAll add BaseModule_SOL")
    end

    local modulestate_MP = self:GetModuleStateByModuleName(BaseModule_MP)
    if modulestate_MP ~= 1 then
        table.insert(moduleNames, 1, BaseModule_MP)
        logerror("[LiteDownloadManager] CheckAndDownloadAll add BaseModule_MP")
    end

    for k, v in pairs(moduleNames) do
        if v ~= curdownlaodQuestName then
            ModuleDownloadState[v] = 2
            local quest = LiteDownloadQuest:New(self, v)
            quest:SetQuestCombine(true)
            quest:SetDownloadTriggle(downloadTrigger)
            quest:SetDownloadStyle(downloadStyle)
            table.insert(downloadQuestQueueNew, quest)
        end
    end

    local now_moduleName = moduleNames[1]

    if curdownlaodQuestName == nil then
        self:RunQuestCheck()
    else
        if now_moduleName ~= curdownlaodQuestName then
            litePackageMgr:StopAll()
        end
    end
end

function LiteDownloadManager:IsDownloadedByQuestID(questID)

    --BEGIN Virtuos Modification @ Peng Bofeng
    --Add PS PlayGo Filter
    if IsPS5() then
        if self:IsPlayGoFinished() then
            return true
        else
            local questName = QuestIDAndModuleNameMap[questID]
            return self:CheckModuleNameInPlayGoInitialChunk(questName)
        end
    end
    --END Virtuos Modification


    if DFHD_LUA == 1 or self:IsSupportLitePackage() == false or self:IsMobileFullPackage() == true then
        if self:IsSimulateLitePack() == false then
            return true
        end
    end

    local moduleName = QuestIDAndModuleNameMap[questID]
    for k, v in pairs(ModuleDownloaded) do
        if k == moduleName then
            return v
        end
    end

    
    return false
end

function LiteDownloadManager:IsDownloadingByQuestID(questID)
    local moduleName = QuestIDAndModuleNameMap[questID]
    return ModuleDownloadState[moduleName] == 1
end

function LiteDownloadManager:IsWaitingByQuestID(questID)
    local moduleName = QuestIDAndModuleNameMap[questID]
    return ModuleDownloadState[moduleName] == 2
end

function LiteDownloadManager:IsDownloadedByModuleName(moduleName)
    -- BEGIN Virtuos Modification
    if IsPS5() then
        if self:IsPlayGoFinished() then
            return true
        else
            return self:CheckModuleNameInPlayGoInitialChunk(moduleName);
        end
    end
    -- END Modification

    if DFHD_LUA == 1 or self:IsSupportLitePackage() == false or self:IsMobileFullPackage() == true then
        if self:IsSimulateLitePack() == false then
            return true
        end
    end

    if moduleName == nil then
        return true
    end

    for k, v in pairs(ModuleDownloaded) do
        if k == moduleName then
            return v
        end
    end

    -- local modulestate = self:GetModuleStateByModuleName(moduleName)
    -- if tonumber(modulestate) == 1 then
    --     return true
    -- elseif modulestate == 0 then
    --     self:CheckModuleAsyncWithQueue(moduleName)
    -- end

    -- return false


    if string.sub(moduleName, 1, 7) == "Runtime" then
        local bModuleDownloaded = false
        local childModules = self:GetRegisterRuntimeChildModules(moduleName)
        if childModules ~= nil then
            local allChildModuleDownloaded = true
            for index, childModule in ipairs(childModules) do
                local childModuleState = self:GetModuleStateByModuleName(childModule)
                if childModuleState ~= 1 then
                    allChildModuleDownloaded = false
                end
            end

            if allChildModuleDownloaded == true then
                bModuleDownloaded = true
            end
        end

        return bModuleDownloaded
    else
        local modulestate = self:GetModuleStateByModuleName(moduleName)
        if tonumber(modulestate) == 1 then
            return true
        elseif modulestate == 0 then
            self:CheckModuleAsyncWithQueue(moduleName)
        end
    end

    return false
end

function LiteDownloadManager:IsDownloadingByModuleName(moduleName)
    return ModuleDownloadState[moduleName] == 1
end

function LiteDownloadManager:IsWaitingByModuleName(moduleName)
    return ModuleDownloadState[moduleName] == 2
end

function LiteDownloadManager:IsFinishedByModuleName(moduleName)
    return ModuleDownloadState[moduleName] == 3
end

function LiteDownloadManager:IsQuestDownloading()
    if curdownlaodQuest ~= nil then
        return true
    end

    return false
end

function LiteDownloadManager:SetCachNowSize(moduleName)
    local nowSize = self:GetNowSizeByModuleName(moduleName)

    if self.lastUpdateCatchSize ~= moduleName then
        self.lastUpdateCatchSize = moduleName
        ModuleLastCachSize[moduleName] = nowSize
    end
end

function LiteDownloadManager:GetCachNowSize(moduleName)
    local nowSize = ModuleLastCachSize[moduleName]
    if nowSize == nil then
        nowSize = 0
    end
    return nowSize
end

function LiteDownloadManager:GetPatchPakNameByPakIndex(PakIndex)
    local ret = nil

    if self.dickCombineMainfestPaks ~= nil then
        for indexNeedCombine, pakNameNeedCombine in pairs(self.dickCombineMainfestPaks) do
            local pakNeedCombineIndex = pakNameNeedCombine:match("pakchunk%d+optional?") or pakNameNeedCombine:match("pakchunk%d+")
            if PakIndex == pakNeedCombineIndex then
                ret = self.combinePath.."/"..pakNameNeedCombine
                loginfo("[LiteDownloadManager] GetPatchPakNameByPakIndex ret:" .. ret)
                break
            end
        end
    end

    return ret
end

function LiteDownloadManager:GetOldVersionPakNameByPakIndex(PakIndex)
    local ret = nil

    if self.pufferPath == nil then
        local bIsShipping = UGameVersionUtils.IsShipping()
        self.pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    end

    if self.lastVersionAllPaks ~= nil then
        for indexNeedCombine, pakNameNeedCombine in pairs(self.lastVersionAllPaks) do
            local pakNeedCombineIndex = pakNameNeedCombine:match("pakchunk%d+optional?") or pakNameNeedCombine:match("pakchunk%d+")
            if PakIndex == pakNeedCombineIndex then
                local versionPakPath = self.pufferPath  .. "/" .. pakNameNeedCombine
                local bFileExist = ULuaExtension.Ext_FileExists(versionPakPath)
                if bFileExist then
                    ret = pakNameNeedCombine
                    loginfo("[LiteDownloadManager] GetOldVersionPakNameByPakIndex pakname:" .. pakNameNeedCombine .. " bFileExist: true")
                else
                    loginfo("[LiteDownloadManager] GetOldVersionPakNameByPakIndex pakname:" .. pakNameNeedCombine .. " bFileExist: false")
                end
                break
            end
        end
    end

    return ret
end

function LiteDownloadManager:GetRealSizeToDownloadByModuleName(moduleName)
    local pakNames = self.DictRealSizeCalcModuleInfo[moduleName]
    local allNowSize = 0
    local allTotalSize = 0

    if pakNames ~= nil then
        for index = 1, #pakNames do
            local pakName = pakNames[index]

            local bfileReady, nowSize, totalSize = litePackageMgr:GetFileStateAndSize(pakName)
            allTotalSize = allTotalSize + totalSize

            local bisInCombined = self:CheckPakNameIsInCombined(pakName)
            if bisInCombined == true or bfileReady == true then
                allNowSize = allNowSize + totalSize
            else
                allNowSize = allNowSize + nowSize
            end
        end
    end

    local ret = 0
    if allTotalSize > allNowSize then
        local newRet = allTotalSize - allNowSize
        if self.DictRealTotalSize ~= nil then
            ret = self.DictRealTotalSize[moduleName]
            if newRet < ret then
                self.DictRealTotalSize[moduleName] = newRet
                ret = newRet
            end
        end
    end

    return ret
end

function LiteDownloadManager:GetRealSizeTotalByModuleName(moduleName)
    if self.DictRealSizeCalcModuleInfo == nil then
        self.DictRealSizeCalcModuleInfo = {}
        self.DictRealTotalSize = {}
    end

    if self.DictRealSizeCalcModuleInfo[moduleName] == nil then
        self.DictRealSizeCalcModuleInfo[moduleName] = {}
        local pakNames = litePackageMgr:GetPakNamesByModuleName(moduleName, true)

        local allNowSize = 0
        local allTotalSize = 0

        if pakNames ~= nil then
            for index = 1, #pakNames do
                local pakName = pakNames[index]
                local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")
                loginfo("[LiteDownloadManager] GetRealSizeTotalByModuleName pakIndex:" .. tostring(pakIndex))
                local oldPakName = self:GetOldVersionPakNameByPakIndex(pakIndex)
                local patchPakName = self:GetPatchPakNameByPakIndex(pakIndex)
                if oldPakName ~= nil and patchPakName ~= nil then
                    loginfo("[LiteDownloadManager] GetRealSizeTotalByModuleName pakName:" .. pakName.." , replace to:"..patchPakName)
                    pakName = patchPakName
                end

                local bfileReady, nowSize, totalSize = litePackageMgr:GetFileStateAndSize(pakName)
                allTotalSize = allTotalSize + totalSize

                local bisInCombined = self:CheckPakNameIsInCombined(pakName)
                if bisInCombined == true or bfileReady == true then
                    allNowSize = allNowSize + totalSize
                else
                    allNowSize = allNowSize + nowSize
                end

                if table.contains(self.DictRealSizeCalcModuleInfo[moduleName], pakName) then
                    logerror("[LiteDownloadManager] GetRealSizeTotalByModuleName contains pakName:"..pakName)
                else
                    table.insert(self.DictRealSizeCalcModuleInfo[moduleName], pakName)
                end
            end

            if allTotalSize > allNowSize then
                self.DictRealTotalSize[moduleName] = allTotalSize - allNowSize
            end
        end
    end

    local realTotalSize = self.DictRealTotalSize[moduleName]
    if realTotalSize == nil then
        return self:GetTotalSizeByModuleName(moduleName)
    end

    return realTotalSize
end

function LiteDownloadManager:GetNowSizeByModuleName(moduleName)
    local nowSize = ModuleNowSize[moduleName]
    if nowSize == nil then
        nowSize = 0
    end
    return nowSize
end

function LiteDownloadManager:GetTotalSizeByModuleName(moduleName)
    local totalSize = ModuleTotalSize[moduleName]
    if totalSize == nil then
        totalSize = 0
    end

    return totalSize
end

function LiteDownloadManager:GetRemainderSizeByModuleName(moduleName)
    local remainderSize= self:GetTotalSizeByModuleName(moduleName)-self:GetNowSizeByModuleName(moduleName)
    remainderSize=remainderSize>=0 and remainderSize or 0
    return remainderSize
end

function LiteDownloadManager:GetMultiModuleRemainderSize(moduleNames)
    if moduleNames == nil or #moduleNames<=0 then
        return 0
    end

    local remainderSize = 0
    local allPaks = {}
    for key, moduleName in pairs(moduleNames) do
        local pakNames = self:GetPakNamesByModuleName(moduleName ,true)
        for pakIndex, pakName in pairs(pakNames) do
            if table.contains(allPaks, pakName) == false then
                local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(pakName)
                if fileState == false then
                    table.insert(allPaks, pakName)
                    remainderSize = remainderSize + totalSize - nowSize
                end
            else
                logerror("[LiteDownloadManager] GetMultiModuleRemainderSize pak already clac, ignore:"..pakName)
            end
        end
    end

    return remainderSize
end

function LiteDownloadManager:GetDownlaodSpeed()
    return litePackageMgr:GetDownloadSpeed()
end

function LiteDownloadManager:GetDownloadProgressInfoByModuleName(moduleName)
    local nowSize = ModuleNowSize[moduleName]
    local totalSize = ModuleTotalSize[moduleName]
    local downloadInfo = DownloadInfoMap[moduleName]
    if downloadInfo == nil then
        DownloadInfoMap[moduleName] = {}
        DownloadInfoMap[moduleName]["FormatSize"] = "- / -MB"
        DownloadInfoMap[moduleName]["PercentValue"] = 0.0
        -- DownloadInfoMap[moduleName]["FormatSizeWidthoutPercent"] = "- / -MB"
        DownloadInfoMap[moduleName]["FormatNowSize"] = "-MB"
        DownloadInfoMap[moduleName]["FormatTotal2SubSize"] = "-MB"

        downloadInfo = DownloadInfoMap[moduleName]
    end

    if totalSize ~= nil and totalSize > 0 then
        local percent = math.round(nowSize / totalSize * 1000) / 10
        local showStr = string.format("%.1f / %.1fMB (%.1f%%)", nowSize / LiteDownloadMBNum,
            totalSize / LiteDownloadMBNum, percent)

        downloadInfo["FormatSize"] = showStr
        downloadInfo["PercentValue"] = percent
        -- downloadInfo["FormatSizeWidthoutPercent"] = string.format("%.1f / %.1fMB", nowSize / LiteDownloadMBNum, totalSize / LiteDownloadMBNum)
        downloadInfo["FormatNowSize"] = string.format("%.1fMB", nowSize / LiteDownloadMBNum)
        downloadInfo["FormatTotal2SubSize"] = string.format("%.1fMB", (totalSize - nowSize) / LiteDownloadMBNum)
    end

    return downloadInfo
end

function LiteDownloadManager:ISQuestsHasBeenChecked()
    return bHasbeenCheckedQuest
end

function LiteDownloadManager:GetDownlaodedProgress()
    local percent = -1
    if bHasbeenCheckedQuest == true then
        local allTotalSize = 0
        for k, v in pairs(ModuleTotalSize) do
            allTotalSize = allTotalSize + v
        end

        local allNowSize = 0
        for k, v in pairs(ModuleNowSize) do
            allNowSize = allNowSize + v
        end

        if allTotalSize > 0 then
            percent = allNowSize / allTotalSize

        end
    end

    return percent
end

function LiteDownloadManager:ReportDLCUpdateEvent(totalSize, nowSize, progress, startTime, endTime, speedStartTime,
                                                  speedEndTime, traffic, result, reason)
    litePackageMgr:ReportDLCUpdateEvent(totalSize, nowSize, progress, startTime, endTime, speedStartTime, speedEndTime,
        traffic, result, reason)
end

function LiteDownloadManager:SetReportServerFlag(bFlag)
    self.bFlag = bFlag
end


------ private logic --------

function LiteDownloadManager:ResetAllDownloadedQuestIDs()
    if self.bFlag == true or DFHD_LUA then
        local ids = self:GetDownloadedQuestIDs()
        if Server.LitePackServer ~= nil and Server.LitePackServer.bValid == true then
            Server.LitePackServer:ResetAllDownloadedQuestIDs(ids)
        end
    end
end

function LiteDownloadManager:ResetAllDownloadedQuestIDsWithNoServer()
    local ids = self:GetDownloadedQuestIDs()
    litePackageMgr:ResetDownloadedQuestIDs(ids)
end

function LiteDownloadManager:RunQuestCheck()
    loginfo("[LiteDownloadManager]:RunQuestCheck")
    if curdownlaodQuest == nil then
        if #downloadQuestQueueNew > 0 then
            local quest = downloadQuestQueueNew[1]
            table.remove(downloadQuestQueueNew, 1)
            quest:SetOldVersionPakNames(self.dictLastVersionPaks)
            quest:SetPatchPakNames(self.dickCombineMainfestPaks)
            quest:SetCombinePath(self.combinePath)
            quest:MoveNext()
            self:DownloadQuest(quest)
        end
    else
        curdownlaodQuest:SetOldVersionPakNames(self.dictLastVersionPaks)
        curdownlaodQuest:SetPatchPakNames(self.dickCombineMainfestPaks)
        curdownlaodQuest:SetCombinePath(self.combinePath)
        local moveNextRet = curdownlaodQuest:MoveNext()
        if moveNextRet == 1 then
            self:DownloadQuest(curdownlaodQuest)
        else
            local moduleName = curdownlaodQuest.ModuleName
            --update quest download state
            ModuleDownloadState[moduleName] = 0
            curdownlaodQuest = nil
        end

        if curdownlaodQuest == nil then
            if #downloadQuestQueueNew > 0 then
                local quest = downloadQuestQueueNew[1]
                table.remove(downloadQuestQueueNew, 1)
                quest:SetOldVersionPakNames(self.dictLastVersionPaks)
                quest:SetPatchPakNames(self.dickCombineMainfestPaks)
                quest:SetCombinePath(self.combinePath)
                quest:MoveNext()
                self:DownloadQuest(quest)
            end
        end
    end
end

function LiteDownloadManager:DownloadQuest(quest)
    loginfo("[LiteDownloadManager]:DownloadQuest")
    if quest ~= nil then
        curdownlaodQuest = quest
        local moduleName = quest.ModuleName
        curdownlaodQuestName = moduleName
        self:CheckModuleSizeInfo(moduleName)
        litePackageMgr:StartDownload(moduleName)
        ModuleDownloadState[moduleName] = 1

        self:DoReportBeginDownloadEvent(curdownlaodQuest)
    end
end

function LiteDownloadManager:CheckLocalSize(moduleName)
    -- check local file size
    if self.bResetPakFileSizeAndMD5 ~= true then
        self:ResetPaksFileSizeAndMD5()
        self.bResetPakFileSizeAndMD5 = true
    end

    if self.bResetLocalModuleInfo ~= true then
        self:ResetModulePakNames()
        self.bResetLocalModuleInfo = true
    end

    local bAllFileReady = true
    local totalSizeLocal = 0
    local readyTotalSizeLocal = 0

    local modulePaks = LocalModuleAndPakName[moduleName]
    if modulePaks ~= nil then
        local bIsShipping = UGameVersionUtils.IsShipping()
        local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
        for key, value in pairs(modulePaks) do
            local readySize = LocalPakNameFileSize[value] or 0
            readyTotalSizeLocal = readyTotalSizeLocal + readySize

            local filePath = pufferPath.."//"..value
            local bZipExists = ULuaExtension.Ext_FileExists(filePath)
            if bZipExists == false then
                bAllFileReady = false
            else
                local fileSize = UFlibPakHelper.GetExtenFileSize(filePath)
                -- if fileSize ~= readySize then
                --     bAllFileReady = false
                -- end
                totalSizeLocal = totalSizeLocal + fileSize
            end
        end
    end

    return bAllFileReady, totalSizeLocal, readyTotalSizeLocal
end
------ LitePackageWrapper call --------------
function LiteDownloadManager:OnLiteDownloadProgress(moduleName, nowSize, totalSize)
    if curdownlaodQuest ~= nil and moduleName == curdownlaodQuest.ModuleName then
        curdownlaodQuest:SetDownloadProgress(moduleName, nowSize, totalSize)
        local bisAllready, fixedNowSize, fixedTotalSize = curdownlaodQuest:GetQuestStateSize()
        totalSize = fixedTotalSize
        nowSize = fixedNowSize

        if curdownlaodQuest.OpenCombine == false then
            self:SetCachNowSize(moduleName) -- update cachsize
        end
    end

    if table.contains(ModuleNowSize, moduleName) == false then
        ModuleNowSize[moduleName] = nowSize
    else
        if nowSize > ModuleNowSize[moduleName] then
            ModuleNowSize[moduleName] = nowSize
        end
    end
    ModuleTotalSize[moduleName] = totalSize

    -- loginfo("[LiteDownloadManager] drmdbg OnLiteDownloadProgress, moduleName", moduleName, "nowSize", nowSize, "totalSize", totalSize)
    Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress:Invoke(moduleName, nowSize, totalSize)
end

function LiteDownloadManager:CheckModuleNameNotRumtimeSpecial(moduleName)
    local found = string.find(moduleName, "RuntimeUpdateCombinePakKey")
    if found ~= nil then
        return false
    end

    if moduleName ~= "RuntimeBeforeLogin" and moduleName ~= "RuntimeBeforeLoginWholePackage" and
        moduleName ~= "RuntimeLitePackModuleConfig" then

        return true
    end

    return false
end

function LiteDownloadManager:OnLiteDownloadReturn(moduleName, bSuccess, errorCode)
    local allDownloaded = bSuccess
    local questAllPakReady = bSuccess
    if curdownlaodQuest ~= nil and moduleName == curdownlaodQuest.ModuleName then
        curdownlaodQuest:SetDownloadResult(moduleName, bSuccess, errorCode)
        -- allDownloaded = curdownlaodQuest:GetFiexdDownloadedRet()
        -- if allDownloaded then
        --     ModuleDownloaded[moduleName] = true
        --     self:MountDownloadedPak()
        -- end
    end

    local bQueueHasQuest = false
    for key, quest in pairs(downloadQuestQueueNew) do
        if quest.ModuleName == moduleName then
            bQueueHasQuest = true
        end
    end

    -- if bQueueHasQuest == false and bIsCombineDownload == false then
    --     ModuleDownloadState[moduleName] = 0
    -- end

    if bSuccess then
        if moduleName~="RuntimeLitePackModuleConfig" and moduleName~="RuntimeBeforeLogin" then
            self:RefreshHDDownloadCollection()
            loginfo("[LiteDownloadManager] drmdbg OnLiteDownloadReturn moduleName:", moduleName, ", bSuccess:", bSuccess, ", errorCode:"
            , errorCode, ", questAllPakReady:", questAllPakReady, "RefreshHDDownloadCollection")
        end
    end

    if curdownlaodQuest ~= nil and moduleName == curdownlaodQuest.ModuleName then
        self:RunQuestCheck()
    elseif curdownlaodQuest == nil then
        self:RunQuestCheck()
    end

    loginfo("[LiteDownloadManager] drmdbg OnLiteDownloadReturn moduleName:", moduleName, ", bSuccess:", bSuccess, ", errorCode:"
        , errorCode, ", questAllPakReady:", questAllPakReady)

    if moduleName == "RuntimeLitePackModuleConfig" or moduleName == "RuntimePakFixed" then
        -- now is modulename = "RuntimeLitePackModuleConfig"
        Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:Invoke(moduleName, allDownloaded, errorCode)
    end

    -- local reportDownloadResult = 1
    -- if bSuccess then
    --     reportDownloadResult = 2
    -- end
    -- self:DoReportFinishedDownloadEvent(moduleName, reportDownloadResult, errorCode)
    -- if questAllPakReady then
    --     Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult:Invoke(moduleName, allDownloaded, errorCode)
    -- end
    
    if bSuccess and moduleName == sHDDownloadCollection then
        self:SetHDDownloadFlagStr(true)
        Module.LitePackage.Config.evtHDDownloadCollectionFinish:Invoke()
        loginfo("[LiteDownloadManager] drmdbg OnLiteDownloadReturn sHDDownloadCollection", sHDDownloadCollection, "download success")
    end
end



function LiteDownloadManager:OnLiteModuleCheckResult(moduleName, bDownloaded, nowSize, totalSize, errorcode)
    if totalSize > 0 then
        if bDownloaded then
            ModuleDownloaded[moduleName] = true
        else
            ModuleDownloaded[moduleName] = false
        end
    else
        -- totalSize = 0 set ModuleDownloaded[moduleName] to false
        ModuleDownloaded[moduleName] = false

        -- check local file state
        local bAllFileReady, totalSizeLocal, readyTotalSizeLocal = self:CheckLocalSize(moduleName)
        nowSize = totalSizeLocal
        totalSize = readyTotalSizeLocal

        if bAllFileReady then
            ModuleDownloaded[moduleName] = true
            logerror("[LiteDownloadManager] OnLiteModuleCheckResult moduleName:"..moduleName..", check local file is ready. totalSizeLocal:"..totalSizeLocal..", readyTotalSizeLocal:"..readyTotalSizeLocal)
        else
            ModuleDownloaded[moduleName] = false
            logerror("[LiteDownloadManager] OnLiteModuleCheckResult moduleName:"..moduleName..", check local file is not ready. totalSizeLocal:"..totalSizeLocal..", readyTotalSizeLocal:"..readyTotalSizeLocal)
        end
    end

    if curdownlaodQuest ~= nil and curdownlaodQuest.ModuleName == moduleName then
        local bisAllready, fixedNowSize, fixedTotalSize = curdownlaodQuest:GetQuestStateSize()
        totalSize = fixedTotalSize
        nowSize = fixedNowSize
    end

    if table.contains(ModuleNowSize, moduleName) then
        if nowSize > ModuleNowSize[moduleName] then
            ModuleNowSize[moduleName] = nowSize
        end
    else
        ModuleNowSize[moduleName] = nowSize
    end
    ModuleTotalSize[moduleName] = totalSize

    --loginfo("[LiteDownloadManager] drmdbg OnLiteModuleCheckResult moduleName:", moduleName, ", bDownloaded:", bDownloaded,
    --    ", nowSize:", nowSize, ", totalSize:", totalSize, ", errorCode:", errorCode)
    Module.LitePackage.Config.evtDownloadManagerNtfModuleCheckResult:Invoke(moduleName, bDownloaded, nowSize, totalSize,
        errorcode)

    if bBeginCheckAllModuleAsync == true then
        local hasQuest = -1
        for i = 1, #AsyncCheckQuest do
            if AsyncCheckQuest[i] == moduleName then
                hasQuest = i
            end
        end

        if hasQuest > 0 then
            table.remove(AsyncCheckQuest, hasQuest)
            --loginfo("[LiteDownloadManager] OnLiteModuleCheckResult remove AsyncCheckQuest, hasQuest", hasQuest)
        end

        if #AsyncCheckQuest <= 0 then
            bHasbeenCheckedQuest = true
            bBeginCheckAllModuleAsync = false

            if self:CheckModuleNameNotRumtimeSpecial(moduleName) then
                self:ResetAllDownloadedQuestIDs()
            end

            loginfo("[LiteDownloadManager] OnLiteModuleCheckResult all module checked.")
            Module.LitePackage.Config.evtDownloadManagerNtfAllModuleChecked:Invoke()
            self:RefreshHDDownloadFlag()
            loginfo("[LiteDownloadManager] OnLiteModuleCheckResult all module checked. RefreshHDDownloadCollection")
            self:RefreshHDDownloadCollection()

            self:MountDownloadedPak()
        else
            --check next
            if hasQuest ~= -1 then
                local nowModuleKey = AsyncCheckQuest[1]
                self:CheckModuleAsync(nowModuleKey)
                --loginfo("[LiteDownloadManager] OnLiteModuleCheckResult check count", #AsyncCheckQuest, "start check", nowModuleKey)
            end
        end
    end
end

function LiteDownloadManager:OnCurrentDownloadQuestNeedMoveNext()
    self:RunQuestCheck()
end

--数据上报
function LiteDownloadManager:GetToReportDownloadedQuest()
    local downloadedids = self:GetDownloadedQuestIDs()
    local result = ""

    for i, num in ipairs(downloadedids) do
        if i > 1 then
            result = result .. ","
        end
        result = result .. tostring(num)
    end

    return result
end

function LiteDownloadManager:DoReportBeginDownloadEvent(quest)
    if pb == nil or quest == nil then
        return
    end

    local moduleName = quest.ModuleName
    local DownloadStyle = quest.DownloadStyle
    local DownloadTrigger = quest.DownloadTrigger
    local questID = 0
    local lowModuleName = self:GetLowModuleName(moduleName)
    if ModuleNameAndQuestIDMap[lowModuleName] then
        questID = ModuleNameAndQuestIDMap[lowModuleName]
    end

    -- if questID == 0 then
    --     --runtime module
    --     return
    -- end

    local idsStr = self:GetToReportDownloadedQuest()
    local deviceName = litePackageMgr:GetDeviceName()
    local freeSpace = litePackageMgr:GetDeviceFreeSpace()
    local spaceStr = string.format("%dGB", math.ceil(freeSpace / LiteDownloadGBNum))
    LogAnalysisTool.DoSendLiteDownloadEvent(questID, moduleName, DownloadStyle, DownloadTrigger, 0, idsStr, 0, deviceName
        , spaceStr)

    logerror(string.format("[LiteDownloadManager] DoReportBeginDownloadEvent:%s DownloadStyle:%s DownloadTrigger:%s", moduleName, DownloadStyle, DownloadTrigger))
end

function LiteDownloadManager:DoReportFinishedDownloadEvent(moduleName, DownloadResult, ErrorCode)
    if pb == nil then
        return
    end

    local moduleName = moduleName
    local DownloadStyle = EDownloadStyle.Manual
    local DownloadTrigger = EDownloadTrigger.None
    if curdownlaodQuest ~= nil then
        moduleName = curdownlaodQuest.ModuleName
        DownloadStyle = curdownlaodQuest.DownloadStyle
        DownloadTrigger = curdownlaodQuest.DownloadTrigger
    end

    local questID = 0
    local lowModuleName = self:GetLowModuleName(moduleName)
    if ModuleNameAndQuestIDMap[lowModuleName] then
        questID = ModuleNameAndQuestIDMap[lowModuleName]
    end

    -- if questID == 0 then
    --     --runtime module
    --     return
    -- end

    local idsStr = self:GetToReportDownloadedQuest()
    local deviceName = litePackageMgr:GetDeviceName()
    local freeSpace = litePackageMgr:GetDeviceFreeSpace()
    local spaceStr = string.format("%dGB", math.ceil(freeSpace / LiteDownloadGBNum))
    LogAnalysisTool.DoSendLiteDownloadEvent(questID, moduleName, DownloadStyle, DownloadTrigger, DownloadResult, idsStr, ErrorCode, deviceName,
        spaceStr)
end

function LiteDownloadManager:CheckIsMapsDownload(mapList,bFromRoom)
    loginfo("LitePackageModule:CheckIsMapsDownload",bFromRoom)
    logtable(mapList, true)
    if self:IsSupportLitePackage() then
        for key, value in pairs(mapList or {}) do
            local moduleName = self:GetModuleNameByMapId(value)
            local isDownload = self:IsDownloadedByModuleName(moduleName)
            if isDownload then
                return true
            end
        end
        Module.Social:ShowNotDownloadTips(mapList[1],nil,bFromRoom)
        return false

    else
        return true
    end
end

function LiteDownloadManager:GetLowModuleName(moduleName)
    local retModuleName = moduleName
    if self:IsHDDownloadStatus() then
        if tHDRuntimeModuleName2LowModuleName[moduleName] ~= nil then
            retModuleName = tHDRuntimeModuleName2LowModuleName[moduleName]
        end
    end

    return retModuleName
end

function LiteDownloadManager:GetQuestNameByMapId(mapId)
    loginfo("LitePackageModule:GetQuestNameByMapId", mapId)
    local moduleName = self:GetModuleNameByMapId(mapId)
    if self:IsHDDownloadStatus() then
        if tHDRuntimeModuleName2LowModuleName[moduleName] ~= nil then
            moduleName = tHDRuntimeModuleName2LowModuleName[moduleName]
        end
    end
    return self:GetQuestNameByModuleName(moduleName)
end

function LiteDownloadManager:GetQuestIdByMapId(mapId)
    loginfo("LitePackageModule:GetQuestIdByMapId", mapId)
    local moduleName = self:GetModuleNameByMapId(mapId)
    if self:IsHDDownloadStatus() then
        if tHDRuntimeModuleName2LowModuleName[moduleName] ~= nil then
            moduleName = tHDRuntimeModuleName2LowModuleName[moduleName]
        end
    end
    if moduleName then
        for k, v in pairs(Module.LitePackage.Config.LitePackageDownloadTable or {}) do
            if v.ModuleKey == moduleName then
                return v.QuestID
            end
        end
    else
        logerror("LiteDownloadManager:GetQuestIdByMapId moduleName is nil!!!")
        return 0
    end
end

function LiteDownloadManager:GetQuestNameByModuleName(moduleName)
    loginfo("LitePackageModule:GetQuestNameByModuleName ", moduleName)
    if moduleName then
        moduleName = tostring(moduleName)
        for k, v in pairs(Module.LitePackage.Config.LitePackageDownloadTable or {}) do
            if tostring(v.ModuleKey) == moduleName then
                return v.QuestName
            end
        end
    else
        logerror("LiteDownloadManager:GetQuestNameByModuleName moduleName is nil!!!")
        return ""
    end
end

function LiteDownloadManager:parse_multimodule_info(content)
    local multimoduleInfo = {}
    for moduleName, childModules in content:gmatch('MultiModule=%(MultiModuleName="(.-)".-ChildModules=%((.-)%)%)') do
        local modules = {}
        for module in childModules:gmatch('"([^"]+)"') do
            table.insert(modules, module)
        end
        multimoduleInfo[moduleName] = modules
    end

    return multimoduleInfo
end

function LiteDownloadManager:parse_module_paks(content)
    local modulePaks = {}
    for moduleName, pakList in content:gmatch('moduleInfo=%(ModuleName="(.-)".-PakList=%((.-)%)%)') do
        local paks = {}
        for pak in pakList:gmatch('"([^"]+)"') do
            table.insert(paks, pak)
        end
        modulePaks[moduleName] = paks
    end

    return modulePaks
end

-- 根据multimoduleInfo和modulePaks生成最终的modulePaks表
function LiteDownloadManager:generate_final_module_paks(multimoduleInfo, modulePaks)
    local final_module_paks = {}
    for multi_module_name, child_modules in pairs(multimoduleInfo) do
        final_module_paks[multi_module_name] = {}
        for _, child_module in ipairs(child_modules) do
            for _, pak in ipairs(modulePaks[child_module] or {}) do
                table.insert(final_module_paks[multi_module_name], pak)
            end
        end
    end
    return final_module_paks
end

function LiteDownloadManager:ResetModulePakNames()
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local multiModuleInfo_path = pufferPath.."//LiteMultiModuleInfo.ini"
    local multiModuleInfo_file = io.open(multiModuleInfo_path, "r")
    local multiModuleInfo_content = ""
    if multiModuleInfo_file then
        multiModuleInfo_content = multiModuleInfo_file:read("*all")
        multiModuleInfo_file:close()
    end

    local modulePakInfo_path = pufferPath.."//LiteModuleInfo.ini"
    local modulePakInfo_file = io.open(modulePakInfo_path, "r")
    local modulePakInfo_content = ""
    if modulePakInfo_file then
        modulePakInfo_content = modulePakInfo_file:read("*all")
        modulePakInfo_file:close()
    end

    if multiModuleInfo_content ~= "" and modulePakInfo_content ~= "" then
        local multimoduleInfo = self:parse_multimodule_info(multiModuleInfo_content)
        local modulePaks = self:parse_module_paks(modulePakInfo_content)
        LocalModuleAndPakName = self:generate_final_module_paks(multimoduleInfo, modulePaks)
    end
end





local sizeInfo ={}
local md5Info = {}

function LiteDownloadManager:ResetPaksFileSizeAndMD5()
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)

    local dfFile = pufferPath.."//puffer_file_info.df"
    local bExists = ULuaExtension.Ext_FileExists(dfFile)

    if bExists then
        loginfo("[LiteDownloadManager] ResetPaksFileSizeAndMD5 file bExists: true")
        for line in io.lines(dfFile) do
            local parts = self:split(line, "=")
            if #parts == 2 then
                local key = parts[1]:match("^%s*(.-)%s*$")
                local valueParts = self:split(parts[2], ",")
                if #valueParts == 2 then
                    local size = tonumber(valueParts[1]:match("^%s*(.-)%s*$"))
                    local md5 = valueParts[2]:match("^%s*(.-)%s*$")
                    LocalPakNameFileSize[key] = size
                    LocalPakNameFileMD5[key] = md5

                    if DebugPakFix then
                        local number = key:match("pakchunk(%d+)")
                        if number == "88" then
                            LocalPakNameFileMD5[key] = "test1234567abcdefg1234567"
                        end
                    end

                    loginfo("[LiteDownloadManager] ResetPaksFileSizeAndMD5 filename:"..key..", size:"..size..", md5:"..md5)
                end
            end
        end

        self.bResetPakFileSizeAndMD5 = true
    else
        loginfo("[LiteDownloadManager] ResetPaksFileSizeAndMD5 file bExists: false")
    end
end

function LiteDownloadManager:split(str, sep)
    local result = {}
    for part in string.gmatch(str, "([^" .. sep .. "]+)") do
        table.insert(result, part)
    end
    return result
end


--for ingame
local HD_RES_STYLE_BASE = 1
local HD_RES_STYLE_SOL = 2
local HD_RES_STYLE_MP = 3
local MODULE_STATE_DOWNLOADED = 1


local DOWNLOAD_CATEGORY_WEAPONPART = 11

---获取地图模式是否有需要下载的高清资源，以及是否全部下载完成。返回true则全部下载完成
---@param mapid number 地图id, 暂时不使用
---@param modeid number 模式id 0:其他模式 1:SOL 2:MP
---@return boolean result 返回true则全部下载完成
function LiteDownloadManager:GetMapModeHDResDownloaded(mapid, modeid)
    local result = true

    local needCheckModules = {}
    for k, v in pairs(self.SortedDownloadTable) do
        --v.HDStyle HD资源类型：0：默认值 1：基础高清包 2：SOL高清包 3：MP高清包
        if v.HDStyle ~= nil and v.HDStyle > 1 then
            if modeid == 1 and v.HDStyle == HD_RES_STYLE_SOL then
                table.insert(needCheckModules, v.ModuleKey)
                loginfo("[LiteDownloadManager] GetMapModeHDResDownloaded , check modulename:"..v.ModuleKey)
            elseif modeid == 2 and v.HDStyle == HD_RES_STYLE_MP then
                table.insert(needCheckModules, v.ModuleKey)
                loginfo("[LiteDownloadManager] GetMapModeHDResDownloaded , check modulename:"..v.ModuleKey)
            end
        end
    end

    if #needCheckModules > 0 then
        for k, v in pairs(needCheckModules) do
            local modulestate = self:GetModuleStateByModuleName(v)
            if modulestate ~= 1 then
                result = false
                break
            end
        end
    end

    loginfo("[LiteDownloadManager] GetMapModeHDResDownloaded , check ret:"..tostring(result))
    return result
end

function LiteDownloadManager:CheckFileIsInPuffer(PakName)
    return litePackageMgr:CheckFileIsInPuffer(PakName)
end

function LiteDownloadManager:MergePaks(SrcPakFileName, PatchPakFileName, outPakName, bCalculateMD5)
    return AssetPackerFunctionLibrary.MergePaks(SrcPakFileName, PatchPakFileName, outPakName, bCalculateMD5)
end

function LiteDownloadManager:MergePaksAsync(SrcPakFileName, PatchPakFileName, outPakName, bCalculateMD5)
    return AssetPackerFunctionLibrary.MergePaksAsync(SrcPakFileName, PatchPakFileName, outPakName, bCalculateMD5)
end

function LiteDownloadManager:MergePaksAsyncChecked(SrcPakFileName, SrcMD5, PatchPakFileName, PatchMD5, OutPakName, OutMD5)
    return AssetPackerFunctionLibrary.MergePaksAsyncChecked(SrcPakFileName, SrcMD5, PatchPakFileName, PatchMD5, OutPakName, OutMD5)
end

function LiteDownloadManager:SetCombinePakNames(CombinedPakNames)
    return litePackageMgr:SetCombinePakNames(CombinedPakNames)
end


function LiteDownloadManager:SetNextLoginToSetCombinedPaks(CombinedPaks)
    local info = ""
    for k, pakName in ipairs(CombinedPaks) do
        if info == "" then
            info = pakName
        else
            info = info..","..pakName
        end
    end

    loginfo("[LiteDownloadManager] SetNextLoginToSetCombinedPaks:"..info)
    Facade.ConfigManager:SetString("SetCombinedPaks", info or "")
    self.NextLoginToSetCombinedPaksCouldBeDeleted = false
end

function LiteDownloadManager:GetNextLoginToSetCombinedPaks(bCloseLog)
    if ENABLE_PAK_COMBINE == false then
        return nil
    end

    local paksStr = Facade.ConfigManager:GetString("SetCombinedPaks", "")
    if bCloseLog ~= true then
        loginfo("[LiteDownloadManager] GetNextLoginToSetCombinedPaks:"..paksStr)
    end

    local result = {}
    local delimiter = ","
    for match in string.gmatch(paksStr, "([^" .. delimiter .. "]+)") do
        table.insert(result, match:match("^%s*(.-)%s*$"))
    end

    return result
end

---获取所有基础高清资源模块是否全部下载完成
---@return boolean result 返回true则全部下载完成
function LiteDownloadManager:GetBaseHDResDownloaded()
    local result = true

    local needCheckModules = {}
    for k, v in pairs(self.SortedDownloadTable) do
        --v.HDStyle HD资源类型：0：默认值 1：基础高清包 2：SOL高清包 3：MP高清包
        if v.HDStyle ~= nil and v.HDStyle == HD_RES_STYLE_BASE then
            table.insert(needCheckModules, v.ModuleKey)
            loginfo("[LiteDownloadManager] GetBaseHDResDownloaded , check modulename:"..v.ModuleKey)
        end
    end

    if #needCheckModules > 0 then
        for k, v in pairs(needCheckModules) do
            local modulestate = self:GetModuleStateByModuleName(v)
            if modulestate ~= 1 then
                result = false
                break
            end
        end
    end

    loginfo("[LiteDownloadManager] GetBaseHDResDownloaded , check ret:"..tostring(result))
    return result
end

---获取武器配件资源模块ModuleKey
---@return string result 返回true则全部下载完成
function LiteDownloadManager:GetWeaponPartModuleKey()
    local result = "None"

    local needCheckModules = {}
    for k, v in pairs(self.SortedDownloadTable) do
        --v.Category 11:武器配件
        if v.Category ~= nil and v.Category == DOWNLOAD_CATEGORY_WEAPONPART then
            table.insert(needCheckModules, v.ModuleKey)
            loginfo("[LiteDownloadManager] GetWeaponPartItemResDownloaded , check modulename:"..v.ModuleKey)
        end
    end

    if #needCheckModules > 0 then
        result = needCheckModules[1]
    end

    loginfo("[LiteDownloadManager] GetWeaponPartItemResDownloaded , check ret:"..tostring(result))
    return result
end

function LiteDownloadManager:IsModuleShow(moduleName)
    for index, name in pairs(ModuleNameShow) do
        if moduleName == name then
            return true
        end
    end
    return false
end

---获取所有武器配件资源模块是否全部下载完成
---@return boolean result 返回true则全部下载完成
function LiteDownloadManager:GetWeaponPartItemResDownloaded()
    local result = true

    local needCheckModules = {}
    for k, v in pairs(self.SortedDownloadTable) do
        --v.Category 11:武器配件
        if v.Category ~= nil and v.Category == DOWNLOAD_CATEGORY_WEAPONPART then
            table.insert(needCheckModules, v.ModuleKey)
            loginfo("[LiteDownloadManager] GetWeaponPartItemResDownloaded , check modulename:"..v.ModuleKey)
        end
    end

    if #needCheckModules > 0 then
        for k, v in pairs(needCheckModules) do
            local modulestate = self:GetModuleStateByModuleName(v)
            if modulestate ~= 1 then
                result = false
                break
            end
        end
    end

    loginfo("[LiteDownloadManager] GetWeaponPartItemResDownloaded , check ret:"..tostring(result))
    return result
end

function LiteDownloadManager:GetDownloadCategary(iItemID)
    local tItem = ItemBase:NewIns(iItemID)
    if tItem.itemMainType == EItemType.WeaponSkin then
        local ret = "InFashion"
        local pakSelectorTableAllDataTable = Facade.TableManager:GetTable("PakSelectorTable")
        if isinvalid(pakSelectorTableAllDataTable) then
            logerror("[LitePackageLogic.GetDownloadCategary] PakSelectorTable is nil iItemID:"..iItemID)
            return ret
        end
        local itemModules = {}
        local numberItemID = tonumber(iItemID)
        for _, itemPakInfo in pairs(pakSelectorTableAllDataTable) do
            local id = tonumber(itemPakInfo.Id)
            if id == numberItemID then
                if itemPakInfo.ModuleList ~= nil then
                    for k, childModuleName in pairs(itemPakInfo.ModuleList) do
                        ret = childModuleName
                        table.insert(itemModules, childModuleName)
                        loginfo("[LiteDownloadManager:GetDownloadCategary] ".."iItemID:"..iItemID..", add child module:"..childModuleName)
                    end
                end
                break
            end
        end

        if itemModules ~= nil and #itemModules > 1 then
            local registerRuntimeItemModule = "RuntimeItemModule_"..iItemID
            self:RegisterRuntimeMultiModuleByChildModule(registerRuntimeItemModule, itemModules, true)
            ret = registerRuntimeItemModule
        end

        -- 武器皮肤
        loginfo("[LiteDownloadManager:GetDownloadCategary] EItemType.WeaponSkin:"..iItemID..", modulename ret:"..ret)
        return ret
    elseif tItem.itemMainType == EItemType.Adapter and tItem.itemSubType ~= ItemConfig.EAdapterItemType.Pendant then
        -- 武器配件-非吊坠
        local ret = "WeaponPart"
        local weaponPartPakPolicyDataTable = Facade.TableManager:GetTable("Weapon/WeaponPartItemPakPolicy")
        if isinvalid(weaponPartPakPolicyDataTable) then
            logerror("[LitePackageLogic.GetDownloadCategary] Weapon/WeaponPartItemPakPolicy is nil iItemID:"..iItemID)
            return ret
        end
        for _, weaponPartPakPolicyInfo in pairs(weaponPartPakPolicyDataTable) do
            local id = tonumber(weaponPartPakPolicyInfo.Id)
            if id == iItemID then
                ret = weaponPartPakPolicyInfo.Category
            end
        end

        loginfo("[LiteDownloadManager:GetDownloadCategary] EItemType.Adapter:"..iItemID..", modulename ret:"..ret)
        return ret
    elseif tItem.itemMainType == EItemType.Adapter and tItem.itemSubType == ItemConfig.EAdapterItemType.Pendant then
        -- 武器配件-吊坠
        local ret = "Weapon"
        local weaponPartItemPakPolicyDataTable = Facade.TableManager:GetTable("Weapon/WeaponPartItemPakPolicy")
        if isinvalid(weaponPartItemPakPolicyDataTable) then
            logerror("[LitePackageLogic.GetDownloadCategary] Weapon/WeaponPartItemPakPolicy is nil iItemID:"..iItemID)
            return ret
        end
        for _, weaponPartItemPakPolicyInfo in pairs(weaponPartItemPakPolicyDataTable) do
            local id = tonumber(weaponPartItemPakPolicyInfo.Id)
            if id == iItemID then
                ret = weaponPartItemPakPolicyInfo.Category
            end
        end

        loginfo("[LiteDownloadManager:GetDownloadCategary] EItemType.Adapter and Pendant:"..iItemID..", modulename ret:"..ret)
        return ret
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.SparyPaint then
        -- 干员商业化周边-喷漆
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Gesture then
        -- 干员商业化周边-表演手势1p
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialAvatarTab then
        -- 社交定制外观-头像
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.SocialAppearance and tItem.itemSubType == ESocialAppearanceType.SocialMilitaryTab then
        -- 社交定制外观-军牌
        return "UICustomized"
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.Card then
        -- 干员商业化周边-名片
        return "UICustomized"
    elseif tItem.itemMainType == EItemType.Hero then
        -- 干员
        return "Character"
    elseif tItem.itemMainType == EItemType.Fashion then
        -- 干员皮肤
        local ret = "Fashion"
        local pakSelectorTableAllDataTable = Facade.TableManager:GetTable("PakSelectorTable")
        if isinvalid(pakSelectorTableAllDataTable) then
            logerror("[LitePackageLogic.GetDownloadCategary] PakSelectorTable is nil iItemID:"..iItemID)
            return ret
        end
        local itemModules = {}
        local numberItemID = tonumber(iItemID)
        for _, itemPakInfo in pairs(pakSelectorTableAllDataTable) do
            local id = tonumber(itemPakInfo.Id)
            if id == numberItemID then
                if itemPakInfo.ModuleList ~= nil then
                    for k, childModuleName in pairs(itemPakInfo.ModuleList) do
                        ret = childModuleName
                        table.insert(itemModules, childModuleName)
                        loginfo("[LiteDownloadManager:GetDownloadCategary] ".."iItemID:"..iItemID..", add child module:"..childModuleName)
                    end
                end
                break
            end
        end

        if itemModules ~= nil and #itemModules > 1 then
            local registerRuntimeItemModule = "RuntimeItemModule_"..iItemID
            self:RegisterRuntimeMultiModuleByChildModule(registerRuntimeItemModule, itemModules, true)
            ret = registerRuntimeItemModule
        end

        -- 干员皮肤
        loginfo("[LiteDownloadManager:GetDownloadCategary] EItemType.Fashion:"..iItemID..", modulename ret:"..ret)
        return ret
    elseif tItem.itemMainType == EItemType.HeroAccessory and tItem.itemSubType == EHeroAccessroy.AnimShow then
        -- 干员动作
        return "OtherCharacter"
    elseif tItem.itemMainType == EItemType.Vehicle then
        local ret = "Vehicles"
        -- 载具表里ID是局内ID,需要转一次
        local vehicleID = VehicleHelperTool.GetVehicleIDByVehicleItemID(iItemID)

        local vehiclePakPolicyDataTable = Facade.TableManager:GetTable("Vehicle/VehiclePakPolicy")
        if isinvalid(vehiclePakPolicyDataTable) then
            logerror("[LitePackageLogic.GetDownloadCategary] Vehicle/VehiclePakPolicy is nil iItemID:", iItemID, ", vehicleID: ", vehicleID)
            return ret
        end
        for _, vehiclePakPolicyInfo in pairs(vehiclePakPolicyDataTable) do
            local id = tonumber(vehiclePakPolicyInfo.Id)
            if id == vehicleID then
                ret = vehiclePakPolicyInfo.Category
            end
        end
        return ret
    elseif tItem.itemMainType == EItemType.VehicleSkin then
        -- 载具皮肤
        local ret = "VehiclesFashion"
        local bFind = true
        local vehicleDefaultSkinAppearanceID = VehicleHelperTool.GetVehicleDefaultSkinAppearanceIDByID(iItemID)
        if vehicleDefaultSkinAppearanceID == nil or vehicleDefaultSkinAppearanceID == 0 then
            bFind = false
        end

        local vehicleSkinPakPolicyDataTable = Facade.TableManager:GetTable("Vehicle/VehicleSkinPakPolicy")
        if isinvalid(vehicleSkinPakPolicyDataTable) then
            logerror("Vehicle/VehicleSkinPakPolicy is nil")
            bFind = false
        end
        if bFind then
            for _, vehicleSkinPakPolicyInfo in pairs(vehicleSkinPakPolicyDataTable) do
                local id = tonumber(vehicleSkinPakPolicyInfo.Id)
                if id == vehicleDefaultSkinAppearanceID then
                    ret = vehicleSkinPakPolicyInfo.Category
                    break
                end
            end
        end
        loginfo("[LiteDownloadManager:GetDownloadCategary] EItemType.VehicleSkin:"..iItemID..", modulename ret:"..ret)
        return ret
    elseif tItem.itemMainType == EItemType.CollectionProp then
        -- 藏品道具
        return "None"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.Helmet then
        -- 头盔
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Medicine then
        -- 药品
        return "OtherProp"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.BreastPlate then
        -- 胸甲
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.ChestHanging then
        -- 胸挂
        return "OtherEquipment"
    elseif tItem.itemMainType == EItemType.Equipment and tItem.itemSubType == EEquipmentType.Bag then
        -- 背包
        return "OtherEquipment"
    elseif iItemID == 17888808889 then
        -- 三角券
        return "None"
    else
        -- 其他类型
        return "None"
    end
end

--function LiteDownloadManager:RefreshHDDownloadStatus()
--    for moduleName, tHDQuestID in pairs(ModuleNameToRelatedHDQuest) do
--        if self:IsDownloadedByModuleName(moduleName) then
--            for _, questID in pairs(tHDQuestID) do
--                if not self:IsDownloadedByQuestID(questID) then
--                    bHDDownloadStatus = false
--                    loginfo("[LiteDownloadManager] drmdbg RefreshHDDownloadStatus bHDDownloadStatus", bHDDownloadStatus)
--                    self:RegisterHDDownloadCollection()
--                    return
--                end
--            end
--        end
--    end
--
--    if not self:IsDownloadedByModuleName("BeforeLoginHD") then
--        bHDDownloadStatus = false
--        self:RegisterHDDownloadCollection()
--        loginfo("[LiteDownloadManager] drmdbg RefreshHDDownloadStatus bHDDownloadStatus", bHDDownloadStatus)
--        return
--    end
--
--    bHDDownloadStatus = true
--    loginfo("[LiteDownloadManager] drmdbg RefreshHDDownloadStatus bHDDownloadStatus", bHDDownloadStatus)
--end

function LiteDownloadManager:RefreshHDDownloadCollection()
    if not self:IsHDDownloadStatus() then
        self:RegisterHDDownloadCollection() 
    end 
end

function LiteDownloadManager:RefreshHDDownloadFlag()
    local childModule = {}

    if IsBuildRegionCN() then
        if not self:IsDownloadedByModuleName("OtherBeforeLoginHD") then
            table.insert(childModule, "OtherBeforeLoginHD")
        end
    else
        if not self:IsDownloadedByModuleName("OtherBeforeLoginHD") then
            table.insert(childModule, "OtherBeforeLoginHD")
        end
    end

    for moduleName, tHDQuestID in pairs(ModuleNameToRelatedHDQuest) do
        if self:IsDownloadedByModuleName(moduleName) then
            for _, questID in pairs(tHDQuestID) do
                local HDModuleName = QuestIDAndModuleNameMap[questID]
                if not self:IsDownloadedByModuleName(HDModuleName) then
                    table.insert(childModule, HDModuleName)
                end
            end
        end
    end

    local childModuleCount = #childModule
    if childModuleCount <= 0 then
        self:SetHDDownloadFlagStr(true)
    end
end

function LiteDownloadManager:RegisterHDDownloadCollection()
    if sHDDownloadCollection ~= nil then
        local bDownloading = self:IsDownloadingByModuleName(sHDDownloadCollection)
        if bDownloading then
            logerror("[LiteDownloadManager] RegisterHDDownloadCollection check but hd is downloading.")
            return
        end
    end

    local childModule = {}
    tHDDownloadCollectionModuleList = {}

    if IsBuildRegionCN() then
        if not self:IsDownloadedByModuleName("OtherBeforeLoginHD") then
            table.insert(childModule, "OtherBeforeLoginHD")
            loginfo("[LiteDownloadManager] drmdbg RegisterHDDownloadCollection BeforeLoginHD")
        end
    else
        if not self:IsDownloadedByModuleName("OtherBeforeLoginHD") then
            table.insert(childModule, "OtherBeforeLoginHD")
            loginfo("[LiteDownloadManager] drmdbg RegisterHDDownloadCollection OtherBeforeLoginHD")
        end
    end

    -- insert nowdownloading quest to hd
    local nowDownloadModuleName = nil
    if curdownlaodQuest ~= nil and curdownlaodQuest.ModuleName ~= nil and sHDDownloadCollection ~= curdownlaodQuest.ModuleName then
        nowDownloadModuleName = curdownlaodQuest.ModuleName
        loginfo("[LiteDownloadManager] drmdbg RegisterHDDownloadCollection add curdownlaodQuest.ModuleName"..nowDownloadModuleName)
    end

    for moduleName, tHDQuestID in pairs(ModuleNameToRelatedHDQuest) do
        if self:IsDownloadedByModuleName(moduleName) or nowDownloadModuleName == moduleName then
            for _, questID in pairs(tHDQuestID) do
                local HDModuleName = QuestIDAndModuleNameMap[questID]
                if not self:IsDownloadedByModuleName(HDModuleName) then
                    table.insert(childModule, HDModuleName)
                    loginfo("[LiteDownloadManager] drmdbg RegisterHDDownloadCollection", HDModuleName)
                end
            end
        end
    end
    


    sHDDownloadCollection = "HDRuntimeCollection"
    tHDDownloadCollectionModuleList = childModule
    LiteDownloadManager:RegisterRuntimeMultiModuleByChildModule(sHDDownloadCollection, childModule, true)

    ModuleDownloadState[sHDDownloadCollection] = 0
    ModuleNowSize[sHDDownloadCollection] = 0
    ModuleTotalSize[sHDDownloadCollection] = 0

    DownloadInfoMap[sHDDownloadCollection] = {}
    
    self:CheckModuleAsync(sHDDownloadCollection)
end

function LiteDownloadManager:RegisterHDDownloadStatusMultiModule()
    tModuleName2HDRuntimeName = {}
    tHDRuntimeModuleName2LowModuleName = {}
    tHDRuntimeModuleName2HDModuleName = {}

    for moduleName, tHDQuestID in pairs(ModuleNameToRelatedHDQuest) do
        local childModule = {}
        local HDRelatedModuleName = {}
        table.insert(childModule, moduleName)
        for _, questID in pairs(tHDQuestID) do
            local HDModuleName = QuestIDAndModuleNameMap[questID]
            table.insert(childModule, HDModuleName)
            table.insert(HDRelatedModuleName, HDModuleName)
        end
        local sHDModuleName = "HDRuntime" .. moduleName
        LiteDownloadManager:RegisterRuntimeMultiModuleByChildModule(sHDModuleName, childModule, true)
        tModuleName2HDRuntimeName[moduleName] = sHDModuleName
        tHDRuntimeModuleName2LowModuleName[sHDModuleName] = moduleName
        tHDRuntimeModuleName2HDModuleName[sHDModuleName] = HDRelatedModuleName

        ModuleDownloadState[sHDModuleName] = 0
        ModuleNowSize[sHDModuleName] = 0
        ModuleTotalSize[sHDModuleName] = 0

        DownloadInfoMap[sHDModuleName] = {}
    end
end

function LiteDownloadManager:DownloadHDCollection()
    Facade.ConfigManager:SetBoolean("AutoBeginDownloadHD", true)
    self:DownloadByModuleName(sHDDownloadCollection)
end

function LiteDownloadManager:CancelDownloadHDCollection()
    if self:IsDownloadingByModuleName(sHDDownloadCollection) then
        self:CancelByModuleName(sHDDownloadCollection)
    end
end

function LiteDownloadManager:IsHDBeginDownload()
    local bNeedAutoDownloadHD = Facade.ConfigManager:GetBoolean("AutoBeginDownloadHD", false)
    return bNeedAutoDownloadHD
end

function LiteDownloadManager:GetHDRuntimeNameByModuleName(sModuleName)
    if tModuleName2HDRuntimeName[sModuleName] then
        return tModuleName2HDRuntimeName[sModuleName]
    end
    return sModuleName
end

function LiteDownloadManager:SetHDDownloadFlagStr(bArg)
    Facade.ConfigManager:SetBoolean(HDDownloadFlagStr, bArg)
    loginfo("[LiteDownloadManager] drmdbg SetHDDownloadFlagStr set", HDDownloadFlagStr, bArg)
end

function LiteDownloadManager:IsHDDownloadStatus()
    local ret = Facade.ConfigManager:GetBoolean(HDDownloadFlagStr, false)
    --loginfo("[LiteDownloadManager] drmdbg IsHDDownloadStatus", HDDownloadFlagStr, ret)
    return ret
end

function LiteDownloadManager:GetCurrDownloadModuleName()
    if curdownlaodQuest ~= nil then
        return curdownlaodQuest.ModuleName
    end

    return nil
end

function LiteDownloadManager:GetHDCollectionModuleName()
    return sHDDownloadCollection 
end

--- EventID: 1->登录前选择要下载的游戏模式, SelectResult: 1->MP 2->SOL, SelectStyle:1->自动选择 2->手动选择, SelectInfo:暂不使用
function LiteDownloadManager:ReportDownloadSelectEvent(EventID, SelectResult, SelectStyle, SelectInfo)
    litePackageMgr:ReportDownloadSelectEvent(EventID, SelectResult, SelectStyle, SelectInfo)
end

--- pak combine --------
function LiteDownloadManager:InitCombinedCountInfo()
    local result = {}

    local paksStr = Facade.ConfigManager:GetString("CombinePakCountInfo", "")
    loginfo("[LiteDownloadManager] InitCombinedCountInfo:"..paksStr)
    local pakindexs = self:split(paksStr, ",")
    if pakindexs ~= nil and #pakindexs > 0 then
        for key, value in pairs(pakindexs) do
            table.insert(result, value)
        end
    end

    return result
end

function LiteDownloadManager:GetPakCombinedCount(pakName)
    if IsInEditor() then
        Facade.ConfigManager:SetString("CombinePakCountInfo", "")
    end

    local counter = 0
    if self.DictPakCombineCount == nil then
        self.DictPakCombineCount = self:InitCombinedCountInfo()
    end

    if self.DictPakCombineCount ~= nil then
        for key, value in pairs(self.DictPakCombineCount) do
            if pakName == value then
                counter = counter + 1
            end
        end
    end

    return counter
end

function LiteDownloadManager:SetPakCombinedCount(newPakName)
    if self.DictPakCombineCount == nil then
        self.DictPakCombineCount = self:InitCombinedCountInfo()
    end

    if self.DictPakCombineCount ~= nil then
        table.insert(self.DictPakCombineCount, newPakName)
    end

    local info = ""
    for k, pakName in ipairs(self.DictPakCombineCount) do
        if info == "" then
            info = pakName
        else
            info = info..","..pakName
        end
    end

    loginfo("[LiteDownloadManager] SetPakCombinedCount:"..info)
    Facade.ConfigManager:SetString("CombinePakCountInfo", info or "")
end

function LiteDownloadManager:CheckAndSetPakFileStateToNeedDownload()
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    local needResetPakPaths = {}
    local allVersionPaks = self:GetAllPakNames()
    local combinedPakNames = self:GetNextLoginToSetCombinedPaks()
    for _, pakName in pairs(allVersionPaks) do
        if string.find(pakName, "%.pak$") then
            logerror("[LiteDownloadManager] CheckAndSetPakFileStateToNeedDownload check pakName:"..pakName)
            local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(pakName)
            if "21f56ef1_Forrest-0-2-pakchunk90-Android_ASTCClient.pak" == pakName then
                logerror("[LiteDownloadManager] CheckAndSetPakFileStateToNeedDownload fileState:"..tostring(fileState) )
            end

            local bisInCombined = false
            if combinedPakNames ~= nil and #combinedPakNames > 0 then
                for key, combinePakName in pairs(combinedPakNames) do
                    if combinePakName == pakName then
                        bisInCombined = true
                        break
                    end
                end
            end

            if fileState == true or bisInCombined then
                local filePath = pufferPath.."/"..pakName
                local bFileExists = ULuaExtension.Ext_FileExists(filePath)
                if bFileExists == false then
                    table.insert(needResetPakPaths, filePath)
                    logerror("[LiteDownloadManager] CheckAndSetPakFileStateToNeedDownload add pakName:"..pakName)
                end
                if "21f56ef1_Forrest-0-2-pakchunk90-Android_ASTCClient.pak" == pakName then
                    logerror("[LiteDownloadManager] CheckAndSetPakFileStateToNeedDownload bFileExists:"..tostring(bFileExists))
                end
            end
        end
    end

    if needResetPakPaths ~= nil and #needResetPakPaths > 0 then
        local needFixPaks = self:GetNeedFixPaks()
        if needFixPaks == nil then
            needFixPaks = {}
        end

        for index, pakPath in ipairs(needResetPakPaths) do
            table.insert(needFixPaks, pakPath)
            logerror("[LiteDownloadManager] CheckAndSetPakFileStateToNeedDownload add fix pakPath:"..pakPath)
        end

        self:SetNeedFixPak(needFixPaks)
        self:SetLaunchFixPak()
    end
end

function LiteDownloadManager:ReportPakMergeEvent(ReportStage, ErrorCode, TargetVersionPakName, MergedMd5, BeginTimeStamp, EndTimeStamp, WriteFailedReason)
    local netState = self:GetNetworkState()
    local freeSpace = litePackageMgr:GetDeviceFreeSpace()
    local spaceStr = string.format("%dGB", math.ceil(freeSpace / LiteDownloadGBNum))
    litePackageMgr:ReportPakMergeEvent(ReportStage, netState, ErrorCode, TargetVersionPakName, MergedMd5, BeginTimeStamp, EndTimeStamp, spaceStr, WriteFailedReason)
end

function LiteDownloadManager:CheckAndSetCombinedPaksToPuffer()
    self:CheckAndSetPakFileStateToNeedDownload()

    local combinedPakNames = self:GetNextLoginToSetCombinedPaks()
    if combinedPakNames ~= nil and #combinedPakNames > 0 then
        self:SetCombinePakNames(combinedPakNames)

        local counter = 0
        local needSetPaks = {}
        local needSetPaksMd5 = {}
        local nilPaks = {}

        local bIsShipping = UGameVersionUtils.IsShipping()
        local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
        for key, value in pairs(combinedPakNames) do
            local filePath = pufferPath.."/"..value
            local bFileExists = ULuaExtension.Ext_FileExists(filePath)
            if bFileExists == true then
                local md5 = self:GetPakFileCorrectlyMD5(value)
                if md5 ~= nil then
                    table.insert(needSetPaks, value)
                    local md5upper = string.upper(md5)
                    table.insert(needSetPaksMd5, md5upper)
                    litePackageMgr:SetCachFileStatusByFileName(value, md5upper)
                    loginfo("LiteDownloadManager:CheckAndSetCombinedPaksToPuffer pakName:"..value.." md5:"..md5upper)
                end
            else
                loginfo("LiteDownloadManager:CheckAndSetCombinedPaksToPuffer but pak be deleted. pakName:"..value)
            end
        end

        loginfo("LiteDownloadManager:CheckAndSetCombinedPaksToPuffer needSetPaks:"..tostring(#needSetPaks))
        loginfo("LiteDownloadManager:CheckAndSetCombinedPaksToPuffer needSetPaksMd5:"..tostring(#needSetPaksMd5))
        -- litePackageMgr:CheckAndSetWholePackageStates(needSetPaks, needSetPaksMd5)
        -- self:SetNextLoginToSetCombinedPaks(nilPaks)
        self.NextLoginToSetCombinedPaksCouldBeDeleted = true
    end
end

function LiteDownloadManager:CheckAndDeleteNextLoginSetCombinedPaks()
    if self.NextLoginToSetCombinedPaksCouldBeDeleted == true then
        self.NextLoginToSetCombinedPaksCouldBeDeleted = false

        --check all file set success
        local bAllFileSetReady = false
        local combinedPakNames = self:GetNextLoginToSetCombinedPaks()
        if combinedPakNames ~= nil and #combinedPakNames > 0 then
            bAllFileSetReady = true
            for key, pakName in pairs(combinedPakNames) do
                local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(pakName)
                if fileState == false then
                    bAllFileSetReady = false
                    logerror("[LiteDownloadManager] CheckAndDeleteNextLoginSetCombinedPaks bAllFileSetReady = false, pakName:"..pakName)
                    break
                end
            end
        end

        if bAllFileSetReady then
            local nilPaks = {}
            self:SetNextLoginToSetCombinedPaks(nilPaks)
            logerror("[LiteDownloadManager] CheckAndDeleteNextLoginSetCombinedPaks SetNextLoginToSetCombinedPaks nilPaks")
        end
    end
end

function LiteDownloadManager:SetModuleDownloaded(moduleName, bDownloaded, errorCode)
    loginfo(string.format("[LiteDownloadManager] SetModuleDownloaded moduleName:%s, bDownloaded:%s", moduleName, tostring(bDownloaded)))
    ModuleDownloadState[moduleName] = 0

    if curdownlaodQuest ~= nil and curdownlaodQuest.ModuleName == moduleName then
        local reportDownloadResult = 1
        if bSuccess then
            reportDownloadResult = 2
        end
        self:DoReportFinishedDownloadEvent(moduleName, reportDownloadResult, errorCode)
        curdownlaodQuest = nil
    end

    if bDownloaded then
        ModuleDownloaded[moduleName] = true

        local childModules = self:GetRegisterRuntimeChildModules(moduleName)
        if childModules ~= nil then
            for index, childModule in ipairs(childModules) do
                local childModuleState = self:GetModuleStateByModuleName(childModule)
                if childModuleState ~= 1 then
                    self:SetModuleStateByModuleName(childModule, 1)
                    logerror("[LiteDownloadManager] SetModuleDownloaded SetModuleStateByModuleName childModule:"..childModule)
                end
            end
        end

        if self:IsHDDownloadStatus() then
            local LowModuleName = tHDRuntimeModuleName2LowModuleName[moduleName]
            if LowModuleName ~= nil then
                ModuleDownloaded[LowModuleName] = true
                self:CheckModuleAsync(LowModuleName)
            end
            local HDModuleNameList = tHDRuntimeModuleName2HDModuleName[moduleName]
            if HDModuleNameList ~= nil then
                for index, HDModuleName in pairs(HDModuleNameList) do
                    if HDModuleName then
                        ModuleDownloaded[HDModuleName] = true
                        self:CheckModuleAsync(HDModuleName)
                        loginfo("[LiteDownloadManager] drmdbg SetModuleDownloaded check ", HDModuleName)
                    end
                end
            end
        else
            self:RefreshHDDownloadCollection()
        end

        if moduleName == sHDDownloadCollection then
            for index, childModuleName in pairs(tHDDownloadCollectionModuleList) do
                ModuleDownloaded[childModuleName] = true
                self:CheckModuleAsync(childModuleName)
                loginfo("[LiteDownloadManager] drmdbg SetModuleDownloaded check ", childModuleName)
            end

            self:StartCheckModuleInfosAsync()
        end

        if self:CheckModuleNameNotRumtimeSpecial(moduleName) then
            self:MountDownloadedPak()
        end
    end

    if self:CheckModuleNameNotRumtimeSpecial(moduleName) then
        loginfo("[LiteDownloadManager] SetModuleDownloaded ResetAllDownloadedQuestIDs")
        self:ResetAllDownloadedQuestIDs()
    end
end

function LiteDownloadManager:SetModuleTotalSize(moduleName, TotalSize)
    if table.contains(ModuleNowSize, moduleName) == false then
        ModuleNowSize[moduleName] = nowSize
    else
        if nowSize > ModuleNowSize[moduleName] then
            ModuleNowSize[moduleName] = nowSize
        end
    end
end

function LiteDownloadManager:SetModuleNowSize(moduleName, nowSize)
    if table.contains(ModuleNowSize, moduleName) == false then
        ModuleNowSize[moduleName] = nowSize
    else
        if nowSize > ModuleNowSize[moduleName] then
            ModuleNowSize[moduleName] = nowSize
        end
    end
end

function LiteDownloadManager:GetPufferFileSize(PakName)
    return litePackageMgr:GetFileSize(PakName)
end

function LiteDownloadManager:GetPufferFileStateAndSize(PakName)
    return litePackageMgr:GetFileStateAndSize(PakName)
end

function LiteDownloadManager:GetHDCollectionNeedDownloadSize()
    return self:GetTotalSizeByModuleName(sHDDownloadCollection) - self:GetNowSizeByModuleName(sHDDownloadCollection)
end

function LiteDownloadManager:GetPakNamesByModuleName(ModuleName, bUseRuntimeModule)
    if bUseRuntimeModule == nil then
        bUseRuntimeModule = false
    end
    return litePackageMgr:GetPakNamesByModuleName(ModuleName, bUseRuntimeModule)
end

function LiteDownloadManager:SetCombineInfo(combinePath)
    self.combinePath = combinePath
end

function LiteDownloadManager:CalcLastVersionPakInfos()
    if self.combinePath == nil then
        loginfo("[LiteDownloadManager] CalcLastVersionPakInfos but self.combinePath is not set")
        return
    end

    self.dictLastVersionBeforeLoginPaks = nil
    self.dictLastVersionNotBeforeLoginPaks = nil
    -- loginfo("[LiteDownloadManager] CalcLastVersionPakInfos.")
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local dfFile = pufferPath.."/"..self.combinePath.."/puffer_file_info.df"
    -- local sJson = self:loadJson(jsonFile)
    -- if sJson ~= "" then
    --     -- loginfo("[LiteDownloadManager] UpdatePakRecombinePaks sJson:"..sJson)
    --     local tb = Json.decode(sJson)
    --     if tb ~= nil then
    --         self.dictLastVersionBeforeLoginPaks = tb["BeforeLogin"]
    --         self.dictLastVersionNotBeforeLoginPaks = tb["NotBeforeLogin"]
    --     end
    -- end

    -- read .df file
    self.dictLastVersionPaks = {}
    self.dictLastVersionPaksMD5 = {}
    local bExists = ULuaExtension.Ext_FileExists(dfFile)
    if bExists then
        for line in io.lines(dfFile) do
            local parts = self:split(line, "=")
            if #parts == 2 then
                local key = parts[1]:match("^%s*(.-)%s*$")
                local valueParts = self:split(parts[2], ",")
                if #valueParts == 2 then
                    local size = tonumber(valueParts[1]:match("^%s*(.-)%s*$"))
                    local md5 = valueParts[2]:match("^%s*(.-)%s*$")
                    LocalPakNameFileSize[key] = size
                    table.insert(self.dictLastVersionPaks, key)
                    self.dictLastVersionPaksMD5[key] = md5
                    -- loginfo("[LiteDownloadManager] CalcLastVersionPakInfos filename:"..key..", size:"..size..", md5:"..md5)
                end
            end
        end
    end

    local mainfestFilePath = pufferPath.."/"..self.combinePath.."/manifest.txt"
    self.dickCombineMainfestPaks, self.dickCombineMainfestPaksMD5 = self:readFileToTable(mainfestFilePath)

    self.hasBeenCalcLastVersionPakInfos = true
end

function LiteDownloadManager:GetLastVersionAllPaks()
    if self.hasBeenCalcLastVersionPakInfos ~= true then
        self:CalcLastVersionPakInfos()
    end

    if self.lastVersionAllPaks == nil then
        self.lastVersionAllPaks = {}

        if self.dictLastVersionPaks ~= nil then
            for index, pakName in ipairs(self.dictLastVersionPaks) do
                table.insert(self.lastVersionAllPaks, pakName)
            end
        end
    end

    return self.lastVersionAllPaks
end

function LiteDownloadManager:GetLastVersionPakCorrectlyMD5(PakName)
    local correctlyMD5 = nil
    if PLATFORM_ANDROID or PLATFORM_IOS or _WITH_EDITOR then
        if self.hasBeenCalcLastVersionPakInfos ~= true then
            self:CalcLastVersionPakInfos()
        end

        if self.dictLastVersionPaksMD5 ~= nil then
            correctlyMD5 = self.dictLastVersionPaksMD5[PakName]
        end
    end

    return correctlyMD5
end

function LiteDownloadManager:GetPathPakCorrectlyMD5(PakName)
    local correctlyMD5 = nil
    if PLATFORM_ANDROID or PLATFORM_IOS or _WITH_EDITOR then
        if self.hasBeenCalcLastVersionPakInfos ~= true then
            self:CalcLastVersionPakInfos()
        end

        if self.dickCombineMainfestPaksMD5 ~= nil then
            correctlyMD5 = self.dickCombineMainfestPaksMD5[PakName]
        end
    end

    return correctlyMD5
end

function LiteDownloadManager:GetAllCombineMainfestPaks()
    return self.dickCombineMainfestPaks
end

function LiteDownloadManager:loadJson(jsonFilePath)
    local file = io.open(jsonFilePath, "r")
    if not file then
        return ""
    end

    local content = file:read("*all")
    file:close()

    return content
end

-- 读取文件并存储到Table
function LiteDownloadManager:readFileToTable(filePath)
    local lines = {}
    local md5s = {}
    local file = io.open(filePath, "r")
    if not file then
        return lines
    end

    for line in file:lines() do
        if line ~= nil and line ~= ""then
            line = self:strip(line)
            local splitRet = self:split(line, ",")
            if splitRet ~= nil and #splitRet >= 2 then
                local pakName = splitRet[1]
                local pakMD5 = splitRet[2]
                if string.sub(pakName, -4) == ".pak" then
                    table.insert(lines, pakName)
                    md5s[pakName] = pakMD5
                    -- logerror("LiteDownloadManager:readFileToTable: pakName:"..pakName.." MD5:"..pakMD5)
                end
            end
        end
    end

    file:close()
    return lines, md5s
end


function LiteDownloadManager:strip(str)
    str = str:gsub("^%s+", "")
    str = str:gsub("%s+$", "")
    return str
end

function LiteDownloadManager:SetBGDEnabled(bEnable)
    litePackageMgr:SetBGDEnabled(bEnable)
    if PLATFORM_ANDROID then
        if bEnable then
            if Module.Permission:IfPermissionGranted(DFPermissionType.Notifications) then
                loginfo("[LiteDownloadManager] ReqPermisson has Notifications permission")
            else
                if not Facade.ConfigManager:GetBoolean("ReqNotificationPermisson", false) then
                    loginfo("[LiteDownloadManager] ReqPermisson no Notifications permission and req")
                    if Module.Permission:IsFirstTimeRequest(DFPermissionType.Notifications) then
                        Facade.ConfigManager:SetBoolean("ReqNotificationPermisson", true)
                        Module.Permission:AsyncRequestPermissions(DFPermissionType.Notifications)
                    end
                else
                    loginfo("[LiteDownloadManager] ReqPermisson no Notifications permission")
                end
            end
        end
    end
end



--- pak fix ---
--- 设置Pak资源深度修复标记位
function LiteDownloadManager:SetAllPakFixDeep(bNeedFix)
    if PLATFORM_ANDROID or PLATFORM_IOS then
        Facade.ConfigManager:SetBoolean(FLAG_LITE_ALL_PAK_FIX_DEEP, bNeedFix)
        logerror("[LiteDownloadManager] SetAllPakFixDeep bNeedFix:"..tostring(bNeedFix))
    end
end

function LiteDownloadManager:GetAllPakFixDeep()
    local ret = false
    if PLATFORM_ANDROID or PLATFORM_IOS then
        ret = Facade.ConfigManager:GetBoolean(FLAG_LITE_ALL_PAK_FIX_DEEP, false)
    end

    logerror("[LiteDownloadManager] GetAllPakFixDeep ret:"..tostring(ret))
    return ret
end

--- delete ---

--- 设置下次启动要删除的模块列表，模块需要是非高清。【高清不通过下载管理删除，只能通过高清的方式删除（策划SHE2版本需求要求如此）】
---@param moduleNames table 要删除的模块列表
function LiteDownloadManager:SetDeletePaksByModuleNames(moduleNames)
    loginfo("[LiteDownloadManager] SetDeletePaksByModuleNames SetDeletePaksByModuleNames. #moduleNames:"..tostring(#moduleNames))
    if moduleNames == nil or #moduleNames <= 0 then
        return
    end

    if PLATFORM_ANDROID or PLATFORM_IOS then
        local cloudDeleteModuleName = ""
        for index, NeedCheckModuleName in ipairs(moduleNames) do
            local moduleName = NeedCheckModuleName
            local found = string.find(NeedCheckModuleName, "HDRuntime")
            if found then
                moduleName = string.sub(NeedCheckModuleName, 10, string.len(NeedCheckModuleName))
            end

            local tModuleConf = self:GetDownloadConfigByModuleName(moduleName)
            if tModuleConf ~= nil then --and tModuleConf.IsDeletable == 0
                if cloudDeleteModuleName == "" then
                    cloudDeleteModuleName = moduleName
                else
                    cloudDeleteModuleName = cloudDeleteModuleName ..","..moduleName
                end
                logerror("[LiteDownloadManager] SetDeletePaksByModuleNames wait to delete moduleName:"..moduleName)
            else
                logerror("[LiteDownloadManager] SetDeletePaksByModuleNames cloud't delete moduleName:"..moduleName)
            end
        end

        Facade.ConfigManager:SetString(FLAG_LITE_DELETE_PAKS_MODULE, cloudDeleteModuleName)
    end
end

--- 开始删除本地被标记的资源
function LiteDownloadManager:StartDeletePaksByModuleNames()
    if PLATFORM_ANDROID or PLATFORM_IOS then
        local needDeleteModulesStr = Facade.ConfigManager:GetString(FLAG_LITE_DELETE_PAKS_MODULE, "")
        if needDeleteModulesStr ~= "" then
            local needDeleteModules = self:split(needDeleteModulesStr, ",")
            Facade.ConfigManager:SetString(FLAG_LITE_DELETE_PAKS_MODULE, "")
            local combinedPakNames = self:GetNextLoginToSetCombinedPaks()

            local waitToDeletePaks = {}
            for index, moduleName in ipairs(needDeleteModules) do
                local paks = self:GetPakNamesByModuleName(moduleName, true)
                if paks ~= nil and #paks > 0 then
                    for _, pakName in pairs(paks) do
                        if string.find(pakName, "%.pak$") then -- 只处理pak文件
                            local bisInCombined = false
                            if combinedPakNames ~= nil and #combinedPakNames > 0 then
                                for key, combinePakName in pairs(combinedPakNames) do
                                    if combinePakName == pakName then
                                        bisInCombined = true
                                        break
                                    end
                                end
                            end

                            if bisInCombined == false then
                                local pakID = pakName:match("pakchunk(%d+)")
                                if table.contains(DICT_COULD_DELETE_PAKS_IDS, pakID) then
                                    table.insert(waitToDeletePaks, pakName)
                                    logerror("[LiteDownloadManager] StartDeletePaksByModuleNames wait to delete pakName:"..pakName)
                                else
                                    logerror("[LiteDownloadManager] StartDeletePaksByModuleNames ignore pakName:"..pakName)
                                end
                            end
                        elseif string.find(pakName, "%.bin") then
                            --视频文件允许删除
                            table.insert(waitToDeletePaks, pakName)
                            logerror("[LiteDownloadManager] StartDeletePaksByModuleNames wait to delete media res:"..pakName)
                        end
                    end
                end
            end

            if #waitToDeletePaks > 0 then
                for index, pakName in ipairs(waitToDeletePaks) do
                    local deleteRet = self:DeleteFileByPakName(pakName)
                    logerror("[LiteDownloadManager] StartDeletePaksByModuleNames delete pakName:"..pakName..", result:"..tostring(deleteRet))
                end
            end
        end
    end
end


--- 设置删除高清标记位，即切换至低清
function LiteDownloadManager:GetHDResourceSize()
    local retNowSize = 0
    if PLATFORM_ANDROID or PLATFORM_IOS then
        local AllNewestFileNames = self:GetAllPakNames()
        local newestPakSet = {}
        for _, newestPakName in pairs(AllNewestFileNames) do
            if string.find(newestPakName, "%.pak$") then -- 只处理pak文件
                newestPakSet[newestPakName] = true
            end
        end

        local bIsShipping = UGameVersionUtils.IsShipping()
        local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
        local localPakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
        for _, localPakFile in pairs(localPakFiles) do
            if localPakFile then
                local shortPakName = localPakFile:match("[^/\\]+$")
                if shortPakName and newestPakSet[shortPakName] == true then
                    local isHDPak = false
                    local pakIndex = shortPakName:match("pakchunk%d+optional?")
                    if pakIndex ~= nil then
                        -- 是高清资源
                        isHDPak = true
                    end

                    if isHDPak == true then
                        local fileState, nowSize, totalSize = self:GetPufferFileStateAndSize(shortPakName)
                        if fileState == true then
                            retNowSize = retNowSize + nowSize
                        end
                    end
                end
            end
        end
    end

    logerror("[LiteDownloadManager] GetHDResourceSize() retNowSize:"..tostring(retNowSize))
    return retNowSize
end

--- 设置删除高清标记位，即切换至低清
function LiteDownloadManager:SetDeleteHDRes(bNeedDelete)
    if PLATFORM_ANDROID or PLATFORM_IOS then
        Facade.ConfigManager:SetBoolean(FLAG_LITE_DELETE_HD, bNeedDelete)
        Facade.ConfigManager:SetUserBoolean("UserNeedCheckAndDownloadHDSHE2", false)
    end
end

--- 检测并开始删除本地高清资源
function LiteDownloadManager:CheckAndStartDeleteHDRes()
    if PLATFORM_ANDROID or PLATFORM_IOS then
        local bNeedDeletHD = Facade.ConfigManager:GetBoolean(FLAG_LITE_DELETE_HD, false)
        loginfo(string.format("[LiteDownloadManager] CheckAndStartDeleteHDRes bNeedDeletHD:%s", tostring(bNeedDeletHD)))

        if bNeedDeletHD == true then
            self:SetDeleteHDRes(false)
            self:SetHDDownloadFlagStr(false)

            Facade.ConfigManager:SetBoolean("LITE_HAS_BEEN_DELETE_HD", true)
            -- clear hd res
            local AllNewestFileNames = self:GetAllPakNames()
            local newestPakSet = {}
            for _, newestPakName in pairs(AllNewestFileNames) do
                if string.find(newestPakName, "%.pak$") then -- 只处理pak文件
                    newestPakSet[newestPakName] = true
                end
            end

            local bIsShipping = UGameVersionUtils.IsShipping()
            local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
            local localPakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
            for _, localPakFile in pairs(localPakFiles) do
                if localPakFile then
                    local shortPakName = localPakFile:match("[^/\\]+$")
                    if shortPakName and newestPakSet[shortPakName] == true then
                        local bCounldDelete = false
                        local pakIndex = shortPakName:match("pakchunk%d+optional?")
                        if pakIndex ~= nil then
                            -- 是高清资源
                            bCounldDelete = true
                            loginfo(string.format("[LiteDownloadManager] CheckAndStartDeleteHDRes is HD:"..shortPakName))
                        else
                            -- delete CrackedPVSHD
                            local bFoundSpecial = string.find(shortPakName, "pakchunk116")
                            if bFoundSpecial then
                                bCounldDelete = true
                                loginfo(string.format("[LiteDownloadManager] CheckAndStartDeleteHDRes is HD:"..shortPakName))
                            else
                                loginfo(string.format("[LiteDownloadManager] CheckAndStartDeleteHDRes not HD:"..shortPakName))
                            end
                        end

                        if bCounldDelete == true then
                            loginfo(string.format("[LiteDownloadManager] CheckAndStartDeleteHDRes delete HD shortPakName: %s", shortPakName))
                            self:DeleteFileByPakName(shortPakName)
                        end
                    end
                end
            end
        end
    end
end

--- 重组相关 ----

function LiteDownloadManager:CheckPakNameIsInCombined(PakName)
    local bisInCombined = false

    self.combinedPakNames = self:GetNextLoginToSetCombinedPaks(true)
    if self.combinedPakNames == nil then
        self.combinedPakNames = {}
    end

    for key, combinedPakName in pairs(self.combinedPakNames) do
        if combinedPakName == PakName then
            bisInCombined = true
            break
        end
    end

    if self.nowCombineOldPak == PakName then
        bisInCombined = true
    end

    return bisInCombined
end

---@param combineInfo table 需要重组的信息：versionPakPath、combinePakPath、outPakPath、oldVersionPak、patchPakName、outPakName
function LiteDownloadManager:CheckAndSetCombineInfoToQueue(combineInfo)
    if combineInfo == nil or #combineInfo < 6 then
        return
    end

    if self.CombinePakWithPatchPakPathAsyncQueue == nil then
        self.CombinePakWithPatchPakPathAsyncQueue = {}
    end

    if PLATFORM_ANDROID or PLATFORM_IOS or IsInEditor() then
        local outPakName = combineInfo[6]
        loginfo("[LiteDownloadManager] CheckAndSetCombineInfoToQueue: "..outPakName)

        local hasBeenInCombineQueue = false

        local bHasBeenPakCombined = self:CheckPakNameIsInCombined(outPakName)
        if bHasBeenPakCombined == true then
            hasBeenInCombineQueue = true
            logerror("[LiteDownloadManager] CheckAndSetCombineInfoToQueue CheckPakNameIsInCombined bHasBeenPakCombined = true.")
        end

        if outPakName == self.nowCombineOutPak then
            hasBeenInCombineQueue = true
            loginfo("[LiteDownloadManager] CheckAndSetCombineInfoToQueue outPakName == self.nowCombineOutPak: ")
        else
            for index, infos in ipairs(self.CombinePakWithPatchPakPathAsyncQueue) do
                loginfo("[LiteDownloadManager] CheckAndSetCombineInfoToQueue infos[6]: "..infos[6])
                if outPakName == infos[6] then
                    hasBeenInCombineQueue = true
                end
            end
        end

        if hasBeenInCombineQueue == false then
            table.insert(self.CombinePakWithPatchPakPathAsyncQueue, combineInfo)
        end
    end

    self:CheckCombinePakWithPatchPakQuest()
end

function LiteDownloadManager:CheckCombinePakWithPatchPakQuest()
    loginfo("[LiteDownloadManager] CheckCombinePakWithPatchPakQuest.")

    if self.nowCombineOutPak == nil then
        if #self.CombinePakWithPatchPakPathAsyncQueue > 0 then
            local infos = self.CombinePakWithPatchPakPathAsyncQueue[1]
            if infos ~= nil and #infos >= 6 then
                table.remove(self.CombinePakWithPatchPakPathAsyncQueue, 1)

                self.nowCombineOldPak = infos[4]
                self.nowCombinePatchPak = infos[5]
                self.nowCombineOutPak = infos[6]

                local inMd5 = self:GetLastVersionPakCorrectlyMD5(infos[4])
                local patchMd5 = self:GetPathPakCorrectlyMD5(infos[5])
                local outMd5 = self:GetPakFileCorrectlyMD5(infos[6])

                local bHasNil =false
                if inMd5 == nil or patchMd5 == nil or outMd5 == nil then
                    bHasNil = true
                    if inMd5 == nil then
                        inMd5 = ""
                    end
                    if patchMd5 == nil then
                        patchMd5 = ""
                    end
                    if outMd5 == nil then
                        outMd5 = ""
                    end
                end
                self:SetPakCombinedCount(infos[4])
                local pakWorker = self:MergePaksAsyncChecked(infos[1], inMd5, infos[2], patchMd5, infos[3], outMd5)
                -- bUPakWorker.FMergePaksCompleteDynamic:Add(OnCombineProgress)
                if pakWorker ~= nil then
                    logerror("[LiteDownloadManager] CheckCombinePakWithPatchPakQuest: nowCombineOutPak: "..self.nowCombineOutPak)
                    self.nowMergeBeginTime = os.time()
                    pakWorker.OnMergeComplete:Add(self.OnCombineResult, self)
                end
            end
        end
    end
end

function LiteDownloadManager:OnCombineResult(ErrCode, SrcPakFile, PatchPakFile, OutputPakFile, OutputPakFileMD5)
    local str = string.format("[LiteDownloadManager] OnCombineResult OnCombineResult:ErrCode:%s, OutputPakFile:%s, OutputPakFileMD5:%s", tostring(ErrCode), OutputPakFile, OutputPakFileMD5)
    logerror(str)
    if self.combinedPakNames == nil then
        self.combinedPakNames = self:GetNextLoginToSetCombinedPaks()
    end

    if self.combinedPakNames == nil then
        self.combinedPakNames = {}
    end

    local srcPakName = string.match(SrcPakFile, "([^/]+)$")
    local patchPakName = string.match(PatchPakFile, "([^/]+)$")
    self:DeleteFileByPakName(srcPakName)
    local patchPakNameFinal = self.combinePath.."/"..patchPakName
    self:DeleteFileByPakName(patchPakNameFinal)

    --check md5 and delete file
    local bNeedReDownlaod = true
    if ErrCode == 0 then
        local correctlyMD5 = self:GetPakFileCorrectlyMD5(self.nowCombineOutPak)
        local md5Check = string.format("[LiteDownloadManager] OnCombineResult correctlyMD5:%s, OutputPakFileMD5:%s", correctlyMD5, OutputPakFileMD5)
        loginfo(md5Check)

        if correctlyMD5 == OutputPakFileMD5 then
            bNeedReDownlaod = false
            table.insert(self.combinedPakNames, self.nowCombineOutPak)
            self:CheckAndSetCombinePakNames()
            self:SetPakCheckMD5Result(self.nowCombineOutPak)
        end
    else
        --delete new pak if exists
        -- local bExists = ULuaExtension.Ext_FileExists(OutputPakFile)
        -- if bExists then
        --     local pakName = OutputPakFile:match("([^/\\]+)$")
        --     local deleteRet = self:DeleteFileByPakName(pakName)
        --     logerror("[LiteDownloadManager] OnCombineResult delete pakName:"..pakName..", result:"..tostring(deleteRet))
        -- end
    end

    local beiginTiemStr = ""
    local endTiemStr = ""

    local mergeEndTime = os.time()
    if self.nowMergeBeginTime ~= nil then
        beiginTiemStr = tostring(self.nowMergeBeginTime)
    end
    if mergeEndTime ~= nil then
        endTiemStr = tostring(mergeEndTime)
    end
    self:ReportPakMergeEvent(1, ErrCode, self.nowCombineOutPak, OutputPakFileMD5, beiginTiemStr, endTiemStr,"")

    -- if bNeedReDownlaod then
    --     table.insert(self.PakNamesToDownloadQueue, self.nowCombineOutPak)
    --     Module.LitePackage.Config.evtCurrentDownloadQuestNeedMoveNext:Invoke()
    -- end

    local outPakName = self.nowCombineOutPak
    self.nowCombineOutPak = nil

    if #self.CombinePakWithPatchPakPathAsyncQueue > 0 then
        self:CheckCombinePakWithPatchPakQuest()
    end

    Module.LitePackage.Config.evtOnPakCombineResult:Invoke(ErrCode, outPakName)
end

function LiteDownloadManager:CheckAndSetCombinePakNames()
    -- cord all combined paknames
    if self.combinedPakNames ~= nil and #self.combinedPakNames > 0 then
        loginfo("[LiteDownloadManager] CheckAndSetCombinePakNames SetCombinePakNames num:"..tostring(#self.combinedPakNames))
        self:SetCombinePakNames(self.combinedPakNames)

        --mark for next login to set to puffer
        self:SetNextLoginToSetCombinedPaks(self.combinedPakNames)
    else
        loginfo("[LiteDownloadManager] CheckAndSetCombinePakNames SetCombinePakNames failed. num:"..tostring(#self.combinedPakNames))
    end
end

function LiteDownloadManager:SetPakCheckMD5Result(newPakName)
    self:CheckAndInitPakCheckMD5Result()

    if self.DictPakMD5Checked == nil then
        logerror("[LiteDownloadManager] SetPakCheckMD5Result self.DictPakMD5Checked is nil.")
        return
    end

    table.insert(self.DictPakMD5Checked, newPakName)
    local info = ""
    for k, pakName in ipairs(self.DictPakMD5Checked) do
        if info == "" then
            info = pakName
        else
            info = info..","..pakName
        end
    end

    loginfo("[LiteDownloadManager] SetPakCheckMD5Result:"..info)
    Facade.ConfigManager:SetString(FLAG_LITE_PAK_MD5_CHECKED, info or "")
end

function LiteDownloadManager:CheckAndInitPakCheckMD5Result()
    if self.DictPakMD5Checked == nil then
        self.DictPakMD5Checked = {}

        local paksStr = Facade.ConfigManager:GetString(FLAG_LITE_PAK_MD5_CHECKED, "")
        loginfo("[LiteDownloadManager] InitPakCheckMD5Result:"..paksStr)
        local pakindexs = self:split(paksStr, ",")
        if pakindexs ~= nil and #pakindexs > 0 then
            for key, value in pairs(pakindexs) do
                table.insert(self.DictPakMD5Checked, value)
            end
        end
    end
end


function LiteDownloadManager:GetPakHasBeenCheckMD5(needCheckName)
    self:CheckAndInitPakCheckMD5Result()

    if self.DictPakMD5Checked == nil then
        logerror("[LiteDownloadManager] GetPakHasBeenCheckMD5 self.DictPakMD5Checked is nil.")
        return true
    end

    local ret = false
    for k, pakName in ipairs(self.DictPakMD5Checked) do
        if needCheckName == pakName then
            ret = true
        end
    end

    return ret
end


function LiteDownloadManager:AddNetWorkControlEvent()
    if Module.NetworkControl:GetNetworkControlEvent() and not self.onNetworlCtrlChange then
        self.onNetworlCtrlChange = Module.NetworkControl:GetNetworkControlEvent():Add(CreateCPlusCallBack(self.OnNetworkCtrlChange, self))
        logerror("[LiteDownloadManager] AddNetWorkControlEvent.")
    end
end

function LiteDownloadManager:OnNetworkCtrlChange(type, ret)
    if ret then
        if IsBuildRegionCN() then
            self:ResetMapleMaxSpeed(CN_LIMITE_DOWNLOAD_SPEED)
            logerror("[LiteDownloadManager] OnNetworkCtrlChange CN PufferMaxSpeed:" .. tonumber(CN_LIMITE_DOWNLOAD_SPEED))
        else
            self:ResetMapleMaxSpeed(GLOBAL_LIMITE_DOWNLOAD_SPEED)
            logerror("[LiteDownloadManager] OnNetworkCtrlChange GLOBAL PufferMaxSpeed:" .. tonumber(GLOBAL_LIMITE_DOWNLOAD_SPEED))
        end
    else
        -- 恢复网络限速
        local ins = UDFMGameMaple.GetMapleIns(GetGameInstance())
        if ins then
            if ins:IsMapleInited() then
                local speed = ins:GetCustomData("PufferMaxSpeed")
                if speed ~= "" then
                    logerror("[LiteDownloadManager] OnNetworkCtrlChange PufferMaxSpeed:" .. speed)
                    local speedNumber = tonumber(speed)
                    self:ResetMapleMaxSpeed(speedNumber)
                end
            end
        end
    end
end


return LiteDownloadManager
