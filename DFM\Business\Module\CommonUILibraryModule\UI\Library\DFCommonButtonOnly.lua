----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonUILibrary)
local orilogerror = logerror
logerror = function(...)
    if not VersionUtil.IsShipping() then
        orilogerror(...)
    end
end
----- LOG FUNCTION AUTO GENERATE END -----------

local EButtonTouchMethod = import "EButtonTouchMethod"

--------------------------------------------------------------------------
--- Library : DFCommonButtonOnly
--- 复杂基类控件
--- *必须包含名称为DFCommonButton的复杂基类按钮
--------------------------------------------------------------------------
---@class DFCommonButtonOnly : LuaUIBaseView
DFCommonButtonOnly = ui('DFCommonButtonOnly')
EButtonState = import"EButtonState"
require("DFM.YxFramework.Managers.Resource.Util.ResImageUtil")
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EButtonState = import "EButtonState"
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"

function DFCommonButtonOnly:Ctor()
    if DFHD_LUA == 1 then
        self.wtCommonBtn = self:Wnd("DFCommonButton_PCOnly", UIWidgetBase)
    end
    if self.wtCommonBtn == nil then
        self.wtCommonBtn = self:Wnd("DFCommonButton", UIWidgetBase)
    end
    UIThemeUtil.CheckIfAutoApplyTheme(self.wtCommonBtn)

    self.wtShadowImg = self.wtCommonBtn:Wnd("DFThemeShadowImage", UIImage)
    if self.wtShadowImg then
        UIThemeUtil.CheckIfAutoApplyTheme(self.wtShadowImg)
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 设置Button是否可以聚焦
function DFCommonButtonOnly:SetCanbeFocusable(bInFocusable)
    self:SetCppValue("bIsFocusable", bInFocusable)
    self.wtCommonBtn.ButtonInternal:SetCppValue("IsFocusable", bInFocusable)
end
--END MODIFICATION 

--BEGIN MODIFICATION @ VIRTUOS : Input extension
function DFCommonButtonOnly:SetDisplayInputAction(displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
    bShowNoneKey = setdefault(bShowNoneKey, true)
    keyIconHeight = setdefault(keyIconHeight, 0)
    if DFHD_LUA == 1 then
        self.wtKeyIconBox = self.wtCommonBtn:Wnd("KeyIcon", HDKeyIconBox)
        if self.wtKeyIconBox then
            --设置是否只在Gamepad上显示
            self.wtKeyIconBox:SetOnlyDisplayOnGamepad(bOnlyDisplayOnGamepad)
            --设置当前KeyIcon绑定的Action
            self.wtKeyIconBox:InitByDisplayInputActionName(displayInputAction, bShowNoneKey, keyIconHeight, true)
        else
            logerror('Cant find KeyIcon named [KeyIcon], Failed to DFCommonButtonOnly:SetDisplayInputAction(displayInputAction,bShowNoneKey, keyIconHeight)', " self:", self)
        end
    end
end

-- 自动检测是否是长按并显示长按图标
function DFCommonButtonOnly:SetDisplayInputActionWithLongPress(actionHandle, caller, displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
    if DFHD_LUA == 1 then
        self:SetDisplayInputAction(displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
        self:SetLongPressBinding(actionHandle, caller, displayInputAction)
    end
end
--END MODIFICATION

function DFCommonButtonOnly:_OnButtonStateChanged(inState)
    if IsHD() then
        if self.wtKeyIconBox then
            self:_RefreshKeyIconVisibility()
        end
    end
end

function DFCommonButtonOnly:RemoveButtonStateChangedEvent()
    if IsHD() then
        if self.wtCommonBtn then
            self:RemoveEvent("OnButtonStateChanged")
        end
    end
end

function DFCommonButtonOnly:OnInitExtraData(tabUIContentData)
    self.tabUIContentData = tabUIContentData
end

function DFCommonButtonOnly:OnOpen()
    if self.tabUIContentData then
        self.tabUIContentData:UpdateTabByLuaTabUIContent(self)
    end

    if IsHD() then
        if self.wtCommonBtn then
            self:Event("OnButtonStateChanged", self._OnButtonStateChanged, self)
        end
    end
end


function DFCommonButtonOnly:Event(delegateName, fCallback, caller, ...)
    if self.wtCommonBtn then
        logframe('Start binding!!! DFCommonButtonOnly:Event(delegateName, fCallback, caller, ...)', delegateName, fCallback, caller, " self:", self)
        self.wtCommonBtn:Event(delegateName, fCallback, caller,  ...)
    else
        logerror('Cant find Btn named [DFCommonButton], Failed to DFCommonButtonOnly:Event(delegateName, fCallback, caller, ...)', " self:", self)
    end
end

function DFCommonButtonOnly:RemoveEvent(delegateName)
    if self.wtCommonBtn then
        logframe('Start Removing!!! DFCommonButtonOnly:RemoveEvent(delegateName)', delegateName, " self:", self)
        self.wtCommonBtn:RemoveEvent(delegateName)
    else
        logerror('Cant find Btn named [DFCommonButton], Failed to DFCommonButtonOnly:RemoveEvent(delegateName)', " self:", self)
    end
end

function DFCommonButtonOnly:SetStretchState(eStretchState)
    if self.wtCommonBtn then
        self._wtScaleBox = self.wtCommonBtn:Wnd("DFScaleBox_0", UIWidgetBase)
        if self._wtScaleBox then
            logframe('Start Removing!!! DFCommonButtonOnly:SetStretchState(eStretchState)', eStretchState, " self:", self)
            self._wtScaleBox:SetStretch(eStretchState)
        else
            logerror('Cant find _wtScaleBox , Failed to DFCommonButtonOnly:SetStretchState(eStretchState)', " self:", self)
        end
    else
        logerror('Cant find Btn named [DFCommonButton], Failed to DFCommonButtonOnly:SetStretchState(eStretchState)', " self:", self)
    end
end

function DFCommonButtonOnly:SetMainTitle(text)
    if text then
        self.btnMainTitle = text
        if self.BP_SetMainTitle then
            if type(text) ~= "table" then
                self:BP_SetMainTitle(text)
            end
        else
            if self.wtCommonBtn and self.wtCommonBtn.SetMainTitleText4AllState then
                logframe('self.wtCommonBtn:SetMainTitleText4AllState(text)', text)
                self.wtCommonBtn:SetMainTitleText4AllState(text)
            else
                logerror('Cant find Btn named [DFCommonButton], Failed to DFCommonButtonOnly:SetMainTitleText4AllState(text)')
            end
        end
    end
end

-- 设置按钮禁用态（不可交互）
function DFCommonButtonOnly:SetBtnEnable(bEnable)
    if self.wtCommonBtn and self.wtCommonBtn.SetIsEnabled then
        self.wtCommonBtn:SetIsEnabled(bEnable)
    end

end

function DFCommonButtonOnly:SetIsLocked(bLocked)
    if self.wtCommonBtn and self.wtCommonBtn.SetIsLocked ~= nil then
        logframe('self.wtCommonBtn:SetIsLocked(bLocked)', bLocked)
        self.wtCommonBtn:SetIsLocked(bLocked)
    else
        logerror('Cant find Btn named [DFCommonButtonOnly], Failed to DFCommonButtonOnly:SetIsLocked(bLocked)', bLocked)
    end
end

function DFCommonButtonOnly:SetIsEnabled(bEnable)
    if isvalid(self.wtCommonBtn) and self.wtCommonBtn.SetIsEnabled then
        self.wtCommonBtn:SetIsEnabled(bEnable)
    end
end

function DFCommonButtonOnly:SetIsEnabledStyle(bEnableStyle)
    if isvalid(self.wtCommonBtn) and self.wtCommonBtn.SetIsEnabledStyle then
        self.wtCommonBtn:SetIsEnabledStyle(bEnableStyle)
    end
end

function DFCommonButtonOnly:AsyncSetImageIconPathAllState(imagePath, bAutoResize)
    return ResImageUtil.AsyncLoadImgObjByPath(imagePath, bAutoResize, self.SetImageIconAllState, self)
end

function DFCommonButtonOnly:SetImageIconAllState(imageAsset, bAutoResize)
    bAutoResize = setdefault(bAutoResize, true)
    -- if self.bSetImgByBP then
    --     self:SetCppValue("bSetImgByBP", false)
    -- end
    if self.wtCommonBtn then
        if imageAsset then
            local classname = LAI.GetObjectClassName(imageAsset)
            if classname == "PaperSprite" then
                self.wtCommonBtn:SetImageIconFromAtlasInterface4AllState(imageAsset, bAutoResize)
            elseif classname == "Texture2D" then
                self.wtCommonBtn:SetImageIcon4AllState(imageAsset, bAutoResize)
            else
                if isvalid(UIImage.IconWaitingObj) and LAI.GetObjectClassName(imageAsset) == "Texture2D" then
                    self.wtCommonBtn:SetImageIcon4AllState(imageAsset, bAutoResize)
                end
                logbox('DFCommonButtonOnly:SetImageIconAllState 请检查资源格式是否正确———— classname:', classname, 'imageAsset:', imageAsset, debug.traceback())
            end
        end
    end
end

function DFCommonButtonOnly:SetImageIconState(paperSpriteRes, state)
    if self.wtCommonBtn then
        self.wtCommonBtn:SetImageIconFromAtlasInterface4ButtonState(paperSpriteRes, state, true)
    end
end

function DFCommonButtonOnly:SetIsShowIcon(bInShowIcon)
    if self.wtCommonBtn then
        self.wtCommonBtn:SetIsShowIcon(bInShowIcon)
    end
end

function DFCommonButtonOnly:RemoveEvent(event)
    if self.wtCommonBtn then
        self.wtCommonBtn:RemoveEvent(event)
    end
end

function DFCommonButtonOnly:OnClose()
end

function DFCommonButtonOnly:SetPressedSoundName(pressedSoundName)
    if self.wtCommonBtn.SetPressedSoundName then
        self.wtCommonBtn:SetPressedSoundName(pressedSoundName)
    end
end

function DFCommonButtonOnly:SetHoveredSoundName(hoveredSoundName)
    if self.wtCommonBtn.SetHoveredSoundName then
        self.wtCommonBtn:SetHoveredSoundName(hoveredSoundName)
    end
end

function DFCommonButtonOnly:SetV1S3ButtonStyle(style)
    if self.wtCommonBtn then
        self.wtCommonBtn:SetStyle(style)
    end
end

function DFCommonButtonOnly:SetV1S3SubTitle(subTitle)
    if self.wtCommonBtn then
        self.wtTextBlockSub = self.wtCommonBtn:Wnd("TextBlockSub", UITextBlock)
        if self.wtTextBlockSub then
            self.wtTextBlockSub:SetText(subTitle)
        end
    end
end

function DFCommonButtonOnly:SetV1S3TextIcon(TextIconPath)
    if self.wtCommonBtn then
        self.wtTextIcon = self.wtCommonBtn:Wnd("TextIcon", UIImage)
        if self.wtTextIcon then
            self.wtTextIcon:AsyncSetImagePath(TextIconPath)
        end
    end
end

function DFCommonButtonOnly:OnShowBegin()
    if self.wtCommonBtn and self.wtCommonBtn.OnShowBegin then
        self.wtCommonBtn:OnShowBegin()
    end

        if IsHD() then
        self.wtKeyIconBox = self.wtCommonBtn:Wnd("KeyIcon", HDKeyIconBox)
        if self.wtKeyIconBox then
            self.wtKeyIconBox:Event("OnIconStateChanged", self._OnIconStateChanged, self)
            self._wantKeyIconVisible = true
            self:_RefreshKeyIconVisibility()
        end
        -- if self.wtKeyIconBox then
        --     if WidgetUtil.IsGamepad() then
        --         self.wtKeyIconBox:SelfHitTestInvisible()
        --     else
        --         self.wtKeyIconBox:Collapsed()
        --     end
        -- end
    end
end

function DFCommonButtonOnly:OnShow()
    if self.wtCommonBtn and self.wtCommonBtn.OnShowBegin then
        self.wtCommonBtn:OnShow()
    end
end

function DFCommonButtonOnly:OnHideBegin()
    if self.wtCommonBtn and self.wtCommonBtn.OnShowBegin then
        self.wtCommonBtn:OnHideBegin()
    end
end

function DFCommonButtonOnly:OnHide()
    if self.wtCommonBtn and self.wtCommonBtn.OnShowBegin then
        self.wtCommonBtn:OnHide()
    end
end

function DFCommonButtonOnly:SetPreciseTap()
    if self.wtCommonBtn and self.wtCommonBtn.ButtonInternal then
        self.wtCommonBtn.ButtonInternal:SetCppValue("TouchMethod", EButtonTouchMethod.PreciseTap)
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 获取按钮Activate状态
function DFCommonButtonOnly:GetActivatedState()
    if self.wtCommonBtn then
        return self.wtCommonBtn:GetActivatedState()
    end
    return false
end
-- 获取按钮的KeyIcon
function DFCommonButtonOnly:GetKeyIconWidget()
    local _wtKeyIconBox = nil

    if self.wtCommonBtn then
        _wtKeyIconBox = self.wtCommonBtn:Wnd("KeyIcon", HDKeyIconBox)
    end

    return _wtKeyIconBox
end

function DFCommonButtonOnly:ButtonClick()
    if self.wtCommonBtn then
        self.wtCommonBtn:HandleClicked()
    end
end

--长按显示绑定
function DFCommonButtonOnly:SetLongPressBinding(ActionHandle, caller, actionName)
    if not actionName then
        return
    end
    -- 处理非常按按键提示
    -- new: 不做这个判定，否则在键鼠模式下绑定的情况就会很难处理
    -- local inputMonitor = Facade.UIManager:GetInputMonitor()
    -- local mapping = inputMonitor:GetBestKeyMappingForDisplayAction(actionName)
    -- if not mapping.bHold then
    --     self.wtKeyIconBox:BP_ShowHoldProgressBarTips(false)
    --     if self._HoldInputHandle~=-1 then
    --         self:RemoveHoldInputActionBinding(self._HoldInputHandle)
    --         self._HoldInputHandle = nil
    --     end
    --     return
    -- end
    -- 处理常按按键提示
    if ActionHandle then
        self._HoldInputHandle = ActionHandle
        if self.wtKeyIconBox then
            self.wtKeyIconBox:BP_ShowHoldProgressBarTips(true)
            caller:AddHoldInputActionProgressedBinding(ActionHandle, self.OnConfirmBtnInLongPressing, self)
            caller:AddHoldInputActionReleaseBinding(ActionHandle, self.OnConfirmBtnLongPressFinished, self)
        end
    end
end

function DFCommonButtonOnly:OnConfirmBtnInLongPressing(Percent)
    if self.wtKeyIconBox then
        self.wtKeyIconBox:BP_UpdateProgressBar(Percent) 
    end
end

function DFCommonButtonOnly:OnConfirmBtnLongPressFinished()
    if self.wtKeyIconBox then
        self.wtKeyIconBox:BP_UpdateProgressBar(0)
    end
end

function DFCommonButtonOnly:ToggleKeyIconInputChangedEvent(bOpenInputChangedEvent)
    if not IsHD() then
        return 
    end

    if self.wtKeyIconBox then
        self.wtKeyIconBox:_ToggleInputChangedEvent(bOpenInputChangedEvent)
    end
end

-- 设置Button中KeyIcon的显示和隐藏，在关闭KeyIcon热切后生效
function DFCommonButtonOnly:SetKeyIconVisibility(bShow)
    if not IsHD() then
        return 
    end

    if self.wtKeyIconBox then
        if bShow then
            -- self.wtKeyIconBox:SelfHitTestInvisible()
            self._wantKeyIconVisible = true
        else
            -- self.wtKeyIconBox:Collapsed()
            self._wantKeyIconVisible = false
        end
        self:_RefreshKeyIconVisibility()
    end
end
--END MODIFICATION

function DFCommonButtonOnly:_OnIconStateChanged()
    if not self._refreshingIcon then --防止重入
        self:_RefreshKeyIconVisibility()
    end
end

function DFCommonButtonOnly:SetDeactivatedKeyIconIsHidden(bHidden)
    if DFHD_LUA == 1 then
        self._DeactivatedKeyIconIsHidden = bHidden
    end
end

-- 之所以这么做是因为重构在外部加了padding，即使KeyIconBox内部Collapse了，还是会占用一定的空间
-- 另外要区分两种情况，一种是真Disable之后Icon隐层，一种是假Disable之后Incon灰态
function DFCommonButtonOnly:_RefreshKeyIconVisibility()
    if self.wtKeyIconBox then
        if self._wantKeyIconVisible and self.wtKeyIconBox:AnyKeyIconVisible() then
            if self.wtCommonBtn.ButtonState < EButtonState.Deactivated then
                self.wtKeyIconBox:SelfHitTestInvisible()
                self._refreshingIcon = true
                self.wtKeyIconBox:SetKeyStyleEnable(true)
                self._refreshingIcon = false
            elseif self.wtCommonBtn.ButtonState ~= EButtonState.Disabled and not self._DeactivatedKeyIconIsHidden then
                self.wtKeyIconBox:SelfHitTestInvisible()
                self._refreshingIcon = true
                self.wtKeyIconBox:SetKeyStyleEnable(false)
                self._refreshingIcon = false
            else
                self.wtKeyIconBox:Collapsed()
            end
        else
            self.wtKeyIconBox:Collapsed()
        end
    end
end

return DFCommonButtonOnly