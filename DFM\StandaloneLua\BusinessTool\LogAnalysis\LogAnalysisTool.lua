local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

---------------------------------------------------------------------------------
--- 经分记录工具（可以被Server、Module使用）
---------------------------------------------------------------------------------
LogAnalysisTool = {}

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local ULuaExtension = import "LuaExtension"
local recordLogData = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.RecordLogData"
local UDFMGameTss = import "DFMGameTss"
local DFMGameTss = UDFMGameTss.Get(GetGameInstance())
local UDFMGameLogin = import "DFMGameLogin"
local loginIns = UDFMGameLogin.Get(GetGameInstance())
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local TGLOG_SEND_DELAY_TIME = 60
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

LogAnalysisTool._bIsTimerExist = false
LogAnalysisTool._buttonIDataArray = {}
LogAnalysisTool._bIsInGame = false -- 是否在局内，如果在局内需要把经分信息放到结算的时候再发送

local function log(...)
    loginfo('[LogAnalysisTool]', ...)
end

local function err(...)
    logerror('[LogAnalysisTool]', ...)
end

LogAnalysisTool._tgLogQueue = {} -- 每隔1秒发送一批，和按钮一致，但逻辑不同

LogAnalysisTool.SetIsInGame = function(bIsInGame)
    log("SetIsInGame()", bIsInGame)
    bIsInGame = setdefault(bIsInGame, false)
    LogAnalysisTool._bIsInGame = bIsInGame
end

-- 将局内所有的经分信息一起发送，在SettlementLogic中被调用
LogAnalysisTool.SendAllInGameData = function()
    --LogAnalysisTool._PakDstLogNameList()
    LogAnalysisTool._DoSend()
end

LogAnalysisTool._PakDstLogNameList = function()
    if LogAnalysisTool._tgLogQueue == nil then
        return
    end

    local map = Facade.LocalTObjectAllocerManager:Relloc()
    for _, data in ipairs(LogAnalysisTool._tgLogQueue) do
        if data ~= nil and data.__name ~= nil then
            map[data.__name] = true
        end
    end

    local list = Facade.LocalTObjectAllocerManager:Relloc()
    for key, value in pairs(map) do
        table.insert(list, key)
    end

    local result = table.concat(list, ",")
    local data = pb.DstLogNameList:New()
    data.NameList = result
    table.insert(LogAnalysisTool._tgLogQueue, data)

    Facade.LocalTObjectAllocerManager:Free(list)
    Facade.LocalTObjectAllocerManager:Free(map)

    LogAnalysisTool._DoSend()
end

-- 添加一条tglog到队列中，一分钟后才会发送
-- 注意，当在局内时将会统一在结算时发送。
LogAnalysisTool.AddTglog = function(tglogData)
    table.insert(LogAnalysisTool._tgLogQueue, tglogData)
    log("Add Tglog", tglogData.__name)
    
    if LogAnalysisTool._bIsInGame then
        log('Cur tglog will send after settlement.', tglogData.__name)
        return
    end

    -- 当当前添加的tglog是第一条时才开始计时
    if table.maxn(LogAnalysisTool._tgLogQueue) == 1 then
        Timer.DelayCall(TGLOG_SEND_DELAY_TIME, LogAnalysisTool._DoSend) -- 一分钟后发送
    end
end

LogAnalysisTool.DoSendSettingsLog = function()
    local settingsInfoData = pb.SettingsInfo:New()
    settingsInfoData.FireWithOpenSights = recordLogData.SettingsInfo.FireWithOpenSights
    settingsInfoData.SightMode = recordLogData.SettingsInfo.SightMode
    settingsInfoData.BtnChangeView = recordLogData.SettingsInfo.BtnChangeView
    settingsInfoData.ControlMode = recordLogData.SettingsInfo.ControlMode
    settingsInfoData.IsLeanPeek = recordLogData.SettingsInfo.IsLeanPeek
    settingsInfoData.SettingLeanPeekMode = recordLogData.SettingsInfo.SettingLeanPeekMode
    settingsInfoData.IsPeekAutoScope = recordLogData.SettingsInfo.IsPeekAutoScope
    settingsInfoData.SettingSilentWalkInputMode = recordLogData.SettingsInfo.SettingSilentWalkInputMode
    settingsInfoData.SettingVaultTriggerMode = recordLogData.SettingsInfo.SettingVaultTriggerMode
    settingsInfoData.SettingSensitivityMode = recordLogData.SettingsInfo.SettingSensitivityMode
    settingsInfoData.SettingGyroOpenMode = recordLogData.SettingsInfo.SettingGyroOpenMode

    settingsInfoData.IsAimAssistOpen = recordLogData.SettingsInfo.IsAimAssistOpen
    settingsInfoData.EnableReloadOnAiming = recordLogData.SettingsInfo.EnableReloadOnAiming
    settingsInfoData.EnableAutoFire = recordLogData.SettingsInfo.EnableAutoFire
    settingsInfoData.CanFireOnQuickScopeOpen = recordLogData.SettingsInfo.CanFireOnQuickScopeOpen
    settingsInfoData.AbilityItemFireMode = recordLogData.SettingsInfo.AbilityItemFireMode
    settingsInfoData.ShotGunFireMode = recordLogData.SettingsInfo.ShotGunFireMode
    settingsInfoData.SRFireMode = recordLogData.SettingsInfo.SRFireMode
    settingsInfoData.IsSRInstantFire = recordLogData.SettingsInfo.IsSRInstantFire
    settingsInfoData.CanSwitchXPP = recordLogData.SettingsInfo.CanSwitchXPP
    settingsInfoData.FireBreakReload = recordLogData.SettingsInfo.FireBreakReload
    settingsInfoData.IsAutoUpRun = recordLogData.SettingsInfo.IsAutoUpRun
    settingsInfoData.IsFireBtnRotated = recordLogData.SettingsInfo.IsFireBtnRotated
    settingsInfoData.IsAimBtnRotated = recordLogData.SettingsInfo.IsAimBtnRotated
    settingsInfoData.IsPeekBtnRotated = recordLogData.SettingsInfo.IsPeekBtnRotated
    settingsInfoData.IsCrouchBtnRotated = recordLogData.SettingsInfo.IsCrouchBtnRotated
    settingsInfoData.IsGyroScopeReverseX = recordLogData.SettingsInfo.IsGyroScopeReverseX
    settingsInfoData.IsGyroScopeReverseY = recordLogData.SettingsInfo.IsGyroScopeReverseY
    settingsInfoData.CarrierVehicleMode = recordLogData.SettingsInfo.CarrierVehicleMode
    settingsInfoData.MoveRunModeSOL = recordLogData.SettingsInfo.MoveRunModeSOL
    settingsInfoData.MoveRunModeRaid = recordLogData.SettingsInfo.MoveRunModeRaid
    settingsInfoData.MoveRunModeBattleField = recordLogData.SettingsInfo.MoveRunModeBattleField
    settingsInfoData.MoveRunModeBreakthrought = recordLogData.SettingsInfo.MoveRunModeBreakthrought
    settingsInfoData.QuickSPCustomPistol = recordLogData.SettingsInfo.QuickSPCustomPistol
    settingsInfoData.QuickSPCustomLightMachine = recordLogData.SettingsInfo.QuickSPCustomLightMachine
    settingsInfoData.QuickSPCustomSubmachine = recordLogData.SettingsInfo.QuickSPCustomSubmachine
    settingsInfoData.QuickSPCustomPrecisionShootingRifle = recordLogData.SettingsInfo.QuickSPCustomPrecisionShootingRifle
    settingsInfoData.QuickSPCustomSniper = recordLogData.SettingsInfo.QuickSPCustomSniper
    settingsInfoData.QuickSPCustomShotgun = recordLogData.SettingsInfo.QuickSPCustomShotgun
    settingsInfoData.QuickSPCustomRifle = recordLogData.SettingsInfo.QuickSPCustomRifle
    settingsInfoData.QuickSPCustomAbilityItem = recordLogData.SettingsInfo.QuickSPCustomAbilityItem
    settingsInfoData.FireWithOpenSights = recordLogData.SettingsInfo.FireWithOpenSights
    settingsInfoData.SensitivityChangeMode = recordLogData.SettingsInfo.SensitivityChangeMode
    if DFMGameTss ~= nil then
        settingsInfoData.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    LogAnalysisTool.AddTglog(settingsInfoData)
end

LogAnalysisTool.DoSendSensitityLog = function()
    local sensitityInfoData = pb.SettingSensitivity:New()

    sensitityInfoData.NormalVeer = recordLogData.SettingSensitivity.NormalVeer
    sensitityInfoData.NormalScope = recordLogData.SettingSensitivity.NormalScope
    sensitityInfoData.NormalMultiple2 = recordLogData.SettingSensitivity.NormalMultiple2
    sensitityInfoData.NormalMultiple3 = recordLogData.SettingSensitivity.NormalMultiple3
    sensitityInfoData.NormalMultiple4 =  recordLogData.SettingSensitivity.NormalMultiple4
    sensitityInfoData.NormalMultiple5 =  recordLogData.SettingSensitivity.NormalMultiple5
    sensitityInfoData.NormalMultipleOther =  recordLogData.SettingSensitivity.NormalMultipleOther

    sensitityInfoData.FireVeer = recordLogData.SettingSensitivity.FireVeer
    sensitityInfoData.FireScope = recordLogData.SettingSensitivity.FireScope
    sensitityInfoData.FireMultiple2 = recordLogData.SettingSensitivity.FireMultiple2
    sensitityInfoData.FireMultiple3 = recordLogData.SettingSensitivity.FireMultiple3
    sensitityInfoData.FireMultiple4 = recordLogData.SettingSensitivity.FireMultiple4
    sensitityInfoData.FireMultiple5 = recordLogData.SettingSensitivity.FireMultiple5
    sensitityInfoData.FireMultipleOther = recordLogData.SettingSensitivity.FireMultipleOther

    sensitityInfoData.GyroVeer = recordLogData.SettingSensitivity.GyroVeer
    sensitityInfoData.GyroScope = recordLogData.SettingSensitivity.GyroScope
    sensitityInfoData.GyroMultiple2 = recordLogData.SettingSensitivity.GyroMultiple2
    sensitityInfoData.GyroMultiple3 = recordLogData.SettingSensitivity.GyroMultiple3
    sensitityInfoData.GyroMultiple4 =  recordLogData.SettingSensitivity.GyroMultiple4
    sensitityInfoData.GyroMultiple5 =  recordLogData.SettingSensitivity.GyroMultiple5
    sensitityInfoData.GyroMultipleOther =  recordLogData.SettingSensitivity.GyroMultipleOther

    sensitityInfoData.GyroFireVeer = recordLogData.SettingSensitivity.GyroFireVeer
    sensitityInfoData.GyroFireScope = recordLogData.SettingSensitivity.GyroFireScope
    sensitityInfoData.GyroFireMultiple2 = recordLogData.SettingSensitivity.GyroFireMultiple2
    sensitityInfoData.GyroFireMultiple3 = recordLogData.SettingSensitivity.GyroFireMultiple3
    sensitityInfoData.GyroFireMultiple4 = recordLogData.SettingSensitivity.GyroFireMultiple4
    sensitityInfoData.GyroFireMultiple5 = recordLogData.SettingSensitivity.GyroFireMultiple5
    sensitityInfoData.GyroFireMultipleOther = recordLogData.SettingSensitivity.GyroFireMultipleOther

    if DFMGameTss ~= nil then
        sensitityInfoData.SecReportData = DFMGameTss:GetSdkCoreData()
    end

    LogAnalysisTool.AddTglog(sensitityInfoData)
end

LogAnalysisTool.DoSendMobileSensitityLog = function(params)
    local sensitityInfoData = pb.SetUpMobile:New()
    for index,val in pairs(params) do
        sensitityInfoData[index] = params[index]
    end
    LogAnalysisTool.AddTglog(sensitityInfoData)
end

-----------------------------------Preset-----------------------------------

-- 开镜模式
-- @param sightMode number
LogAnalysisTool.SetScopeOpenMode = function(sightMode)
    loginfo("LogAnalysisTool.SetSightMode()", sightMode)
    setdefault(sightMode, 0)
    recordLogData.SettingsInfo.SightMode = sightMode
end

-- 功能键转向
-- @param bIsOpen boolean
LogAnalysisTool.SetBtnChangeView = function(bIsOpen)
    loginfo("LogAnalysisTool.SetBtnChangeView()", bIsOpen)
    setdefault(bIsOpen, true)
    if bIsOpen then
        recordLogData.SettingsInfo.BtnChangeView = 1
    else
        recordLogData.SettingsInfo.BtnChangeView = 0
    end
end

-- 按键模式
-- @param controlMode number
LogAnalysisTool.SetControlMode = function(controlMode)
    loginfo("LogAnalysisTool.SetControlMode()", controlMode)
    setdefault(controlMode, 0)
    recordLogData.SettingsInfo.ControlMode = controlMode
end

-- 是否开启探头功能，1为开启
-- @param IsLeanPeek number
LogAnalysisTool.SetIsLeanPeek = function(bIsOpen)
    loginfo("LogAnalysisTool.SetIsLeanPeek()", bIsOpen)
    setdefault(bIsOpen, true)
    if bIsOpen then
        recordLogData.SettingsInfo.IsLeanPeek = 1
    else
        recordLogData.SettingsInfo.IsLeanPeek = 0
    end
end

-- 探头模式
-- @param SettingLeanPeekMode number
LogAnalysisTool.SetSettingLeanPeekMode = function(SettingLeanPeekMode)
    loginfo("LogAnalysisTool.SetSettingLeanPeekMode()", SettingLeanPeekMode)
    setdefault(SettingLeanPeekMode, 0)
    recordLogData.SettingsInfo.SettingLeanPeekMode = SettingLeanPeekMode
end

-- 是否开启探头自动开镜，1为开启
-- @param IsPeekAutoScope number
LogAnalysisTool.SetIsPeekAutoScope = function(bIsOpen)
    loginfo("LogAnalysisTool.SetIsPeekAutoScope()", bIsOpen)
    setdefault(bIsOpen, true)
    if bIsOpen then
        recordLogData.SettingsInfo.IsPeekAutoScope = 1
    else
        recordLogData.SettingsInfo.IsPeekAutoScope = 0
    end
end

-- 静步模式
-- @param SettingSilentWalkInputMode number
LogAnalysisTool.SetSettingSilentWalkInputMode = function(SettingSilentWalkInputMode)
    loginfo("LogAnalysisTool.SetSettingSilentWalkInputMode()", SettingSilentWalkInputMode)
    setdefault(SettingSilentWalkInputMode, 0)
    recordLogData.SettingsInfo.SettingSilentWalkInputMode = SettingSilentWalkInputMode
end

-- 翻越设置
-- @param SettingVaultTriggerMode number
LogAnalysisTool.SetSettingVaultTriggerMode = function(SettingVaultTriggerMode)
    loginfo("LogAnalysisTool.SetSettingVaultTriggerMode()", SettingVaultTriggerMode)
    setdefault(SettingVaultTriggerMode, true)

    if SettingVaultTriggerMode then
        recordLogData.SettingsInfo.SettingVaultTriggerMode = 1
    else
        recordLogData.SettingsInfo.SettingVaultTriggerMode = 0
    end
end

-- 陀螺仪设置
-- @param SettingGyroOpenMode number
LogAnalysisTool.SetSettingGyroOpenMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingGyroOpenMode()", mode)
    recordLogData.SettingsInfo.SettingGyroOpenMode = mode
end

-- 灵敏度设置
-- @param SettingSensitivityMode number
LogAnalysisTool.SetSettingSensitivityMode = function(SettingSensitivityMode)
    loginfo("LogAnalysisTool.SetSettingSensitivityMode()", SettingSensitivityMode)
    setdefault(SettingSensitivityMode, 0)
    recordLogData.SettingsInfo.SettingSensitivityMode = SettingSensitivityMode
end

---------------------------------------------------------------------------------------------------------------------------

--辅助瞄准开关，1为开启
LogAnalysisTool.SetSettingIsAimAssistOpen = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsAimAssistOpen()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsAimAssistOpen = 1
    else
        recordLogData.SettingsInfo.IsAimAssistOpen = 0
    end
end

-- 开镜换弹开关，1为开启
LogAnalysisTool.SetSettingEnableReloadOnAiming = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingEnableReloadOnAiming()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.EnableReloadOnAiming = 1
    else
        recordLogData.SettingsInfo.EnableReloadOnAiming = 0
    end
end

--按住自动开火开关，1为开启
LogAnalysisTool.SetSettingEnableAutoFire = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingEnableAutoFire()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.EnableAutoFire = 1
    else
        recordLogData.SettingsInfo.EnableAutoFire = 0
    end
end

-- 一键开镜立即开火，1为开启
LogAnalysisTool.SetSettingCanFireOnQuickScopeOpen = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingCanFireOnQuickScopeOpen()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.CanFireOnQuickScopeOpen = 1
    else
        recordLogData.SettingsInfo.CanFireOnQuickScopeOpen = 0
    end
end

--兵种武器开火模式，枚举FireMode
LogAnalysisTool.SetSettingAbilityItemFireMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingAbilityItemFireMode()", mode)
    recordLogData.SettingsInfo.AbilityItemFireMode = mode
end

--散弹枪开火模式，枚举FireMode
LogAnalysisTool.SetSettingShotGunFireMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingShotGunFireMode()", mode)
    recordLogData.SettingsInfo.ShotGunFireMode = mode
end

--狙击枪开火模式，枚举FireMode
-- @param SettingGyroOpenMode number
LogAnalysisTool.SetSettingSRFireMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingSRFireMode()", mode)
    recordLogData.SettingsInfo.SRFireMode = mode
end

-- 狙击枪立即开火，1为开启
LogAnalysisTool.SetSettingIsSRInstantFire = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsSRInstantFire()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsSRInstantFire = 1
    else
        recordLogData.SettingsInfo.IsSRInstantFire = 0
    end
end

-- 一三人称视野切换功能，1为开启
LogAnalysisTool.SetSettingCanSwitchXPP = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingCanSwitchXPP()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.CanSwitchXPP = 1
    else
        recordLogData.SettingsInfo.CanSwitchXPP = 0
    end
end

-- 开火打断换弹模式
LogAnalysisTool.SetSettingFireBreakReload = function(mode)
    loginfo("LogAnalysisTool.SetSettingFireBreakReload()", mode)
    recordLogData.SettingsInfo.ShotGunFireMode = mode
end

-- 一三人称视野切换功能，1为开启
LogAnalysisTool.SetSettingIsAutoUpRun = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsAutoUpRun()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsAutoUpRun = 1
    else
        recordLogData.SettingsInfo.IsAutoUpRun = 0
    end
end

-- 是否开启右开火转向，1为开启
LogAnalysisTool.SetSettingIsFireBtnRotated = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsFireBtnRotated()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsFireBtnRotated = 1
    else
        recordLogData.SettingsInfo.IsFireBtnRotated = 0
    end
end

-- 是否开启开镜转向，1为开启
LogAnalysisTool.SetSettingIsAimBtnRotated = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsAimBtnRotated()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsAimBtnRotated = 1
    else
        recordLogData.SettingsInfo.IsAimBtnRotated = 0
    end
end

-- 是否开启探头转向，1为开启
LogAnalysisTool.SetSettingIsPeekBtnRotated = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsPeekBtnRotated()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsPeekBtnRotated = 1
    else
        recordLogData.SettingsInfo.IsPeekBtnRotated = 0
    end
end

-- 是否开启下蹲转向，1为开启
LogAnalysisTool.SetSettingIsCrouchBtnRotated = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsCrouchBtnRotated()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsCrouchBtnRotated = 1
    else
        recordLogData.SettingsInfo.IsCrouchBtnRotated = 0
    end
end

-- 是否开启陀螺仪水平反向，1为开启
LogAnalysisTool.SetSettingIsGyroScopeReverseX = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsGyroScopeReverseX()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsGyroScopeReverseX = 1
    else
        recordLogData.SettingsInfo.IsGyroScopeReverseX = 0
    end
end

-- 是否开启陀螺仪垂直反向，1为开启
LogAnalysisTool.SetSettingIsGyroScopeReverseY = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingIsGyroScopeReverseY()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.IsGyroScopeReverseY = 1
    else
        recordLogData.SettingsInfo.IsGyroScopeReverseY = 0
    end
end

-- 载具按键设置
LogAnalysisTool.SetSettingCarrierVehicleMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingCarrierVehicleMode()", mode)
    recordLogData.SettingsInfo.CarrierVehicleMode = mode
end

-- 操作布局设置(战区行动)
LogAnalysisTool.SetSettingMoveRunModeSOL = function(mode)
    loginfo("LogAnalysisTool.SetSettingMoveRunModeSOL()", mode)
    recordLogData.SettingsInfo.MoveRunModeSOL = mode
end

-- 操作布局设置(合作行动)
LogAnalysisTool.SetSettingMoveRunModeRaid = function(mode)
    loginfo("LogAnalysisTool.SetSettingMoveRunModeRaid()", mode)
    recordLogData.SettingsInfo.MoveRunModeRaid = mode
end

-- 操作布局设置(全面战场经典)
LogAnalysisTool.SetSettingMoveRunModeBattleField = function(mode)
    loginfo("LogAnalysisTool.SetSettingMoveRunModeBattleField()", mode)
    recordLogData.SettingsInfo.MoveRunModeBattleField = mode
end

-- 操作布局设置(全面战场攻防)
LogAnalysisTool.SetSettingMoveRunModeBreakthrought = function(mode)
    loginfo("LogAnalysisTool.SetSettingMoveRunModeBreakthrought()", mode)
    recordLogData.SettingsInfo.MoveRunModeBreakthrought = mode
end

-- 是否一键开启立即开火
LogAnalysisTool.SetSettingFireWithOpenSights = function(mode)
    loginfo("LogAnalysisTool.SetSettingFireWithOpenSights()", mode)
    recordLogData.SettingsInfo.FireWithOpenSights = mode
end


-- 自定义一键开火开镜-手枪
LogAnalysisTool.SetSettingQuickSPCustomPistol = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomPistol()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomPistol = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomPistol = 0
    end
end

-- 自定义一键开火开镜-轻机枪
LogAnalysisTool.SetSettingQuickSPCustomLightMachine = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomLightMachine()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomLightMachine = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomLightMachine = 0
    end
end

-- 自定义一键开火开镜-冲锋枪
LogAnalysisTool.SetSettingQuickSPCustomSubmachine = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomSubmachine()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomSubmachine = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomSubmachine = 0
    end
end

-- 自定义一键开火开镜-射手步枪
LogAnalysisTool.SetSettingQuickSPCustomPrecisionShootingRifle = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomPrecisionShootingRifle()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomPrecisionShootingRifle = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomPrecisionShootingRifle = 0
    end
end

-- 自定义一键开火开镜-狙击枪
LogAnalysisTool.SetSettingQuickSPCustomSniper = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomSniper()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomSniper = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomSniper = 0
    end
end


-- 自定义一键开火开镜-霰弹枪
LogAnalysisTool.SetSettingQuickSPCustomShotgun = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomShotgun()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomShotgun = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomShotgun = 0
    end
end

-- 自定义一键开火开镜-步枪
LogAnalysisTool.SetSettingQuickSPCustomRifle = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomRifle()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomRifle = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomRifle = 0
    end
end

-- 自定义一键开火开镜-兵种道具
LogAnalysisTool.SetSettingQuickSPCustomAbilityItem = function(isOpen)
    loginfo("LogAnalysisTool.SetSettingQuickSPCustomAbilityItem()", isOpen)
    setdefault(isOpen, false)
    if isOpen then
        recordLogData.SettingsInfo.QuickSPCustomAbilityItem = 1
    else
        recordLogData.SettingsInfo.QuickSPCustomAbilityItem = 0
    end
end

-- 灵敏度切换模式
LogAnalysisTool.SetSettingSensitivityChangeMode = function(mode)
    loginfo("LogAnalysisTool.SetSettingSensitivityChangeMode()", mode)
    recordLogData.SettingsInfo.SensitivityChangeMode = mode
end

-----------------------------------灵敏度相关-----------------------------------

--NormalVeer = 8; // 滑屏不开火灵敏度-转向
LogAnalysisTool.SetNormalVeer = function(NormalVeer)
    loginfo("LogAnalysisTool.SetNormalVeer()")
    setdefault(NormalVeer, 0)
    recordLogData.SettingSensitivity.NormalVeer = NormalVeer
end

--FireVeer 滑屏开火灵敏度-转向
LogAnalysisTool.SetFireVeer = function(FireVeer)
    loginfo("LogAnalysisTool.SetFireVeer()")
    setdefault(FireVeer, 0)
    recordLogData.SettingSensitivity.FireVeer = FireVeer
end

--GyroVeer = 8; // 滑屏不开火灵敏度-转向
LogAnalysisTool.SetGyroVeer = function(GyroVeer)
    loginfo("LogAnalysisTool.SetGyroVeer()")
    setdefault(GyroVeer, 0)
    recordLogData.SettingSensitivity.GyroVeer = GyroVeer
end

--FireVeer 滑屏开火灵敏度-转向
LogAnalysisTool.SetGyroFireVeer = function(GyroFireVeer)
    loginfo("LogAnalysisTool.SetGyroFireVeer()")
    setdefault(GyroFireVeer, 0)
    recordLogData.SettingSensitivity.GyroFireVeer = GyroFireVeer
end

--滑屏不开火灵敏度List
LogAnalysisTool.SetNormalValueList = function(NormalList)
    loginfo("LogAnalysisTool.SetNormalValueList()")
    if NormalList then
        recordLogData.SettingSensitivity.NormalScope = NormalList:Get(0)
        recordLogData.SettingSensitivity.NormalMultiple2 = NormalList:Get(1)
        recordLogData.SettingSensitivity.NormalMultiple3 = NormalList:Get(2)
        recordLogData.SettingSensitivity.NormalMultiple4 = NormalList:Get(3)
        recordLogData.SettingSensitivity.NormalMultiple5 = NormalList:Get(4)
        recordLogData.SettingSensitivity.NormalMultipleOther = NormalList:Get(5)
    end
end

--滑屏开火灵敏度List
LogAnalysisTool.SetFireValueList = function(FireList)
    loginfo("LogAnalysisTool.SetFireValueList()")
    if FireList then
        recordLogData.SettingSensitivity.FireScope = FireList:Get(0)
        recordLogData.SettingSensitivity.FireMultiple2 = FireList:Get(1)
        recordLogData.SettingSensitivity.FireMultiple3 = FireList:Get(2)
        recordLogData.SettingSensitivity.FireMultiple4 = FireList:Get(3)
        recordLogData.SettingSensitivity.FireMultiple5 = FireList:Get(4)
        recordLogData.SettingSensitivity.FireMultipleOther = FireList:Get(5)
    end
end

--滑屏不开火灵敏度List
LogAnalysisTool.SetGroyNormalValueList = function(GroyNormalList)
    loginfo("LogAnalysisTool.SetGroyNormalValueList()")
    if GroyNormalList then
        recordLogData.SettingSensitivity.GyroScope = GroyNormalList:Get(0)
        recordLogData.SettingSensitivity.GyroMultiple2 = GroyNormalList:Get(1)
        recordLogData.SettingSensitivity.GyroMultiple3 = GroyNormalList:Get(2)
        recordLogData.SettingSensitivity.GyroMultiple4 = GroyNormalList:Get(3)
        recordLogData.SettingSensitivity.GyroMultiple5 = GroyNormalList:Get(4)
        recordLogData.SettingSensitivity.GyroMultipleOther = GroyNormalList:Get(5)
    end
end

--滑屏开火灵敏度List
LogAnalysisTool.SetGroyFireValueList = function(GroyFireList)
    loginfo("LogAnalysisTool.SetGroyFireValueList()")
    if GroyFireList then
        recordLogData.SettingSensitivity.GyroFireScope = GroyFireList:Get(0)
        recordLogData.SettingSensitivity.GyroFireMultiple2 = GroyFireList:Get(1)
        recordLogData.SettingSensitivity.GyroFireMultiple3 = GroyFireList:Get(2)
        recordLogData.SettingSensitivity.GyroFireMultiple4 = GroyFireList:Get(3)
        recordLogData.SettingSensitivity.GyroFireMultiple5 = GroyFireList:Get(4)
        recordLogData.SettingSensitivity.GyroFireMultipleOther = GroyFireList:Get(5)
    end
end

------------------------------------配装相关-------------------------------
--- 玩家推荐方案查看，每次查看一种风格的推荐配装报一次
LogAnalysisTool.DoSendPlayerOutFitViewFlowLog = function()
    log("DoSendPlayerOutFitViewFlowLog()")
    local playerOutfitViewFlowData = pb.PlayerOutfitViewFlow:New()
    playerOutfitViewFlowData.OutfitID = recordLogData.PlayerOutfitFlowData.OutfitID
    LogAnalysisTool.AddTglog(playerOutfitViewFlowData)
end

--- 玩家推荐方案使用，每次应用一种风格的推荐配装报一次
LogAnalysisTool.DoSendPlayerOutFitApplyFlowLog = function()
    log("DoSendPlayerOutFitApplyFlowLog()")
    local playerOutfitApplyFlowData = pb.PlayerOutfitApplyFlow:New()
    playerOutfitApplyFlowData.OutfitID = recordLogData.PlayerOutfitFlowData.OutfitID
    LogAnalysisTool.AddTglog(playerOutfitApplyFlowData)
end

--- 单局推荐方案使用情况，开局报一次
LogAnalysisTool.DoSendPlayerOutFitModifyAGameFlowLog = function()
    log("DoSendPlayerOutFitModifyAGameFlowLog()")
    local playerOutfitModifyAGameFlowData = pb.PlayerOutfitModifyAGameFlow:New()
    playerOutfitModifyAGameFlowData.OutfitID = recordLogData.PlayerOutfitFlowData.OutfitID
    playerOutfitModifyAGameFlowData.IsNotModify = recordLogData.PlayerOutfitFlowData.IsNotModify
    playerOutfitModifyAGameFlowData.IsModifiedWeapon = recordLogData.PlayerOutfitFlowData.IsModifiedWeapon
    playerOutfitModifyAGameFlowData.IsModifiedHelmet = recordLogData.PlayerOutfitFlowData.IsModifiedHelmet
    playerOutfitModifyAGameFlowData.IsModifiedBreastPlate = recordLogData.PlayerOutfitFlowData.IsModifiedBreastPlate
    playerOutfitModifyAGameFlowData.IsModifiedBag = recordLogData.PlayerOutfitFlowData.IsModifiedBag
    playerOutfitModifyAGameFlowData.IsModifiedChestHanging = recordLogData.PlayerOutfitFlowData.IsModifiedChestHanging
    playerOutfitModifyAGameFlowData.IsModifiedMedicine = recordLogData.PlayerOutfitFlowData.IsModifiedMedicine
    playerOutfitModifyAGameFlowData.IsModifiedOther = recordLogData.PlayerOutfitFlowData.IsModifiedOther
    LogAnalysisTool.AddTglog(playerOutfitModifyAGameFlowData)
end

LogAnalysisTool.AnalysisCarryAction = function(item, scrSlotType, destSlotType)
     -- 有查看过推荐
     if LogAnalysisTool.GetPlayerOutFitOutfitID() ~= 0 then
        -- 应用了推荐(接下来需要判断是否完全应用该推荐)
        if LogAnalysisTool.GetPlayerOutFitIsNotModify() ~= 0 then
            local bScr = ItemHelperTool.IsContainerSlotType(scrSlotType) or scrSlotType == ESlotType.MainContainer
            local bDest = ItemHelperTool.IsContainerSlotType(destSlotType) or destSlotType == ESlotType.MainContainer
            if bScr and bDest then
                if item.itemMainType == EItemType.Medicine then
                    LogAnalysisTool.SetPlayerOutFitIsModifiedMedicine(1)
                else
                    LogAnalysisTool.SetPlayerOutFitIsModifiedOther(1)
                end
            end
        end
    end
end

LogAnalysisTool.SetPlayerOutFitIsModifiedMedicine = function(bIsModifiedMedicine)
    recordLogData.PlayerOutfitFlowData.IsModifiedMedicine = bIsModifiedMedicine
end

LogAnalysisTool.SetPlayerOutFitIsModifiedOther = function(bIsModifiedOther)
    recordLogData.PlayerOutfitFlowData.IsModifiedOther = bIsModifiedOther
end

LogAnalysisTool.GetPlayerOutFitIsModifiedMedicine = function()
    return recordLogData.PlayerOutfitFlowData.IsModifiedMedicine
end

LogAnalysisTool.GetPlayerOutFitIsModifiedOther = function()
    return recordLogData.PlayerOutfitFlowData.IsModifiedOther
end

LogAnalysisTool.ResetPlayerOutFitLogData = function()
    for key, data in pairs(recordLogData.PlayerOutfitFlowData) do
        recordLogData.PlayerOutfitFlowData[key] = 0
    end
end

LogAnalysisTool.SetPlayerOutFitOutfitID = function(outfitID)
    recordLogData.PlayerOutfitFlowData.OutfitID = outfitID
end

LogAnalysisTool.SetPlayerOutFitIsNotModify = function(bIsNotModify)
    recordLogData.PlayerOutfitFlowData.IsNotModify = bIsNotModify
end

LogAnalysisTool.GetPlayerOutFitOutfitID = function()
    return recordLogData.PlayerOutfitFlowData.OutfitID
end

LogAnalysisTool.GetPlayerOutFitIsNotModify = function()
    return recordLogData.PlayerOutfitFlowData.IsNotModify
end

LogAnalysisTool.SetPlayerOutFitModified = function(slotType,bDiff)
    if slotType == ESlotType.MainWeaponLeft or slotType == ESlotType.MainWeaponRight then
        recordLogData.PlayerOutfitFlowData.IsModifiedWeapon = bDiff
    elseif slotType == ESlotType.Helmet then
        recordLogData.PlayerOutfitFlowData.IsModifiedHelmet = bDiff
    elseif slotType == ESlotType.BreastPlate then
        recordLogData.PlayerOutfitFlowData.IsModifiedBreastPlate = bDiff
    elseif slotType == ESlotType.Bag then
        recordLogData.PlayerOutfitFlowData.IsModifiedBag = bDiff
    elseif slotType == ESlotType.ChestHanging then
        recordLogData.PlayerOutfitFlowData.IsModifiedChestHanging = bDiff
    end
end
-------------------------------------------------------------------

--region 活动相关-------------------------------

--- 活动数据上报
LogAnalysisTool.DoSendActivityClientReportLog = function(Time, TabID, ActivityID, LeavingWay)
    loginfo("[DoSendActivityClientReportLog]", Time, TabID, ActivityID, LeavingWay)
    local activityClientReport = pb.ActivityClientReport:New()
    activityClientReport.Time = Time
    activityClientReport.TabID = TabID
    activityClientReport.ActivityID = ActivityID
    activityClientReport.LeavingWay = LeavingWay
    LogAnalysisTool.AddTglog(activityClientReport)
end

--- 活动电台数据上报
LogAnalysisTool.DoSendActivityClientSBCReportLog = function(ActivityID, NameID, Sequence, Channel, StayTime, ReportTime)
    loginfo("[DoSendActivityClientSBCReportLog]", ActivityID, NameID, Sequence, Channel, StayTime, ReportTime)
    local activityClientSBCReport = pb.ActivityClientSBCReport:New()
    activityClientSBCReport.ActivityID = ActivityID
    activityClientSBCReport.NameID = NameID
    activityClientSBCReport.Sequence = Sequence
    activityClientSBCReport.Channel = Channel
    activityClientSBCReport.StayTime = StayTime
    activityClientSBCReport.ReportTime = ReportTime
    LogAnalysisTool.AddTglog(activityClientSBCReport)
end

--region Guide

-- azhengzheng:结算引导视频数据上报
LogAnalysisTool.DoSendSettlementGuideVideoClientReportLog = function(playVideoID, playVideoType, playVideoDuration, isVideoPlayFinished, isHDWithGamePad)
    local settlementGuideVideoClientReport = pb.SettlementGuideVideoClientReport:New()
    settlementGuideVideoClientReport.PlayVideoID = playVideoID
    settlementGuideVideoClientReport.PlayVideoType = playVideoType
    settlementGuideVideoClientReport.PlayVideoDuration = playVideoDuration
    settlementGuideVideoClientReport.IsVideoPlayFinished = isVideoPlayFinished
    settlementGuideVideoClientReport.IsHDWithGamePad =  isHDWithGamePad
    LogAnalysisTool.AddTglog(settlementGuideVideoClientReport)
end

---@param evacuationType EGuideEvacuationPointType
LogAnalysisTool.DoSendGuideEvacuationPointInstructReportLog = function(evacuationType)
    local log = pb.GuideEvacuationPointInstructReport:New()
    log.EvacuationType = evacuationType
    LogAnalysisTool.AddTglog(log)
end


--endregion


--region 商城相关-------------------------------

--- 商城页面打开时上报
-- PageID			uint32 0:商城打开 1:热门推荐 2:枪械皮肤 3:曼德尔砖 4:安全箱 5:充值 6:预览商品详情 7:补充物资 8:干员皮肤抽奖 9:限时返场 10:黑鹰坠落 11:联动页签 12:联动捆绑包页签 13:幸运鸟窝
-- PageParam		uint32 PageID=5时,1:货币栏加号进入 2:订单货币不足跳转进入. PageID=3和8时,PageParam为抽奖ID；PageID=13时,1:幸运鸟窝激活前 2:幸运鸟窝激活后
-- ViewType		    uint32 页面打开参数 PageID=0时,1:点击商城按钮和F4进入 2:大厅轮播图进入, 3:拍脸图 4:其他系统跳转; PageID=6时 1:bundle 2:gift 3:gunskin单品 预览商品详情时使用 4:干员皮肤抽奖中的奖励预览
-- ViewID			uint32 关联的推荐bundleID或者giftID或者商品的ID 预览商品详情时使用 ViewType=4时,ViewID为奖励的itemid
LogAnalysisTool.DoSendStoreViewPageReportLog = function(PageID, PageParam, ViewType, ViewID)
    local storeViewPage = pb.StoreViewPageFlow:New()
    storeViewPage.PageID = PageID
    storeViewPage.PageParam = PageParam
    storeViewPage.ViewType = ViewType
    storeViewPage.ViewID = ViewID
    LogAnalysisTool.AddTglog(storeViewPage)
end

--- 商城Banner事件
-- BannerArea			uint32 1:推荐页 2:礼包页
-- BindID				uint32 关联的推荐bundleID或者giftID
-- BindName			    string 关联的bundle或者礼包的名字
-- EventType			uint32 1:自动选中 2:手动选中 3:选中后点击跳转
LogAnalysisTool.DoSendStoreBannerEventReportLog = function(BannerArea, BindID, BindName, EventType)
    local storeBannerEvent = pb.StoreBannerEventFlow:New()
    storeBannerEvent.BannerArea = BannerArea
    storeBannerEvent.BindID = BindID
    storeBannerEvent.BindName = tostring(BindName)
    storeBannerEvent.EventType = EventType
    LogAnalysisTool.AddTglog(storeBannerEvent)
end

--- 商城商品详情页事件
-- BindType				uint32 1:bundle 2:gift 3:gunskin单品 4:限时返场详情触达
-- BindID				uint32 关联的推荐bundleID或者giftID或者商品的ID
-- EventType			uint32 1:切换选中的商品 2:单品购买 3:整包购买
-- EventParam			uint32 EventType=1和2时:选中的道具ID
-- PayType				uint32 0:无意义 1:三角币购买 2:现金直购 EventType=2和3时有意义
-- PayParam				uint32 0:无意义 1:货币足够 2:货币不足 EventType=2和3时有意义
LogAnalysisTool.DoSendStoreViewDetailEventReportLog = function(BindType, BindID, EventType, EventParam, PayType, PayParam)
    local storeViewDetailEvent = pb.StoreViewDetailEventFlow:New()
    storeViewDetailEvent.BindType = BindType
    storeViewDetailEvent.BindID = BindID
    storeViewDetailEvent.EventType = EventType
    storeViewDetailEvent.EventParam = EventParam
    storeViewDetailEvent.PayType = PayType
    storeViewDetailEvent.PayParam = PayParam
    LogAnalysisTool.AddTglog(storeViewDetailEvent)
end

--- 商城视频播放事件
-- VideoArea        uint32 1:推荐页 2:抽奖页 3:干员皮肤抽奖页
-- VideoKey			string 播放视频的Key
-- EventType		uint32 1:自动播放 2:手动播放 3:跳过播放
LogAnalysisTool.DoSendStoreVideoEventReportLog = function(VideoArea, VideoKey, EventType)
    local storeVideoEvent = pb.StoreVideoEventFlow:New()
    storeVideoEvent.VideoArea = VideoArea
    storeVideoEvent.VideoKey = VideoKey
    storeVideoEvent.EventType = EventType
    LogAnalysisTool.AddTglog(storeVideoEvent)
end

--- 鸟窝选择事件
--- BirdNestId      uint32 鸟窝id
--- BirdEggId       uint32 鸟蛋id
LogAnalysisTool.DoSendLuckyNestSelectReportLog = function(BirdNestId, BirdEggid)
    local LuckyNestSelectEvent = pb.ShopLuckyNestClickFlow:New()
    LuckyNestSelectEvent.BirdNestId = BirdNestId
    LuckyNestSelectEvent.BirdEggid = BirdEggid
    LogAnalysisTool.AddTglog(LuckyNestSelectEvent)
end


--- 充值页面相关事件
-- ClickGear		uint32 1~6表示点击1~6的充值档位
-- ProductID		string 对应的ProductID
LogAnalysisTool.DoSendStoreRechargePageEventReportLog = function(ClickGear, ProductID)
    if ClickGear == nil or ClickGear == "" then
        ClickGear = 0
    end

    local reprotClickGear = string.format("%.2f", ClickGear)
    local storeRechargePageEvent= pb.StoreRechargePageEventFlow:New()
    storeRechargePageEvent.ClickGear = reprotClickGear
    storeRechargePageEvent.ProductID = ProductID or ""
    LogAnalysisTool.AddTglog(storeRechargePageEvent)
end

LogAnalysisTool.DoSendStoreMoneyFlow = function(AfterMoneyNew, IMoneyNew, OnlyChargeMoney, PlayerLevel, SubReason)
    local moenyflow= pb.MoneyFlow:New()
    moenyflow.IMoneyType = 17888808888
    moenyflow.AddOrReduce = 0
    moenyflow.AfterMoneyNew = AfterMoneyNew
    moenyflow.IMoneyNew = IMoneyNew
    moenyflow.Level = PlayerLevel
    moenyflow.Reason = 888
    moenyflow.SubReason = SubReason or 0
    moenyflow.OnlyChargeMoney = OnlyChargeMoney
    LogAnalysisTool.AddTglog(moenyflow)
end

--- 曼德尔砖补充物资相关事件
-- LotteryID				uint32 关联的抽奖ID
-- MandelID				    uint32 曼德尔砖的道具ID
-- MandelKeyID				uint32 量子密钥的道具ID
-- MandelNumPrice			string 曼德尔砖的购买信息{数量，总价格，上浮比例} 如上浮20%，则上浮比例上报20
-- MandelKeyNumPrice		string 量子密钥的购买信息{数量，总价格}
-- BuyResult				uint32 1:购买成功 2:购买失败
-- BuyFaildReason			uint32 1:补充物资时市场砖不足 2:补充物资时价格发生变化不足以支付 3:市场砖不足无法进入补充物资页面
LogAnalysisTool.DoSendStorePaddedGoodsEventReportLog = function(LotteryID, MandelID, MandelKeyID, MandelNumPrice, MandelKeyNumPrice, BuyResult, BuyFaildReason)
    local storePaddedGoodsEvent = pb.StorePaddedGoodsEvent:New()
    storePaddedGoodsEvent.LotteryID = LotteryID
    storePaddedGoodsEvent.MandelID = MandelID
    storePaddedGoodsEvent.MandelKeyID = MandelKeyID
    storePaddedGoodsEvent.MandelNumPrice = MandelNumPrice
    storePaddedGoodsEvent.MandelKeyNumPrice = MandelKeyNumPrice
    storePaddedGoodsEvent.BuyResult = BuyResult
    storePaddedGoodsEvent.BuyFaildReason = BuyFaildReason
    LogAnalysisTool.AddTglog(storePaddedGoodsEvent)
end

--region 下载相关-------------------------------

-- 小包下载相关事件
-- QuestID					uint32 QuestID
-- ModuleKey				uint32 ModuleKey
-- DownloadStyle			uint32 0:无意义 1:手动下载 2:自动下载
-- DownloadTrigger			uint32 0:无意义 1:资源下载界面点击下载 2:战略版界面点击下载 3:组队邀请链接跳转下载 4:竞技场界面点击下载 5:大战场地图界面
-- DownloadResult			uint32 0:无意义 1:下载未完成 2:下载完成
-- DownloadedAllQuestID	string 全部下载完成的QuestID {id1,id2,id3...}
-- ErrorCode				uint32 错误码
-- DeviceName				string 机型名称
-- DiskSpace				string 可用磁盘空间，单位GB，如60.5GB
LogAnalysisTool.DoSendLiteDownloadEvent = function(QuestID, ModuleKey, DownloadStyle, DownloadTrigger, DownloadResult, DownloadedAllQuestID, ErrorCode, DeviceName, DiskSpace)
    local downloadEvent = pb.LiteDownloadEvent:New()
    downloadEvent.QuestID = QuestID
    downloadEvent.ModuleKey = ModuleKey
    downloadEvent.DownloadStyle = DownloadStyle
    downloadEvent.DownloadTrigger = DownloadTrigger
    downloadEvent.DownloadedAllQuestID = DownloadedAllQuestID
    downloadEvent.ErrorCode = ErrorCode
    downloadEvent.DeviceName = DeviceName
    downloadEvent.DiskSpace = DiskSpace

    if PLATFORM_WINDOWS then
        downloadEvent.PlatID = 2
    elseif PLATFORM_ANDROID then
        downloadEvent.PlatID = 1
    elseif PLATFORM_OPENHARMONY then
        downloadEvent.PlatID = 12
    elseif PLATFORM_IOS then
        downloadEvent.PlatID = 0
    elseif PLATFORM_XSX then
        downloadEvent.PlatID = 9
    elseif PLATFORM_PS5 then
        downloadEvent.PlatID = 8
    end

    LogAnalysisTool.AddTglog(downloadEvent)
end

-- 小包下载相关事件
-- ModuleKey				uint32 ModuleKey
-- DownloadStyle			uint32 0:无意义 1:开始下载 2:暂停下载  复用字段
-- DownloadTrigger			uint32 0:无意义 1:资源下载界面点击下载 2:战略版界面点击下载 3:组队邀请链接跳转下载 4:竞技场界面点击下载 5:大战场地图界面 --6:SOL
LogAnalysisTool.DoSendLiteDownloadEventPC = function(ModuleKey, DownloadStyle, DownloadTrigger)
    if not PLATFORM_WINDOWS then
        return
    end
    local downloadEvent = pb.LiteDownloadEvent:New()
    --downloadEvent.QuestID = QuestID
    downloadEvent.ModuleKey = ModuleKey
    downloadEvent.DownloadStyle = DownloadStyle
    downloadEvent.DownloadTrigger = DownloadTrigger
    --downloadEvent.DownloadedAllQuestID = DownloadedAllQuestID
    --downloadEvent.ErrorCode = ErrorCode
    --downloadEvent.DeviceName = DeviceName
    --downloadEvent.DiskSpace = DiskSpace
    downloadEvent.PlatID = 2


    --if PLATFORM_ANDROID then
    --    downloadEvent.PlatID = 1
    --elseif PLATFORM_OPENHARMONY then
    --    downloadEvent.PlatID = 12
    --elseif PLATFORM_IOS then
    --    downloadEvent.PlatID = 0
    --elseif PLATFORM_XSX then
    --    downloadEvent.PlatID = 9
    --elseif PLATFORM_PS5 then
    --    downloadEvent.PlatID = 8
    --end

    LogAnalysisTool.AddTglog(downloadEvent)
end

--region 详情页相关-------------------------------

--- 点击打开武器详情页按钮时上报
LogAnalysisTool.DoSendClickBtnOpenWeaponAttrLog = function()
    local weaponDetailPageClickFlow = pb.WeaponDetailPageClickFlow:New()
    weaponDetailPageClickFlow.WeaponID = recordLogData.WeaponDetailPageClickFlow.WeaponID
    weaponDetailPageClickFlow.IsEffectiveClick = recordLogData.WeaponDetailPageClickFlow.IsEffectiveClick
    LogAnalysisTool.AddTglog(weaponDetailPageClickFlow)
end

LogAnalysisTool.SetWeaponAttiPageWeaponID = function(weaponId)
    recordLogData.WeaponDetailPageClickFlow.WeaponID = weaponId
end

-- 打开各界面时上报
---@param fromUI LogAnalysisTool.EItemDetailFromUIType 
LogAnalysisTool.DoSendOpenUILog = function(fromUI)
    local pagesMayWithDetailClickFlow = pb.PagesMayWithDetailClickFlow:New()
    pagesMayWithDetailClickFlow.PageID = fromUI
    LogAnalysisTool.AddTglog(pagesMayWithDetailClickFlow)
    log("[Add Tglog] DoSendOpenUILog", pagesMayWithDetailClickFlow.PageID)
end

-- 设置当前可能包含详情页的界面id（打开业务界面时调用）
---@param fromUI LogAnalysisTool.EItemDetailFromUIType
LogAnalysisTool.SetCurHaveItemDetailUI = function(fromUI)
    recordLogData.ItemDetailInfo.FromUI = fromUI
end

-- 清除当前可能包含详情页的界面id（关闭业务界面时调用）
LogAnalysisTool.ClearCurHaveItemDetailUI = function()
    recordLogData.ItemDetailInfo.FromUI = LogAnalysisTool.EItemDetailFromUIType.Unknow
end

-- 获取当前查看物品的id
LogAnalysisTool.GetCurCurHaveItemDetailUI = function()
    return recordLogData.ItemDetailInfo.FromUI
end

-- 设置当前查看物品的id
---@param itemId ItemId
LogAnalysisTool.SetCurItemDetailItemId = function(itemId)
    recordLogData.ItemDetailInfo.CurItemId = itemId
end

-- 打开详情页时上报，包括弹窗式和嵌入式
LogAnalysisTool.DoSendOpenItemDetailUILog = function(itemId)
    local detailPagesClickFlow = pb.DetailPagesClickFlow:New()
    detailPagesClickFlow.PageID = recordLogData.ItemDetailInfo.FromUI
    detailPagesClickFlow.ItemId = itemId
    LogAnalysisTool.AddTglog(detailPagesClickFlow)
    LogAnalysisTool._CheckFromUI()
    log("[Add Tglog] DoSendOpenItemDetailUILog", detailPagesClickFlow.PageID, detailPagesClickFlow.ItemId)
end

-- 打开详情页中，来源弹窗上报
--@param btnType ItemDetailConfig.EItemSource
LogAnalysisTool.DoSendOpenItemSourceUILog = function(btnType)
    local popupClickFlow = pb.PopupClickFlow:New()
    popupClickFlow.PopupType = Module.ItemDetail.Config.EItemDetailPopUIType.Source
    popupClickFlow.PageID = recordLogData.ItemDetailInfo.FromUI
    popupClickFlow.ItemId = recordLogData.ItemDetailInfo.CurItemId
    popupClickFlow.FunctionBtnID = btnType
    LogAnalysisTool.AddTglog(popupClickFlow)
    LogAnalysisTool._CheckFromUI()
    log("[Add Tglog] DoSendOpenItemSourceUILog", popupClickFlow.PopupType, popupClickFlow.PageID, popupClickFlow.ItemId, popupClickFlow.FunctionBtnID)
end

-- 打开详情页中，用途弹窗上报
--@param btnType ItemDetailConfig.EItemUsePlace
LogAnalysisTool.DoSendOpenItemUsePlaceUILog = function(btnType)
    local popupClickFlow = pb.PopupClickFlow:New()
    popupClickFlow.PopupType = Module.ItemDetail.Config.EItemDetailPopUIType.UsePlace
    popupClickFlow.PageID = recordLogData.ItemDetailInfo.FromUI
    popupClickFlow.ItemId = recordLogData.ItemDetailInfo.CurItemId
    popupClickFlow.FunctionBtnID = btnType
    LogAnalysisTool.AddTglog(popupClickFlow)
    LogAnalysisTool._CheckFromUI()
    log("[Add Tglog] DoSendOpenItemUsePlaceUILog", popupClickFlow.PopupType, popupClickFlow.PageID, popupClickFlow.ItemId, popupClickFlow.FunctionBtnID)
end

-- 来源弹窗中点击跳转上报
--@param btnType ItemDetailConfig.EItemSourceDetail
LogAnalysisTool.DoSendJumpItemSourceUILog = function(btnType)
    local pageJumpFlow = pb.PageJumpFlow:New()
    pageJumpFlow.PopupType = Module.ItemDetail.Config.EItemDetailPopUIType.Source
    pageJumpFlow.PageID = recordLogData.ItemDetailInfo.FromUI
    pageJumpFlow.ItemId = recordLogData.ItemDetailInfo.CurItemId
    pageJumpFlow.FunctionBtnID = btnType
    if DFMGameTss ~= nil then
        pageJumpFlow.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    LogAnalysisTool.AddTglog(pageJumpFlow)
    LogAnalysisTool._CheckFromUI()
    log("[Add Tglog] DoSendJumpItemSourceUILog", pageJumpFlow.PopupType, pageJumpFlow.PageID, pageJumpFlow.ItemId, pageJumpFlow.FunctionBtnID)
end

-- 用途弹窗中点击跳转上报
LogAnalysisTool.DoSendJumpItemUsePlaceUILog = function(btnType)
    local pageJumpFlow = pb.PageJumpFlow:New()
    pageJumpFlow.PopupType = Module.ItemDetail.Config.EItemDetailPopUIType.UsePlace
    pageJumpFlow.PageID = recordLogData.ItemDetailInfo.FromUI
    pageJumpFlow.ItemId = recordLogData.ItemDetailInfo.CurItemId
    pageJumpFlow.FunctionBtnID = btnType
    LogAnalysisTool.AddTglog(pageJumpFlow)
    LogAnalysisTool._CheckFromUI()
    log("[Add Tglog] DoSendJumpItemUsePlaceUILog", pageJumpFlow.PopupType, pageJumpFlow.PageID, pageJumpFlow.ItemId, pageJumpFlow.FunctionBtnID)
end

LogAnalysisTool._CheckFromUI = function()
    if recordLogData.ItemDetailInfo.FromUI == LogAnalysisTool.EItemDetailFromUIType.Unknow then
        logwarning("[Add Tglog] LogAnalysisTool._CheckFromUI, cur item detail from ui is nil")
    end
end

--endregion

--#region 地图选择经分

---@param modeIdList integer[]
---@param gameMode 1|2 1是大世界 2是大战场
---@param groupId 1|2
LogAnalysisTool.SignMatchMapInfo = function (modeIdList, groupId, gameMode)
    -- local req = pb.MatchingInfo:New()
    -- req.MapIDs = table.concat(modeIdList, ",")
    -- req.GameMode = gameMode
    -- req.GroupID = groupId
    -- req.State = 1
    -- LogAnalysisTool.AddTglog(req)
end

--#endregion

--#region 选择界面 武器经分
LogAnalysisTool.DoSendSelectionWeaponClickFlowLog = function()
    local weaponClickFlow = pb.WeaponClickFlow:New()
    weaponClickFlow.TypID = recordLogData.WeaponClickFlow.TypID
    weaponClickFlow.WeaponID = recordLogData.WeaponClickFlow.WeaponID
    weaponClickFlow.ClickID = recordLogData.WeaponClickFlow.ClickID
    weaponClickFlow.BagID = recordLogData.WeaponClickFlow.BagID
    if DFMGameTss ~= nil then
        weaponClickFlow.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    LogAnalysisTool.AddTglog(weaponClickFlow)
end

LogAnalysisTool.SetSelectionWeaponID = function(weaponId)
    recordLogData.WeaponClickFlow.WeaponID = weaponId
end

LogAnalysisTool.SetSelectionBagID = function(bagId)
    recordLogData.WeaponClickFlow.BagID = bagId
end

LogAnalysisTool.SetSelectionTypID = function(armedForceMode)
    recordLogData.WeaponClickFlow.TypID = armedForceMode
end

LogAnalysisTool.SetSelectionButtonID = function(buttonId)
    recordLogData.WeaponClickFlow.ClickID = buttonId
end

--#endregion

--#region 商店购买入口经分
LogAnalysisTool.DoSendMallClickFlowLog = function()
    local mallClickFlow = pb.MallClickFlow:New()
    mallClickFlow.ExchangeType = recordLogData.MallClickFlow.ExchangeType
    mallClickFlow.EntranceType = recordLogData.MallClickFlow.EntranceType
    mallClickFlow.BuyItemId = recordLogData.MallClickFlow.BuyItemId
    mallClickFlow.BuyItemNum = recordLogData.MallClickFlow.BuyItemNum
    mallClickFlow.CostItemId = recordLogData.MallClickFlow.CostItemId
    mallClickFlow.CostItemNum = recordLogData.MallClickFlow.CostItemNum
    LogAnalysisTool.AddTglog(mallClickFlow)
end

LogAnalysisTool.SetMallClickInfo = function(exchangeType, entranceType, buyItemId, buyItemNum)
    recordLogData.MallClickFlow.ExchangeType = exchangeType
    recordLogData.MallClickFlow.EntranceType = entranceType
    recordLogData.MallClickFlow.BuyItemId = buyItemId
    recordLogData.MallClickFlow.BuyItemNum = buyItemNum
    -- recordLogData.MallClickFlow.CostItemId = exchangeType
    -- recordLogData.MallClickFlow.CostItemNum = exchangeType
end

--#endregion

--#region 拍卖行玩家查询某物品价格次数经分
LogAnalysisTool.DoSendAuctionPlayerSearchPropFlowLog = function(PropID)
    local auctionPlayerSearchPropFlow = pb.AuctionPlayerSearchPropFlow:New()
    auctionPlayerSearchPropFlow.PropID = PropID
    LogAnalysisTool.AddTglog(auctionPlayerSearchPropFlow)
    --LogAnalysisTool.ReSetPresetTipValue()
end

--#endregion

--#region 玩家进入拍卖行的总次数经分
LogAnalysisTool.DoSendAuctionEnterCountFlowLog = function()
    local auctionEnterCountFlow = pb.AuctionEnterCountFlow:New()
    auctionEnterCountFlow.PlayerID = 0
    LogAnalysisTool.AddTglog(auctionEnterCountFlow)
    --LogAnalysisTool.ReSetPresetTipValue()
end

--#endregion

--#region 配装提醒经分
LogAnalysisTool.DoSendPresetTipLog = function()
    local presetTip = pb.PresetTip:New()
    presetTip.IsTipShow = recordLogData.PresetTip.IsTipShow
    presetTip.IsIgnoreTip = recordLogData.PresetTip.IsIgnoreTip
    presetTip.TipSafeBoxNotClear = recordLogData.PresetTip.TipSafeBoxNotClear
    presetTip.TipBagNoSpace = recordLogData.PresetTip.TipBagNoSpace
    presetTip.TipNoHealthMedicine = recordLogData.PresetTip.TipNoHealthMedicine
    presetTip.TipNoStatusMedicine = recordLogData.PresetTip.TipNoStatusMedicine
    presetTip.TipNoArmorMedicine = recordLogData.PresetTip.TipNoArmorMedicine
    presetTip.TipNoBullet = recordLogData.PresetTip.TipNoBullet
    presetTip.TipBreastPlateHealth = recordLogData.PresetTip.TipBreastPlateHealth
    presetTip.TipHelmetHealth = recordLogData.PresetTip.TipHelmetHealth
    LogAnalysisTool.AddTglog(presetTip)
    LogAnalysisTool.ReSetPresetTipValue()
end

LogAnalysisTool.SetPresetTipValueByStr = function(str, value)
    if recordLogData.PresetTip[str] then
        recordLogData.PresetTip[str] = value
    end
end

LogAnalysisTool.ReSetPresetTipValue = function()
    for key, value in pairs(recordLogData.PresetTip) do
        recordLogData.PresetTip[key] = 0
    end
end
--#endregion

--#region 配装各配装功能情况
LogAnalysisTool.DoSendPresetModuleUseLog = function()
    local presetModuleUse = pb.PresetModuleUse:New()
    presetModuleUse.TakeMedicine = recordLogData.PresetModuleUse.TakeMedicine
    presetModuleUse.CheckBag = recordLogData.PresetModuleUse.CheckBag
    presetModuleUse.Preset2Deposit = recordLogData.PresetModuleUse.Preset2Deposit
    presetModuleUse.Bag2Deposit = recordLogData.PresetModuleUse.Bag2Deposit
    presetModuleUse.UseLastPreset = recordLogData.PresetModuleUse.UseLastPreset
    presetModuleUse.UseLowPreset = recordLogData.PresetModuleUse.UseLowPreset
    presetModuleUse.UseMidPreset = recordLogData.PresetModuleUse.UseMidPreset
    presetModuleUse.UseHighPreset = recordLogData.PresetModuleUse.UseHighPreset
    presetModuleUse.UseDiyPreset = recordLogData.PresetModuleUse.UseDiyPreset
    presetModuleUse.ChangeHero = recordLogData.PresetModuleUse.ChangeHero
    presetModuleUse.Bullet2SlotCnt = recordLogData.PresetModuleUse.Bullet2SlotCnt
    presetModuleUse.Bullet2BagCnt = recordLogData.PresetModuleUse.Bullet2BagCnt
    presetModuleUse.Bullet2SafeBoxCnt = recordLogData.PresetModuleUse.Bullet2SafeBoxCnt
    presetModuleUse.BulletCarryNum1 = recordLogData.PresetModuleUse.BulletCarryNum1
    presetModuleUse.BulletCarryNum2 = recordLogData.PresetModuleUse.BulletCarryNum2
    if DFMGameTss ~= nil then
        presetModuleUse.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    LogAnalysisTool.AddTglog(presetModuleUse)
    LogAnalysisTool.ReSetPresetModuleUseValue()
end

LogAnalysisTool.SetPresetModuleUseValueByStr = function(str, value)
    if recordLogData.PresetModuleUse[str] then
        recordLogData.PresetModuleUse[str] = value
    end
end

LogAnalysisTool.ReSetPresetModuleUseValue = function()
    for key, value in pairs(recordLogData.PresetModuleUse) do
        value = 0
    end
end


--#region 自定义方案保存状态
LogAnalysisTool.DoSendDiyPresetSaveLog = function(num)
    local diyPresetSave = pb.DiyPresetSave:New()
    diyPresetSave.SavedDiyPreset = num
    LogAnalysisTool.AddTglog(diyPresetSave)
end

--#endregion
--#region 配装时价格波动弹窗情况表
LogAnalysisTool.DoSendPresetPriceFluctuationFlowLog = function()
    if recordLogData.PresetPriceFluctuationFlow.PresetScene ~= -1 then
        log(string.format("配装时价格波动弹窗情况 DoSendPresetPriceFluctuationFlowLog 场景:%s,次数:%s", recordLogData.PresetPriceFluctuationFlow.PresetScene, recordLogData.PresetPriceFluctuationFlow.TipCnt))
        local presetPriceFluctuationFlow = pb.PresetPriceFluctuationFlow:New()
        presetPriceFluctuationFlow.PresetScene = recordLogData.PresetPriceFluctuationFlow.PresetScene
        presetPriceFluctuationFlow.TipCnt = recordLogData.PresetPriceFluctuationFlow.TipCnt
        LogAnalysisTool.AddTglog(presetPriceFluctuationFlow)
        LogAnalysisTool.RePresetPriceFluctuationFlowValue()
    end
end

LogAnalysisTool.SetPresetPriceFluctuationFlowValueByStr = function(str, value)
    log(string.format("配装时价格波动弹窗情况 SetPresetPriceFluctuationFlowValueByStr str:%s ,value:%s", str, value))
    if recordLogData.PresetPriceFluctuationFlow[str] then
        recordLogData.PresetPriceFluctuationFlow[str] = value
    end
end

LogAnalysisTool.GetPresetPriceFluctuationFlowTipCnt = function()
    return recordLogData.PresetPriceFluctuationFlow.TipCnt
end

LogAnalysisTool.GetPresetPriceFluctuationFlowPresetScene = function()
    return recordLogData.PresetPriceFluctuationFlow.PresetScene
end

LogAnalysisTool.RePresetPriceFluctuationFlowValue = function()
    recordLogData.PresetPriceFluctuationFlow.PresetScene = -1
    recordLogData.PresetPriceFluctuationFlow.TipCnt = 0
end
--#endregion

--#region 开始匹配时玩家配装数据
LogAnalysisTool.DoSendPresetMatchBeginFlowLog = function(mainWeaponValue, secondWeaponValue, breastplateLvl, helmetLvl, bagLvl, chesthangingLvl, carryMedicineTypeStrs, carryBulletLevelStr)
	-- int64	MainWeaponValue = 8; // 主武器价值
	-- int64	SecondWeaponValue = 9; // 副武器价值
	-- string	HealthMedicine = 10; // 伤害救治药品id与数量，格式：id1:num1;id2:num2;, size: 256
	-- string	StatusMedicine = 11; // 异常清除药品id与数量，格式：id1:num1;id2:num2;, size: 256
	-- string	ArmorMedicine = 12; // 护甲修复药品id与数量，格式：id1:num1;id2:num2;, size: 256
	-- int32	BreastplateLvl = 13; // 携带护甲等级
	-- int32	HelmetLvl = 14; // 携带护甲等级
	-- int32	BagLvl = 15; // 携带护甲等级
	-- int32	ChesthangingLvl = 16; // 携带护甲等级
	-- string	BulletLvl = 17; // 携带子弹等级和数量，格式：等级:num;等级:num;, size: 256
    logwarning(string.format("LogAnalysisTool.DoSendPresetMatchBeginFlowLog ==>主武器价值：%s, 副武器价值：%s, 护甲等级：%s, 头盔等级：%s, 背包等级：%s, 胸挂等级：%s",
        mainWeaponValue,
        secondWeaponValue,
        breastplateLvl,
        helmetLvl,
        bagLvl,
        chesthangingLvl))
    logwarning(string.format("LogAnalysisTool.DoSendPresetMatchBeginFlowLog ==>伤害救治药品id与数量：%s, 异常清除药品id与数量：%s, 护甲修复药品id与数量：%s, 携带子弹等级和数量：%s",
        carryMedicineTypeStrs[EDispensingMedicineType.HP] or "",
        carryMedicineTypeStrs[EDispensingMedicineType.BUFF] or "",
        carryMedicineTypeStrs[EDispensingMedicineType.Armor] or "",
        carryBulletLevelStr))
    local presetMatchBeginFlow = pb.PresetMatchBeginFlow:New()
    presetMatchBeginFlow.MainWeaponValue = mainWeaponValue
    presetMatchBeginFlow.SecondWeaponValue = secondWeaponValue
    presetMatchBeginFlow.BreastplateLvl = breastplateLvl
    presetMatchBeginFlow.HelmetLvl = helmetLvl
    presetMatchBeginFlow.BagLvl = bagLvl
    presetMatchBeginFlow.ChesthangingLvl = chesthangingLvl
    presetMatchBeginFlow.HealthMedicine = carryMedicineTypeStrs[EDispensingMedicineType.HP] or ""
    presetMatchBeginFlow.StatusMedicine = carryMedicineTypeStrs[EDispensingMedicineType.BUFF] or ""
    presetMatchBeginFlow.ArmorMedicine = carryMedicineTypeStrs[EDispensingMedicineType.Armor] or ""
    presetMatchBeginFlow.BulletLvl = carryBulletLevelStr
    LogAnalysisTool.AddTglog(presetMatchBeginFlow)
end
--#endregion

LogAnalysisTool._sendCommonLogNextFrameTimer = nil -- 保证这些高频的log一帧只会发出去一次，一帧内的合并到一个里面，下一帧发出

local lowerBound = 1 * 10 ^7
local upperBound = 2 * 10 ^7

--- 客户端自定义事件埋点，只包含玩家等级和策划指定的事件id
--- bImmediately: 是否即时上报
---@param eventID integer 事件id
---@param bImmediately boolean 是否立即上报，默认为true | 2025/7/23: 这个参数已废弃，所有事件都塞入列表在下一帧上报
---@param payload string|nil 自定义数据，一般是 serialized json
LogAnalysisTool.DoSendClientCommonEventLog = function(eventID, bImmediately, payload)
    if not eventID or eventID <= 0 then
        err("DoSendClientCommonEventLog event id invalid", eventID, bImmediately)
        return
    end

    -- 对于10000000 ~ 19999999的id
    if eventID > lowerBound and eventID < upperBound then
        if not IsHD() then
            --手游侧映射到20000000 ~ 29999999
            eventID = eventID + lowerBound
        else
            -- 手柄映射到 30000000 ~ 39999999
            if WidgetUtil.IsGamepad() then
                eventID = eventID + upperBound
            end
        end
    end

    log("DoSendClientCommonEventLog", eventID, bImmediately)
    bImmediately = setdefault(bImmediately, true)
    local ClientCommonEventData = pb.ClientCommonEvent:New()
    ClientCommonEventData.Level = recordLogData.PlayerInfo.Level
    ClientCommonEventData.EventID = eventID
    if DFMGameTss ~= nil then
        ClientCommonEventData.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    if payload ~=nil then
        ClientCommonEventData.Payload = payload
    end

    LogAnalysisTool.AddTglog(ClientCommonEventData)
    -- LogAnalysisTool._DoSend(bImmediately, true)
    if LogAnalysisTool._sendCommonLogNextFrameTimer then
        -- log("_sendCommonLogNextFrameTimer is not nil, wait")
    else
        LogAnalysisTool._sendCommonLogNextFrameTimer = Timer.DelayCall(0, LogAnalysisTool._RealSendClientCommonEventLog)
    end
end

LogAnalysisTool._RealSendClientCommonEventLog = function()
    -- log("_RealSendClientCommonEventLog")
    LogAnalysisTool._DoSend(true, true)
    LogAnalysisTool._sendCommonLogNextFrameTimer= nil
end

LogAnalysisTool.DoSendBeginSeamlessLog = function()
    if Server.GuideServer:IsForceNewPlayerGuideFinished() then return end
    LogAnalysisTool.DoSendClientCommonEventLog(Module.Guide.Config.ESpecCommonEventLogId.BeginSeamlessMatch, true)
end

LogAnalysisTool.SetPlayerInfoLevel = function(level)
    if not level or level < 0 then return end
    recordLogData.PlayerInfo.Level = level
end
--#endregion



---------------------------------------------------------------------
--- 改枪台TLog
---------------------------------------------------------------------
LogAnalysisTool.GunsmithTLog_SetWassemblyTriggerTime = function(WassemblyTriggerTime)
    recordLogData.GunsmithClientActionReportTLog.WassemblyTriggerTime = WassemblyTriggerTime
end

LogAnalysisTool.GunsmithTLog_SetExitGunsmithWay = function(ExitGunsmithWay)
    recordLogData.GunsmithClientActionReportTLog.ExitGunsmithWay = ExitGunsmithWay
end

LogAnalysisTool.GunsmithTLog_SetExitPreAssemblyWay = function(ExitPreAssemblyWay)
    recordLogData.GunsmithClientActionReportTLog.ExitPreAssemblyWay = ExitPreAssemblyWay
end

LogAnalysisTool.GunsmithTLog_SetSaveSettingInfo = function(SaveSettingInfo)
    recordLogData.GunsmithClientActionReportTLog.SaveSettingInfo = SaveSettingInfo
end

LogAnalysisTool.GunsmithTLog_SetOneClickUnlockInfo = function(OneClickUnlockInfo)
    recordLogData.GunsmithClientActionReportTLog.OneClickUnlockInfo = OneClickUnlockInfo
end

LogAnalysisTool.GunsmithTLog_SetSkinInfo = function(SkinInfo)
    recordLogData.GunsmithClientActionReportTLog.SkinInfo = SkinInfo
end

LogAnalysisTool.GunsmithTLog = function()
    local GunsmithClientActionReport = pb.GunsmithClientActionReport:New()
    GunsmithClientActionReport.WassemblyTriggerTime = recordLogData.GunsmithClientActionReportTLog.WassemblyTriggerTime
    GunsmithClientActionReport.ExitGunsmithWay = recordLogData.GunsmithClientActionReportTLog.ExitGunsmithWay
    GunsmithClientActionReport.ExitPreAssemblyWay = recordLogData.GunsmithClientActionReportTLog.ExitPreAssemblyWay
    GunsmithClientActionReport.SaveSettingInfo = recordLogData.GunsmithClientActionReportTLog.SaveSettingInfo
    GunsmithClientActionReport.OneClickUnlockInfo = recordLogData.GunsmithClientActionReportTLog.OneClickUnlockInfo
    GunsmithClientActionReport.SkinInfo = recordLogData.GunsmithClientActionReportTLog.SkinInfo
    if DFMGameTss ~= nil then
        GunsmithClientActionReport.SecReportData = DFMGameTss:GetSdkCoreData()
    end
    LogAnalysisTool.AddTglog(GunsmithClientActionReport)

    -- logerror("---------------GunsmtihTlog--------------------")
    -- for key, value in pairs(GunsmithClientActionReport) do
    --     logerror("[GunsmtihTlog]", key, value)
    -- end

    LogAnalysisTool.ReSetPresetModuleUseValue()
end

LogAnalysisTool.ResetGunsmithTLog = function()
    LogAnalysisTool.GunsmithTLog_SetWassemblyTriggerTime(0)
    LogAnalysisTool.GunsmithTLog_SetExitGunsmithWay(0)
    LogAnalysisTool.GunsmithTLog_SetExitPreAssemblyWay(0)
    LogAnalysisTool.GunsmithTLog_SetSaveSettingInfo(0)
    LogAnalysisTool.GunsmithTLog_SetOneClickUnlockInfo(0)
    LogAnalysisTool.GunsmithTLog_SetSkinInfo("")
end

LogAnalysisTool.UObjectMonitorTLog = function(uobjectCount, luaMemory)
    local UObjectMonitorData = pb.UObjectMonitor:New()
    UObjectMonitorData.UObjectCount = uobjectCount
    UObjectMonitorData.LuaMemorySize = uobjectCount
    LogAnalysisTool.AddTglog(UObjectMonitorData)
end

----------------------------------------------------------------
--- MP装备经分
----------------------------------------------------------------
LogAnalysisTool.MPExpertSkillInfoFlowTLog_SignClickTime = function(playerId)
    local clickTime = recordLogData.ViewExpertSkillInfoFlow.ClickTime
    recordLogData.ViewExpertSkillInfoFlow.ClickTime = clickTime + 1
    recordLogData.PlayerId = playerId
end

LogAnalysisTool.MPExpertSkillInfoFlowTLog_SetClickTime = function(clickTime)
    recordLogData.ViewExpertSkillInfoFlow.ClickTime = clickTime
end

LogAnalysisTool.MPExpertSkillInfoFlowTLog = function()
    local ViewExpertSkillInfoFlow = pb.ViewExpertSkillInfoFlow:New()
    ViewExpertSkillInfoFlow.ClickTime = recordLogData.ViewExpertSkillInfoFlow.ClickTime
    LogAnalysisTool.AddTglog(ViewExpertSkillInfoFlow)
end

LogAnalysisTool.ResetMPExpertSkillInfoFlowTLog = function()
    LogAnalysisTool.MPExpertSkillInfoFlowTLog_SetClickTime(0)
end
----------------------------------------------------------------
--- 按钮点击经分
----------------------------------------------------------------
--- 调用该方法可以标记一次该按钮点击的记录，将其加入到待发送的点击消息队列中，一分钟后发送给服务器
--- @param buttonId number
LogAnalysisTool.SignButtonClicked = function(buttonId)
    if LogAnalysisTool._bIsTimerExist then
        LogAnalysisTool._AddButtonClickNumById(buttonId) -- 添加一次按钮点击数据
    else
        -- 判断当前日志容器中是否还有未发送的消息
        if table.isempty(LogAnalysisTool._buttonIDataArray) then
            local ButtonIDataStruct = LogAnalysisTool._NewButtonClickData(buttonId)
            table.insert(LogAnalysisTool._buttonIDataArray, ButtonIDataStruct) -- 加入到消息队列中

            Timer.DelayCall(TGLOG_SEND_DELAY_TIME, LogAnalysisTool._SendButtonIData, LogAnalysisTool)
            LogAnalysisTool._bIsTimerExist = true -- 标记当前Timer存在
        else
            logerror("ButtonIDataArray Not Empty, Please Check Out If Not Sended!")
        end
    end
end

-- 声明一个新的按钮点击的pb数据并返回
LogAnalysisTool._NewButtonClickData = function(buttonId)
    log("LogAnalysisTool._NewButtonClickData()", buttonId)
    local ButtonIDataStruct = pb.ButtonClickFlow:New() -- 声明一条新的消息
    ButtonIDataStruct.ButtonID = buttonId
    ButtonIDataStruct.ClickNum = 1 -- 点击一次即加1
    if DFMGameTss ~= nil then
        ButtonIDataStruct.SecReportData = DFMGameTss:GetSdkCoreData()
    end

    return ButtonIDataStruct
end

-- 遍历当前表中存在的信息，给对应的按钮点击次数加一
-- 若表中不存在，则新建button信息
LogAnalysisTool._AddButtonClickNumById = function(buttonId)
    log("LogAnalysisTool._AddButtonClickNumById()", buttonId)
    for _, pbDataIns in ipairs(LogAnalysisTool._buttonIDataArray) do
        if pbDataIns.ButtonID == buttonId then
            pbDataIns.ClickNum = pbDataIns.ClickNum + 1
            return
        end
    end

    -- 走到这里说明是一分钟内新的按钮点击
    local ButtonIDataStruct = LogAnalysisTool._NewButtonClickData(buttonId)
    table.insert(LogAnalysisTool._buttonIDataArray, ButtonIDataStruct) -- 加入到消息队列中
end

-- 发送按钮点击的经分消息，为默认调用，不能手动调用
LogAnalysisTool._SendButtonIData = function()
    log("LogAnalysisTool._SendButtonIData()")
    local req = pb.CSTlogAgentTglogReq:New() -- 一条新的tglog队列

    local entry_array = {} -- 消息容器
    for _, IDataIns in ipairs(LogAnalysisTool._buttonIDataArray) do
        loginfo("LogAnalysisTool: ButtonId = ", IDataIns.ButtonID, "ClickNum = ", IDataIns.ClickNum)
        local pb_TgLogEntry = pb.TgLogEntry:New()
        pb_TgLogEntry.name = "pb.ButtonClickFlow"
        pb_TgLogEntry.pbtlog = LogAnalysisTool._EncodeProtoData(IDataIns)

        table.insert(entry_array, pb_TgLogEntry) -- 添加到消息队列中
    end

    req.player_id = Server.AccountServer:GetPlayerId()
    req.entry_array = entry_array
    req:SendNtf()

    LogAnalysisTool._bIsTimerExist = false -- 重置当前状态
    LogAnalysisTool._buttonIDataArray = {} -- 重置消息队列
end

---------------------------------------------------------------------
--- 客户端信息上报
---------------------------------------------------------------------
LogAnalysisTool.GetClientInfoLog = function()
    local ScreenSize = UWidgetLayoutLibrary.GetViewportSize(GetWorld())
    local client_info = {}
    --lua
    client_info.screen_width = tostring(ScreenSize.X)
    client_info.screen_height = tostring(ScreenSize.Y)
    client_info.denisty = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
    -- client_info.login_channel = Facade.ProtoManager.channelId
    client_info.login_channel = Server.SDKInfoServer:GetConfigChannelIdNumber()
    client_info.game_language = LocalizeTool.GetCurrentCulture()
    client_info.mobile_network = Server.SDKInfoServer:GetNetWorkDetailInfo()
    -- 海外不传入Game_center
    if IsBuildRegionCN() then
        client_info.game_center = Server.SDKInfoServer:GetLaunchChannelId()
    else
        client_info.game_center = 0
    end

    --C++
    client_info = ULuaExtension.Ext_GetPlatformClientInfo(client_info)
    local newclient_info = {}
    local intValues = {
        "screen_width",
        "screen_height",
        "memory",
        "login_channel",
        "mobile_network",
    }
    for k,v in pairs(client_info) do
        newclient_info[k] = client_info:Get(k)
        if table.contains(intValues, k) then
            if newclient_info[k] and tonumber(newclient_info[k]) then
                newclient_info[k] = math.ceil(tonumber(newclient_info[k]))
            else
                logerror("get cient info error: k : v:",k,newclient_info[k])
            end
        end
        if k == "denisty" then
            if newclient_info[k] then
                newclient_info[k] = tonumber(newclient_info[k])
            end
        end
        if k == "client_ip" then
            Server.SDKInfoServer:SetClientIp(newclient_info[k])
        end

        if k == "mobile_network" and not (IsMobile()) then
            newclient_info[k] = ""
        end
		
        if k == "network" then
            Server.SDKInfoServer:SetNetworkType(newclient_info[k])
        end
    end
    
    local xwid = ""
    local UDFMGameGPM = import "DFMGameGPM"
    if UDFMGameGPM then
        xwid = UDFMGameGPM:GetXID()
    end
    newclient_info["xid"] = xwid

    --O2平台 注册/登录信息上报
    --需要用户同意协议
    if Module.Login:IsServiceAgree() and (IsMobile()) then
        local UDFMGameTDM = import "DFMGameTDM"
        local tdmIns = UDFMGameTDM.Get(GetGameInstance())
        local device_id = ""
        local oaid = ""
        local caid = ""
        local user_agent = ""
        local idfv = ""

        if tdmIns == nil then
            logerror("LogAnalysisTool.GetClientInfoLog:UDFMGameTDM.Get(GetGameInstance())=nil!")
        else
            if PLATFORM_ANDROID or PLATFORM_OPENHARMONY then
                if IsBuildRegionCN() then
                    -- 国内使用qimei，海外取不到
                    device_id = tdmIns:GetIEMI() or ""
                else
                    -- 国外使用xwid
                    device_id = xwid or ""
                end
            elseif PLATFORM_IOS then
                device_id = tdmIns:GetIDFA() or ""
                user_agent = tdmIns:GetUserAgent() or ""
                idfv = tdmIns:GetIDFV() or ""
            end
            -- PC 就用之前在UE获得的Device_id

        end

        if UDFMGameGPM then
            oaid = UDFMGameGPM.GetOAID() or ""
            caid = UDFMGameGPM:GetCAID() or ""
        end

        newclient_info["device_id"] = device_id
        newclient_info["oaid"] = oaid
        newclient_info["caid"] = caid
        newclient_info["user_agent"] = user_agent
        newclient_info["idfv"] = idfv
    end
    return newclient_info
end

---------------------------------------------------------------------
--- private
---------------------------------------------------------------------
LogAnalysisTool._EncodeProtoData = function(protoData)
    if not protoData then
        logerror("tglog data could not be nil!")
        return
    end

    local buffer = nil
    if __proto_require_editor_file == true then
        buffer = LogAnalysisTool._LuaEncodeProtoData(protoData)
    else
        buffer = LogAnalysisTool._CppEncodeProtoData(protoData)
    end
    return buffer
end

LogAnalysisTool._CppEncodeProtoData = function(protoData)
    local msgName = protoData.__name
    local buffer = g_cpp_pb.serialize(msgName, protoData)
    return buffer
end

LogAnalysisTool._LuaEncodeProtoData = function(protoData)
    local msgName = protoData.__name
    local encodeFuncName = "pb_" .. msgName .. "Encode"
    local encodeFunc = pb[encodeFuncName]
    local headEncoder = PbEncoder()
    local buffer = nil
    if encodeFunc then
        encodeFunc(protoData, headEncoder)
        buffer = headEncoder:encode()
    else
        log("LogAnalysisTool:ProtoProcess EncodeFunc is nil")
    end

    return buffer
end

LogAnalysisTool._GetPbName = function(pbData)
    if pbData then
        return "pb."..pbData.__name -- 需要在前面加一个pb.前缀服务器才能获取到
    end

    return nil
end

-- 安全屋数据上报
LogAnalysisTool.SafeHouseReportedData = function(data)
    local safeHouseReportedData = pb.SafehouseClickEventFlow:New()

    safeHouseReportedData.GameSvrId = data.GameSvrId
    safeHouseReportedData.DtEventTime = data.DtEventTime
    safeHouseReportedData.VGameAppid = data.VGameAppid
    safeHouseReportedData.PlatID = data.PlatID
    safeHouseReportedData.IZoneAreaID = data.IZoneAreaID
    safeHouseReportedData.Vopenid = data.Vopenid
    safeHouseReportedData.PlayerId = data.PlayerId
    safeHouseReportedData.SafehouseScene = data.SafehouseScene
    safeHouseReportedData.SafehouseButtonClick1 = data.SafehouseButtonClick1
    safeHouseReportedData.SafehouseButtonClick2 = data.SafehouseButtonClick2
    safeHouseReportedData.SafehouseButtonClick3 = data.SafehouseButtonClick3
    safeHouseReportedData.SafehouseButtonClick4 = data.SafehouseButtonClick4
    safeHouseReportedData.SafehouseButtonClick5 = data.SafehouseButtonClick5
    safeHouseReportedData.CheckedValue = data.CheckedValue
    safeHouseReportedData.UncheckedValue = data.UncheckedValue
    safeHouseReportedData.TotalValue = data.TotalValue
    safeHouseReportedData.Cost = data.Cost
    safeHouseReportedData.ItemID = data.ItemID
    safeHouseReportedData.ItemValue = data.ItemValue
    safeHouseReportedData.Link = data.Link
    safeHouseReportedData.MapId = data.MapId
    safeHouseReportedData.BuildType = data.BuildType
    safeHouseReportedData.Conditions = data.Conditions
    safeHouseReportedData.ProdTime = data.ProdTime

    LogAnalysisTool.AddTglog(safeHouseReportedData)
    LogAnalysisTool._DoSend(true)
end

-- azhengzheng:特勤处交互数据上报
LogAnalysisTool.DoSendBlackSiteInteractiveData = function(interactiveType)
    local safehouseBlackSiteInteractiveFlow = pb.SafehouseBlackSiteInteractiveFlow:New()
    safehouseBlackSiteInteractiveFlow.InteractiveType = interactiveType or 0
    LogAnalysisTool.AddTglog(safehouseBlackSiteInteractiveFlow)
end

-- ASA数据采集和上报
LogAnalysisTool.O2ASAReportedData = function(data)
    if Module.Login:IsServiceAgree() and PLATFORM_IOS then
        data = data or {}
        local ASAIdInfoData = pb.ASAIdInfo:New()
        -- 服务器自动填入
        ASAIdInfoData.GameSvrId = data.GameSvrId
        ASAIdInfoData.DtEventTime = data.DtEventTime
        ASAIdInfoData.VGameAppid = data.VGameAppid
        ASAIdInfoData.PlatID = data.PlatID
        ASAIdInfoData.IZoneAreaID = data.IZoneAreaID
        ASAIdInfoData.Vopenid = data.Vopenid
        ASAIdInfoData.PlayerId = data.PlayerId

        -- O2平台ASA数据上报
        local UDFMGameTDM = import "DFMGameTDM"
        local tdmIns = UDFMGameTDM.Get(GetGameInstance())
        if tdmIns == nil then
            loginfo("LogAnalysisTool.O2ASAReportedData:UDFMGameTDM.Get(GetGameInstance())=nil!")
            return
        end

        local asaJson = tdmIns:GetAppleASA()
        logerror("LogAnalysisTool.O2ASAReportedData:GetAppleASA->", asaJson)
        if asaJson == "" then
            return
        end
        
        local Json = require("DFM.YxFramework.Plugin.Json.Json").createJson()
        local asaTable = Json.decode(asaJson)
        if asaTable ~= nil then
            local Attribution = asaTable["iad-attribution"] or false
            local OrgID = asaTable["iad-org-id"] or ""
            local OrgName = asaTable["iad-org-name"] or ""
            local CampaignID = asaTable["iad-campaign-id"] or ""
            local CampaignName = asaTable["iad-campaign-name"] or ""
            local AdgroupID = asaTable["iad-adgroup-id"] or ""
            local AdgroupName = asaTable["iad-adgroup-name"] or ""
            local KeywordID = asaTable["iad-keyword-id"] or ""
            local Keyword = asaTable["iad-keyword"] or ""
            local KeywordMatchtype = asaTable["iad-keyword-matchtype"] or ""
            local AdId = asaTable["iad-adId"] or ""
            local CreativesetName = ""
            local ClickDate = asaTable["clickDate"] or asaTable["iad-click-date"] or ""
            local PurchaseDate = asaTable["iad-purchase-date"] or ""
            local ConversionDate = asaTable["iad-conversion-date"] or ""
            local ConversionType = asaTable["iad-conversion-type"] or ""
            local CountryOrRegion = asaTable["iad-country-or-region"] or ""

            ASAIdInfoData.Attribution = Attribution and "1" or "0"
            ASAIdInfoData.OrgID = tostring(OrgID)
            ASAIdInfoData.OrgName = tostring(OrgName)
            ASAIdInfoData.CampaignID = tostring(CampaignID)
            ASAIdInfoData.CampaignName = tostring(CampaignName)
            ASAIdInfoData.AdgroupID = tostring(AdgroupID)
            ASAIdInfoData.AdgroupName = tostring(AdgroupName)
            ASAIdInfoData.KeywordID = tostring(KeywordID)
            ASAIdInfoData.Keyword = tostring(Keyword)
            ASAIdInfoData.KeywordMatchtype = tostring(KeywordMatchtype)
            ASAIdInfoData.AdId = tostring(AdId)
            ASAIdInfoData.CreativesetName = tostring(CreativesetName)
            ASAIdInfoData.ClickDate = tostring(ClickDate)
            ASAIdInfoData.PurchaseDate = tostring(PurchaseDate)
            ASAIdInfoData.ConversionDate = tostring(ConversionDate)
            ASAIdInfoData.ConversionType = tostring(ConversionType)
            ASAIdInfoData.CountryOrRegion = tostring(CountryOrRegion)

            LogAnalysisTool.AddTglog(ASAIdInfoData)
            LogAnalysisTool._DoSend(true)
        else
            loginfo("LogAnalysisTool.O2ASAReportedData:Could not resolve asaJson->", asaJson)
        end
    end
end

-- bImmediately: 是否即时上报
LogAnalysisTool._DoSend = function(bImmediately, bHighFrequency)
    if not bImmediately and LogAnalysisTool._bIsInGame then
        -- 当在局内的时候直接返回，等结算完毕再调
        return
    end

    log("[DoSend] Try send tglog data. Tglog num = ", tostring(#LogAnalysisTool._tgLogQueue))
    local req = pb.CSTlogAgentTglogReq:New()
    local entry_array = {}
    for _, data in ipairs(LogAnalysisTool._tgLogQueue) do
        local pb_TgLogEntry = pb.TgLogEntry:New()
        pb_TgLogEntry.name = LogAnalysisTool._GetPbName(data)
        local buffer = LogAnalysisTool._EncodeProtoData(data)
        if buffer then
            pb_TgLogEntry.pbtlog = buffer
            table.insert(entry_array, pb_TgLogEntry)
        end
    end

    LogAnalysisTool._tgLogQueue = {} -- 直接置空

    --当存在有效数据的时候才会发送
    if #entry_array > 0 then
        req.player_id = Server.AccountServer:GetPlayerId()
        req.entry_array = entry_array
        if bHighFrequency then
            req:SendNtf({bEnableHighFrequency = true})
        else
            req:SendNtf()
        end
    end
end

--- 上报sku门票自动购买情况，type：1/2/3分别对应战略版切换，弹出一键补齐，登出
LogAnalysisTool.DoSendAutoPurchaseSkuLog = function(type,bAutoPurchaseSku,skuItemId,mapId)
    local autoPurchaseSkuFlow = pb.AutoPurchaseSkuFlow:New()
    autoPurchaseSkuFlow.Type=type
    autoPurchaseSkuFlow.SkuItemId=skuItemId
    autoPurchaseSkuFlow.MapId=mapId
    autoPurchaseSkuFlow.SandBoxAutoPurchaseState=bAutoPurchaseSku and 1 or 0
    autoPurchaseSkuFlow.LogoutAutoPurchaseState=bAutoPurchaseSku and 1 or 0

    LogAnalysisTool.AddTglog(autoPurchaseSkuFlow)
end

--- 离开模式大厅时上报
LogAnalysisTool.DoSendModeHallLog = function(secondEnterModeHallTimespan)
    local clientGameLobbyFlow = pb.ClientGameLobbyFlow:New()
    clientGameLobbyFlow.StayTime=secondEnterModeHallTimespan-recordLogData.ModeHallInfo.EnterModeHallTimespan
    clientGameLobbyFlow.ModeId=recordLogData.ModeHallInfo.ModeId

    LogAnalysisTool.AddTglog(clientGameLobbyFlow)
end

--退出成长之路界面时上报一次次数
LogAnalysisTool.DoSendModeMPGrowthLog = function(ClickWeaponCount, ViewDetailsCount)
    local MPGrowthViewClickFlow = pb.MPGrowthViewClickFlow:New()
    MPGrowthViewClickFlow.PlayerId = Server.AccountServer:GetPlayerId()
    MPGrowthViewClickFlow.ClickWeaponCount = ClickWeaponCount
    MPGrowthViewClickFlow.ViewDetailsCount = ViewDetailsCount
    LogAnalysisTool.AddTglog(MPGrowthViewClickFlow)
end

--退出成长之路界面时上报一次时长
LogAnalysisTool.DoSendModeSOLGrowthLog = function(source, mode, duration)
    local GrowthViewLog = pb.GrowthViewFlow:New()
    GrowthViewLog.PlayerId = Server.AccountServer:GetPlayerId()
    GrowthViewLog.Source = source --界面
    GrowthViewLog.Mode = mode --模式
    GrowthViewLog.Duration = duration --时长
    LogAnalysisTool.AddTglog(GrowthViewLog)
end

--- 进入模式大厅时记录信息
LogAnalysisTool.SetModeHallModeId = function(modeId)
    recordLogData.ModeHallInfo.ModeId=modeId
end

LogAnalysisTool.SetModeHallTimespan = function(enterTimespan)
    recordLogData.ModeHallInfo.EnterModeHallTimespan=enterTimespan
end

LogAnalysisTool.GetModeHallTimespan = function()
    return recordLogData.ModeHallInfo.EnterModeHallTimespan
end

--- 离开战略版时上报
LogAnalysisTool.DoSendStrategicSelectionLog = function(mapId,roomId,stayTime,clickRewardCount,clickQuestionCount,bStartGame,entranceClickInfo)
    local clientMapBoardFlow = pb.ClientMapBoardFlow:New()
    clientMapBoardFlow.MapId=mapId
    clientMapBoardFlow.RoomID=roomId
    clientMapBoardFlow.StayTime=stayTime
    clientMapBoardFlow.ClickRewardCount=clickRewardCount
    clientMapBoardFlow.ClickQuestionCount=clickQuestionCount
    clientMapBoardFlow.StartGame=bStartGame
    clientMapBoardFlow.EntranceClickInfo=entranceClickInfo

    LogAnalysisTool.AddTglog(clientMapBoardFlow)
end

--IDC测速超时上报
LogAnalysisTool.DoSendIDCTimeoutLog = function(Idc,IP,Port,PlayerId,Result)
    local roundtripReportTime = pb.RoundtripReportTime:New()
    roundtripReportTime.Idc = Idc
    roundtripReportTime.IP = IP
    roundtripReportTime.Port = Port
    roundtripReportTime.PlayerId = PlayerId
    roundtripReportTime.Result = Result
    LogAnalysisTool.AddTglog(roundtripReportTime)
end



--离开藏品时上报
LogAnalysisTool.DoSendCollectionLog = function()
    local collectionViewFlow = pb.CollectionViewFlow:New()
    collectionViewFlow.PlayerId = Server.AccountServer:GetPlayerId()
    collectionViewFlow.CollectionTabEntryTime = ""
    for key, value in pairs(recordLogData.CollectionInfo.CollectionTabEntryTime) do
        collectionViewFlow.CollectionTabEntryTime = collectionViewFlow.CollectionTabEntryTime..key..":"..value..";"
    end
    collectionViewFlow.RecIDs = ""
    for key, value in pairs(recordLogData.CollectionInfo.RecIDs) do
        collectionViewFlow.RecIDs = collectionViewFlow.RecIDs..key..":"..value..";"
    end
    collectionViewFlow.SkinIDs = ""
    for key, value in pairs(recordLogData.CollectionInfo.SkinIDs) do
        collectionViewFlow.SkinIDs = collectionViewFlow.SkinIDs..key..":"..value..";"
    end
    collectionViewFlow.ClickSkinDefPreset = ""
    for key, value in pairs(recordLogData.CollectionInfo.ClickSkinDefPreset) do
        collectionViewFlow.ClickSkinDefPreset = collectionViewFlow.ClickSkinDefPreset..key..":"..value..";"
    end
    LogAnalysisTool.AddTglog(collectionViewFlow)
end


LogAnalysisTool.ResetCollectionLog = function()
    recordLogData.CollectionInfo = {}
    recordLogData.CollectionInfo.CollectionTabEntryTime = {}
    recordLogData.CollectionInfo.RecIDs = {}
    recordLogData.CollectionInfo.SkinIDs = {}
    recordLogData.CollectionInfo.ClickSkinDefPreset = {}
end

LogAnalysisTool.AddCollectionTabEntryTime = function(tabType)
    declare_if_nil(recordLogData.CollectionInfo.CollectionTabEntryTime, tabType, 0)
    recordLogData.CollectionInfo.CollectionTabEntryTime[tabType] = recordLogData.CollectionInfo.CollectionTabEntryTime[tabType] + 1
end

LogAnalysisTool.AddRecIDs = function(RecID)
    declare_if_nil(recordLogData.CollectionInfo.RecIDs, RecID, 0)
    recordLogData.CollectionInfo.RecIDs[RecID] = recordLogData.CollectionInfo.RecIDs[RecID] + 1
end

LogAnalysisTool.AddSkinIDs = function(SkinID)
    declare_if_nil(recordLogData.CollectionInfo.SkinIDs, SkinID, 0)
    recordLogData.CollectionInfo.SkinIDs[SkinID] = recordLogData.CollectionInfo.SkinIDs[SkinID] + 1
end

LogAnalysisTool.AddClickSkinDefPreset = function(SkinID)
    if recordLogData.CollectionInfo ~= nil then
        declare_if_nil(recordLogData.CollectionInfo.ClickSkinDefPreset, SkinID, 0)
        recordLogData.CollectionInfo.ClickSkinDefPreset[SkinID] = recordLogData.CollectionInfo.ClickSkinDefPreset[SkinID] + 1
    end
end


--新大厅界面进入特勤处交互埋点
LogAnalysisTool.DoSendEnterBlackSiteLog = function()
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter = setdefault(recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter, 0)
    recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter = recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter + 1
    local BlackSiteInteractionFlow = pb.PlayerBlackSiteInteractionFlow:New()
    BlackSiteInteractionFlow.PlayerId = Server.AccountServer:GetPlayerId()
    BlackSiteInteractionFlow.BlackSiteEnter = recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter
    BlackSiteInteractionFlow.BlackSiteEnterTime = Facade.ClockManager:GetServerTimestamp()
    LogAnalysisTool.AddTglog(BlackSiteInteractionFlow)
end

LogAnalysisTool.DoSendQuitBlackSiteLog = function()
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter = setdefault(recordLogData.BlackSiteInteractionInfo.BlackSiteEnterCounter, 0)
    local BlackSiteInteractionFlow = pb.PlayerBlackSiteInteractionFlow:New()
    BlackSiteInteractionFlow.PlayerId = Server.AccountServer:GetPlayerId()
    BlackSiteInteractionFlow.BlackSiteQuitTime = Facade.ClockManager:GetServerTimestamp()
    LogAnalysisTool.AddTglog(BlackSiteInteractionFlow)
end

LogAnalysisTool.SetBlackSiteEfficient = function(bIsEfficient)
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.BlackSiteEfficient = bIsEfficient == true and 1 or 0
end

LogAnalysisTool.SetBlackSiteEfficientAction = function(bIsEfficient)
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.BlackSiteEfficientAction = bIsEfficient == true and 1 or 0
end

LogAnalysisTool.SetIsBlackSite3DInteraction = function(b3DInteract)
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.bIsBlackSite3DInteraction = b3DInteract or false
end

LogAnalysisTool.GetIsBlackSite3DInteraction = function()
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    return recordLogData.BlackSiteInteractionInfo.bIsBlackSite3DInteraction or false
end

LogAnalysisTool.DoSendBlackSiteInteractionLog = function()
    recordLogData.BlackSiteInteractionInfo = setdefault(recordLogData.BlackSiteInteractionInfo, {})
    recordLogData.BlackSiteInteractionInfo.BlackSiteEfficient = setdefault(recordLogData.BlackSiteInteractionInfo.BlackSiteEfficient, 0)
    recordLogData.BlackSiteInteractionInfo.BlackSiteTotalActionCounter = setdefault(recordLogData.BlackSiteInteractionInfo.BlackSiteTotalActionCounter, 0)
    recordLogData.BlackSiteInteractionInfo.BlackSiteEfficientAction = setdefault(recordLogData.BlackSiteInteractionInfo.BlackSiteEfficientAction, 0)
    recordLogData.BlackSiteInteractionInfo.BlackSiteTotalActionCounter = recordLogData.BlackSiteInteractionInfo.BlackSiteTotalActionCounter + 1
    local BlackSiteInteractionFlow = pb.PlayerBlackSiteInteractionFlow:New()
    BlackSiteInteractionFlow.PlayerId = Server.AccountServer:GetPlayerId()
    BlackSiteInteractionFlow.BlackSiteEfficient = recordLogData.BlackSiteInteractionInfo.BlackSiteEfficient
    BlackSiteInteractionFlow.BlackSiteTotalAction = recordLogData.BlackSiteInteractionInfo.BlackSiteTotalActionCounter
    BlackSiteInteractionFlow.BlackSiteEfficientAction = recordLogData.BlackSiteInteractionInfo.BlackSiteEfficientAction
    LogAnalysisTool.AddTglog(BlackSiteInteractionFlow)
end


-- 物品操作上报
LogAnalysisTool.DoSendItemOperateReport = function(ActionMethod, Button)
    local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
    local ItemOperateReport = pb.ItemOperateReport:New()
    ItemOperateReport.PlayerId = Server.AccountServer:GetPlayerId()
    if ItemOperaTool.CheckRunWarehouseLogic() then
        if ItemOperaTool.bInSettlement then
            ItemOperateReport.ReportTime = ItemOperateReportTime.ItemOperateReportTime_Settlement
        else
            ItemOperateReport.ReportTime = ItemOperateReportTime.ItemOperateReportTime_Deposition
        end
    else
        ItemOperateReport.ReportTime = ItemOperateReportTime.ItemOperateReportTime_Looting
    end
    ItemOperateReport.ActionMethod = ActionMethod or ItemOperateActionMethod.ItemOperateActionMethod_None
    ItemOperateReport.Button = Button or ButtonOperateMethod.ButtonOperateMethod_None
    ItemOperateReport.ActionLv = Server.RoleInfoServer.seasonLevel
    LogAnalysisTool.AddTglog(ItemOperateReport)
end

-- 切换时装/装备模式上报
LogAnalysisTool.DoSendModelDisplayFlow = function(TypeAfterChange)
    local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
    local ModelDisplayFlow = pb.ModelDisplayFlow:New()
    ModelDisplayFlow.PlayerId = Server.AccountServer:GetPlayerId()
    if ItemOperaTool.CheckRunWarehouseLogic() then
        if Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain then
            ModelDisplayFlow.ChangedSite = 2 -- 仓库
        else
            ModelDisplayFlow.ChangedSite = 1 -- 大厅
        end
    else
        ModelDisplayFlow.ChangedSite = 3 -- 局内
    end
    ModelDisplayFlow.TypeAfterChange = TypeAfterChange
    LogAnalysisTool.AddTglog(ModelDisplayFlow)
end

LogAnalysisTool.DoSendTournamentRewardClickReport = function(clickData)
    local SeasonMPTournamentClientFlow= pb.SeasonMPTournamentClientFlow:New()
    SeasonMPTournamentClientFlow.SeasonNo=clickData.SeasonNo
    SeasonMPTournamentClientFlow.MajorLevel=clickData.MajorLevel
    SeasonMPTournamentClientFlow.ItemID=clickData.ItemID
    SeasonMPTournamentClientFlow.RewardType=clickData.RewardType
    LogAnalysisTool.AddTglog(SeasonMPTournamentClientFlow)

end

LogAnalysisTool.DoSendRankingRewardClickReport = function(clickData)
    local SeasonSOLRankClientFlow= pb.SeasonSOLRankClientFlow:New()
    SeasonSOLRankClientFlow.SeasonNo=clickData.SeasonNo
    SeasonSOLRankClientFlow.MajorLevel=clickData.MajorLevel
    SeasonSOLRankClientFlow.ItemID=clickData.ItemID
    SeasonSOLRankClientFlow.RewardType=clickData.RewardType
    LogAnalysisTool.AddTglog(SeasonSOLRankClientFlow)

end

LogAnalysisTool.DoSendLobbyTeamJoinLog = function(inviterId, source, roomId)
    local LobbyTeamJoinFlow = pb.LobbyTeamJoinFlow:New()
    LobbyTeamJoinFlow.InviterID = inviterId
    LobbyTeamJoinFlow.Source = source
    LobbyTeamJoinFlow.RoomID = roomId
    LogAnalysisTool.AddTglog(LobbyTeamJoinFlow)
end

LogAnalysisTool.DOSendMatchDsCLB = function(playerId, roomId, domain, serverCLB, port, reconnectType)
    local DsConnectFlow = pb.DsConnectFlow:New()
    --DsConnectFlow.PlayerId = playerId
    DsConnectFlow.RoomID = roomId
    DsConnectFlow.Domain = domain
    DsConnectFlow.ServerCLB = serverCLB
    DsConnectFlow.Port = port
	DsConnectFlow.MobileNetwork = Server.SDKInfoServer:GetNetWorkDetailInfo()
    DsConnectFlow.Network = Server.SDKInfoServer:GetNetworkType()
    DsConnectFlow.ClientVersion = VersionUtil.GetVersionFull()
    DsConnectFlow.IsReconnect = reconnectType
    if Module.NetworkBusiness:IsXunYouSpeedNow() then
        DsConnectFlow.IsUseXunYou = 1
    else
        DsConnectFlow.IsUseXunYou = 0
    end
   
    LogAnalysisTool.AddTglog(DsConnectFlow)
    LogAnalysisTool._DoSend(true, true)
end

LogAnalysisTool.DOSendConnectDSFailed = function(info,dsroomId)
    local DsConnectFlow = pb.DsConnectFailedFlow:New()
    --DsConnectFlow.PlayerId = playerId
    DsConnectFlow.RoomID = dsroomId
    DsConnectFlow.Domain = info.Domain
    DsConnectFlow.ServerCLB = info.Ip
    DsConnectFlow.Port = info.Port
	DsConnectFlow.MobileNetwork = Server.SDKInfoServer:GetNetWorkDetailInfo()
    DsConnectFlow.Network = Server.SDKInfoServer:GetNetworkType()
    DsConnectFlow.ClientVersion = VersionUtil.GetVersionFull()
    DsConnectFlow.KeyActorRepMask = info.ActorRepMask
    DsConnectFlow.TotalInPacketCnt = info.TotalInPackets
    DsConnectFlow.TotalInPacketListCnt = info.TotalInPacketsLost
    DsConnectFlow.TotalOutPacketCnt = info.TotalOutPackets
    DsConnectFlow.TotalOutPacketListCnt = info.TotalOutPacketsLost
    DsConnectFlow.LastRecvPacketSec = info.LastRecvPacketDeltaSecond
    DsConnectFlow.LastRecvAckSec = info.LastAckPakcetDeltaSecond
    DsConnectFlow.ConnectSec = info.ConntectedSecond

    LogAnalysisTool.AddTglog(DsConnectFlow)
    LogAnalysisTool._DoSend(true, true)
end

--干员选择上报
LogAnalysisTool.DoSendHeroSelectedReport = function(mode, heroId)
    local HeroSelectedFlow = pb.HeroSelectedFlow:New()
    HeroSelectedFlow.Mode = mode
    HeroSelectedFlow.HeroID = heroId
    LogAnalysisTool.AddTglog(HeroSelectedFlow)
end

--局外干员周边道具选取上报
LogAnalysisTool.DoSendHeroAccessorySelectedReport = function(heroId, propId, slotId, isUnequipped, reason)
    local HeroAccessorySelectedFlow = pb.HeroAccessorySelectedFlow:New()
    HeroAccessorySelectedFlow.HeroID = heroId
    HeroAccessorySelectedFlow.PropID = propId
    HeroAccessorySelectedFlow.SlotID = slotId
    HeroAccessorySelectedFlow.IsUnequipped = isUnequipped
    HeroAccessorySelectedFlow.Reason = reason
    LogAnalysisTool.AddTglog(HeroAccessorySelectedFlow)
end

--局外干员周边道具点击上报
LogAnalysisTool.DoSendHeroAccessoryClientClickReport = function(heroId, propId)
    local HeroAccessoryClientClick = pb.HeroAccessoryClientClick:New()
    HeroAccessoryClientClick.HeroID = heroId
    HeroAccessoryClientClick.PropID = propId
    LogAnalysisTool.AddTglog(HeroAccessoryClientClick)
end

--干员系统曝光记录上报
LogAnalysisTool.HeroOpItemSysExpose = function(inHeroID,inHeroSkinIDe,inHeroSkinCon,inClickItem)
    local HeroOpItemSysExpose =  pb.HeroOpItemSysExpose:New()
    HeroOpItemSysExpose.HeroID = inHeroID
    HeroOpItemSysExpose.HeroSkinID = inHeroSkinIDe
    HeroOpItemSysExpose.HeroSkinCon = inHeroSkinCon
    HeroOpItemSysExpose.ClickItem = inClickItem
    LogAnalysisTool.AddTglog(HeroOpItemSysExpose)
end

--干员系统点击记录上报
LogAnalysisTool.HeroClickFlow = function(inHeroID,inButtontype,inEnterOrNot)
    local HeroClickFlow = pb.HeroClickFlow:New()
    HeroClickFlow.HeroID = inHeroID
    HeroClickFlow.Buttontype = inButtontype
    HeroClickFlow.EnterOrNot = inEnterOrNot
    LogAnalysisTool.AddTglog(HeroClickFlow)
end

LogAnalysisTool.DoSendMossUsingLog = function(mossOpenCount, bMossUsed, mossAnswerCount)
    local MossAIClientReport = pb.MossAIClientReport:New()
    MossAIClientReport.MossClick=mossOpenCount
    MossAIClientReport.MossTalkEfficient=bMossUsed and 1 or 0
    MossAIClientReport.MossTalk=mossAnswerCount
    LogAnalysisTool.AddTglog(MossAIClientReport)
end

LogAnalysisTool.DoSendAppStoreReviewLog = function(confirmType, triggerType)
    local FeedBackPlayerChoiceFlow = pb.FeedBackPlayerChoiceFlow:New()
    FeedBackPlayerChoiceFlow.OptType = confirmType
    FeedBackPlayerChoiceFlow.TriggerType = triggerType
    LogAnalysisTool.AddTglog(FeedBackPlayerChoiceFlow)
end

LogAnalysisTool.DoSendLobbyNetworkStatus= function(maxPing, minPing, avgPing, SDPing)
    if pb == nil then
        return
    end
    local PlayerLobbyNetworkStatus = pb.PlayerLobbyNetworkStatus:New()
    PlayerLobbyNetworkStatus.MaxPingMs = maxPing
    PlayerLobbyNetworkStatus.MinPingMs = minPing
    PlayerLobbyNetworkStatus.AvgPingMs = avgPing
    PlayerLobbyNetworkStatus.SDPingMs = SDPing
    PlayerLobbyNetworkStatus.CountryCode = Server.SDKInfoServer:GetLoginRegionCode()
    LogAnalysisTool.AddTglog(PlayerLobbyNetworkStatus)
end

LogAnalysisTool.DoSendCheckPlayerSeasonRecord = function(TargetVopenid, PlayMode, SkillLevel, TotalGameTime, TotalFight, TotalValue, WinValue, KillEnemy, ReceiveValue)
    local CheckPlayerSeasonRecord = pb.CheckPlayerSeasonRecord:New()
    CheckPlayerSeasonRecord.TargetVopenid = tostring(TargetVopenid)
    CheckPlayerSeasonRecord.PlayMode = tostring(PlayMode)
    CheckPlayerSeasonRecord.SkillLevel = tostring(SkillLevel)
    CheckPlayerSeasonRecord.TotalGameTime = tostring(TotalGameTime)
    CheckPlayerSeasonRecord.TotalFight = tostring(TotalFight)
    CheckPlayerSeasonRecord.TotalValue = tostring(TotalValue)
    CheckPlayerSeasonRecord.WinValue = tostring(WinValue)
    CheckPlayerSeasonRecord.KillEnemy = tostring(KillEnemy)
    CheckPlayerSeasonRecord.ReceiveValue = tostring(ReceiveValue)
    CheckPlayerSeasonRecord.Level = "0"
    LogAnalysisTool.AddTglog(CheckPlayerSeasonRecord)
end

LogAnalysisTool.DoSendCheckPlayerSeasonDetailRecord = function(TargetVopenid, PlayMode, SeasonBest, TotalGameTime, TotalFight,
    DataTile1,
    DataDetail11,
    DataDetail12,
    DataDetail13,
    DataTile2,
    DataDetail21,
    DataDetail22,
    DataDetail23,
    DataTile3,
    DataDetail31,
    DataDetail32,
    DataDetail33
)
    local CheckPlayerSeasonDetailRecord = pb.CheckPlayerSeasonDetailRecord:New()
    CheckPlayerSeasonDetailRecord.TargetVopenid = tostring(TargetVopenid)
    CheckPlayerSeasonDetailRecord.PlayMode = tostring(PlayMode)
    CheckPlayerSeasonDetailRecord.TotalGameTime = tostring(TotalGameTime)
    CheckPlayerSeasonDetailRecord.TotalFight = tostring(TotalFight)
    CheckPlayerSeasonDetailRecord.SeasonBest = tostring(SeasonBest)

    CheckPlayerSeasonDetailRecord.DataTile1 = tostring(DataTile1)
    CheckPlayerSeasonDetailRecord.DataDetail11 = tostring(DataDetail11)
    CheckPlayerSeasonDetailRecord.DataDetail12 = tostring(DataDetail12)
    CheckPlayerSeasonDetailRecord.DataDetail13 = tostring(DataDetail13)

    CheckPlayerSeasonDetailRecord.DataTile2 = tostring(DataTile2)
    CheckPlayerSeasonDetailRecord.DataDetail21 = tostring(DataDetail21)
    CheckPlayerSeasonDetailRecord.DataDetail22 = tostring(DataDetail22)
    CheckPlayerSeasonDetailRecord.DataDetail23 = tostring(DataDetail23)

    CheckPlayerSeasonDetailRecord.DataTile3 = tostring(DataTile3)
    CheckPlayerSeasonDetailRecord.DataDetail31 = tostring(DataDetail31)
    CheckPlayerSeasonDetailRecord.DataDetail32 = tostring(DataDetail32)
    CheckPlayerSeasonDetailRecord.DataDetail33 = tostring(DataDetail33)

    LogAnalysisTool.AddTglog(CheckPlayerSeasonDetailRecord)
end

LogAnalysisTool.DoSendPlayerMatchFlows = function(param)
    if param and param.TraceID and param.TraceID ~= "" then
        local PlayerMatchFlow = pb.PlayerMatchFlow:New()
        PlayerMatchFlow.ChannelId = param.ChannelId or 0
        PlayerMatchFlow.ClientIp = param.ClientIp or ""
        PlayerMatchFlow.Code = param.Code or 0
        PlayerMatchFlow.DsIp = param.DsIp or ""
        PlayerMatchFlow.DsPort = param.DsPort or ""
        PlayerMatchFlow.Event = param.Event or 0
        PlayerMatchFlow.FlowId = param.FlowId or 0
        PlayerMatchFlow.IsCaptial = param.IsCaptial or 0
        PlayerMatchFlow.IsTeam = param.IsTeam or 0
        PlayerMatchFlow.ModeId = param.ModeId or 0
        PlayerMatchFlow.Msg = param.Msg or ""
        PlayerMatchFlow.PersonNum = param.PersonNum or 1
        PlayerMatchFlow.Status = param.Status or 1
        PlayerMatchFlow.TraceID = param.TraceID or ""
        PlayerMatchFlow.DsRoomId = param.DsRoomId or ""
        LogAnalysisTool.AddTglog(PlayerMatchFlow)
    end
end

LogAnalysisTool.DoSendPlayerMatchError = function(param)
    if param and param.TraceID and param.TraceID ~= "" then
        local PlayerMatchError = pb.PlayerMatchError:New()
        PlayerMatchError.TraceID = param.TraceID or ""
        PlayerMatchError.Type = param.Type or 0
        PlayerMatchError.ErrorCode = param.ErrorCode or 0
        PlayerMatchError.ChannelId = param.ChannelId or 0
        LogAnalysisTool.AddTglog(PlayerMatchError)
    end
end

LogAnalysisTool.DoSendPlayerMachineMemory = function(memorySpace,totalSpace,dFUseSpace)
    if memorySpace then
        local PlayerMachineMemory = pb.PlayerMachineMemory:New()
        PlayerMachineMemory.MemorySpace =  memorySpace
        PlayerMachineMemory.DFGameMemorySpace = dFUseSpace
        PlayerMachineMemory.DeviceTotalMemorySpace = totalSpace
        LogAnalysisTool.AddTglog(PlayerMachineMemory)
    end
end

LogAnalysisTool.DoSendXunYouState = function(eventID, curNetDalay, downloadState, result, speedNetDelay)
    local XunYouSpeedFlow = pb.XunYouSpeedFlow:New()
    XunYouSpeedFlow.EventID =  eventID 
    XunYouSpeedFlow.CurNetDelay = curNetDalay
    XunYouSpeedFlow.DownloadState = downloadState
    XunYouSpeedFlow.Result = result
    XunYouSpeedFlow.SpeedNetDelay = speedNetDelay
    LogAnalysisTool.AddTglog(XunYouSpeedFlow)
end

--点击大厅Banner子项并跳转时上报
LogAnalysisTool.DoSendLobbyBannerLog = function(bannerId, bannerIdList)
    local LobbyBannersBehaviorClient = pb.LobbyBannersBehaviorClient:New()
    LobbyBannersBehaviorClient.CurBanner = bannerId or 0
    LobbyBannersBehaviorClient.BannerIDs = ""
    if bannerIdList and #bannerIdList > 0 then
        for index, id in ipairs(bannerIdList) do
            LobbyBannersBehaviorClient.BannerIDs = LobbyBannersBehaviorClient.BannerIDs..tostring(id)..";"
        end
    end
    LogAnalysisTool.AddTglog(LobbyBannersBehaviorClient)
end

--发生干员抽奖行为记录
LogAnalysisTool.DoRoleSkinLotteryLog = function(LotteryId, LotteryType, LotteryRound, CostItemId, CostItemNum, CostTotal, Rewards, IsLuck)
    local ShopRoleSkinLotteryFlow = pb.ShopRoleSkinLotteryFlow:New()
    ShopRoleSkinLotteryFlow.LotteryId = LotteryId
    ShopRoleSkinLotteryFlow.LotteryType = LotteryType
    ShopRoleSkinLotteryFlow.LotteryRound = LotteryRound
    ShopRoleSkinLotteryFlow.CostItemId = CostItemId
    ShopRoleSkinLotteryFlow.CostItemNum = CostItemNum
    ShopRoleSkinLotteryFlow.CostTotal = CostTotal
    ShopRoleSkinLotteryFlow.Rewards = Rewards
    ShopRoleSkinLotteryFlow.IsLuck = IsLuck
    
    LogAnalysisTool.AddTglog(ShopRoleSkinLotteryFlow)
end

-- 申请单个好友上报
LogAnalysisTool.DoSendFriendApplyLog = function(friendApplySource)
    friendApplySource = friendApplySource or 99  -- 99表示其他
    local friendApplyFlow = pb.FriendApplyFlow:New()
    friendApplyFlow.FriendApplySource = friendApplySource
    LogAnalysisTool.AddTglog(friendApplyFlow)
end

-- 发起邀请组队/申请入队上报
LogAnalysisTool.DoSendTeamInviteAndApplyJoinReqLog = function(source)
    source = source or 99  -- 99表示其他
    local reqInst = pb.LobbyTeamReleaseInviteFlow:New()
    reqInst.InviteSource = source
    LogAnalysisTool.AddTglog(reqInst)
end

-- 回应邀请组队/申请入队上报
LogAnalysisTool.DoSendTeamInviteAndApplyJoinResLog = function(source)
    source = source or 99  -- 99表示其他
    local reqInst = pb.LobbyTeamResponseInviteFlow:New()
    reqInst.InviteSource = source
    LogAnalysisTool.AddTglog(reqInst)
end

-- 排行榜曝光
LogAnalysisTool.DoRankBoardExposureFlow = function(source)
    local req = pb.RankBoardExposureFlow:New()
    req.ExposureType = source.ExposureType
    req.DataTypelD = source.DataTypelD
    req.RankBoardlD = source.RankBoardlD
    req.RankBoardRegion = source.RankBoardRegion
    req.RankBoardRegionAdcode = source.RankBoardRegionAdcode
    LogAnalysisTool.AddTglog(req)
end

-- 玩家选中的语音上报
LogAnalysisTool.DoSendPlayerUsedGameVoiceLog = function(voiceAudioKey)
    local reqInst = pb.PlayerUsedVoiceAudioFlow:New()
    reqInst.VoiceAudioKey = voiceAudioKey
    LogAnalysisTool.AddTglog(reqInst)
end

-------------------------------------------------------------------------------------
--#region 军械库

LogAnalysisTool.LogArmoryFilterSelection = function(itemType, seasonID)
    -- 仅GA 需要
    if IsBuildRegionGA() then
        local reqInst = pb.PlayerUsedVoiceAudioFlow:New()
        reqInst.VoiceAudioKey = voiceAudioKey
        LogAnalysisTool.AddTglog(reqInst)
    end
end

--#endregion
-------------------------------------------------------------------------------------

-------------------------------------------------------------------------------------
--#region 改枪台数据上报
LogAnalysisTool.GunsmithSolutionReportedData = function(ConfigType, Mode, Name)
    local reqInst = pb.GunSmithCodeUsedFlow:New()
    reqInst.ConfigType = ConfigType
    reqInst.Mode = Mode
    reqInst.Name = tostring(Name)
    LogAnalysisTool.AddTglog(reqInst)
end

LogAnalysisTool.GunsmithSolutionShareCodeReportedData = function(SharingCode, Mode, RecID, Component)
    local reqInst = pb.GunSmithCodeUsedFlow:New()
    reqInst.SharingCode = tostring(SharingCode)
    reqInst.ConfigType = 3
    reqInst.Mode = Mode
    reqInst.RecID = RecID
    reqInst.Component = Component
    LogAnalysisTool.AddTglog(reqInst)
end
--#endregion
-------------------------------------------------------------------------------------

-------------------------------------------------------------------------------------
--#region 曼德尔砖UI上报

LogAnalysisTool.ShopSetMandelBoxUpUIFlow = function(BoxId, GroupId, PropId, SourceUI)
    local reqInst = pb.ShopSetMandelBoxUpUIFlow:New()
    reqInst.BoxId = BoxId
    reqInst.GroupId = GroupId
    reqInst.PropId = PropId
    reqInst.SourceUI = SourceUI
    LogAnalysisTool.AddTglog(reqInst)
end

LogAnalysisTool.ShopEnterMandelBoxUpUIFlow = function(BoxId, GroupId, SourceUI)
    local reqInst = pb.ShopEnterMandelBoxUpUIFlow:New()
    reqInst.BoxId = BoxId
    reqInst.GroupId = GroupId
    reqInst.SourceUI = SourceUI
    LogAnalysisTool.AddTglog(reqInst)
end

LogAnalysisTool.ShopEnterMandelOpenUIFlow = function(BoxId, SourceUI)
    local reqInst = pb.ShopEnterMandelOpenUIFlow:New()
    reqInst.BoxId = BoxId
    reqInst.SourceUI = SourceUI
    LogAnalysisTool.AddTglog(reqInst)
end

--#endregion
-------------------------------------------------------------------------------------

LogAnalysisTool.ReportGamePing = function(ping, bIsUseXunYou)
    local gameping = pb.GamePingFlow:New()
    gameping.Ping = ping
    gameping.IsUseXunYou = bIsUseXunYou and 1 or 0
    LogAnalysisTool.AddTglog(gameping)
end

LogAnalysisTool.ReportXunYouWindows = function(bConfirm)
    local XunYouWindowFlow = pb.XunYouWindowsFlow:New()
    XunYouWindowFlow.OpType = bConfirm and 1 or 2
    LogAnalysisTool.AddTglog(XunYouWindowFlow)
end

LogAnalysisTool.ReportXunYouMail = function()
    local  XunYouMailFlow = pb.XunYouMailFlow:New()
    LogAnalysisTool.AddTglog(XunYouMailFlow)
end

ECollectionRoomButtonType = {
    MainPanelSwitchCamera = 1, -- 收藏室主界面切换视角按钮
    CabinetEntrance = 2, -- 收藏室主界面各设施入口按钮
    SwitchDevice = 3, -- 切换设施按钮
    MainPanelSwitchPage = 4, -- 收藏室主界面切换页签按钮
    BlackSiteShowCollectionRoomPanel = 5, -- 特情处界面收藏室页签打开收藏室主界面的按钮
    DisplayCabinetSwitchLevel = 6, -- 收藏柜切换等级显示效果按钮
    SwitchDepositFoldState = 7, -- 展示台显示/隐藏仓库按钮
    ShowUpgradePanel = 8, -- 打开升级界面按钮
}

LogAnalysisTool.ReportCollectionRoomButtonClickedFlow = function(buttonType, extraInfo)
    log("ReportCollectionRoomButtonClickedFlow", buttonType, extraInfo)
    local reqInst = pb.CollectionRoomButtonClickedFlow:New()
    reqInst.ButtonType = buttonType or 0
    reqInst.ExtraInfo = extraInfo and tostring(extraInfo) or ""
    LogAnalysisTool.AddTglog(reqInst)
end

table.merge(LogAnalysisTool, require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool_Settings")

return LogAnalysisTool