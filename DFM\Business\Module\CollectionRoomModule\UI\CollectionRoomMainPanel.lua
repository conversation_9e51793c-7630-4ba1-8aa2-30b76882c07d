----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local ItemOperaTool      = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local EMouseCursor = import "EMouseCursor"
local UGPAudioStatics = import "GPAudioStatics"
local UMTAPI_CoreGlobals = import "MTAPI_CoreGlobals"

---@class CollectionRoomMainPanel : LuaUIBaseView
local CollectionRoomMainPanel = ui("CollectionRoomMainPanel")

local uiNavIDList = {
    UIName2ID.CollectionRoomPanel,
    UIName2ID.CollectionListPanel
}

function CollectionRoomMainPanel:Ctor()
    Facade.UIManager:RegSwitchSubUI(self, uiNavIDList)

    self._wtRootPanel = self:Wnd("DFCanvasPanel_19", UIWidgetBase)
    self._wtButton = self:Wnd("DFMButton_216", DFButtonOnly)
    self._wtButton:SetCursor(EMouseCursor.Default)
    if IsHD() then
        self._wtButton:Collapsed()
    end

    self._pageId = 0
    self._showFrameCount = 0
end

function CollectionRoomMainPanel:OnInitExtraData(pageId, ...)
    pageId = setdefault(pageId, 1)
    ---@type topTabGroupRegInfo

    local topTabGroupRegInfo = {
        tabTxtList = {
            CollectionRoomConfig.Loc.CollectionRoom,
            CollectionRoomConfig.Loc.CollectionList,
        },
        bNewReddotTrie = true,
        reddotTrieRegItemList = {
            {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.CollectionRoom, key = "CollectionCanShelve"}}
            },
            {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.CollectionRoom, key = "CabinetCanLevel"}}
            },
        },
        fCallbackIns = SafeCallBack(self._SwitchToPage, self),
        defalutIdx = pageId,
    }
    if not IsHD() then
        topTabGroupRegInfo.imgPathList = {
            "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_CollectionRoom_Icon_0001.CommonHall_CollectionRoom_Icon_0001'",
            "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_CollectionRoom_Icon_0002.CommonHall_CollectionRoom_Icon_0002'",
        }
    end
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTabGroupRegInfo)
    if not IsHD() then
        Module.CommonBar:RegStackUITopBarTitle(self, CollectionRoomConfig.Loc.CollectionRoomTitle)
    end
    self._showFrameCount = UMTAPI_CoreGlobals.GetGFrameCounter()
    self:_SwitchToPage(pageId, ...)
end

function CollectionRoomMainPanel:OnOpen()
    loginfo("CollectionRoomMainPanel:OnOpen")
    UGPAudioStatics.SetEmoteSoundOn()
    CollectionRoomLogic._OnStateChange(nil, nil, true)
    if ItemOperaTool.bIsInCollectionRoom then
        CollectionRoomLogic.SendCollectionRoomQuitFlow(ECollectionRoomLeaveFrom.EnterMainPanel)
    end
end

function CollectionRoomMainPanel:OnClose()
    loginfo("CollectionRoomMainPanel:OnClose")
    Facade.UIManager:ClearAllSubUI(self)
end

function CollectionRoomMainPanel:OnShow()
    loginfo("CollectionRoomMainPanel:OnShow")
    self._showFrameCount = UMTAPI_CoreGlobals.GetGFrameCounter()
end

function CollectionRoomMainPanel:OnHide()
    loginfo("CollectionRoomMainPanel:OnHide")
end

function CollectionRoomMainPanel:_SwitchToPage(pageId, ...)
    if pageId == self._pageId then
        return
    end
    loginfo("CollectionRoomPanel:_SwitchToPage", pageId, self._pageId)
    self._pageId = pageId
    Facade.UIManager:SwitchSubUIByIndex(self, pageId, self._wtRootPanel, self, ...)

    -- azhengzheng:收藏室交互数据上报
    LogAnalysisTool.DoSendBlackSiteInteractiveData(90 + pageId)

    if UMTAPI_CoreGlobals.GetGFrameCounter() ~= self._showFrameCount then
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.MainPanelSwitchPage, pageId)
    end
end

function CollectionRoomMainPanel:GetCurrentSubUINavID()
    return uiNavIDList[self._pageId]
end

return CollectionRoomMainPanel