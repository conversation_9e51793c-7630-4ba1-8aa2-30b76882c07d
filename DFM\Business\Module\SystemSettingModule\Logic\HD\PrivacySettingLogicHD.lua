----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local PrivacySettingLogicHD = {}
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local PrivacySettingCommonLogic = require "DFM.Business.Module.SystemSettingModule.Logic.PrivacySettingCommonLogic"
local _displaySetting = import("ClientDisplaySettingHD").Get()
local _videoSetting = import("ClientVideoSettingHD").Get()
local _privacySetting = import("ClientPrivacySettingHD").Get()
local UGPAudioStatics = import "GPAudioStatics"
local UDFMLocalizationManager = import("DFMLocalizationManager")
local UKismetSystemLibrary = import("KismetSystemLibrary")
local SystemUtil = require "DFM.YxFramework.Managers.UI.Util.UISystemUtil"
-- BEGIN MODIFICATION @ VIRTUOS
local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
-- END MODIFICATION

local function OpenPrivacy()
    local url = "https://df.qq.com/cp/a20240125main/tencent_other_privacy_PC.shtml"
    SystemUtil.OpenWeb(url)
end

local function OpenPrivacy_SDK()
    local url = "https://df.qq.com/cp/a20240125main/tencent_other_privacy_SDK_PC.shtml"
    SystemUtil.OpenWeb(url)
end

local function OpenContract()
    local url = "https://df.qq.com/cp/a20240125main/tencent_other_contract.shtml"
    SystemUtil.OpenWeb(url)
end

local function OpenChildren_Privacy()
    local url = "https://df.qq.com/cp/a20240125main/tencent_other_children_privacy.shtml"
    SystemUtil.OpenWeb(url)
end

local function OpenCredit()
    local url = "https://gamecredit.qq.com/static/index.htm#/"
    SystemUtil.OpenWeb(url)
end

local function OpenPrivacyAssistant()
    local originUrl = "https://xyapi.game.qq.com/xiaoyue/service/redirect"
    local game_id = 21198
    local source = "xy_privacy"
    local login_type = "msdk"
    local system_id = PLATFORM_WINDOWS and 2 or (Server.SDKInfoServer:GetChannel() == EChannelType.kChannelQQ and 1 or 2)
    local plat_id = PLATFORM_WINDOWS and 2 or ((PLATFORM_ANDROID or PLATFORM_OPENHARMONY) and 1 or 0)
    local role_id = Server.SDKInfoServer:GetOpenIdStr()
    local msdktype = "pcsdk"
    local url = string.format("%s?game_id=%d&source=%s&login_type=%s&system_id=%d&plat_id=%d&role_id=%s&msdktype=%s",
            originUrl, game_id, source, login_type, system_id, plat_id, role_id, msdktype)
    Module.GCloudSDK:OpenUrl(url, 1, false, true, "", false)
end

local function OpenDeleteAccount()
    Module.Login:DeleteAccount()
end

-- BEGIN MODIFICATION @ VIRTUOS: Add bind account feature for Console
local function OpenBindAccount()
    if IsConsole() then
        Module.Login:SetLIUIRoot(Module.SystemSetting.Field:GetLIUIRoot())
        local UGPUINavigationManager = import("GPUINavigationManager")
        local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")    
        UGPUINavigationManager.Get(GetGameInstance()):UnRegisterAnalogCursor()
        UDFNavigationSelectorBase.SetHighlight_DirectlyOrNavigation(true)
        Module.Login:BindChannelWithLIPassQueryInfo()
    end
end

local function OpenUnbindAccount()
    if IsConsole() then
        local url = "https://global.yzfchat.com/newgames/scene_product.html?scene_id=****************&gameid=29158&platid=5&topc=1&lang_type=en"
        SystemUtil.OpenWeb(url)
    else
        local url = "https://global.yzfchat.com/newgames/scene_product.html?scene_id=****************&gameid=29158&platid=2&topc=1&lang_type=en"
        SystemUtil.OpenWeb(url)
    end
end

local function _BindAccountGetter(settingObj, key)
    return nil
end

local function _BindAccountSetter(settingObj, key, value)

end

local function _UnbindAccountGetter(settingObj, key)
    return nil
end

local function _UnbindAccountSetter(settingObj, key, value)

end

local function _RegBindAccountBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _BindAccountGetter, _BindAccountSetter, nil, nil)
end

local function _RegUnbindAccountBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _UnbindAccountGetter, _UnbindAccountSetter, nil, nil)
end

-- Add South Korean parental control feature for Console
local function OpenKRParentalControl()
    if IsConsole() then
        local url = "https://www.playdeltaforce.com/kr/parentalcontrol/"
        SystemUtil.OpenWeb(url)
    end
end

local function _BindKRParentalControlGetter(settingObj, key)
    return nil
end

local function _BindKRParentalControlSetter(settingObj, key, value)

end

local function _RegKRParentalControl(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _BindKRParentalControlGetter, _BindKRParentalControlSetter, nil, nil)
end
-- END MODIFICATION

local function _PrivacyPolicyGetter(settingObj, key)
    return nil
end

local function _PrivacyPolicySetter(settingObj, key, value)

end

local function _TermsOfUseGetter(settingObj, key)
    return nil
end

local function _TermsOfUseSetter(settingObj, key, value)

end

local function _PrivacyGetter(settingObj, key)
    return nil
end

local function _PrivacySetter(settingObj, key, value)

end

local function _Privacy_SDKGetter(settingObj, key)
    return nil
end

local function _Privacy_SDKSetter(settingObj, key, value)

end

local function _ContractGetter(settingObj, key)
    return nil
end

local function _ContractSetter(settingObj, key, value)

end

local function _Children_PrivacyGetter(settingObj, key)
    return nil
end

local function _Children_PrivacySetter(settingObj, key, value)

end

local function _CreditGetter(settingObj, key)
    return nil
end

local function _CreditSetter(settingObj, key, value)

end

local function _DeleteAccountGetter(settingObj, key)
    return nil
end

local function _DeleteAccountSetter(settingObj, key, value)

end

local function _HistoricalRecordsGetter(settingObj, key)
    return Module.SystemSetting.Field:GetIsShowHistoricalRecords()
end

local function _HistoricalRecordsSetter(settingObj, key, value)
    
end

local function _PersonalInformationGetter(settingObj, key)
    return nil
end

local function _PersonalInformationSetter(settingObj, key, value)

end

local function _PrivacyAssistantGetter(settingObj, key)
    return nil
end

local function _PrivacyAssistantSetter(settingObj, key, value)

end

local function _RankingInformationGetter(settingObj, key)
    return Module.SystemSetting.Field:GetIsShowRankingInformation()
end

local function _RankingInformationSetter(settingObj, key, value)

end

local function _InvisibleGetter(settingObj, key)
    return settingObj[key]
end

local function _InvisibleSetter(settingObj, key, value)
    settingObj[key] = value
end

local function _StrangerPrivateChatGetter(settingObj, key)
    return Module.SystemSetting.Field:GetIsPermitStrangerPrivateChat()
end

local function _StrangerPrivateChatSetter(settingObj, key, value)

end

local function _StrangerApplyFriendsGetter(settingObj, key)
    return Module.SystemSetting.Field:GetIsPermitStrangerApplyFriends()
end

local function _StrangerApplyFriendsSetter(settingObj, key, value)

end


local function _showDeleteAccountConfirmWindow()
    -- BEGIN MODIFICATION @ VIRTUOS : 主机删除账号跳转到客户页面
    if IsConsole() then
        Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.ESCPage)
    else
        local tipText = Module.SystemSetting.Config.Loc.HDSetting.DeleteAccountTxt
        local confirmText = Module.SystemSetting.Config.Loc.HDSetting.DeleteAccountConfirmTxt
        local cancelText = Module.SystemSetting.Config.Loc.HDSetting.DeleteAccountCancelTxt
        Module.CommonTips:ShowConfirmWindow(
            tipText,
            OpenDeleteAccount,
            nil,
            cancelText,
            confirmText
        )
    end
    -- END MODIFICATION
end

local function _openPersonalInformationPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDPersonalInformation)
end

local function _RegPrivacyPolicyBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _PrivacyPolicyGetter, _PrivacyPolicySetter, nil, nil)
end

local function _RegTermsOfUseBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _TermsOfUseGetter, _TermsOfUseSetter, nil, nil)
end

local function _RegPrivacyBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _PrivacyGetter, _PrivacySetter, nil, nil)
end

local function _RegPrivacy_SDKBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _Privacy_SDKGetter, _Privacy_SDKSetter, nil, nil)
end

local function _RegContractBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _ContractGetter, _ContractSetter, nil, nil)
end

local function _RegChildren_PrivacyBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _Children_PrivacyGetter, _Children_PrivacySetter, nil, nil)
end

local function _RegCreditBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _CreditGetter, _CreditSetter, nil, nil)
end

local function _RegDeleteAccountBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _DeleteAccountGetter, _DeleteAccountSetter, nil, nil)
end

local function _RegShowHistoricalRecordsBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _HistoricalRecordsGetter, _HistoricalRecordsSetter, nil, nil)
end

local function _RegPersonalInformationBatch(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _PersonalInformationGetter, _PersonalInformationSetter, nil, nil)
end

local function _RegPrivacyAssistant(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _PrivacyAssistantGetter, _PrivacyAssistantSetter, nil, nil)
end

local function _RegRankingInformation(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _RankingInformationGetter, _RankingInformationSetter, nil, nil)
end

local function _RegInvisible(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, _privacySetting, key, _InvisibleGetter, _InvisibleSetter, nil, nil)
end

local function _RegPermitStrangerPrivateChat(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _StrangerPrivateChatGetter, _StrangerPrivateChatSetter, nil, nil)
end

local function _RegPermitStrangerApplyFriends(id, key)
    local settingObj = _displaySetting
    SettingRegLogicHD.RegMapping(id, settingObj, key, _StrangerApplyFriendsGetter, _StrangerApplyFriendsSetter, nil, nil)
end

--------------------Developer

function PrivacySettingLogicHD._GPUCrashDebuggingGetter(settingObj, _, _)
    if isinvalid(_videoSetting) then
        log("Error : UClientVideoSettingHD is not valid")
        return
    end
    return _videoSetting:IsGPUCrashDebuggingEnabled()
end

function PrivacySettingLogicHD._GPUCrashDebuggingSetter(settingObj, _, value)
    if isinvalid(_videoSetting) then
        log("Error : UClientVideoSettingHD is not valid")
        return
    end
    local enable = value
    _videoSetting:SetGPUCrashDebuggingEnable(enable)
end

function PrivacySettingLogicHD._GPUCrashDebuggingApplyFunc(settingObj, key, value)
    if isinvalid(_videoSetting) then
        log("Error : UClientVideoSettingHD is not valid")
        return
    end
    local enable = value
    _videoSetting:SetGPUCrashDebuggingEnable(enable)
end

function PrivacySettingLogicHD._RegGPUCrashDebuggingBatch(id, key)
    local settingObj = _videoSetting
    SettingRegLogicHD.RegGetMapping(id, settingObj, key, PrivacySettingLogicHD._GPUCrashDebuggingGetter)
    SettingRegLogicHD.RegSetMapping(id, settingObj, key, PrivacySettingLogicHD._GPUCrashDebuggingSetter)
    SettingRegLogicHD.RegApplyMapping(id, settingObj, key, PrivacySettingLogicHD._GPUCrashDebuggingApplyFunc)
end

local function _privacySettingSetter(settingObj,key, value)
    settingObj[key] = value
end

local function _privacySettingGetter(settingObj,key)
    return settingObj[key]
end

function PrivacySettingLogicHD.Register()
    SettingRegLogicHD.BeginRegSection(_displaySetting)
    --SettingRegLogicHD.BeginRegSection(_privacySetting)
    _RegInvisible("bInvisible","bInvisible")
    --海外版
    _RegPrivacyPolicyBatch("PrivacyPolicy","PrivacyPolicy")
    _RegTermsOfUseBatch("TermsOfUse","TermsOfUse")
    SettingRegLogicHD.RegJumpMapping("PrivacyPolicy", PrivacySettingCommonLogic.OpenPrivacyPolicy)
    SettingRegLogicHD.RegJumpMapping("TermsOfUse", PrivacySettingCommonLogic.OpenTermsOfUse)

    -- BEGIN MODIFICATION @ VIRTUOS: 
    if IsConsole() then
        -- Add bind account feature for Console
        _RegBindAccountBatch("BindAccount","BindAccount")
        _RegUnbindAccountBatch("UnbindAccount","UnbindAccount")
        SettingRegLogicHD.RegJumpMapping("BindAccount", OpenBindAccount)
        SettingRegLogicHD.RegJumpMapping("UnbindAccount", OpenUnbindAccount)

        -- Add South Korean parental control feature for Console
        _RegKRParentalControl("KRParentalControl","KRParentalControl")
        SettingRegLogicHD.RegJumpMapping("KRParentalControl", OpenKRParentalControl)
    end
    -- END MODIFICATION

    --国内版
    _RegPrivacyBatch("Privacy","Privacy")
    _RegPrivacy_SDKBatch("Privacy_SDK","Privacy_SDK")
    _RegContractBatch("Contract","Contract")
    _RegChildren_PrivacyBatch("Children_Privacy","Children_Privacy")
    _RegCreditBatch("Credit","Credit")
    _RegDeleteAccountBatch("DeleteAccount","DeleteAccount")
    _RegShowHistoricalRecordsBatch("bShowHistoricalRecords","bShowHistoricalRecords")
    _RegPersonalInformationBatch("PersonalInformation","PersonalInformation")
    _RegPrivacyAssistant("PrivacyAssistant","PrivacyAssistant")
    _RegRankingInformation("bShowRankingInformation","bShowRankingInformation")
    _RegPermitStrangerPrivateChat("bPermitStrangerPrivateChat", "bPermitStrangerPrivateChat")
    _RegPermitStrangerApplyFriends("bPermitStrangerApplyFriends", "bPermitStrangerApplyFriends")

    -- developer setting
    PrivacySettingLogicHD._RegGPUCrashDebuggingBatch("GPUCrashDebugging", "GPUCrashDebugging")

    --SettingRegLogicHD.RegApplyMapping("bShowHistoricalRecords",_displaySetting,"bShowHistoricalRecords",_showHistoricalRecordsApplyFunc)
    SettingRegLogicHD.RegJumpMapping("Privacy",OpenPrivacy)
    SettingRegLogicHD.RegJumpMapping("Privacy_SDK",OpenPrivacy_SDK)
    SettingRegLogicHD.RegJumpMapping("Contract",OpenContract)
    SettingRegLogicHD.RegJumpMapping("Children_Privacy",OpenChildren_Privacy)
    SettingRegLogicHD.RegJumpMapping("Credit",OpenCredit)
    SettingRegLogicHD.RegJumpMapping("DeleteAccount",_showDeleteAccountConfirmWindow)
    SettingRegLogicHD.RegJumpMapping("PersonalInformation",_openPersonalInformationPanel)
    SettingRegLogicHD.RegJumpMapping("PrivacyAssistant",OpenPrivacyAssistant)
    SettingRegLogicHD.EndRegSection()
end



function PrivacySettingLogicHD.Init()
end

function PrivacySettingLogicHD.Uninit()
end


return PrivacySettingLogicHD
