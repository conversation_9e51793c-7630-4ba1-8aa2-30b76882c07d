----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"

---@class CollectionRoomCabinetEntranceItem : LuaUIBaseView
local CollectionRoomCabinetEntranceItem = ui("CollectionRoomCabinetEntranceItem")

function CollectionRoomCabinetEntranceItem:Ctor()
    self._wtButton = self:Wnd("DFButton_43", DFButtonOnly)
    self._wtButton:Event("OnClicked", self._OnButtonClicked, self)
    self._wtCabinetName = self:Wnd("DFTextBlock_72", UITextBlock)
    self._wtStoreState = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtSelected = self:Wnd("WBP_SlotCompSelected", UIWidgetBase)
    self._wtRootCanvas = self:Wnd("DFCanvasPanel_19", UIWidgetBase)
    self._wtImageIcon = self:Wnd("DFImage_Icon", UIImage)
    self._cabinetType = EShowCabinetType.None
    self._cabinetId = 0
    self._unlocked = false
    ---@type CollectionRoomSwitchCabinet
    self._parent = nil
    ---@type number
    self._subUIInstanceId = nil
    if IsHD() then
        self:SetCppValue("bIsFocusable", true)
    end
end

function CollectionRoomCabinetEntranceItem:OnClose()
    Facade.UIManager:ClearAllSubUI(self)
    self._subUIInstanceId = nil
end

function CollectionRoomCabinetEntranceItem:SetCabinetTypeAndIdAndParent(cabinetType, cabinetId, parent)
    self._parent = parent
    self._cabinetType = cabinetType
    self._cabinetId = cabinetId
    local itemNum, itemCapacity
    self._unlocked = Server.CollectionRoomServer:IsCabinetUnlocked(self._cabinetType, cabinetId)
    if self._cabinetType == EShowCabinetType.Display then
        self._wtCabinetName:SetText(CollectionRoomConfig.Loc.DisplayCabinetName)
    elseif self._cabinetType == EShowCabinetType.Special then
        self._wtCabinetName:SetText(CollectionRoomConfig.Loc.SpecialCabinetName)
    else
        self._wtCabinetName:SetText(CollectionRoomConfig.Loc.DIYCabinetName)
    end
    itemNum = CollectionRoomLogic.GetItemNumByCabinet(self._cabinetType, cabinetId)
    itemCapacity = CollectionRoomLogic.GetItemCapacityByCabinet(self._cabinetType, cabinetId)
    self._wtStoreState:SetText(string.format("%d/%d", itemNum, itemCapacity))
    self._wtSelected:Collapsed()
    if self:CheckCurrentShowCabinetIsSelf() then
        self._wtSelected:SelfHitTestInvisible()
    end
    if Server.CollectionRoomServer:IsCabinetUnlocked(self._cabinetType, self._cabinetId) then
        if self._subUIInstanceId then
            Facade.UIManager:RemoveSubUI(self, UIName2ID.IVMaskSmallLockComponent, self._subUIInstanceId)
            self._subUIInstanceId = nil
        end
    else
        if not self._subUIInstanceId then
            self._subUIInstanceId = Facade.UIManager:AddSubUI(self, UIName2ID.IVMaskSmallLockComponent, self._wtRootCanvas)
        end
    end
    if CollectionRoomConfig.CabinetEntranceIcon[cabinetType] then
        self._wtImageIcon:AsyncSetImagePath(CollectionRoomConfig.CabinetEntranceIcon[cabinetType])
    end
end

function CollectionRoomCabinetEntranceItem:_OnButtonClicked()
    if self._parent and self._parent.HideSwitchCabinetSide then
        self._parent:HideSwitchCabinetSide()
    end
    if self:CheckCurrentShowCabinetIsSelf() then
        return
    end
    if not Server.CollectionRoomServer:IsCabinetUnlocked(self._cabinetType, self._cabinetId) then
        Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.CabinetLockedTips)
        return
    end
    ---@type CollectionRoomPanel
    local grandParent
    local currentCabinetType, currentCabinetId
    if self._parent then
        grandParent, currentCabinetType, currentCabinetId = self._parent:GetParentAndCurrentCabinet()
    end
    if self._cabinetType == EShowCabinetType.Display then
        Module.CollectionRoom.Field.mainPanelCurrentCameraId = 2
        if grandParent then
            if currentCabinetType == EShowCabinetType.Special then
                ---@type CollectionRoomDisplaySpecialCabinetPanel
                local obj = grandParent
                obj:SwitchCabinetType(EShowCabinetType.Display)
                LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.Display)
                return
            end
            Facade.UIManager:CloseUI(grandParent)
        end
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDisplaySpecialCabinetPanel, nil, nil, true)
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.Display)
    elseif self._cabinetType == EShowCabinetType.Special then
        Module.CollectionRoom.Field.mainPanelCurrentCameraId = 1
        if grandParent then
            if currentCabinetType == EShowCabinetType.Display then
                ---@type CollectionRoomDisplaySpecialCabinetPanel
                local obj = grandParent
                obj:SwitchCabinetType(EShowCabinetType.Special)
                LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.Special)
                return
            end
            Facade.UIManager:CloseUI(grandParent)
        end
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDisplaySpecialCabinetPanel, nil, nil, false)
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.Special)
    else
        Module.CollectionRoom.Field.mainPanelCurrentCameraId = 3
        if grandParent then
            if currentCabinetType == EShowCabinetType.DIY then
                if currentCabinetId == self._cabinetId then
                    return
                end
                ---@type CollectionRoomDIYCabinetPanel
                local obj = grandParent
                obj:SwitchCabinetId(self._cabinetId)
                LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.DIY .. "," .. self._cabinetId)
                return
            end
            Facade.UIManager:CloseUI(grandParent)
        end
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDIYCabinetPanel, nil, nil, self._cabinetId)
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, EShowCabinetType.DIY .. "," .. self._cabinetId)
    end
end

function CollectionRoomCabinetEntranceItem:CheckCurrentShowCabinetIsSelf()
    local currentCabinetType, currentCabinetId
    if self._parent then
        _, currentCabinetType, currentCabinetId = self._parent:GetParentAndCurrentCabinet()
    end
    return currentCabinetType == self._cabinetType and currentCabinetId == self._cabinetId
end

function CollectionRoomCabinetEntranceItem:OnNativeOnMouseButtonUp(InGeometry, InGestureEvent)
    self:_OnButtonClicked()
end

return CollectionRoomCabinetEntranceItem