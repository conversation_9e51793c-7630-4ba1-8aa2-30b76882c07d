----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerLayer)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class FeatureLayerControllerBase : Object
FeatureLayerControllerBase = class("FeatureLayerControllerBase", Object)

function FeatureLayerControllerBase:Ctor(layerType)
    --- 当前层级类型
    self.layerType = layerType
    --- 当前层级的ui Ref强引用列表
    self._layerUIList = {}
end

function FeatureLayerControllerBase:_InternalResetLayerUIList(layerUIList)
    local bImmediate = true
    local bLuaPendingKill = true
    if layerUIList and next(layerUIList) then
        local length = #layerUIList
        for i = -length, -1 do
            local order = - i
            local layerUI = layerUIList[order]
            if not hasdestroy(layerUI) then
                logframe("[ GameFlow Debug ] ******* Lua Clean All UI _InternalResetLayerUIList -------------", self._cname, " Reset FinalCloseUI", layerUI._cname)
                Facade.UIManager:FinalCloseUI(layerUI, bImmediate, bLuaPendingKill)
            else
                logwarning("[ GameFlow Debug ] ******* Lua Clean All UI _InternalResetLayerUIList -------------", self._cname, " Reset but ui hasdestroy", hasdestroy(layerUI))
            end
        end
    end
end

function FeatureLayerControllerBase:Reset()
    local layerUIList = self._layerUIList
    self:_InternalResetLayerUIList(layerUIList)
    self._layerUIList = {}
end

function FeatureLayerControllerBase:Destroy()
    self:Reset()
end

--------------------------------------------------------------------------
--- Must Override
--------------------------------------------------------------------------
function FeatureLayerControllerBase:AddUI(uiIns, ...)
    logerror('[ Virtual Function ] You Must Override This Function')
    --- LifeCircle Process...
    self._layerUIList[#self._layerUIList + 1] = uiIns
    return uiIns
end

---@param uiIns LuaUIBaseView
function FeatureLayerControllerBase:RemoveUI(uiIns)
    logerror('[ Virtual Function ] You Must Override This Function')
    Facade.UIManager:FinalCloseUI(uiIns)
end

function FeatureLayerControllerBase:InternalRemoveUI(uiIns)
    table.removebyvalue(self._layerUIList, uiIns)
end
--------------------------------------------------------------------------
--- default
--------------------------------------------------------------------------
function FeatureLayerControllerBase:GetTopZOrder()
    local zOrder = UILayer2ZOrder[self.layerType] + #self._layerUIList
    return zOrder
end

function FeatureLayerControllerBase:GetNewestUI()
    if self._layerUIList and next(self._layerUIList) then
        return self._layerUIList[#self._layerUIList]
    else
        return nil
    end
end

--- 慎用，可能过于大量
function FeatureLayerControllerBase:TryGetUIInsByNavID(uiNavId, bIncludeDeactive)
    bIncludeDeactive = setdefault(bIncludeDeactive, false)
    if self._layerUIList and next(self._layerUIList) then
        for idx, layerUI in ipairs(self._layerUIList) do
            if not hasdestroy(layerUI) and layerUI.UINavID == uiNavId then
                if bIncludeDeactive and not rawget(layerUI, "_is_enable_") then
                    return layerUI
                else
                    if rawget(layerUI, "_is_enable_") then
                        return layerUI
                    end
                end
            end
        end
    else
        return nil
    end
end

return FeatureLayerControllerBase
