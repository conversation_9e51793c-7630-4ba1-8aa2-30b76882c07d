----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestLinePanel : LuaUIBaseView
local QuestLinePanel = ui("QuestLinePanel")
local FAnchors = import "Anchors"
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local UGPInputHelper = import "GPInputHelper"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local QUEST_LINE_ITEM_WIDTH = 290

function QuestLinePanel:Ctor()
    self._wtQuestLineBorder = self:Wnd("wQuestLineBorder", UIWidgetBase)
    self._wtQuestLineContent = self:Wnd("wQuestLineContent", UIWidgetBase)
    
    self._wtWBP_SignalItem_1 = self:Wnd("WBP_SignalItem_1", UIWidgetBase)
    self._wtWBP_SignalItem_2 = self:Wnd("WBP_SignalItem_2", UIWidgetBase)
    self._wtWBP_SignalItem_3 = self:Wnd("WBP_SignalItem_3", UIWidgetBase)

    self._wtQuestList = self:Wnd("Hori_SignalList", UIWidgetBase)
    
    --- 赛季任务 ---    
    self._wtSeasonList = self:Wnd("Hori_SeasonalList", UIWidgetBase)
    self._wtStarText = self:Wnd("DFRichTextBlock_61", UITextBlock)
    self._wtWBP_SignalItem_4 = self:Wnd("WBP_SignalItem_4", UIWidgetBase)
    self._wtWBP_SignalItem_5 = self:Wnd("WBP_SignalItem_5", UIWidgetBase)

    self._bDraggingLineView = false
    self._questLineInfo = nil
    self._lineBorderSize = FVector2D(0, 0)
    self._curAsycLoadNum = 0
    self:_AddEventListener()

    self._bIsSeasonStage = false
    self._lineInfo = nil
    self._stageInfo = nil

    self._focusWidget = nil
end

------------------------------------ Override function ------------------------------------
function QuestLinePanel:OnInitExtraData(questLineInfo, targetQuest, bIsSeasonStage)

    if questLineInfo == nil then
        logerror(" QuestLinePanel OnInitExtraData : questLineInfo is nil ")
        return
    end

    self._bIsSeasonStage = bIsSeasonStage
    if bIsSeasonStage then
        self._stageInfo = questLineInfo
        self._lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    else
        self._questLineInfo = questLineInfo
        Module.CommonBar:RegStackUITopBarTitle(self, self._questLineInfo.lineName)
    end
    if targetQuest then
        Module.Quest.Field.jumpToQuestInfo = targetQuest
    end
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
end

function QuestLinePanel:OnOpen()
    -- 大界面为缓存，只会执行一次OnOpen
end

function QuestLinePanel:OnShowBegin()
    --清除一下分帧任务,避免任务堆积
    Facade.LuaFramingManager:CancelAllFrameTasks(self)

    Module.Quest.Field:SetQuestLinePanelHandle(self)
    Module.Quest.Field:SetQuestLinePanelItemObject(self._wtQuestLineContent)
    Module.Quest.Field.lineItemInstanceId = {}

    if self._bIsSeasonStage then
        self:_UpdateStageInfo()
    else
        self:_UpdateQuestLineInfo(self._questLineInfo)
    end

    if IsHD() then
        self:_EnableGamepad()
    end

end

function QuestLinePanel:OnShow()
    self:SetVisibility(ESlateVisibility.Visible)
end

function QuestLinePanel:OnHideBegin()
    --清除一下分帧任务,避免任务堆积
	Facade.LuaFramingManager:CancelAllFrameTasks(self)
    if IsHD() then
        self:_DisableGamepad()
    end
end

function QuestLinePanel:OnHide()
    -- 前往记录当前打开任务详情页，返回任务界面时候打开
    -- Module.Quest.Field:SetCurQuestDetailInfo(self._objectiveInfo.ownerQuestInfo)
    -- Module.Quest:CloseSOLQuestDetailView()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestLineContent)
    Module.Quest.Field:SetQuestLinePanelItemObject(nil)
    Module.Quest.Field:SetQuestLinePanelHandle(nil)

    --清除一下分帧任务,避免任务堆积
	Facade.LuaFramingManager:CancelAllFrameTasks(self)
end

function QuestLinePanel:OnActivate()
end

function QuestLinePanel:OnDeactivate()
    Module.Quest.Field.jumpToQuestInfo = nil
    Module.Quest.Field:ResetQuestLineContentSize()
    Module.Quest.Field:ResetQuestLineItemPos()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestLineContent)
    Facade.LuaFramingManager:CancelAllFrameTasks(self)
end

function QuestLinePanel:OnClose()
    Module.Quest.Field.jumpToQuestInfo = nil
    Module.Quest.Field.lineItemInstanceId = {}
    self:_RemoveEventListener()
    Facade.UIManager:ClearSubUIByParent(self, self._wtQuestLineContent)
    Module.Quest.Field:ResetQuestLineContentSize()
    Module.Quest.Field:ResetRewardItemInfo()
    Module.Quest.Field:ResetQuestLineItemPos()
    Module.Quest.Field:SetQuestLinePanelHandle(nil)
    --清除一下分帧任务,避免任务堆积
	Facade.LuaFramingManager:CancelAllFrameTasks(self)
end

function QuestLinePanel:OnNativeOnTouchStarted(inGeometry, inGestureEvent)
    local absolutePosition = inGestureEvent:GetScreenSpacePosition()
    local lineCanvasGeo = self._wtQuestLineBorder:GetCachedGeometry()
    local localPosition = UE.SlateBlueprintLibrary.AbsoluteToLocal(lineCanvasGeo, absolutePosition)
    self._lineBorderSize = lineCanvasGeo:GetLocalSize()
    local touchBox = FBox2D(FVector2D(0, 0), self._lineBorderSize)
    if touchBox:IsInside(localPosition) then
        self._bDraggingLineView = true
        self._dragPos = absolutePosition
    end
end

function QuestLinePanel:OnNativeOnTouchMoved(inGeometry, inGestureEvent)
    if self._bDraggingLineView then
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local moveDelta = absolutePosition - self._dragPos
        self:_MoveLineContent(self._wtQuestLineContent.RenderTransform.Translation + moveDelta)
        self._dragPos = absolutePosition
    end
end

function QuestLinePanel:ClearAllItems()
    Facade.UIManager:ClearSubUIByParent(self, self._wtQuestLineContent)
end

function QuestLinePanel:OnNativeOnTouchEnded(inGeometry, inGestureEvent)
    self._bDraggingLineView = false
end

---------------------------Touch改Mouse--------------------------------------
function QuestLinePanel:OnNativeOnMouseButtonDown(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local lineCanvasGeo = self._wtQuestLineBorder:GetCachedGeometry()
        local localPosition = UE.SlateBlueprintLibrary.AbsoluteToLocal(lineCanvasGeo, absolutePosition)
        self._lineBorderSize = lineCanvasGeo:GetLocalSize()
        local touchBox = FBox2D(FVector2D(0, 0), self._lineBorderSize)
        if touchBox:IsInside(localPosition) then
            self._bDraggingLineView = true
            self._dragPos = absolutePosition
        end
    end
end

function QuestLinePanel:OnNativeOnMouseMove(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        if self._bDraggingLineView then
            local absolutePosition = inGestureEvent:GetScreenSpacePosition()
            local moveDelta = absolutePosition - self._dragPos
            self:_MoveLineContent(self._wtQuestLineContent.RenderTransform.Translation + moveDelta * 1.2)
            self._dragPos = absolutePosition
        end
    end
end

function QuestLinePanel:OnNativeOnMouseButtonUp(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        self._bDraggingLineView = false
    end
end

---------------------------Touch改Mouse--------------------------------------

------------------------------------ Event listener function ------------------------------------
function QuestLinePanel:_OnJumpToQuestLineItem(questItemPos)
    if questItemPos then
        local function fCallback()
            self._lineBorderSize = self._wtQuestLineBorder:GetCachedGeometry():GetLocalSize()
            if self._lineBorderSize and self._lineBorderSize.X ~= 0 then
                self:_MoveLineContent(
                    {
                        X = -(questItemPos.X - self._lineBorderSize.X / 2), ---(questItemPos.X + QUEST_LINE_ITEM_WIDTH - self._lineBorderSize.X / 2),
                        Y = -questItemPos.Y
                    },
                    true
                )
            else
                Facade.LuaFramingManager:RegisterFrameTask(fCallback,self)
            end
        end
        Facade.LuaFramingManager:CancelAllFrameTasks(self)
        Facade.LuaFramingManager:RegisterFrameTask(fCallback,self)

    end
end

function QuestLinePanel:_OnQuestLinePanelJumpedFinish(widget)
    self._focusWidget = widget
    if self._wtNavGroup1 then
        WidgetUtil.SetUserFocusToWidget(self._focusWidget, true)
    end
end

function QuestLinePanel:_OnQuestJumpToDetailPanel(questId)
    
    if self._bIsSeasonStage then
        Module.Jump:JumpByID(Module.Quest.Config.QuestSeasonListPanelJumpID, questId)  
    else
        Module.Jump:JumpByID(Module.Quest.Config.QuestlineId2Jumpid[self._questLineInfo.questLineId], questId)
    end

end
------------------------------------ private function ------------------------------------
function QuestLinePanel:_AddEventListener()
    self:AddLuaEvent(Module.Quest.Config.evtJumpToQuestLineItem, self._OnJumpToQuestLineItem, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtQuestExitDetailView, self.ClearAllItems, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestLinePanelJumpedFinish, self._OnQuestLinePanelJumpedFinish, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestJumpToDetailPanel, self._OnQuestJumpToDetailPanel, self)
end

function QuestLinePanel:_RemoveEventListener()
    self:RemoveLuaEvent(Module.Quest.Config.evtJumpToQuestLineItem)
    self:RemoveLuaEvent(Server.QuestServer.Events.evtQuestExitDetailView)
end

function QuestLinePanel:_MoveLineContent(translation, bAutoFix)
    local contentSize = Module.Quest.Field:GetQuestLineContentSize()
    if (translation.X + contentSize.X > self._lineBorderSize.X) and 
        (translation.X + QUEST_LINE_ITEM_WIDTH + 400 * 3 <= self._lineBorderSize.X and -(contentSize.X - QUEST_LINE_ITEM_WIDTH - 290 - 20 * 2) <= translation.X) and
            -- 限制X轴移动范围
            (translation.Y == 0 or
                (translation.Y > 0 and translation.Y <= -contentSize.upY - self._lineBorderSize.Y / 2) or
                -- 限制向上移范围
                (translation.Y < 0 and -translation.Y <= contentSize.downY - self._lineBorderSize.Y / 2))
     then -- 限制向下移范围
        -- self._dragPos = absolutePosition
        -- 自动调节超框移动
        self._wtQuestLineContent:SetRenderTranslation(translation)
    elseif bAutoFix then
        if translation.X > QUEST_LINE_ITEM_WIDTH then
            translation.X = QUEST_LINE_ITEM_WIDTH
        elseif math.abs(translation.X) > QUEST_LINE_ITEM_WIDTH and (translation.X + contentSize.X < self._lineBorderSize.X) then
            translation.X = self._lineBorderSize.X - contentSize.X
        end
        if translation.Y > 0 and translation.Y > -contentSize.upY - self._lineBorderSize.Y / 2 then
            translation.Y = -contentSize.upY - self._lineBorderSize.Y / 2
        elseif translation.Y < 0 and -translation.Y > contentSize.downY - self._lineBorderSize.Y / 2 then
            translation.Y = self._lineBorderSize.Y / 2 - contentSize.downY
        end
        self._wtQuestLineContent:SetRenderTranslation(translation)
    end
end

local itemAnchor = FAnchors()
itemAnchor.Minimum = FVector2D(0, 0.5)
itemAnchor.Maximum = FVector2D(0, 0.5)
function QuestLinePanel:_UpdateQuestItemSlotLayout(canvasSlot)
    canvasSlot:SetAnchors(itemAnchor)
    canvasSlot:SetAlignment(FVector2D(0, 0.5))
    canvasSlot:SetPosition(FVector2D(22, 0))
    canvasSlot:SetAutoSize(true)
    -- canvasSlot:SetOffsets(FMargin(100,85,100,30))
end

function QuestLinePanel:_UpdateQuestLineInfo(questLineInfo)
    self._questLineInfo = questLineInfo
    local list = Server.QuestServer:GetAllQuestsByQuestLine(questLineInfo)
    local LineQuestsDroDownTypes = {}
    for _, questInfo in ipairs(list) do
        if table.contains(LineQuestsDroDownTypes, questInfo.type) == false then
            table.insert(LineQuestsDroDownTypes, questInfo.type)
        end
    end
    self._wtSeasonList:Collapsed()
    self._wtQuestList:SelfHitTestInvisible()
    if table.contains(LineQuestsDroDownTypes, QuestType.Mission) then
        self._wtWBP_SignalItem_1:SelfHitTestInvisible()
    end
    if table.contains(LineQuestsDroDownTypes, QuestType.Branch) then
        self._wtWBP_SignalItem_2:SelfHitTestInvisible()
    end
    if table.contains(LineQuestsDroDownTypes, QuestType.ImportantQuest) then
        self._wtWBP_SignalItem_3:SelfHitTestInvisible()
    end

    self:_UpdateLineView(self._questLineInfo.rootQuestId)
end

function QuestLinePanel:_UpdateLineView(rootQuestId)
    Module.Quest.Field.lineItemInstanceId = {}
    Module.Quest.Field:ResetQuestLineContentSize()
    self._wtQuestLineContent:SetRenderTranslation({X = 0, Y = 0})
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestLineContent)
    local rootQuestInfo = Server.QuestServer:GetQuestInfoById(rootQuestId)
    if rootQuestInfo ~= nil then
        local OnCreateSubUIFinished = function(weakUiIns)
            local lineItem = getfromweak(weakUiIns)
            lineItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            local questItemslot = UWidgetLayoutLibrary.SlotAsCanvasSlot(lineItem)
            self:_UpdateQuestItemSlotLayout(questItemslot)
            lineItem:RootUpdateChildLineItem(rootQuestInfo)

            local specGuideItemInfo =
                Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.questItem)
            if specGuideItemInfo and rootQuestInfo.id == tonumber(specGuideItemInfo[1]) then
                Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyQuestItem, uiIns)
            end
            specGuideItemInfo =
                Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.questItem2)
            if specGuideItemInfo and rootQuestInfo.id == tonumber(specGuideItemInfo[1]) then
                Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyQuestItem2, uiIns)
            end

            self._curAsycLoadNum = self._curAsycLoadNum - 1
            if self._curAsycLoadNum == 0 then
                Module.Quest.Config.evtQuestItemLoadFinish:Invoke()
            end
        end
        self._curAsycLoadNum = self._curAsycLoadNum + 1

        if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.QuestLineOneComponent) then
            local weakUiIns, instanceID
            instanceID = Module.Quest.Field.lineItemInstanceId[1]
            if instanceID then
                weakUiIns = Facade.UIManager:GetSubUI(self, UIName2ID.QuestLineOneComponent, instanceID)
				if weakUiIns and getfromweak(weakUiIns) then
					local uiIns = getfromweak(weakUiIns)
					uiIns:RefreshData(nil, 1, rootQuestInfo)
				end
            end
            if weakUiIns == nil then
                weakUiIns, instanceID =
                    Facade.UIManager:AddSubUI(
                    self,
                    UIName2ID.QuestLineOneComponent,
                    self._wtQuestLineContent,
                    nil,
                    nil,
                    1,
                    rootQuestInfo
                )
                Module.Quest.Field.lineItemInstanceId[1] = instanceID
            end

            if weakUiIns and getfromweak(weakUiIns) then
                if OnCreateSubUIFinished then
                    OnCreateSubUIFinished(weakUiIns)
                end
            else
                logerror("QuestLinePanel:_UpdateLineView:UIManager:AddSubUI: weakUiIns is nil!")
            end
        else
            local uiBatchId = Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.QuestLineOneComponent, OnCreateSubUIFinished, nil)
        end
    end
end

function QuestLinePanel:_UpdateStageInfo()

    local stageID = self._stageInfo.stageID
    self._wtQuestList:Collapsed()
    self._wtSeasonList:SelfHitTestInvisible()
    local gained = self._lineInfo:CalGainStarByStageID(stageID)
    local total = self._lineInfo:CalTotalStarByStageID(stageID)
    self._wtStarText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonLinePanelStar, gained, total))
    
    if self._lineInfo:GetMainGroupIDByStageID(stageID) ~= -1 then
        self._wtWBP_SignalItem_4:SelfHitTestInvisible()
    end

    if #self._lineInfo:GetBranchGroupIDsByStageID(stageID) ~= 0 then
        self._wtWBP_SignalItem_5:SelfHitTestInvisible()
    end

    self:_UpdateStageView()
end

function QuestLinePanel:_UpdateStageView()
    Module.Quest.Field.lineItemInstanceId = {}
    Module.Quest.Field:ResetQuestLineContentSize()
    self._wtQuestLineContent:SetRenderTranslation({X = 0, Y = 0})
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestLineContent)

    local stageID = self._stageInfo.stageID
    local EQuestSeasonType = Module.Quest.Config.EQuestSeasonType
    local lastGroup = nil


    local mainGroupID = self._lineInfo:GetMainGroupIDByStageID(stageID)
    local sortedGroupList = {mainGroupID}

    local HardGroups = self._lineInfo:GetSortedBranchGroupByStageIDAndType(stageID, EQuestSeasonType.Hard)
    table.append(sortedGroupList, HardGroups)

    local EasyGroups = self._lineInfo:GetSortedBranchGroupByStageIDAndType(stageID, EQuestSeasonType.Easy)
    table.append(sortedGroupList, EasyGroups)

    local midgroupId = sortedGroupList[math.ceil(#sortedGroupList / 2)]
    local midGroup = self:_UpdateGroup(midgroupId, nil, 
        midgroupId == mainGroupID or midgroupId == HardGroups[#HardGroups], true)

    lastGroup = midGroup
    for i = math.ceil(#sortedGroupList / 2) - 1, 1, -1 do
        lastGroup = self:_UpdateGroup(sortedGroupList[i], lastGroup, 
            (sortedGroupList[i] == mainGroupID and #HardGroups > 0 ) or 
            (sortedGroupList[i] == HardGroups[#HardGroups] and #EasyGroups > 0), true)
    end
    lastGroup = midGroup
    for i = math.ceil(#sortedGroupList / 2) + 1, #sortedGroupList, 1 do
        lastGroup = self:_UpdateGroup(sortedGroupList[i], lastGroup, 
            (sortedGroupList[i] == mainGroupID and #HardGroups > 0 ) or 
            (sortedGroupList[i] == HardGroups[#HardGroups] and #EasyGroups > 0), false)
    end

end

function QuestLinePanel:_UpdateGroup(groupId, lastGroup, bShowDecoLine, bIsAbove)
    local stageID = self._stageInfo.stageID
    local weak, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestSeasonLineHeadEntry, self._wtQuestLineContent, nil,
                    self._lineInfo, stageID, groupId, bShowDecoLine)
    local uiIns = getfromweak(weak)
    uiIns:SetPosition(lastGroup, bIsAbove)

    local rootQuestInfo = self._lineInfo:GetRootQuestByGroupId(groupId)
    rootQuestInfo = Server.QuestServer:GetQuestInfoById(rootQuestInfo)

    if rootQuestInfo ~= nil then
        weak, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.QuestLineOneComponent, self._wtQuestLineContent, nil,
                    nil, 1, rootQuestInfo)
        local rootUIIns = getfromweak(weak)

        rootUIIns:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        rootUIIns:SeasonRootUpdatePosition(uiIns, rootQuestInfo)

    end

    return uiIns
end

---------- Gamepad ----------------

function QuestLinePanel:_EnableGamepad()
    self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtQuestLineContent, self, "Hittest")
    if self._wtNavGroup1 then
        self._wtNavGroup1:AddNavWidgetToArray(self._wtQuestLineContent)
    end  

    if self._focusWidget then
        WidgetUtil.SetUserFocusToWidget(self._focusWidget, true)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)
    end

    self:_InitShortcuts()
end

function QuestLinePanel:_DisableGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtNavGroup1 = nil
    self._focusWidget = nil
    self:_RemoveShortcuts()
end

-- BEGIN MODIFICATION @ VIRTUOS : 新增手柄快捷键（右摇杆可以滚动线路图，已达到和拖拽相同的功能）
function QuestLinePanel:_InitShortcuts()
    if self._SetOffsetX == nil then
        self._SetOffsetX = self:AddAxisInputActionBinding("Common_Right_X", self._SetScrollOffsetX, self, EDisplayInputActionPriority.UI_Pop)
    end
    if self._SetOffsetY == nil then
        self._SetOffsetY = self:AddAxisInputActionBinding("Common_RIght_Y", self._SetScrollOffsetY, self, EDisplayInputActionPriority.UI_Pop)
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList({
        {actionName = "QuestLine_Preview",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
    }, false, true)
end

function QuestLinePanel:_SetScrollOffsetX(InXValue)
    local Offset = -math.sin(InXValue) * 37
    self:_MoveLineContent(self._wtQuestLineContent.RenderTransform.Translation +  FVector2D(Offset, 0))
end

function QuestLinePanel:_SetScrollOffsetY(InYValue)
    local Offset = math.sin(InYValue) * 37
    self:_MoveLineContent(self._wtQuestLineContent.RenderTransform.Translation + FVector2D(0, Offset))
end

function QuestLinePanel:_RemoveShortcuts()
    if self._SetOffsetX then
        self:RemoveInputActionBinding(self._SetOffsetX)
        self._SetOffsetX = nil
    end
    if self._SetOffsetY then
        self:RemoveInputActionBinding(self._SetOffsetY)
        self._SetOffsetY = nil
    end
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end
-- END MODIFICATION


------------------------------------ public function ------------------------------------

return QuestLinePanel
