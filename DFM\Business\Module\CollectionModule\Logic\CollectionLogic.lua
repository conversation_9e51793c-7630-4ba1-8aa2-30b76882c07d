----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionLogic = {}
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"

---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开
CollectionLogic.DoSomeThingProcess = function(...)
    -- TODO 业务逻辑、弹窗Tips表现、发送Server Req等等一系列事情
    -- Server.CollectionServer:DoSomeThingReq(...)
    -- return 
end
CollectionLogic.ShowMainPanelProcess = function(tabIndex)
    Module.ModuleSwitcher:ModuleOpenByCheck(SwitchSystemID.SwitchSystemCollection, UIName2ID.CollectionMainPanel, nil, CollectionLogic.OnMainPanelCreateFinished,nil, tabIndex)
end
CollectionLogic.OnMainPanelCreateFinished = function(uiIns)
    Module.Collection.Field:SetMainPanel(uiIns)
end
CollectionLogic.CloseMainPanelProcess = function()
    local mainPanel = Module.Collection.Field:GetMainPanel()
    if mainPanel then
        Facade.UIManager:CloseUI(mainPanel)
        Module.Collection.Field:SetMainPanel(nil)
    end
end


CollectionLogic.GetCollectionItems = function(mainTabIndex, subTabIndex)
    local tabInfo = Module.Collection.Field:GetTabInfo()
    local mainTabInfo = tabInfo[mainTabIndex]
    if table.isempty(mainTabInfo) then
        return {}
    end
    local mainTypes = {}
    local subTypes = {}
    if subTabIndex > 0 and not table.isempty(mainTabInfo.subTabList) then
        local subTabInfo = mainTabInfo.subTabList[subTabIndex]
        if not table.isempty(subTabInfo) then
            if #mainTabInfo.MainTabToMainTypes > 0  and #subTabInfo.SubTabToMainTypes > 0 then
                for index, mainType in ipairs(mainTabInfo.MainTabToMainTypes) do
                    if table.contains(subTabInfo.SubTabToMainTypes, mainType) then
                        table.insert(mainTypes, mainType)
                    end
                end
            else
                mainTypes = mainTabInfo.MainTabToMainTypes
            end
            if #mainTabInfo.MainTabToSubTypes > 0 and #subTabInfo.SubTabToSubTypes > 0 then
                for index, subType in ipairs(mainTabInfo.MainTabToSubTypes) do
                    if table.contains(subTabInfo.SubTabToSubTypes, subType) then
                        table.insert(subTypes, subType)
                    end
                end
            else
                subTypes = mainTabInfo.MainTabToSubTypes
            end
        else
            mainTypes = mainTabInfo.MainTabToMainTypes
            subTypes = mainTabInfo.MainTabToSubTypes
        end
    else
        mainTypes = mainTabInfo.MainTabToMainTypes
        subTypes = mainTabInfo.MainTabToSubTypes
    end  
    local items = {}
    for index, mainType in ipairs(mainTypes) do
        if #subTypes > 0 then
            for index, subType in ipairs(subTypes) do
                table.append(items, Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(mainType, subType))
            end
        else
            table.append(items, Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(mainType))
        end
    end
    local visibleItems = {}
    for index, item in ipairs(items) do
        if Server.CollectionServer:IsVisibleProp(item.id) == true then
            table.insert(visibleItems, item)   
        end
    end
    return visibleItems
end

CollectionLogic.ResetWeaponListWithSkins = function()
    Module.Collection.Field:SetWeaponListWithSkins(nil)
end


CollectionLogic.GetWeaponSeriesList = function(tabIndex, bIgnoreVisible)
    local weaponListMap = {}
    local weaponSkinsMap = Server.CollectionServer:GetAllWeaponSkins()
    local weaponSkinIds = table.keys(weaponSkinsMap)
    local bShouldInclude
    local itemSubType
    for index, skinId in ipairs(weaponSkinIds) do
        bShouldInclude = true
        if not bIgnoreVisible and not Server.CollectionServer:IsVisibleProp(skinId) then
            bShouldInclude = false
        end
        if bShouldInclude then
            local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(skinId)
            if skinDataRow and skinDataRow.BaseWeaponId then
                itemSubType = ItemHelperTool.GetSubTypeById(skinId)
                declare_if_nil(weaponListMap, itemSubType,{gunTypeId=itemSubType, weapons={}})
                declare_if_nil(weaponListMap[itemSubType].weapons, skinDataRow.BaseWeaponId, skinDataRow.BaseWeaponId)
            end
        end
    end
    local weaponList = {}
    for gunTypeId, weaponSeries in pairs(weaponListMap) do
        weaponSeries.weapons = table.values(weaponSeries.weapons)
        table.sort(weaponSeries.weapons, function (a, b)
            return a < b
        end)
        if tabIndex == 1 or tabIndex == nil then
            table.insert(weaponList, weaponSeries)
        elseif tabIndex == gunTypeId + 1 then
            table.insert(weaponList, weaponSeries)
            break
        end
    end
    table.sort(weaponList, function (a, b)
        return a.gunTypeId < b.gunTypeId
    end)
    return weaponList
end

CollectionLogic.GetWeaponPreset = function()
    local weaponpresentListMap = Module.Collection.Field:GetWeaponPresentListMap()
    if weaponpresentListMap == nil then 
        local config = Module.Collection.Config.RecFunctionConfig
        weaponpresentListMap = {}
        for k,data in pairs(config) do
            if data.IsBaseWeapon and data.MPUnlockType ~= 0 then
                local subType = ItemHelperTool.GetSubTypeById(data.DefaultPreset)
                declare_if_nil(weaponpresentListMap, subType,{gunTypeId=subType, weapons={}})
                declare_if_nil(weaponpresentListMap[subType].weapons, data.RecFunctionId, data.RecFunctionId)
            end
        end
        Module.Collection.Field:SetWeaponPresentListMap(weaponpresentListMap)
    end
    local weaponList = {}
    for gunTypeId, weaponSeries in pairs(weaponpresentListMap) do
        weaponSeries.weapons = table.values(weaponSeries.weapons)
        table.sort(weaponSeries.weapons, function (a, b)
            return a < b
        end)
        table.insert(weaponList, weaponSeries)
    end
    table.sort(weaponList, function (a, b)
        return a.gunTypeId < b.gunTypeId
    end)
    return weaponList
end

CollectionLogic.GetWeaponSkins = function(skinGroup, skinType, baseWeaponId, weaponType, qualityType, seasonId, bIgnoreInstances, bIgnoreVisible)
    local defaultWeaponSkins = Server.CollectionServer:GetAllWeaponSkins()
    if skinGroup == Module.Collection.Config.EItemGroup.Owned then
        defaultWeaponSkins = Server.CollectionServer:GetOwnedWeaponSkins()
    elseif skinGroup == Module.Collection.Config.EItemGroup.NotOwned then
        local findWeaponSkins = {}
        for skinId, group in pairs(defaultWeaponSkins) do
            if not Server.CollectionServer:IsOwnedWeaponSkin(skinId) then
                declare_if_nil(findWeaponSkins, skinId, group)
            end 
        end
        defaultWeaponSkins = findWeaponSkins 
    end
    if skinType ~= nil and skinType ~= Module.Collection.Config.EItemType.Any then
        local findWeaponSkins = {}
        local bMysticalType = skinType == Module.Collection.Config.EItemType.Mystical
        for skinId, group in pairs(defaultWeaponSkins) do
            if ItemHelperTool.IsMysticalSkin(skinId) == bMysticalType then
                declare_if_nil(findWeaponSkins, skinId, group)
            end
        end
        defaultWeaponSkins = findWeaponSkins
    end
    if baseWeaponId ~= nil and baseWeaponId > 0 then
        local findWeaponSkins = {}
        for skinId, group in pairs(defaultWeaponSkins) do
            if CollectionLogic.GetBaseWeaponIdFromSkinId(skinId) == baseWeaponId then
                declare_if_nil(findWeaponSkins, skinId, group)
            end
        end
        defaultWeaponSkins = findWeaponSkins
    end
    if weaponType ~= nil and weaponType > 0 then
        local findWeaponSkins = {}
        for skinId, group in pairs(defaultWeaponSkins) do
            if ItemHelperTool.GetSubTypeById(skinId) == weaponType then
                declare_if_nil(findWeaponSkins, skinId, group)
            end
        end
        defaultWeaponSkins = findWeaponSkins
    end
    if seasonId ~= nil and seasonId > 0 then
        local findWeaponSkins = {}
        for skinId, group in pairs(defaultWeaponSkins) do
            local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(id)
            if skinDataRow and skinDataRow.SeasonID == seasonId then
                declare_if_nil(findWeaponSkins, skinId, group)
            end
        end
        defaultWeaponSkins = findWeaponSkins
    end
    local findWeaponSkins = {}
    local skinItems
    local bShouldInclude
    for skinId, group in pairs(defaultWeaponSkins) do
        bShouldInclude = true
        skinItems = table.values(group)
        if not bIgnoreVisible and not Server.CollectionServer:IsVisibleProp(skinId) then
            bShouldInclude = false
        elseif qualityType ~= nil and qualityType >= 0 then
            if skinItems[1] ~= nil and skinItems[1].quality ~= qualityType then
                bShouldInclude = false
            end
        end
        if bShouldInclude then
            if Server.CollectionServer:IsOwnedWeaponSkin(skinId) and ItemHelperTool.IsMysticalSkin(skinId) then
                local skinInstances = CollectionLogic.GetMysticalSkinInstanceById(skinId)
                if bIgnoreInstances and #skinInstances > 1 then
                    skinItems = {skinInstances[1]}
                end
            end
            table.append(findWeaponSkins, skinItems) 
        end
    end
    return findWeaponSkins
end


CollectionLogic.GetMysticalSkinInstanceById = function(skinId)
    local mysticalWeaponSkinInstances
    if Server.CollectionServer:IsOwnedWeaponSkin(skinId) then
        local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
        mysticalWeaponSkinInstances = Module.Collection.Field:GetMysticalSkinInstanceList(skinId)
        if #mysticalWeaponSkinInstances == 0 and ownedSkins[skinId]then
            mysticalWeaponSkinInstances = table.values(ownedSkins[skinId])
            if #mysticalWeaponSkinInstances > 1 then
                table.sort(mysticalWeaponSkinInstances, CollectionLogic.MysticalSkinInstanceListSort)
            end
            if #mysticalWeaponSkinInstances > 0 then
                Module.Collection.Field:SetMysticalSkinInstanceList(skinId, mysticalWeaponSkinInstances)
            end
        end
    end
    return mysticalWeaponSkinInstances or {}
end

CollectionLogic.GetPendants = function(pendantGroup, pendantType, baseWeaponId, weaponType, qualityType, seasonId)
    local defaultPendants = Server.CollectionServer:GetAllHangings()
    if pendantGroup ~= nil and pendantGroup ~= Module.Collection.Config.EItemGroup.Any then
        local ownedPendants = Server.CollectionServer:GetOwnedHangings()
        local findPendants = {}
        local bOwnedGroup = pendantGroup == Module.Collection.Config.EItemGroup.Owned
        for id, group in pairs(defaultPendants) do
            if Server.CollectionServer:IsOwnedHanging(id) == bOwnedGroup then
                declare_if_nil(findPendants, id, ownedPendants[id] or group)
            end
        end
        defaultPendants = findPendants
    end
    if pendantType ~= nil and pendantType ~= Module.Collection.Config.EItemType.Any then
        local findPendants = {}
        local bMysticalType = pendantType == Module.Collection.Config.EItemType.Mystical
        for id, group in pairs(defaultPendants) do
            if ItemHelperTool.IsMysticalPendant(id) == bMysticalType then
                declare_if_nil(findPendants, id, group)
            end
        end
        defaultPendants = findPendants
    end
    if baseWeaponId ~= nil and baseWeaponId > 0 then
    end

    if weaponType ~= nil and weaponType > 0 then
    end

    if seasonId ~= nil and seasonId > 0 then
        local findPendants = {}
        for id, group in pairs(defaultPendants) do
            local pendantConfig = CollectionLogic.GetPendantDataRow(id)
            if pendantConfig then 
                if pendantConfig.SeasonID == seasonId then 
                    declare_if_nil(findPendants, id, group)
                end
            end
        end
        defaultPendants = findPendants
    end

    local findPendants = {}
    for id, group in pairs(defaultPendants) do
        if Server.CollectionServer:IsVisibleProp(id) then
            local items = table.values(group)
            -- if ItemHelperTool.IsMysticalPendant(id) then
            --     table.sort(items, CollectionLogic.MysticalPendantRaritySortDecend)
            -- end
            if qualityType ~= nil and qualityType >= 0 then
                if items[1] ~= nil and items[1].quality == qualityType then
                    table.append(findPendants, items) 
                end
            else
                table.append(findPendants, items) 
            end
        end
    end
    return findPendants
end


CollectionLogic.GetMandelBricks = function()
    local allMandelBricks = Server.CollectionServer:GetAllMandelBricks()
    local mandelBricks = {}
    for mandelBrickId, mandelBrickInstances in pairs(allMandelBricks) do
        if mandelBrickInstances[0] then
            if Server.CollectionServer:IsVisibleProp(mandelBrickId) then
                table.insert(mandelBricks, mandelBrickInstances[0])
            end
        end
    end
    table.sort(mandelBricks, CollectionLogic.MandelBrickDefaultSort)
    return mandelBricks
end

CollectionLogic.GetActivityMandelBricks = function()
    local mandelBricks = Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(EItemType.Gift, ECollectableType.LotteryBox)
    local tResult = {}
    for index, mandelBrickItem in ipairs(mandelBricks) do
        if Module.Store:IsActivityMandelBrick(mandelBrickItem.id) then
            if mandelBrickItem.num > 0 then
                table.insert(tResult, mandelBrickItem)
            end
        end
    end
    return tResult
end


CollectionLogic.GetWeaponSkinDataRow = function(skinId)
    return Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(skinId)) or Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(skinId))
end

CollectionLogic.GetPendantDataRow = function(id)
    return Module.Collection.Config.PendantConfig[tonumber(id)]
end

CollectionLogic.GetMysticalPendantSuitRow = function(seasonID)
    return Module.Collection.Config.MysticalPendantSuitConfig[tonumber(seasonID)]
end

CollectionLogic.GetBaseWeaponIdFromSkinId = function(skinId)
    local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(skinId)
    return skinDataRow and (skinDataRow.BaseWeaponId or skinDataRow.MeleeWeaponID) or 0
end

CollectionLogic.GetOwnedBlueprintItems = function()
    return Server.CollectionServer:GetOwnedWeaponSkins()
end


CollectionLogic.IsPropAvailableInCurrentMode = function(item)
    if item.inventory == 0 then
        return true
    end
    if CollectionLogic.IsInMp() then
        return item.inventory == 2 
    else
        return item.inventory == 1
    end
end


CollectionLogic.IsInMp = function()
    return Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby
end


CollectionLogic.getWeaponPropFromInventory = function(weaponId)
    local bInMp = CollectionLogic.IsInMp()
    local bagID, groupID = Server.ArmedForceServer:GetBagIDFromUIContext()
    local weaponProp = Server.InventoryServer:GetWeaponPropInfoByWeaponID(weaponId, bInMp and ESlotGroup.MPApply or ESlotGroup.Player, bagID)
    return weaponProp
end

CollectionLogic.EquipMeleeWeapon = function(skinId)
    local bInMp = CollectionLogic.IsInMp()
    Module.ArmedForce:TryHandleCIDEquipMeleeItem(skinId, bInMp and ESlotGroup.MPApply or ESlotGroup.Player)
end

CollectionLogic.CheckIfMeleeWeaponEquiped = function(skinId)
    local bInMp = CollectionLogic.IsInMp()
    return Module.ArmedForce:CheckCIDIsMeleeItemEquiped(skinId, bInMp and ESlotGroup.MPApply or ESlotGroup.Player)
end


CollectionLogic.ApplyWeaponSkin = function(weaponId, skinId, skinGid)
    local bInMp = CollectionLogic.IsInMp()
    local command = pb.WeaponApplySkinCmd:New()
    if command == nil then
        return
    end
    command.weapon_id = weaponId
    local bagID, groupID = Server.ArmedForceServer:GetBagIDFromUIContext()
    local weaponProp = Server.InventoryServer:GetWeaponPropInfoByWeaponID(weaponId, bInMp and ESlotGroup.MPApply or ESlotGroup.Player, bagID)
    command.weapon_gid = weaponProp ~= nil and weaponProp.gid or nil
    if bInMp then
        command.bag_id = bagID
    else
        command.apply_all = true
    end
    command.pendant_id = WeaponAssemblyTool.GetPendantIDFormPropInfo(weaponProp) or 0
    command.pendant_gid = WeaponAssemblyTool.GetPendantGUIDFormPropInfo(weaponProp) or 0
    if command.pendant_id > 0 then 
        -- command.pendant_apply_all = true
    end
    command.skin_id = skinId
    command.skin_gid = skinGid
    local datatype = bInMp and 1 or 0
    Server.GunsmithServer:C2SCSWAssemblyApplySkinReq(datatype, command)
end


CollectionLogic.GetWeaponSkinsCarring = function()
    local slotTypes = {
        ESlotType.Pistrol, 
        ESlotType.MainWeaponLeft,
        ESlotType.MainWeaponRight,
        ESlotType.MeleeWeapon
    }
    local bInMp = CollectionLogic.IsInMp()
    local skinPropInfoList = {}
    local weaponSlot
    local weaponItem
    local appliedSkinId
    local appliedSkinGid
    for index, slotType in ipairs(slotTypes) do
        weaponSlot = Server.InventoryServer:GetSlot(slotType, bInMp and ESlotGroup.MPApply or ESlotGroup.Player)
        if weaponSlot then
            weaponItem = weaponSlot:GetEquipItem()
            if weaponItem then
                appliedSkinId = nil
                appliedSkinGid = nil
                local weaponProp = weaponItem:GetRawPropInfo()
                if weaponProp then
                    appliedSkinId = weaponProp.weapon and weaponProp.weapon.skin_id or 0
                    appliedSkinGid = weaponProp.weapon and weaponProp.weapon.skin_gid or 0
                end
                local propInCollection = Server.CollectionServer:GetCollectionPropById(appliedSkinId, appliedSkinGid)
                if propInCollection then
                    table.insert(skinPropInfoList, propInCollection)
                end
            end
        end
    end 
    return skinPropInfoList
end


CollectionLogic.getWeaponlist= function ()
    local _listWeapon = {}
    local _defaultWeaponMp = {}
    local list = Server.InventoryServer:GetDefaultWeaponSkins()
    for k,v in pairs(list) do 
        _defaultWeaponMp[v.weapon_id] = true
    end
    for _, item in Server.InventoryServer:GetItemsIterator(ESlotGroup.MPApply) do
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        local isWeapon = weaponFeature and weaponFeature:IsWeapon()
        local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
        if isWeapon then 
            if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then
                table.insert(_listWeapon,item)
            end
        end
    end
    local weaponId2UnLockInfoMap = Server.InventoryServer:GetMPWeaponLockedInfoMap()
    for weaponId, unlockInfo in pairs(weaponId2UnLockInfoMap) do
        local itemSubType = ItemHelperTool.GetSubTypeById(weaponId)
        if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then
            local item = ItemBase:NewIns(weaponId, 0, 0)
            item:SetRawPropInfo(WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId))
            item:ClientSetNum(0)
            table.insert(_listWeapon,item)
        end
    end
    return _listWeapon
end

CollectionLogic.getPendantByEquiped = function(pendantId, pendantGid)
    pendantGid = pendantGid or 0
    local _listWeapon =  CollectionLogic.getWeaponlist()
    local weaponApplyList = {}
    local bInMp = CollectionLogic.IsInMp()
    local findItem = false
    for k, v in pairs(_listWeapon) do 
        local weaponId = v.id
        if bInMp then
            local _defaultWeaponMp = Server.InventoryServer:GetDefaultWeaponSkins()
            if _defaultWeaponMp[weaponId] then
                if _defaultWeaponMp[weaponId].pendant_id == pendantId then 
                    findItem = true
                    break
                end
            else
                local weaponProp = v.rawPropInfo
                if weaponProp == nil then
    
                else
                    if pendantGid == 0 then
                        if weaponProp.weapon.pendant_id == pendantId then 
                            findItem = true
                            break
                        end
                    else
                        if weaponProp.weapon.pendant_id == pendantId and weaponProp.weapon.pendant_gid == pendantGid then
                            findItem = true
                            break
                        end
                    end     
                end
            end
        else
            local appliedPendantId = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(weaponId)
            if pendantId == 0 then
                if appliedPendantId == pendantId then 
                    findItem = true
                    break
                end
            else
                local appliedPendantGid = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(weaponId)
                if appliedPendantId == pendantId and appliedPendantGid == pendantGid then
                    findItem = true
                    break
                end
            end
        end
    end
    if findItem then 
        if ItemHelperTool.IsMysticalPendant(pendantId) then
            if pendantGid == 0 then
                local list = CollectionLogic.GetMysticalPendantById(pendantId)
                return list and list[1] or nil
            else
                return Server.CollectionServer:GetHangingIfOwned(pendantId,pendantGid)
            end
        else
            return Server.CollectionServer:GetHangingIfOwned(pendantId,pendantGid)
        end
    end
    return nil
end

CollectionLogic.GetMysticalPendantById = function(pendantId)
    local mysticalPendantOwned = CollectionLogic.GetPendants(1,1)
    local mysticalPendantItems = {}
    for index,pendantItem in ipairs(mysticalPendantOwned)do
        if pendantId == pendantItem.id then 
            table.insert(mysticalPendantItems,pendantItem)
        end
    end
    table.sort(mysticalPendantItems,CollectionLogic.MysticalPendantRaritySortDecend)
    return mysticalPendantItems
end

CollectionLogic.getEquipPendants = function() --aniPendant 表示，只要是该ID的都替换
    local _listWeapon =  CollectionLogic.getWeaponlist()
    local _defaultWeaponMp = Server.InventoryServer:GetDefaultWeaponSkins()
    local pendantApplyList = {}
    for k, v in pairs(_listWeapon) do 
        local weaponId = v.id
        local bInMp = CollectionLogic.IsInMp()
        if bInMp then
            if _defaultWeaponMp[weaponId] then
                if _defaultWeaponMp[weaponId].pendant_id ~= 0 then
                    pendantApplyList[_defaultWeaponMp[weaponId].pendant_id] = _defaultWeaponMp[weaponId].pendant_gid
                end
            else
                local weaponProp = v.rawPropInfo
                if weaponProp == nil then
    
                else
                    if weaponProp.weapon.pendant_id ~= 0 then 
                        pendantApplyList[weaponProp.weapon.pendant_id] = weaponProp.weapon.pendant_gid
                    end
                end
            end
        else
            local appliedPendantId = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(weaponId)
            local appliedPendantGid = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(weaponId)
            if appliedPendantId ~= 0 then 
                pendantApplyList[appliedPendantId] = appliedPendantGid
            end
        end
    end
    return pendantApplyList
end

CollectionLogic.getWeaponlistByEquipPendant = function(pendantId, pendantGid,aniPendant) --aniPendant 表示，只要是该ID的都替换
    local _listWeapon =  CollectionLogic.getWeaponlist()
    local _defaultWeaponMp = Server.InventoryServer:GetDefaultWeaponSkins()
    local weaponApplyList = {}
    for k, v in pairs(_listWeapon) do 
        local weaponId = v.id
        local bInMp = CollectionLogic.IsInMp()
        if bInMp then
            if _defaultWeaponMp[weaponId] then
                if _defaultWeaponMp[weaponId].pendant_id == pendantId and (_defaultWeaponMp[weaponId].pendant_gid == pendantGid or aniPendant) then
                    table.insert(weaponApplyList,v)
                end
            else
                local weaponProp = v.rawPropInfo
                if weaponProp == nil then
    
                else
                    if not pendantGid or aniPendant then
                        if weaponProp.weapon.pendant_id == pendantId then 
                            table.insert(weaponApplyList,v)
                        end
                    else
                        if weaponProp.weapon.pendant_id == pendantId and weaponProp.weapon.pendant_gid == pendantGid then
                            table.insert(weaponApplyList,v)
                        end
                    end
                end
            end
        else
            local appliedPendantId = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(weaponId)
            if not pendantGid or aniPendant then
                if appliedPendantId == pendantId then 
                    table.insert(weaponApplyList,v)
                end
            else
                local appliedPendantGid = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(weaponId)
                if appliedPendantId == pendantId and appliedPendantGid == pendantGid then
                    table.insert(weaponApplyList,v)
                end
            end
        end
    end
    return weaponApplyList
end

CollectionLogic.checkIfPendantAppliedOnAnyWeapon = function(pendantId, pendantGid)
    return #CollectionLogic.getWeaponlistByEquipPendant(pendantId, pendantGid) > 0
end

CollectionLogic.checkIfPendantAppliedOnWeapon = function(weaponId,pendantId, pendantGid)
    pendantGid = pendantGid or 0
    local bInMp = CollectionLogic.IsInMp()
    if bInMp then
        local _defaultWeaponMp = Server.InventoryServer:GetDefaultWeaponSkins()
        if _defaultWeaponMp[weaponId] then
            return _defaultWeaponMp[weaponId].pendant_id == pendantId and _defaultWeaponMp[weaponId].pendant_gid == pendantGid
        else
            local weaponProp = CollectionLogic.getWeaponPropFromInventory(weaponId)
            if weaponProp == nil then
                return false
            else
                if not pendantGid then
                    if weaponProp.weapon.pendant_id == pendantId then 
                        return true
                    end
                else
                    if weaponProp.weapon.pendant_id == pendantId and weaponProp.weapon.pendant_gid == pendantGid then
                       return true
                    end
                end     
            end
        end
    else
        local appliedPendantId = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(weaponId)
        if not pendantId then
            if appliedPendantId == pendantId then 
                return true
            end
        else
            local appliedPendantGid = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(weaponId)
            if appliedPendantId == pendantId and appliedPendantGid == pendantGid then
                return true
            end
        end
    end
end

CollectionLogic.checkIfSkinAppliedOnWeapon = function(skinId, skinGid, res)
    skinGid = skinGid or 0
    local weaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(skinId)
    local bInMp = CollectionLogic.IsInMp()
    if res and res.result then
        return res.result == 0
    elseif bInMp then
        local weaponProp = CollectionLogic.getWeaponPropFromInventory(weaponId)
        if weaponProp == nil then
        else
            if not skinGid then
                return weaponProp.weapon.skin_id == skinId
            else
                return weaponProp.weapon.skin_id == skinId and weaponProp.weapon.skin_gid == skinGid 
            end     
        end 
    else
        local appliedSkinId = Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(weaponId)
        if not skinGid then
            return appliedSkinId == skinId
        else
            local appliedSkinGid = Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponId)
            return appliedSkinId == skinId and appliedSkinGid == skinGid 
        end 
    end
    return false
end


CollectionLogic.EquipPendantAllModeByWeaponList = function(weaponItems, pendantId, pendantGid)
    local mpCommands = {}
    local solCommands = {}
    for _,weaponItem in pairs(weaponItems) do 
        local mpCommand = pb.WeaponApplySkinCmd:New()
        local solCommand = pb.WeaponApplySkinCmd:New()
        if mpCommand == nil or solCommand == nil then
            return
        end
        mpCommand.weapon_id = weaponItem.id
        mpCommand.weapon_gid = weaponItem.gid
        solCommand.weapon_id = weaponItem.id
        solCommand.weapon_gid = weaponItem.gid
        local bagID, groupID = Server.ArmedForceServer:GetBagIDFromUIContext()
        local weaponProp = Server.InventoryServer:GetWeaponPropInfoByWeaponID(weaponItem.id,ESlotGroup.MPApply , bagID)
        if weaponProp and weaponProp.weapon then 
            mpCommand.skin_id= weaponProp.weapon.skin_id
            mpCommand.skin_gid = weaponProp.weapon.skin_gid
        end
        local appliedSkinId = Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(weaponItem.id)
        local appliedSkinGid = Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(weaponItem.id)
        solCommand.skin_id = appliedSkinId
        solCommand.skin_gid = appliedSkinGid
        -- command.apply_all = true
        mpCommand.pendant_id = pendantId
        mpCommand.pendant_gid = pendantGid
        mpCommand.pendant_apply_all = true
        solCommand.pendant_id = pendantId
        solCommand.pendant_gid = pendantGid
        solCommand.pendant_apply_all = true
        table.insert(mpCommands,mpCommand)
        table.insert(solCommands,solCommand)
    end
    Server.GunsmithServer:C2SCSWAssemblyApplySkinReqByCommonds(0, solCommands)
    Server.GunsmithServer:C2SCSWAssemblyApplySkinReqByCommonds(1, mpCommands)
end

CollectionLogic.EquipPendant = function(weaponId,weaponSkinId,weaponSkinGid, pendantId, pendantGid)
    local bInMp = CollectionLogic.IsInMp()
    local command = pb.WeaponApplySkinCmd:New()
    if command == nil then
        return
    end
    command.weapon_id = weaponId
    local bagID, groupID = Server.ArmedForceServer:GetBagIDFromUIContext()
    local weaponProp = Server.InventoryServer:GetWeaponPropInfoByWeaponID(weaponId, bInMp and ESlotGroup.MPApply or ESlotGroup.Player, bagID)
    command.weapon_gid = weaponProp ~= nil and weaponProp.gid or nil
    if bInMp then
        command.bag_id = bagID
    else
        command.apply_all = true
    end
    command.skin_id = weaponSkinId
    command.skin_gid = weaponSkinGid
    command.pendant_id = pendantId
    command.pendant_gid = pendantGid
    local datatype = bInMp and 1 or 0
    Server.GunsmithServer:C2SCSWAssemblyApplySkinReq(datatype, command)
end

CollectionLogic.FilterCommonPropBySortMode = function(props, sortMode)
    local filteredProps = {}
    local subTypes = ESortMode2ItemSubType[sortMode]
    if not table.isempty(subTypes) then
        local collectionItemsSub = {}
        if #subTypes > 0 then
            for _, subType in ipairs(subTypes) do
                for index, prop in ipairs(props) do
                    if subType >= 0 then
                        if prop.itemSubType == subType then
                            table.insert(filteredProps, prop)
                        end
                    else
                        if prop.itemSubType ~= subType*-1 then
                            table.insert(filteredProps, prop)
                        end
                    end
                end
            end   
        else
            filteredProps = props
        end     
    end
    return filteredProps
end


CollectionLogic.IDSort = function(a, b)
    return a.id < b.id
end

CollectionLogic.MandelBrickDefaultSort = function(a, b)
    local mandelBrickDataRowA = Facade.TableManager:GetRowByKey("MandelBrickDataTable", a.id)
    local mandelBrickDataRowB = Facade.TableManager:GetRowByKey("MandelBrickDataTable", b.id)
    if mandelBrickDataRowA and mandelBrickDataRowB then
        if mandelBrickDataRowA.SeasonID ~= mandelBrickDataRowB.SeasonID then
            return mandelBrickDataRowA.SeasonID >= mandelBrickDataRowB.SeasonID
        end
    end
    local bOwnedA = Server.CollectionServer:IsOwnedMandelBrick(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedMandelBrick(b.id)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.sortWeight ~= b.sortWeight then
        return a.sortWeight > b.sortWeight
    end
    return a.id > b.id
end

CollectionLogic.DefaultSort = function(a, b)
    local bHasReddotA = Server.CollectionServer:IsPropWithRedDot(a.id)
    local bHasReddotB = Server.CollectionServer:IsPropWithRedDot(b.id)
    if bHasReddotA ~= bHasReddotB then
        return bHasReddotA
    end
    local bAvailableA = CollectionLogic.IsPropAvailableInCurrentMode(a)
    local bAvailableB = CollectionLogic.IsPropAvailableInCurrentMode(b)
    if bAvailableA ~= bAvailableB then
        return bAvailableA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    return a.id < b.id
end

CollectionLogic.WeaponSkinGainTimeSortAscend = function(a, b)
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid) or Server.CollectionServer:IsOwnedMeleeSkin(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid) or Server.CollectionServer:IsOwnedMeleeSkin(b.id)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA < gainedTimeB
        end
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    return a.gid < b.gid
end

CollectionLogic.WeaponSkinGainTimeSortDecend = function(a, b)
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid) or Server.CollectionServer:IsOwnedMeleeSkin(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid) or Server.CollectionServer:IsOwnedMeleeSkin(b.id)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA > gainedTimeB
        end
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    return a.gid < b.gid
end

CollectionLogic.WeaponSkinQualitySortAscend = function(a, b)
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid) or Server.CollectionServer:IsOwnedMeleeSkin(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid) or Server.CollectionServer:IsOwnedMeleeSkin(b.id)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality < b.quality
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA < gainedTimeB
        end
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    return a.gid < b.gid
end

CollectionLogic.WeaponSkinQualitySortDecend = function(a, b)
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid) or Server.CollectionServer:IsOwnedMeleeSkin(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid) or Server.CollectionServer:IsOwnedMeleeSkin(b.id)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA > gainedTimeB
        end
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    return a.gid < b.gid
end

CollectionLogic.WeaponSkinFirearmSortDecend = function(a, b)
    local bHasReddotA = Server.CollectionServer:IsPropWithRedDot(a.id)
    local bHasReddotB = Server.CollectionServer:IsPropWithRedDot(b.id)
    if bHasReddotA ~= bHasReddotB then
        return bHasReddotA
    end
    local baseWeaponIdA = CollectionLogic.GetBaseWeaponIdFromSkinId(a.id)
    local baseWeaponIdB = CollectionLogic.GetBaseWeaponIdFromSkinId(b.id)
    if baseWeaponIdA ~= baseWeaponIdB then
        return baseWeaponIdA < baseWeaponIdB
    end
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    local bIsMysticalA = ItemHelperTool.IsMysticalSkin(a.id)
    local bIsMysticalB = ItemHelperTool.IsMysticalSkin(b.id)
    if bIsMysticalA ~= bIsMysticalB then
        return bIsMysticalA
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA > gainedTimeB
        end
    end
    return a.id > b.id
end

CollectionLogic.WeaponSkinFirearmSortAscend = function(a, b)
    local bHasReddotA = Server.CollectionServer:IsPropWithRedDot(a.id)
    local bHasReddotB = Server.CollectionServer:IsPropWithRedDot(b.id)
    if bHasReddotA ~= bHasReddotB then
        return bHasReddotA
    end
    local baseWeaponIdA = CollectionLogic.GetBaseWeaponIdFromSkinId(a.id)
    local baseWeaponIdB = CollectionLogic.GetBaseWeaponIdFromSkinId(b.id)
    if baseWeaponIdA ~= baseWeaponIdB then
        return baseWeaponIdA > baseWeaponIdB
    end
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id, a.gid)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id, b.gid)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    local bIsMysticalA = ItemHelperTool.IsMysticalSkin(a.id)
    local bIsMysticalB = ItemHelperTool.IsMysticalSkin(b.id)
    if bIsMysticalA ~= bIsMysticalB then
        return bIsMysticalA
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA > gainedTimeB
        end
    end
    return a.id > b.id
end

CollectionLogic.WeaponSkinInCamouflageSort = function(a, b)
    local weaponTypeA = a.itemSubType
    local weaponTypeB = b.itemSubType
    if weaponTypeA ~= weaponTypeB then
        return weaponTypeA < weaponTypeB
    end
    local bOwnedA = Server.CollectionServer:IsOwnedWeaponSkin(a.id)
    local bOwnedB = Server.CollectionServer:IsOwnedWeaponSkin(b.id)
    if bOwnedA ~= bOwnedB then
        return not bOwnedA
    end
    local taskInfoA = Server.CollectionServer:GetSkinTaskInfoBySkinId(a.id)
    local taskInfoB = Server.CollectionServer:GetSkinTaskInfoBySkinId(b.id)
    if taskInfoA.bFinished ~= taskInfoB.bFinished then
        return taskInfoA.bFinished
    end
    local modeTaskInfoA
    local modeTaskInfoB
    if CollectionLogic.IsInMp() then
        modeTaskInfoA = taskInfoA.MP_TaskInfo
        modeTaskInfoB = taskInfoB.MP_TaskInfo
    else
        modeTaskInfoA = taskInfoA.SOL_TaskInfo
        modeTaskInfoB = taskInfoB.SOL_TaskInfo
    end
    if modeTaskInfoA.bUnlocked ~= modeTaskInfoB.bUnlocked then
        return modeTaskInfoA.bUnlocked
    elseif modeTaskInfoA.bUnlocked then
        if modeTaskInfoA.progress ~= modeTaskInfoB.progress then
            return modeTaskInfoA.progress > modeTaskInfoB.progress
        end
    else
        if modeTaskInfoA.preProgress ~= modeTaskInfoB.preProgress then
            return modeTaskInfoA.preProgress > modeTaskInfoB.preProgress
        end
    end
    local baseWeaponIdA = CollectionLogic.GetBaseWeaponIdFromSkinId(a.id)
    local baseWeaponIdB = CollectionLogic.GetBaseWeaponIdFromSkinId(b.id)
    if baseWeaponIdA ~= baseWeaponIdB then
        return baseWeaponIdA < baseWeaponIdB
    end
end

CollectionLogic.WeaponSkinInMasterSort = function(a, b)
    local baseWeaponIdA = CollectionLogic.GetBaseWeaponIdFromSkinId(a.id)
    local baseWeaponIdB = CollectionLogic.GetBaseWeaponIdFromSkinId(b.id)
    if baseWeaponIdA ~= baseWeaponIdB then
        return baseWeaponIdA < baseWeaponIdB
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    return a.id > b.id
end

CollectionLogic.WeaponSkinTaskSort = function(a, b)
    local bDoneA = a.curValue == a.maxValue
    local bDoneB = b.curValue == b.maxValue
    if bDoneA ~= bDoneB then
        return not bDoneA
    end
    local taskProgressA = a.curValue/a.maxValue
    local taskProgressB = b.curValue/b.maxValue
    if taskProgressA ~= taskProgressB then
        return taskProgressA > taskProgressB
    end
    return a.goalId < b.goalId
end

CollectionLogic.CamouflageSort = function(a, b)
    local bIsActivatedA = Server.CollectionServer:CheckIsActivatedCamouflageById(a.patternId)
    local bIsActivatedB = Server.CollectionServer:CheckIsActivatedCamouflageById(b.patternId)
    if bIsActivatedA ~= bIsActivatedB then
        return bIsActivatedA
    end
    return a.seasonId > b.seasonId
end

CollectionLogic.HangingsDefaultSort = function(a, b)
    local bHasReddotA = Server.CollectionServer:IsPropWithRedDot(a.id)
    local bHasReddotB = Server.CollectionServer:IsPropWithRedDot(b.id)
    if bHasReddotA ~= bHasReddotB then
        return bHasReddotA
    end
    local bOwnedA = Server.CollectionServer:IsOwnedHanging(a.id, a.gid)
    local bOwnedB = Server.CollectionServer:IsOwnedHanging(b.id, b.gid)
    if bOwnedA ~= bOwnedB then
        return bOwnedA
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    local weaponTypeA = a.itemSubType
    local weaponTypeB = b.itemSubType
    if weaponTypeA ~= weaponTypeB then
        return weaponTypeA < weaponTypeB
    end
    -- local baseWeaponIdA = CollectionLogic.GetBaseWeaponIdFromSkinId(a.id)
    -- local baseWeaponIdB = CollectionLogic.GetBaseWeaponIdFromSkinId(b.id)
    -- if baseWeaponIdA ~= baseWeaponIdB then
    --     return baseWeaponIdA < baseWeaponIdB
    -- end
    local bIsMysticalA = ItemHelperTool.IsMysticalPendant(a.id)
    local bIsMysticalB = ItemHelperTool.IsMysticalPendant(b.id)
    local qualityA = ItemHelperTool.GetQualityTypeById(a.id)
    local qualityB = ItemHelperTool.GetQualityTypeById(b.id)
    if bIsMysticalA ~= bIsMysticalB then
        return bIsMysticalA
    end
    if bOwnedA then
        local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
        local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
        if gainedTimeA ~= gainedTimeB then
            return gainedTimeA > gainedTimeB
        end
    end
    if bIsMysticalA == bIsMysticalB then 
        return qualityA > qualityB
    end
    return a.id < b.id
end

CollectionLogic.MysticalPendantRaritySortDecend = function(a, b)
    local bOwnedA = Server.CollectionServer:GetHangingIfOwned(a.id, a.gid)
    local bOwnedB = Server.CollectionServer:GetHangingIfOwned(b.id, b.gid)
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.mystical_pendant_data and propB.mystical_pendant_data then
            local rarityA = propA.mystical_pendant_data.show[1].rarity
            local rarityB = propB.mystical_pendant_data.show[1].rarity
            if rarityA and rarityB then
                if rarityA ~= rarityB then
                    return rarityA > rarityB
                end
            elseif not rarityA then
                return false
            elseif not rarityB then
                return true
            end
            local wearA = propA.mystical_pendant_data.wear
            local wearB = propB.mystical_pendant_data.wear
            if wearA and wearB then
                return wearA < wearB
            elseif not wearA then
                return true
            elseif not wearB then
                return false
            end
        elseif not propA.mystical_pendant_data then
            return false
        elseif not propB.mystical_pendant_data then
            return true
        end
    elseif not propA then
        return false
    elseif not propB then
        return true
    end
    return a.id < b.id
end

CollectionLogic.MysticalSkinInstanceListSort = function(a, b)
    local appliedA = CollectionLogic.checkIfSkinAppliedOnWeapon(a.id, a.gid)
    if appliedA then
        return true
    else
        local appliedB = CollectionLogic.checkIfSkinAppliedOnWeapon(a.id, b.gid)
        if appliedB then
            return false
        end
    end
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.mystical_skin_data and propB.mystical_skin_data then
            local rarityA = propA.mystical_skin_data.rarity
            local rarityB = propB.mystical_skin_data.rarity
            if rarityA and rarityB then
                if rarityA ~= rarityB then
                    return rarityA > rarityB
                end
            elseif not rarityA then
                return false
            elseif not rarityB then
                return true
            end
            local wearA = propA.mystical_skin_data.wear
            local wearB = propB.mystical_skin_data.wear
            if wearA and wearB then
                return wearA < wearB
            elseif not wearA then
                return true
            elseif not wearB then
                return false
            end
        elseif not propA.mystical_skin_data then
            return false
        elseif not propB.mystical_skin_data then
            return true
        end
    elseif not propA then
        return false
    elseif not propB then
        return true
    end
    return a.id < b.id
end


CollectionLogic.MysticalSkinQualitySortAscend = function(a, b)
    if a.quality ~= b.quality then
        return a.quality < b.quality
    end
    local wearA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).mystical_skin_data.wear
    local wearB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).mystical_skin_data.wear
    if wearA ~= wearB then
        return wearA > wearB
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end


CollectionLogic.MysticalSkinQualitySortDecend = function(a, b)
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    local wearA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).mystical_skin_data.wear
    local wearB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).mystical_skin_data.wear
    if wearA ~= wearB then
        return wearA > wearB
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end


CollectionLogic.MysticalSkinWearSortAscend = function(a, b)
    local wearA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).mystical_skin_data.wear
    local wearB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).mystical_skin_data.wear
    if wearA ~= wearB then
        return wearA < wearB
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end


CollectionLogic.MysticalSkinWearSortDecend = function(a, b)
    local wearA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).mystical_skin_data.wear
    local wearB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).mystical_skin_data.wear
    if wearA ~= wearB then
        return wearA > wearB
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.MysticalSkinGainTimeSortAscend = function(a, b)
    local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
    local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
    if gainedTimeA ~= gainedTimeB then
        return gainedTimeA < gainedTimeB
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.MysticalSkinGainTimeSortDecend = function(a, b)
    local gainedTimeA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid).gained_time
    local gainedTimeB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid).gained_time
    if gainedTimeA ~= gainedTimeB then
        return gainedTimeA > gainedTimeB
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.MysticalPendantQualitySortAscend = function(a, b)
    if a.quality ~= b.quality then
        return a.quality < b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.gained_time and propB.gained_time then
            if propA.gained_time ~= propB.gained_time then
                return propA.gained_time < propB.gained_time
            end
        elseif not propA.gained_time then
            return false
        elseif not propB.gained_time then
            return true
        end
    elseif not propA then
        return false
    elseif not propB then
        return true
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end


CollectionLogic.MysticalPendantQualitySortDecend = function(a, b)
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.gained_time and propB.gained_time then
            if propA.gained_time ~= propB.gained_time then
                return propA.gained_time < propB.gained_time
            end
        elseif not propA.gained_time then
            return false
        elseif not propB.gained_time then
            return true
        end
    elseif not propA then
        return false
    elseif not propB then
        return true
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.MysticalPendantGainTimeSortAscend = function(a, b)
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.gained_time and propB.gained_time then
            if propA.gained_time ~= propB.gained_time then
                return propA.gained_time < propB.gained_time
            end
        elseif not propA.gained_time then
            return false
        elseif not propB.gained_time then
            return true
        end
    elseif not propA then
        return false
    elseif not propB then
        return true
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.MysticalPendantGainTimeSortDecend = function(a, b)
    local propA = Server.CollectionServer:GetCollectionPropById(a.id, a.gid)
    local propB = Server.CollectionServer:GetCollectionPropById(b.id, b.gid)
    if propA and propB then
        if propA.gained_time and propB.gained_time then
            if propA.gained_time ~= propB.gained_time then
                return propA.gained_time > propB.gained_time
            end
        elseif not propA.gained_time then
            return true
        elseif not propB.gained_time then
            return false
        end
    elseif not propA then
        return true
    elseif not propB then
        return false
    end
    if a.id ~= b.id then
        return a.id < b.id
    end
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.gid ~= b.gid then
        return a.gid < b.gid
    end
end

CollectionLogic.UseCollectionProp = function(item, prizes, bEquipWhenUnboxing, equipItemGroups)
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        return
    end
    local rawExpireInfo = Server.CollectionServer:GetPropExpireInfo(item.id)
    if rawExpireInfo and #rawExpireInfo > 0 then
        local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
        local bPropExpired = true
        for index, info in ipairs(rawExpireInfo) do
            if info.expireTime > currentTimestamp then
                bPropExpired = false
                break
            end
        end
        if bPropExpired == true then
            Server.CollectionServer:RemoveExpiredPropLocally(item, true)
            Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.UseExpiredPropTip)
            return
        end
    end
    if item.itemSubType == ECollectableType.RenameCard then
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionChangeNameView, nil, self, item)
    elseif item.itemSubType == ECollectableType.ActCardSafeBox or item.itemSubType == ECollectableType.ActCardPack then
        local activatingTable = Facade.TableManager:GetTable("SafeAndCardPackActivatingConfig")
        local activatingDataRow = nil
        if activatingTable then
            for key, activatingData in pairs(activatingTable) do
                if tonumber(activatingData.TheActivating) == item.id then
                    activatingDataRow = activatingData
                    break
                end
            end
        end
        local activeTime = 0
        if activatingDataRow ~= nil then
            activeTime = activatingDataRow.ActiveTime
            if activeTime == 0 and activatingDataRow.EndTime > 0 then
                activeTime = activatingDataRow.EndTime > Facade.ClockManager:GetServerTimestamp() and activatingDataRow.EndTime or 0
            end
        end
        if activeTime > 0 or activeTime == -1 then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionActivationCard, nil, self, item, true)
        else
            Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.ActCardInvalidTip)
        end
    elseif item.itemSubType == ECollectableType.SelectivePackage then
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionSelectivePackagePop, nil, self, item, prizes)
    elseif item.itemSubType == ECollectableType.FullArmedPackage then
        if bEquipWhenUnboxing == true then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionEquipItems, nil, self, item, equipItemGroups)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionPackagePop, nil, self, item, prizes)
        end
    elseif item.itemSubType == ECollectableType.SuitPackage
    or item.itemSubType == ECollectableType.RandomPackage then
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionPackagePop, nil, self, item, prizes) 
    elseif item.itemSubType == ECollectableType.RandomLottery then
        local items = {item}
        local nums = {1}
        local group_ids = {}
        local prop_ids = {}
        CollectionLogic.RequestOpenBox(items, nums, group_ids, prop_ids, false)
    elseif item.itemSubType == ECollectableType.RightsCard then
        local items = {item}
        local nums = {1}
        CollectionLogic.RequestUseProp(items, nums)
    elseif item.itemSubType == ECollectableType.MissionFileExpCard 
        or item.itemSubType == ECollectableType.BattleDoubleLevelExpCard
        or item.itemSubType == ECollectableType.BattleDoubleWeaponExpCard
        or item.itemSubType == ECollectableType.BattleDoubleTournamentExpCard
        or item.itemSubType == ECollectableType.BattlePointProtectiveCard then
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionActivationCard, nil, self, item)
    elseif item.itemSubType == ECollectableType.MissionFileActivateCard then
        Server.CollectionServer:DoUseMissionFileActivateCard(item)
    end
end


CollectionLogic.RequestUseProp = function(items, nums, fCallbackIns)
    Server.CollectionServer:DoUseMultiItems(items, nums, {}, {}, fCallbackIns)
end


CollectionLogic.RequestOpenBox = function(items, nums, group_ids, prop_ids, bAssemble, fCallbackIns)
    Server.CollectionServer:OpenBoxs(items, nums, group_ids, prop_ids, bAssemble, fCallbackIns)
end

CollectionLogic.RenamePendant = function(item, newName, bFree, fCustomCallback)
    if Module.Collection.Config.PendantRenameConfig and table.nums(Module.Collection.Config.PendantRenameConfig) > 0 then
        local pendantRenameConfigRow = table.values(Module.Collection.Config.PendantRenameConfig)[1]
        local currencyId = pendantRenameConfigRow.PendantNameCurrency or 0
        local price = pendantRenameConfigRow.PendantNamePrice or 0
        local currecny = Module.Currency:GetNumByItemId(currencyId)
        if currecny >= price or bFree == true then
            Server.CollectionServer:RenamePendant(item, newName, fCustomCallback)
        else
            local showStr = Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView
            local confirmStr = Module.Store.Config.Loc.StoreMainTabRecharge
            local _rechargeCancelHandle = function()
            end
            local _rechargeConfirmHandle = function()
                Module.Store:ShowStoreRechargeMainPanle()
            end
            Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
        end
    end
end

CollectionLogic.RenameMysticalSkin = function(item, newName, currencyItemId, secondaryCurrencyItemId, price, fCustomCallback)
    if not item
    or not newName
    or newName == ""
    or not ItemHelperTool.IsMysticalSkin(item.id)
    or not item:GetRawPropInfo().mystical_skin_data
    or not price then
        return
    end
    local oldName = item:GetRawPropInfo().mystical_skin_data.custom_name
    local bFree = oldName == ""
    local bEnoughMoney = bFree or Module.Currency:HasEnoughMoneyByItemId(currencyItemId, price)
    local extraCurrencyItemNumRequired = 0
    local tip
    if secondaryCurrencyItemId and not bEnoughMoney then
        extraCurrencyItemNumRequired = price - Module.Currency:GetNumByItemId(currencyItemId)
        bEnoughMoney = Module.Currency:GetNumByItemId(secondaryCurrencyItemId) >= extraCurrencyItemNumRequired
    end
    if bEnoughMoney then
        if bFree then
            tip = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.SkinFreeRenameConfirmationTip,
            {["NewName"] = newName
        })
        else
            local cashString
            if extraCurrencyItemNumRequired > 0 then
                cashString = string.format(Module.Store.Config.Loc.ItemPriceNormal, Module.Currency:GetRichTxtImgByItemId(secondaryCurrencyItemId), MathUtil.GetNumberFormatStr(extraCurrencyItemNumRequired))
                if extraCurrencyItemNumRequired < price then
                    cashString = string.format(Module.Store.Config.Loc.ItemPriceNormal, Module.Currency:GetRichTxtImgByItemId(currencyItemId), MathUtil.GetNumberFormatStr(Module.Currency:GetNumByItemId(currencyItemId))).." "..cashString
                end
            else
                cashString = string.format(Module.Store.Config.Loc.ItemPriceNormal, Module.Currency:GetRichTxtImgByItemId(currencyItemId), MathUtil.GetNumberFormatStr(price))
            end
            tip = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.SkinRenameConfirmationTip,
                    {["Cash"] = cashString,
                     ["NewName"] = newName
                    })
        end
        Module.CommonTips:ShowConfirmWindow(
                tip,
                function()
                    Server.CollectionServer:RenameMysticalSkin(item.id, item.gid, newName, currencyItemId, price-extraCurrencyItemNumRequired, secondaryCurrencyItemId, extraCurrencyItemNumRequired, fCustomCallback)
                end,
                function()
                end,
                Module.Collection.Config.Loc.Cancel,
                Module.Collection.Config.Loc.Confirm, nil, nil, nil, nil, nil
        )
    else
        local _rechargeCancelHandle = function()
        end
        local _rechargeConfirmHandle = function()
            Module.Store:ShowStoreRechargeMainPanle()
        end
        Module.CommonTips:ShowConfirmWindow(
                Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView,
                _rechargeConfirmHandle,
                _rechargeCancelHandle,
                nil,
                Module.Store.Config.Loc.StoreMainTabRecharge, nil, nil, nil, nil, nil, true
        )
    end
end

CollectionLogic.RecombineMysticalSkins = function(items, bIsBatchProcess, fCustomCallback)
    Server.CollectionServer:RecombineMysticalSkins(items, bIsBatchProcess, fCustomCallback)
end

CollectionLogic.ExchangeMysticalPendants = function(items, bIsBatchProcess, fCustomCallback)
    Server.CollectionServer:ExchangeMysticalPendants(items, bIsBatchProcess, fCustomCallback)
end

CollectionLogic.GetPropRedDots = function()
    return Server.CollectionServer:GetPropRedDots()
end

CollectionLogic.RemoveRedDots = function(items, patternIds)
    local propInfoList = {}
    if items then
        for index, item in ipairs(items) do
            table.insert(propInfoList, {id=item.id, gid=item.gid})
        end
    end
    Server.CollectionServer:UpdateRedDot(propInfoList, patternIds)
 end
 
CollectionLogic.IsItemInStore = function(itemId)
    local skinGiftList = Server.StoreServer:GetMallGiftWeaponSkins()
    for index, skinGift in ipairs(skinGiftList) do
        if skinGift.BundleItems then
            for index, itemInfo in ipairs(skinGift.BundleItems) do
                if itemInfo.id == itemId then
                    return true
                end
            end
        end
    end
    return false
end

CollectionLogic.PurchaseItem = function(itemId)
    local storeItem = nil
    local skinGiftList = Server.StoreServer:GetMallGiftWeaponSkins()
    for index, skinGift in ipairs(skinGiftList) do
        if skinGift.BundleItems then
            for index, itemInfo in ipairs(skinGift.BundleItems) do
                if itemInfo.id == itemId then
                    storeItem = skinGift
                    break
                end
            end
        end
    end
    if storeItem ~= nil then
        local goods_id = storeItem.GoodsId
        local price = storeItem.Price
        local currency_type = storeItem.CurrencyType
        local currecny = Module.Currency:GetNumByItemId(currency_type)
        local currency_type_sub = 17888808888
        local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)
        local fee = 0
        local fee_sub = 0
        local bMoneyEnough = false
        if currecny >= price then
            bMoneyEnough = true
            fee = price
        else
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub >= price then
                    bMoneyEnough = true
                    fee = currecny
                    fee_sub = price - currecny
                end
            end
        end
        if bMoneyEnough then
            Server.StoreServer:SendShopBuyBuyMallGiftReq(goods_id, currency_type, fee, currency_type_sub, fee_sub, 1)
        else
            loginfo("[ProductPreview] DoBuyMallGifts NotEnoughMoney currecny:" .. currecny)
            local showStr = Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView
            local confirmStr = Module.Store.Config.Loc.StoreMainTabRecharge
            local _rechargeCancelHandle = function()
            end
            local _rechargeConfirmHandle = function()
                Module.Store:ShowStoreRechargeMainPanle()
            end
            Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
        end
    end
end


CollectionLogic.GePriceStrByItemId = function(itemId)
    local shopData = Server.StoreServer:GetMallGiftWeaponSkinDataByItemID(itemId)
    if shopData ~= nil then
        local buyLimted = false
        if shopData == nil or shopData.GoodsId == nil then
        else
            local buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(shopData.GoodsId)
            if buyRecord ~= nil then
                if shopData.LimitType ~= nil and shopData.LimitType > 0 and shopData.LimitAmount > 0 then
                    local timeStamp =  Server.StoreServer:GetBuyLimitResetTimeByLimitType(shopData.LimitType)
                    if timeStamp ~= 0 then
                        if buyRecord.num >= shopData.LimitAmount and buyRecord.buy_time > timeStamp then
                            buyLimted = true
                        end
                    end
                else
                    buyLimted = true
                end
            end
        end
        if buyLimted then

        else
            local priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal,
                        Module.Currency:GetRichTxtImgByItemId(shopData.CurrencyType),
                        MathUtil.GetNumberFormatStr(shopData.Price))
            return priceStr
        end
    end
    return "0"
end

CollectionLogic.GeSkinRenamePriceStrByItemId = function(itemId)
    local storeItem = Server.StoreServer:GetStoreItemByItemId(itemId)
    if storeItem then
        local payment = storeItem:GetDefaultPayment()
        local time = TimeUtil.GetCurrentTime()
        local enable_discount = payment.enable_discount and time >= payment.discount_begin_time and time < payment.discount_end_time
        local price = 0
        local bEnoughMoney = Module.Currency:HasEnoughMoneyByItemId(payment.item, price)
        local priceStr = ""
        if bEnoughMoney then
            priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal, Module.Currency:GetRichTxtImgByItemId(payment.item), MathUtil.GetNumberFormatStr(price))
        else
            priceStr = string.format(Module.Store.Config.Loc.ItemPriceNotEnough, Module.Currency:GetRichTxtImgByItemId(payment.item), MathUtil.GetNumberFormatStr(price))
        end
        return priceStr, price
    end
    return "", 0
end

CollectionLogic.GetMandelBrickIdBySkinId = function(skinId)
    local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(skinId)
    if skinDataRow then
        return skinDataRow.MandelBrickID or 0
    else
        return 16110000014        
    end
end

CollectionLogic.ShowMandelBrickPage = function(mandelBrickId,type,source)
    Module.Store:ShowStoreMandelOnlyPanle(mandelBrickId, type, source)
end

CollectionLogic.ShowMarketPage = function(subPageType)
    Module.Market:ShowMainPanel(subPageType)
end

CollectionLogic.GetGlobalPosAndSizeByWidget = function(widget)
    if widget == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

    if not widget.GetCachedGeometry then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local alignWidgetGeometry = widget:GetCachedGeometry()
	local itemScreenPosLT = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    local itemScreenPosRB = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))

    return itemScreenPosLT,  FVector2D(itemScreenPosRB.X - itemScreenPosLT.X, itemScreenPosRB.Y- itemScreenPosLT.Y)
end

CollectionLogic.SetBackgroundImgByPropId = function(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, propId, transformBoxWidget, imageTransforms)
    local gameItemRow = ItemConfigTool.GetItemConfigById(propId)
    local CDNImagePaths
    if gameItemRow then
        CDNImagePaths = gameItemRow.Pictures
        imageTransforms = imageTransforms or gameItemRow.PictureTransforms
    end
    CollectionLogic.SetBackgroundImg(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, CDNImagePaths, transformBoxWidget, imageTransforms)
end

CollectionLogic.SetBackgroundImg = function(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, CDNImagePaths, transformBoxWidget, imageTransforms)
    CDNImagePaths = CDNImagePaths or {""}
    if bShow then
        eCdnTagEnum = eCdnTagEnum or Module.CDNIcon.Config.ECdnTagEnum.Collection
        CDNPathPrefix = CDNPathPrefix or "Resource/Texture/Collection/MS24/"
        if imageWidgets ~= nil then
            for index, imageWidget in ipairs(imageWidgets) do
                if CDNImagePaths[index] ~= nil then
                    imageWidget:SetCDNImage(CDNPathPrefix..CDNImagePaths[index], false, eCdnTagEnum)
                    imageWidget:SelfHitTestInvisible()
                else
                    imageWidget:Collapsed() 
                end
            end
        end
        if transformBoxWidget ~= nil then
            if imageTransforms ~= nil and #imageTransforms > 0 then
                if imageTransforms[1] ~= nil then
                    --imageTransforms[1].Scale
                    --imageTransforms[1].Shear
                    --imageTransforms[1].Angle
                    if imageTransforms[1].Translation then
                        transformBoxWidget:SetRenderTranslation(imageTransforms[1].Translation)
                    end
                    if imageTransforms[1].Angle then
                        transformBoxWidget:SetRenderTransformAngle(imageTransforms[1].Angle)
                    end
                end
            else
                transformBoxWidget:SetRenderTranslation(FVector2D(0, 0))
                transformBoxWidget:SetRenderTransformAngle(0)
            end
        end
    elseif imageWidgets ~= nil then
        for index, imageWidget in ipairs(imageWidgets) do
            imageWidget:Collapsed()
        end
    end    
end


CollectionLogic.SetMysticalSkinFilterData = function(data)
    Module.Collection.Field:SetFilterData("MysticalSkin", data)
end

CollectionLogic.ResetMysticalSkinFilterData = function()
    local mysticalSkinFilterData = {}
    local lastMysticalSkinFilterData = CollectionLogic.GetLastMysticalSkinFilterData()
    deepcopy(mysticalSkinFilterData, lastMysticalSkinFilterData)
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
end

CollectionLogic.GetMysticalSkinFilterData = function()
    local mysticalSkinFilterData = Module.Collection.Field:GetFilterData("MysticalSkin")
    mysticalSkinFilterData = setdefault(mysticalSkinFilterData, {})
    mysticalSkinFilterData.selectedQualityIDs = setdefault(mysticalSkinFilterData.selectedQualityIDs, {})
    mysticalSkinFilterData.selectedGunTypeIDs = setdefault(mysticalSkinFilterData.selectedGunTypeIDs, {})
    mysticalSkinFilterData.seasonChoice = setdefault(mysticalSkinFilterData.seasonChoice, 0)
    mysticalSkinFilterData.weaponChoice = setdefault(mysticalSkinFilterData.weaponChoice, 0)
    mysticalSkinFilterData.wearRange = setdefault(mysticalSkinFilterData.wearRange, {min=0, max=5})
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    return mysticalSkinFilterData
end


CollectionLogic.SetLastMysticalSkinFilterData = function(data)
    Module.Collection.Field:SetLastFilterData("MysticalSkin", data)
end

CollectionLogic.GetLastMysticalSkinFilterData = function()
    local mysticalSkinFilterData = Module.Collection.Field:GetLastFilterData("MysticalSkin")
    mysticalSkinFilterData = setdefault(mysticalSkinFilterData, {})
    mysticalSkinFilterData.selectedQualityIDs = setdefault(mysticalSkinFilterData.selectedQualityIDs, {})
    mysticalSkinFilterData.selectedGunTypeIDs = setdefault(mysticalSkinFilterData.selectedGunTypeIDs, {})
    mysticalSkinFilterData.seasonChoice = setdefault(mysticalSkinFilterData.seasonChoice, 0)
    mysticalSkinFilterData.weaponChoice = setdefault(mysticalSkinFilterData.weaponChoice, 0)
    mysticalSkinFilterData.wearRange = setdefault(mysticalSkinFilterData.wearRange, {min=0, max=5})
    CollectionLogic.SetLastMysticalSkinFilterData(mysticalSkinFilterData)
    return mysticalSkinFilterData
end

CollectionLogic.UpdateLastMysticalSkinFilterData = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    local lastMysticalSkinFilterData = {}
    deepcopy(lastMysticalSkinFilterData, mysticalSkinFilterData)
    CollectionLogic.SetLastMysticalSkinFilterData(lastMysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterUpdated:Invoke()
end


CollectionLogic.ClearAllMysticalSkinFilterData = function()
    CollectionLogic.SetMysticalSkinFilterData(nil)
    CollectionLogic.SetLastMysticalSkinFilterData(nil)
end


CollectionLogic.ToggleMysticalSkinFilterQuality = function(qualityId)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    if mysticalSkinFilterData.selectedQualityIDs[qualityId] ~= nil then
        mysticalSkinFilterData.selectedQualityIDs[qualityId] = nil 
    else
        mysticalSkinFilterData.selectedQualityIDs[qualityId] = true
    end
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterQualityChanged:Invoke()
end

CollectionLogic.IsMysticalSkinFilterQualitySelected = function(qualityId)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.selectedQualityIDs[qualityId] == true
end

CollectionLogic.GetMysticalSkinFilterQuality = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.selectedQualityIDs
end

CollectionLogic.ClearMysticalSkinFilterQuality = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    mysticalSkinFilterData.selectedQualityIDs = {}
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterQualityChanged:Invoke()
end


CollectionLogic.ToggleMysticalSkinFilterGunType = function(gunTypeId)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    if mysticalSkinFilterData.selectedGunTypeIDs[gunTypeId] ~= nil then
        mysticalSkinFilterData.selectedGunTypeIDs[gunTypeId] = nil 
    else
        mysticalSkinFilterData.selectedGunTypeIDs[gunTypeId] = true
    end
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterGunTypeChanged:Invoke()
end

CollectionLogic.IsMysticalSkinFilterGunTypeSelected = function(gunTypeId)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.selectedGunTypeIDs[gunTypeId] == true
end

CollectionLogic.GetMysticalSkinFilterGunType = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.selectedGunTypeIDs
end

CollectionLogic.ClearMysticalSkinFilterGunType = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    mysticalSkinFilterData.selectedGunTypeIDs = {}
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterGunTypeChanged:Invoke()
end


CollectionLogic.GetMysticalSkinFilterSeasonChoice = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.seasonChoice
end

CollectionLogic.SetMysticalSkinFilterSeasonChoice = function(choice)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    mysticalSkinFilterData.seasonChoice = setdefault(choice, 0)
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterSeasonChoiceChanged:Invoke()
end


CollectionLogic.GetMysticalSkinFilterWeaponChoice = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.weaponChoice
end

CollectionLogic.SetMysticalSkinFilterWeaponChoice = function(choice)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    mysticalSkinFilterData.weaponChoice = setdefault(choice, 0)
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterWeaponChoiceChanged:Invoke()
end

CollectionLogic.GetMysticalSkinFilterWearRange = function()
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    return mysticalSkinFilterData.wearRange
end

CollectionLogic.SetMysticalSkinFilterWearRange = function(range)
    local mysticalSkinFilterData = CollectionLogic.GetMysticalSkinFilterData()
    mysticalSkinFilterData.wearRange = setdefault(range, {min=0, max=1})
    CollectionLogic.SetMysticalSkinFilterData(mysticalSkinFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterWearRangeChanged:Invoke()
end





CollectionLogic.SetGunSkinFilterData = function(data)
    Module.Collection.Field:SetFilterData("WeaponSkin", data)
end

CollectionLogic.ResetGunSkinFilterData = function()
    local gunSkinFilterData = {}
    local lastGunSkinFilterData = CollectionLogic.GetLastGunSkinFilterData()
    deepcopy(gunSkinFilterData, lastGunSkinFilterData)
    CollectionLogic.SetGunSkinFilterData(gunSkinFilterData)
end

CollectionLogic.GetGunSkinFilterData = function()
    local gunSkinFilterData = Module.Collection.Field:GetFilterData("WeaponSkin")
    gunSkinFilterData = setdefault(gunSkinFilterData, {})
    gunSkinFilterData.sortMode = setdefault(gunSkinFilterData.sortMode, 0)
    gunSkinFilterData.selectedSkinGroups = setdefault(gunSkinFilterData.selectedSkinGroups, {})
    gunSkinFilterData.selectedSkinTypes = setdefault(gunSkinFilterData.selectedSkinTypes, {})
    gunSkinFilterData.selectedQualityIDs = setdefault(gunSkinFilterData.selectedQualityIDs, {})
    gunSkinFilterData.selectedGunTypeIDs = setdefault(gunSkinFilterData.selectedGunTypeIDs, {})
    gunSkinFilterData.weaponChoice = setdefault(gunSkinFilterData.weaponChoice, 0)
    CollectionLogic.SetGunSkinFilterData(gunSkinFilterData)
    return gunSkinFilterData
end


CollectionLogic.SetLastGunSkinFilterData = function(data)
    Module.Collection.Field:SetLastFilterData("WeaponSkin", data)
end

CollectionLogic.GetLastGunSkinFilterData = function()
    local gunSkinFilterData = Module.Collection.Field:GetLastFilterData("WeaponSkin")
    gunSkinFilterData = setdefault(gunSkinFilterData, {})
    gunSkinFilterData.sortMode = setdefault(gunSkinFilterData.sortMode, 0)
    gunSkinFilterData.selectedSkinGroups = setdefault(gunSkinFilterData.selectedSkinGroups, {})
    gunSkinFilterData.selectedSkinTypes = setdefault(gunSkinFilterData.selectedSkinTypes, {})
    gunSkinFilterData.selectedQualityIDs = setdefault(gunSkinFilterData.selectedQualityIDs, {})
    gunSkinFilterData.selectedGunTypeIDs = setdefault(gunSkinFilterData.selectedGunTypeIDs, {})
    gunSkinFilterData.weaponChoice = setdefault(gunSkinFilterData.weaponChoice, 0)
    CollectionLogic.SetLastGunSkinFilterData(gunSkinFilterData)
    return gunSkinFilterData
end

CollectionLogic.UpdateLastGunSkinFilterData = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    local lastGunSkinFilterData = {}
    deepcopy(lastGunSkinFilterData, gunSkinFilterData)
    CollectionLogic.SetLastGunSkinFilterData(lastGunSkinFilterData)
    Module.Collection.Config.Events.evtOnGunSkinFilterUpdated:Invoke()
end


CollectionLogic.ClearAllGunSkinFilterData = function()
    CollectionLogic.SetGunSkinFilterData(nil)
    CollectionLogic.SetLastGunSkinFilterData(nil)
end

CollectionLogic.ToggleGunSkinFilterSkinGroup = function(skinGroup)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    if gunSkinFilterData.selectedSkinGroups[skinGroup] ~= nil then
        gunSkinFilterData.selectedSkinGroups[skinGroup] = nil 
    else
        gunSkinFilterData.selectedSkinGroups[skinGroup] = true
    end
    Module.Collection.Config.Events.evtOnGunSkinFilterSkinGroupChanged:Invoke()
end

CollectionLogic.IsGunSkinFilterSkinGroupSelected = function(skinGroup)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedSkinGroups[skinGroup] == true
end

CollectionLogic.GetGunSkinFilterSkinGroup = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedSkinGroups
end

CollectionLogic.ClearGunSkinFilterSkinGroup = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.selectedSkinGroups = {}
    Module.Collection.Config.Events.evtOnGunSkinFilterSkinGroupChanged:Invoke()
end


CollectionLogic.ToggleGunSkinFilterSkinType = function(skinType)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    if gunSkinFilterData.selectedSkinTypes[skinType] ~= nil then
        gunSkinFilterData.selectedSkinTypes[skinType] = nil 
    else
        gunSkinFilterData.selectedSkinTypes[skinType] = true
    end
    Module.Collection.Config.Events.evtOnGunSkinFilterSkinTypeChanged:Invoke()
end

CollectionLogic.IsGunSkinFilterSkinTypeSelected = function(skinType)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedSkinTypes[skinType] == true
end

CollectionLogic.GetGunSkinFilterSkinType = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedSkinTypes
end

CollectionLogic.ClearGunSkinFilterSkinType = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.selectedSkinTypes = {}
    Module.Collection.Config.Events.evtOnGunSkinFilterSkinTypeChanged:Invoke()
end


CollectionLogic.ToggleGunSkinFilterQuality = function(qualityId)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    if gunSkinFilterData.selectedQualityIDs[qualityId] ~= nil then
        gunSkinFilterData.selectedQualityIDs[qualityId] = nil 
    else
        gunSkinFilterData.selectedQualityIDs[qualityId] = true
    end
    Module.Collection.Config.Events.evtOnGunSkinFilterQualityChanged:Invoke()
end

CollectionLogic.IsGunSkinFilterQualitySelected = function(qualityId)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedQualityIDs[qualityId] == true
end

CollectionLogic.GetGunSkinFilterQuality = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedQualityIDs
end

CollectionLogic.ClearGunSkinFilterQuality = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.selectedQualityIDs = {}
    Module.Collection.Config.Events.evtOnGunSkinFilterQualityChanged:Invoke()
end


CollectionLogic.ToggleGunSkinFilterGunType = function(gunTypeId)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    if gunSkinFilterData.selectedGunTypeIDs[gunTypeId] ~= nil then
        gunSkinFilterData.selectedGunTypeIDs[gunTypeId] = nil
    else
        gunSkinFilterData.selectedGunTypeIDs[gunTypeId] = true
    end
    Module.Collection.Config.Events.evtOnGunSkinFilterGunTypeChanged:Invoke()
end

CollectionLogic.IsGunSkinFilterGunTypeSelected = function(gunTypeId)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedGunTypeIDs[gunTypeId] == true
end

CollectionLogic.GetGunSkinFilterGunType = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.selectedGunTypeIDs
end

CollectionLogic.SetGunSkinFilterGunType = function(gunTypeId)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.selectedGunTypeIDs = {}
    gunSkinFilterData.selectedGunTypeIDs[gunTypeId] = true
    Module.Collection.Config.Events.evtOnGunSkinFilterGunTypeChanged:Invoke()
end

CollectionLogic.ClearGunSkinFilterGunType = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.selectedGunTypeIDs = {}
    Module.Collection.Config.Events.evtOnGunSkinFilterGunTypeChanged:Invoke()
end

CollectionLogic.GetGunSkinFilterWeaponChoice = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.weaponChoice
end

CollectionLogic.SetGunSkinFilterWeaponChoice = function(choice)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.weaponChoice = setdefault(choice, 0)
    Module.Collection.Config.Events.evtOnGunSkinFilterWeaponChoiceChanged:Invoke()
end

CollectionLogic.GetGunSkinFilterSortMode = function()
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    return gunSkinFilterData.sortMode
end

CollectionLogic.SetGunSkinFilterSortMode = function(mode)
    local gunSkinFilterData = CollectionLogic.GetGunSkinFilterData()
    gunSkinFilterData.sortMode = setdefault(mode, 0)
    Module.Collection.Config.Events.evtOnGunSkinFilterSortModeChanged:Invoke()
end

CollectionLogic.GetPendantSeasonList = function()
    local seasonIdList = Module.Collection.Field:GetPendantSeasonList()
    if not seasonIdList then
        local pendantConfig = Module.Collection.Config.PendantConfig
        local seasonIdMap = {}
        for key, pendantConfigRow in pairs(pendantConfig) do
            if not seasonIdMap[pendantConfigRow.SeasonID] then 
                seasonIdMap[pendantConfigRow.SeasonID] = pendantConfigRow
            end
        end
        seasonIdList =  table.keys(seasonIdMap)
        table.sort(seasonIdList, function (a, b)
            return a < b
        end)
        local map = {}
        for k,v in ipairs(seasonIdList) do 
            map[v] = k
        end
        Module.Collection.Field:SetPendantSeasonMap(map)
        Module.Collection.Field:SetPendantSeasonList(seasonIdList)
    end
    return seasonIdList
end

CollectionLogic.GetWeaponSkinSeasonIDList = function()
    local seasonIdList = Module.Collection.Field:GetWeaponSkinSeasonIDList()
    if not seasonIdList then
        local skinConfig = Module.Collection.Config.WeaponSkinConfig
        local seasonIdMap = {}
        for key, skinConfigRow in pairs(skinConfig) do
            if skinConfigRow.SeasonID and skinConfigRow.SeasonID > 0 then
                declare_if_nil(seasonIdMap, skinConfigRow.SeasonID, true)
            end
        end
        seasonIdList =  table.keys(seasonIdMap)
        table.sort(seasonIdList, function (a, b)
            return a < b
        end)
        Module.Collection.Field:SetWeaponSkinSeasonIDList(seasonIdList)
    end
    return seasonIdList
end
------------------------------------挂饰

CollectionLogic.SetHangingFilterData = function(data)
    Module.Collection.Field:SetFilterData("Hanging",data)
end

CollectionLogic.ResetHangingFilterData = function()
    local hangingFilterData = {}
    local lastHangingFilterData = CollectionLogic.GetLastHangingFilterData()
    deepcopy(hangingFilterData, lastHangingFilterData)
    CollectionLogic.SetHangingFilterData(hangingFilterData)
end

CollectionLogic.GetHangingFilterData = function()
    local hangingFilterData = Module.Collection.Field:GetFilterData("Hanging")
    hangingFilterData = setdefault(hangingFilterData, {})
    hangingFilterData.selectedSeason = setdefault(hangingFilterData.selectedSeason,0)
    hangingFilterData.selectedSkinGroups = setdefault(hangingFilterData.selectedSkinGroups, {})
    hangingFilterData.selectedSkinTypes = setdefault(hangingFilterData.selectedSkinTypes, {})
    hangingFilterData.selectedQualityIDs = setdefault(hangingFilterData.selectedQualityIDs, {})
    CollectionLogic.SetHangingFilterData(hangingFilterData)
    return hangingFilterData
end


CollectionLogic.SetLastHangingFilterData = function(data)
    Module.Collection.Field:SetLastFilterData("Hanging",data)
end

CollectionLogic.GetLastHangingFilterData = function()
    local hangingFilterData = Module.Collection.Field:GetLastFilterData("Hanging")
    hangingFilterData = setdefault(hangingFilterData, {})
    hangingFilterData.selectedSeason = setdefault(hangingFilterData.selectedSeason,0)
    hangingFilterData.selectedSkinGroups = setdefault(hangingFilterData.selectedSkinGroups, {})
    hangingFilterData.selectedSkinTypes = setdefault(hangingFilterData.selectedSkinTypes, {})
    hangingFilterData.selectedQualityIDs = setdefault(hangingFilterData.selectedQualityIDs, {})
    CollectionLogic.SetLastHangingFilterData(hangingFilterData)
    return hangingFilterData
end

CollectionLogic.UpdateLastHangingFilterData = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    local lastHangingFilterData = {}
    deepcopy(lastHangingFilterData, hangingFilterData)
    CollectionLogic.SetLastHangingFilterData(lastHangingFilterData)
    Module.Collection.Field:SetSelectedPos(-1)
    Module.Collection.Config.Events.evtOnHangingFilterUpdated:Invoke()
end


CollectionLogic.ClearAllHangingFilterData = function()
    CollectionLogic.SetHangingFilterData(nil)
    CollectionLogic.SetLastHangingFilterData(nil)
end

CollectionLogic.SetHangingFilterSeason = function(season)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    hangingFilterData.selectedSeason = season
    Module.Collection.Config.Events.evtOnHangingFilterSeasonChanged:Invoke()
end

CollectionLogic.IsHangingFilterSeasonSelected = function(season)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSeason == season
end

CollectionLogic.GetHangingFilterSeason = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSeason
end

CollectionLogic.ClearHangingFilterSeason = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    hangingFilterData.selectedSeasons = {}
    Module.Collection.Config.Events.evtOnHangingFilterSeasonChanged:Invoke()
end

CollectionLogic.ToggleHangingFilterSkinGroup = function(skinGroup)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    if hangingFilterData.selectedSkinGroups[skinGroup] ~= nil then
        hangingFilterData.selectedSkinGroups[skinGroup] = nil 
    else
        hangingFilterData.selectedSkinGroups[skinGroup] = true
    end
    Module.Collection.Config.Events.evtOnHangingFilterSkinGroupChanged:Invoke()
end

CollectionLogic.IsHangingFilterSkinGroupSelected = function(skinGroup)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSkinGroups[skinGroup] == true
end

CollectionLogic.GetHangingFilterSkinGroup = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSkinGroups
end

CollectionLogic.ClearHangingFilterSkinGroup = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    hangingFilterData.selectedSkinGroups = {}
    Module.Collection.Config.Events.evtOnHangingFilterSkinGroupChanged:Invoke()
end


CollectionLogic.ToggleHangingFilterSkinType = function(skinType)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    if hangingFilterData.selectedSkinTypes[skinType] ~= nil then
        hangingFilterData.selectedSkinTypes[skinType] = nil 
    else
        hangingFilterData.selectedSkinTypes[skinType] = true
    end
    Module.Collection.Config.Events.evtOnHangingFilterSkinTypeChanged:Invoke()
end

CollectionLogic.IsHangingFilterSkinTypeSelected = function(skinType)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSkinTypes[skinType] == true
end

CollectionLogic.GetHangingFilterSkinType = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedSkinTypes
end

CollectionLogic.ClearHangingFilterSkinType = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    hangingFilterData.selectedSkinTypes = {}
    Module.Collection.Config.Events.evtOnHangingFilterSkinTypeChanged:Invoke()
end


CollectionLogic.ToggleHangingFilterQuality = function(qualityId)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    if hangingFilterData.selectedQualityIDs[qualityId] ~= nil then
        hangingFilterData.selectedQualityIDs[qualityId] = nil 
    else
        hangingFilterData.selectedQualityIDs[qualityId] = true
    end
    Module.Collection.Config.Events.evtOnHangingFilterQualityChanged:Invoke()
end

CollectionLogic.IsHangingFilterQualitySelected = function(qualityId)
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedQualityIDs[qualityId] == true
end

CollectionLogic.GetHangingFilterQuality = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    return hangingFilterData.selectedQualityIDs
end

CollectionLogic.ClearHangingFilterQuality = function()
    local hangingFilterData = CollectionLogic.GetHangingFilterData()
    hangingFilterData.selectedQualityIDs = {}
    Module.Collection.Config.Events.evtOnHangingFilterQualityChanged:Invoke()
end


------------------------------------典藏挂饰

CollectionLogic.SetMysticalPendantFilterData = function(data)
    Module.Collection.Field:SetFilterData("MysticalPendant",data)
end

CollectionLogic.ResetMysticalPendantFilterData = function()
    local mysticalPendantFilterData = {}
    local lastMysticalPendantFilterData = CollectionLogic.GetLastMysticalPendantFilterData()
    deepcopy(mysticalPendantFilterData, lastMysticalPendantFilterData)
    CollectionLogic.SetMysticalPendantFilterData(mysticalPendantFilterData)
end

CollectionLogic.GetMysticalPendantFilterData = function()
    local mysticalPendantFilterData = Module.Collection.Field:GetFilterData("MysticalPendant")
    mysticalPendantFilterData = setdefault(mysticalPendantFilterData, {})
    mysticalPendantFilterData.seasonChoice = setdefault(mysticalPendantFilterData.seasonChoice, 0)
    mysticalPendantFilterData.selectedQualityIDs = setdefault(mysticalPendantFilterData.selectedQualityIDs, {})
    CollectionLogic.SetMysticalPendantFilterData(mysticalPendantFilterData)
    return mysticalPendantFilterData
end


CollectionLogic.SetLastMysticalPendantFilterData = function(data)
    Module.Collection.Field:SetLastFilterData("MysticalPendant",data)
end

CollectionLogic.GetLastMysticalPendantFilterData = function()
    local mysticalPendantFilterData = Module.Collection.Field:GetLastFilterData("MysticalPendant")
    mysticalPendantFilterData = setdefault(mysticalPendantFilterData, {})
    mysticalPendantFilterData.seasonChoice = setdefault(mysticalPendantFilterData.seasonChoice, 0)
    mysticalPendantFilterData.selectedQualityIDs = setdefault(mysticalPendantFilterData.selectedQualityIDs, {})
    CollectionLogic.SetLastMysticalPendantFilterData(mysticalPendantFilterData)
    return mysticalPendantFilterData
end

CollectionLogic.UpdateLastMysticalPendantFilterData = function()
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    local lastMysticalPendantFilterData = {}
    deepcopy(lastMysticalPendantFilterData, mysticalPendantFilterData)
    CollectionLogic.SetLastMysticalPendantFilterData(lastMysticalPendantFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterUpdated:Invoke()
end


CollectionLogic.ClearAllMysticalPendantFilterData = function()
    CollectionLogic.SetMysticalPendantFilterData(nil)
    CollectionLogic.SetLastMysticalPendantFilterData(nil)
end

CollectionLogic.GetMysticalPendantFilterSeasonChoice = function()
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    return mysticalPendantFilterData.seasonChoice
end

CollectionLogic.SetMysticalPendantFilterSeasonChoice = function(choice)
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    mysticalPendantFilterData.seasonChoice = setdefault(choice, 0)
    CollectionLogic.SetMysticalPendantFilterData(mysticalPendantFilterData)
    Module.Collection.Config.Events.evtOnWorkshopFilterSeasonChoiceChanged:Invoke()
end

CollectionLogic.ToggleMysticalPendantFilterQuality = function(qualityId)
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    if mysticalPendantFilterData.selectedQualityIDs[qualityId] ~= nil then
        mysticalPendantFilterData.selectedQualityIDs[qualityId] = nil 
    else
        mysticalPendantFilterData.selectedQualityIDs[qualityId] = true
    end
    Module.Collection.Config.Events.evtOnWorkshopFilterQualityChanged:Invoke()
end

CollectionLogic.IsMysticalPendantFilterQualitySelected = function(qualityId)
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    return mysticalPendantFilterData.selectedQualityIDs[qualityId] == true
end

CollectionLogic.GetMysticalPendantFilterQuality = function()
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    return mysticalPendantFilterData.selectedQualityIDs
end

CollectionLogic.ClearMysticalPendantFilterQuality = function()
    local mysticalPendantFilterData = CollectionLogic.GetMysticalPendantFilterData()
    mysticalPendantFilterData.selectedQualityIDs = {}
    Module.Collection.Config.Events.evtOnWorkshopFilterQualityChanged:Invoke()
end

CollectionLogic.GetPendantIsOpenCollection = function(pendantId)
    local config = CollectionLogic.GetPendantDataRow(pendantId)
    if config then 
        return config.OpenCollection
    else
        return false
    end
end

CollectionLogic.GetPendantIsDisplayResources = function(pendantId)
    local config = CollectionLogic.GetPendantDataRow(pendantId)
    if config then 
        return config.DisplayResources
    else
        return false,false
    end
end

CollectionLogic.GetPendantIsOpenCollectionAndDisplayResources = function(pendantId)
    local config = CollectionLogic.GetPendantDataRow(pendantId)
    if config then 
        return config.OpenCollection,config.DisplayResources
    else
        return false
    end
end

CollectionLogic.GetMandelBrickIdByPendantId= function(pendantId)
    local config = CollectionLogic.GetPendantDataRow(pendantId)
    if config then 
        return config.MandelBrickID or 0
    else
        return 16110001001
    end
end

CollectionLogic.GetDownloadModuleKeyByItemId = function(itemId)
    local moduleKey = "WeaponSkin"
    if itemId then
        moduleKey = Module.ExpansionPackCoordinator:GetDownloadCategary(itemId) or moduleKey
    end
    return moduleKey
end

CollectionLogic.OpenRandomSkinUI = function(weaponID)
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRandomSkinOverviewPanel, nil, nil, weaponID)
end

CollectionLogic.CheckIsSkinInRandomPool = function(skinId, skinGid)
    return Server.CollectionServer:CheckIsSkinInRandomPool(CollectionLogic.GetBaseWeaponIdFromSkinId(skinId), skinId, skinGid)
end

CollectionLogic.CheckAreAnySkinsInRandomPool = function(weaponId)
    return Server.CollectionServer:CheckAreAnySkinsInRandomPool(weaponId)
end

CollectionLogic.CheckIsRandomPoolEnabled = function(baseWeaponId)
    return Server.CollectionServer:CheckIsRandomPoolEnabled(baseWeaponId)
end

CollectionLogic.UpdateRandomSkin = function (skinId, skinGid, bAppend, fCustomCallback)
    Server.CollectionServer:UpdateRandomSkinPool(CollectionLogic.GetBaseWeaponIdFromSkinId(skinId), {skinId}, {skinGid}, bAppend, false ,fCustomCallback)
end

CollectionLogic.EnableRandomSkin = function (weaponId, bEnable, fCustomCallback)
    Server.CollectionServer:EnableRandomSkin(weaponId, bEnable, fCustomCallback) 
end

CollectionLogic.UpdateAllRandomSkin = function (weaponId, skinIds, skinGids, fCustomCallback)
    Server.CollectionServer:UpdateRandomSkinPool(weaponId, skinIds, skinGids, false, true ,fCustomCallback)
end

CollectionLogic.GetProcessedPropExpireInfo = function(propId)
    local rawExpireInfo = Server.CollectionServer:GetPropExpireInfo(propId)
    if not rawExpireInfo or #rawExpireInfo == 0 then
        return nil
    end
    local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
    local expireGroupMap = {}
    for index, info in ipairs(rawExpireInfo) do
        local secondsLeft = info.expireTime - currentTimestamp
        if secondsLeft > 0 then
            if secondsLeft > 60 then
                if secondsLeft > 3600 then
                    if secondsLeft > 86400 then
                        local day = math.floor(secondsLeft/60/60/24)
                        declare_if_nil(expireGroupMap, Module.Collection.Config.EExpireType.Days, {})
                        declare_if_nil(expireGroupMap[Module.Collection.Config.EExpireType.Days], day, {expireTime = info.expireTime, num = 0})
                        expireGroupMap[Module.Collection.Config.EExpireType.Days][day].num = expireGroupMap[Module.Collection.Config.EExpireType.Days][day].num + info.num
                        expireGroupMap[Module.Collection.Config.EExpireType.Days][day].expireTime = math.min(info.expireTime, expireGroupMap[Module.Collection.Config.EExpireType.Days][day].expireTime)
                    else
                        local hour = math.floor(secondsLeft/60/60)
                        declare_if_nil(expireGroupMap, Module.Collection.Config.EExpireType.Hours, {})
                        declare_if_nil(expireGroupMap[Module.Collection.Config.EExpireType.Hours], hour, {expireTime = info.expireTime, num = 0})
                        expireGroupMap[Module.Collection.Config.EExpireType.Hours][hour].num = expireGroupMap[Module.Collection.Config.EExpireType.Hours][hour].num + info.num
                        expireGroupMap[Module.Collection.Config.EExpireType.Hours][hour].expireTime = math.min(info.expireTime, expireGroupMap[Module.Collection.Config.EExpireType.Hours][hour].expireTime)
                    end
                else
                    local minute = math.floor(secondsLeft/60)
                    declare_if_nil(expireGroupMap, Module.Collection.Config.EExpireType.Minutes, {})
                    declare_if_nil(expireGroupMap[Module.Collection.Config.EExpireType.Minutes], minute, {expireTime = info.expireTime, num = 0})
                    expireGroupMap[Module.Collection.Config.EExpireType.Minutes][minute].num = expireGroupMap[Module.Collection.Config.EExpireType.Minutes][minute].num + info.num
                    expireGroupMap[Module.Collection.Config.EExpireType.Minutes][minute].expireTime = math.min(info.expireTime, expireGroupMap[Module.Collection.Config.EExpireType.Minutes][minute].expireTime)
                end
            else
                declare_if_nil(expireGroupMap, Module.Collection.Config.EExpireType.Seconds, {})
                declare_if_nil(expireGroupMap[Module.Collection.Config.EExpireType.Seconds], 0, {expireTime = info.expireTime, num = 0})
                expireGroupMap[Module.Collection.Config.EExpireType.Seconds][0].num = expireGroupMap[Module.Collection.Config.EExpireType.Seconds][0].num + info.num
                expireGroupMap[Module.Collection.Config.EExpireType.Seconds][0].expireTime = math.min(info.expireTime, expireGroupMap[Module.Collection.Config.EExpireType.Seconds][0].expireTime)
            end
        else
            declare_if_nil(expireGroupMap, Module.Collection.Config.EExpireType.Expired, {})
            declare_if_nil(expireGroupMap[Module.Collection.Config.EExpireType.Expired], 0, {expireTime = 0, num = 0})
            expireGroupMap[Module.Collection.Config.EExpireType.Expired][0].num = expireGroupMap[Module.Collection.Config.EExpireType.Expired][0].num + info.num
        end
    end
    local expireGroupList = {}
    for type, groupInfo in pairs(expireGroupMap) do
        for groupId, timeInfo in pairs(groupInfo) do
            if type == Module.Collection.Config.EExpireType.Expired then
                timeInfo.timeLeftText_1 = Module.Collection.Config.Loc.Expired
                timeInfo.timeLeftText_2 = Module.Collection.Config.Loc.Expired
            elseif type == Module.Collection.Config.EExpireType.Seconds then
                timeInfo.timeLeftText_1 = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.PropExpirationInSecondsTip,
                {["Amount"] = timeInfo.num or 1})
                timeInfo.timeLeftText_2 = StringUtil.PluralTextFormat(Module.Collection.Config.Loc.PropExpirationInSecondsHint,
                {["Amount"] = timeInfo.num or 1})
            elseif type == Module.Collection.Config.EExpireType.Minutes 
                or type == Module.Collection.Config.EExpireType.Hours 
                or type == Module.Collection.Config.EExpireType.Days then
                local simpleExpireText = CollectionLogic.GetExpireTimeTextByTimestamp(timeInfo.expireTime)
                timeInfo.timeLeftText = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.PropExpirationInTimeSimpleTip,
                {["TimeText"] = simpleExpireText,
                ["Amount"] = timeInfo.num or 1})
                timeInfo.timeLeftText_1 = StringUtil.Key2StrFormat(Module.Collection.Config.Loc.PropExpirationInTimeTip,
                {["TimeText"] = simpleExpireText,
                ["Amount"] = timeInfo.num or 1})
                timeInfo.timeLeftText_2 = StringUtil.PluralTextFormat(Module.Collection.Config.Loc.PropExpirationInTimeHint,
                {["TimeText"] = simpleExpireText,
                ["Amount"] = timeInfo.num or 1})
            end
            table.insert(expireGroupList, timeInfo)
        end
    end
    table.sort(expireGroupList, function(timeInfoA, timeInfoB)
        return timeInfoA.expireTime < timeInfoB.expireTime 
    end)
    return expireGroupList
end


CollectionLogic.GetExpireTimeTextByTimestamp = function(expireTimestamp)
    local secondsLeft = expireTimestamp - Facade.ClockManager:GetServerTimestamp()
    if secondsLeft > 0 then
        if secondsLeft > 60 then
            if secondsLeft > 3600 then
                if secondsLeft > 86400 then
                    local day = math.floor(secondsLeft/60/60/24)
                    return StringUtil.PluralTextFormat(Module.Collection.Config.Loc.NumOfDay,{["Day"] = day})
                else
                    local hour = math.floor(secondsLeft/60/60)
                    return StringUtil.PluralTextFormat(Module.Collection.Config.Loc.NumOfHour,{["Hour"] = hour})
                end
            else
                local minute = math.floor(secondsLeft/60)
                return StringUtil.PluralTextFormat(Module.Collection.Config.Loc.NumOfMinute,{["Minute"] = minute})
            end
        else
            return Module.Collection.Config.Loc.InOneMinute
        end
    else
        return Module.Collection.Config.Loc.Expired
    end
end


CollectionLogic.GetMandelBrickTopPrizeItemId = function(mandelBrickId)
    local topPropId
    local itemMainType
    local itemSubType
    if isvalid(mandelBrickId) then
        local topQuality = -1
        local itemConfig = ItemConfigTool.GetItemConfigById(mandelBrickId)
        if itemConfig then
            local lotteryBoxGroupConfigRows = Server.StoreServer:GetLotteryBoxGroupConfigByID(itemConfig.ConnectedPool)
            for index, lotteryBoxGroupConfigRow in ipairs(lotteryBoxGroupConfigRows) do
                if lotteryBoxGroupConfigRow.CoreFlag == 1 then
                    local lotteryBoxConfigRow = Server.StoreServer:GetLotteryBoxConfigByID(lotteryBoxGroupConfigRow.BoxID)
                    local lotteryBoxPropConfigRows = Server.StoreServer:GetLotteryBoxPropConfigByID(lotteryBoxGroupConfigRow.GroupID)
                    for index, lotteryBoxPropConfigRow in ipairs(lotteryBoxPropConfigRows) do
                        itemMainType = ItemHelperTool.GetMainTypeById(lotteryBoxPropConfigRow.PropID)
                        itemSubType = ItemHelperTool.GetSubTypeById(lotteryBoxPropConfigRow.PropID)
                        if itemMainType == EItemType.WeaponSkin or itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant then
                            itemConfig = ItemConfigTool.GetItemConfigById(lotteryBoxPropConfigRow.PropID)
                            if itemConfig then
                                if itemConfig.Quality > topQuality then
                                    topQuality = itemConfig.Quality
                                    topPropId = lotteryBoxPropConfigRow.PropID
                                elseif itemConfig.Quality == topQuality and lotteryBoxConfigRow and lotteryBoxConfigRow.ShowID1 == lotteryBoxPropConfigRow.PropID then
                                    topPropId = lotteryBoxPropConfigRow.PropID
                                end
                            end
                        end
                    end
                end
            end 
        end
    end
    return topPropId, itemMainType, itemSubType
end

CollectionLogic.RegStackUIInputSummary = function(summary, bReplace)
    if IsHD() then
        Module.CommonBar:SetBottomBarTempInputSummaryList(summary or {}, bReplace, bReplace)
    end
end

return CollectionLogic
