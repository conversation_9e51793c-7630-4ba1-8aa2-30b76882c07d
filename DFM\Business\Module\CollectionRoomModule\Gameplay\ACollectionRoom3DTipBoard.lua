----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local UWidgetComponent = import "WidgetComponent"

---@class ACollectionRoom3DTipBoard : ACollectionRoomInteractorBase
local ACollectionRoom3DTipBoard = class("ACollectionRoom3DTipBoard", ACollectionRoomInteractorBase)

function ACollectionRoom3DTipBoard:Ctor()
    self._widgetUI = nil
end

function ACollectionRoom3DTipBoard:Imp_ReceiveBeginPlay()
    loginfo("ACollectionRoom3DTipBoard:Imp_ReceiveBeginPlay")
    local widgetComponent = self:GetComponentByClass(UWidgetComponent)
    if widgetComponent then
        self._widgetUI = widgetComponent:GetUserWidgetObject()
    end
    if self._widgetUI then
        self._widgetUI:SetState(0)
        local SoftPath = FSoftObjectPath()
        SoftPath:SetPath("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_CollectionRoom_Icon_0001.CommonHall_CollectionRoom_Icon_0001'")
        self._widgetUI:SetInfo(SoftPath, CollectionRoomConfig.Loc.CollectionRoomManage, 0, 0, 1)
    else
        loginfo("ACollectionRoom3DTipBoard:Imp_ReceiveBeginPlay fail to find WidgetUI")
    end
end

function ACollectionRoom3DTipBoard:Imp_ReceiveEndPlay()
    self._widgetUI = nil
end

return ACollectionRoom3DTipBoard