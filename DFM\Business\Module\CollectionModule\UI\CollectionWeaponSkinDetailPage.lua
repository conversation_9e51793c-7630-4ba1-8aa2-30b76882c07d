----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionWeaponSkinDetailPage : LuaUIBaseWindow
local CollectionWeaponSkinDetailPage = ui("CollectionWeaponSkinDetailPage", require("DFM.YxFramework.Managers.UI.LuaUIBaseWindow"))
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local UGunPresetTableManager = import "GunPresetTableManager"
local EModularPartNodeType = import "EModularPartNodeType"
local UGPGameViewportClient = import "GPGameViewportClient"
local EMouseCursor = import("EMouseCursor")
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
-- BEGIN MODIFICATION @ VIRTUOS : UI Input Navigitor
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
-- END MODIFICATION

function CollectionWeaponSkinDetailPage:Ctor()
    self._wtMask = self:Wnd("DFImage", UIImage)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtPartsSlot = self:Wnd("wtPartsSlot", UIWidgetBase)
    self._wtEffectsBtn = self:Wnd("wtEffectsBtn", DFCommonButtonOnly)
    self._wtEffectsBtn:Event("OnClicked", self._ShowSkinEffectsPage, self)
    self._wtRenameBtn = self:Wnd("wtRenameBtn", DFCommonButtonOnly)
    self._wtRenameBtn:Event("OnClicked", self._ShowSkinRenamePage, self)
    self._wtShowPresetPartsCheckBox = self:Wnd("wtShowPresetPartsCheckBox", DFCheckBox)
    self._wtShowPresetPartsCheckBox:Event("OnCheckStateChanged", self._OnShowPresetPartsCheckBoxStateChanged, self)
    self._wtPurchaseBtn = self:Wnd("wtPurchaseBtn", DFCommonButtonOnly)
    self._wtPurchaseBtn:Event("OnClicked", self._OnPurchaseBtnClicked, self)
    self._wtShowPresetHint = self:Wnd("wtShowPresetHint", UITextBlock)
    self._wtShowPresetPartsCheckBox:SetIsChecked(false, false)
    self._wtShowPresetPartsCheckBox:Collapsed()
    self._wtShowPresetHint:Collapsed()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self:BP_SetType(0)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {ECurrencyClientId.Special, ECurrencyClientId.Diamond})
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Collection.Config.Loc.WeaponSkinDetail)
    self._wtEffectsBtn:Collapsed()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 600
        self._WeaponScaleZoom = 30
        self._wtShowPresetPartsIcon = self:Wnd("WBP_CommonKeyIconBox", UIWidgetBase)
        self._wtShowPresetPartsIcon:SetOnlyDisplayOnGamepad(true)
        self._wtShowPresetPartsIcon:InitByDisplayInputActionName("GunSkin_DisplayDefaultPart", true, 0, false)
        -- self._wtShowPresetPartsIcon:SetDisplayInputAction(("GunSkin_DisplayDefaultPart", true, nil, true))
    end
    -- END MODIFICATION
end


function CollectionWeaponSkinDetailPage:OnInitExtraData(item, hidePopUpCallback, bAutoShowPresetParts, skinItem)
    self._item = item
    self._hidePopUpCallback = hidePopUpCallback
    self._bAutoShowPresetParts = bAutoShowPresetParts or false
    self._skinItem = skinItem
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionWeaponSkinDetailPage:OnOpen()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallWeaponShow)
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Add(self._OnRotateModel, self)
    end
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(true,Module.Reward.Config.ECollectionDetaiType.WeaponSkin)
    self:_AddListeners()
    UGPGameViewportClient.SetCursor(self,EMouseCursor.Hand)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionWeaponSkinDetailPage:OnClose()
end

function CollectionWeaponSkinDetailPage:OnActivate()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallWeaponShow)
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Add(self._OnRotateModel, self)
    end
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(true,Module.Reward.Config.ECollectionDetaiType.WeaponSkin)
    self:_AddListeners()
    UGPGameViewportClient.SetCursor(self,EMouseCursor.Hand)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
end


function CollectionWeaponSkinDetailPage:OnDeactivate()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Remove(self._OnRotateModel, self)
    end
    -- UGPGameViewportClient.SetCursor(self,EMouseCursor.Default) 不能是Default
    UGPGameViewportClient.SetCursor(self,EMouseCursor.None)
    self:RemoveAllLuaEvent()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
    local displayType = IsHD() and "Weapon" or "WeaponM"
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayType", displayType)
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(false,Module.Reward.Config.ECollectionDetaiType.WeaponSkin)
    self._hidePopUpCallback = nil
    self._bEnableShowPresetPartsCheckBox = false
    self._item = nil
    self._bAutoShowPresetParts = false
    self._skinItem = nil
    self._bIsActivated = false
end


-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionWeaponSkinDetailPage:OnShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionWeaponSkinDetailPage:OnHide()
    self._wtShowPresetPartsCheckBox:SetIsChecked(false, false)
    self._wtPartsSlot:Collapsed()
    self:BP_SetType(0)
    UGPGameViewportClient.SetCursor(self,EMouseCursor.None)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "DestroyWatch")
end


function CollectionWeaponSkinDetailPage:OnShowBegin()
    if not self._bIsActivated then
        self:_OnRefreshItemDetail()
        self._bIsActivated = true
    elseif Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallWeaponShow then
        self:_OnRefreshModel(ESubStage.HallWeaponShow)
    end
    self._PCActionList = {}
    if IsHD() and self._skinInfo ~= nil and self._skinInfo.VideosPath and #self._skinInfo.VideosPath > 0 then
        table.insert(self._PCActionList, {actionName = "GunSkinDetail_ShowEffects",func = self._ShowSkinEffectsPage, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._PCActionList, false)
    end
    if self._hidePopUpCallback then
        self._hidePopUpCallback(false)
    end
    if not self._inputTypeChangedHandle then
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:_EnableGamepadFeature()
end


function CollectionWeaponSkinDetailPage:OnHideBegin()
    if self._hidePopUpCallback then
        self._hidePopUpCallback(true)
    end
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end


function CollectionWeaponSkinDetailPage:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._ShowCount = 0
    --配置keyIcon
    if self._wtPurchaseBtn then
        self._wtPurchaseBtn:SetDisplayInputAction("MallPurchase", true, nil, true)
    end
    --配置输入
    if not self._Purchase and self._wtPurchaseBtn:IsVisible() then
        self._Purchase  = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self._OnPurchaseBtnClicked,
        self, 
        EDisplayInputActionPriority.UI_Stack
    )
    end
    if not self._ToggleTip and self._wtItemDetailView then
        if self._wtItemDetailView:IsVisible() and self:_IsShowedTipBox() then
            self._ToggleTip  = self:AddInputActionBinding(
                "Common_ToggleTip", 
                EInputEvent.IE_Pressed, 
                self._ToggleTips,
                self, 
                EDisplayInputActionPriority.UI_Stack
            )
            table.insert(self._PCActionList,{actionName="Common_ToggleTip", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        end
    end
    if not self._ShowPresetPartsHandle and self._wtShowPresetPartsCheckBox then
        self._ShowPresetPartsHandle  = self:AddInputActionBinding(
            "GunSkin_DisplayDefaultPart", 
            EInputEvent.IE_Pressed, 
            self._OnShowPresetPartsCheckBoxClick,
            self,
            EDisplayInputActionPriority.UI_Stack
        )
    end
    --武器旋转的输入
    if self._RotationX == nil then
        self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
    end
    if self._RotationY == nil then
        self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleUp == nil then
        self._ScaleUp = self:AddAxisInputActionBinding("Common_Right_Trigger_Axis", self._WeaponScaleUp, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleDown == nil then
        self._ScaleDown = self:AddAxisInputActionBinding("Common_Left_Trigger_Axis", self._WeaponScaleDown, self, EDisplayInputActionPriority.UI_Stack)
    end
    CollectionLogic.RegStackUIInputSummary(self._PCActionList, false)
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup then
            self._navGroup:MarkIsStackControlGroup()
        end
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoDpad_NoA, self)
    end
    self._wtRenameBtn:SetDisplayInputAction("Collection_Rename_Gamepad", true, nil, true)
    if not self._RenameBtn and self._wtRenameBtn:IsVisible() then
        self._RenameBtn = self:AddInputActionBinding("Collection_Rename_Gamepad", EInputEvent.IE_Pressed, self._ShowSkinRenamePage,self, EDisplayInputActionPriority.UI_Stack)
    end
end

function CollectionWeaponSkinDetailPage:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_HideTip()
    self._ShowCount = 0
    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase= nil
    end
    if self._ToggleTip then
        self:RemoveInputActionBinding(self._ToggleTip)
        self._ToggleTip= nil
    end
    if self._ShowPresetPartsHandle then
        self:RemoveInputActionBinding(self._ShowPresetPartsHandle)
        self._ShowPresetPartsHandle = nil 
    end
    if self._RotationX then
        self:RemoveInputActionBinding(self._RotationX)
        self._RotationX = nil
    end
    if self._RotationY then
        self:RemoveInputActionBinding(self._RotationY)
        self._RotationY = nil
    end
    if self._ScaleUp then
        self:RemoveInputActionBinding(self._ScaleUp)
        self._ScaleUp = nil
    end
    if self._ScaleDown then
        self:RemoveInputActionBinding(self._ScaleDown)
        self._ScaleDown = nil
    end
    if #self._PCActionList > 1 then
        table.remove(self._PCActionList,2)
    end
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self._navGroup = nil
    end
    if self._RenameBtn then 
        self:RemoveInputActionBinding(self._RenameBtn)
        self._RenameBtn = nil
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionWeaponSkinDetailPage:_OnShowPresetPartsCheckBoxClick()
    if IsHD() and self._wtShowPresetPartsCheckBox:IsVisible() then
        self._wtShowPresetPartsCheckBox:NavigationClick()
    end
end
-- END MODIFICATION

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionWeaponSkinDetailPage:OnAnimFinished(anim)
end



function CollectionWeaponSkinDetailPage:_AddListeners()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self._OnRefreshModel, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnRefreshItemDetail, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyMallGiftSuc, self._OnStoreBuyHotRecommendationSuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyRecordUpdate, self._OnStoreBuyRecordUpdate, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end



function CollectionWeaponSkinDetailPage:_OnRefreshItemDetail()
    self._wtItemDetailView:Collapsed()
    self._wtPartsSlot:Collapsed()
    self._wtEffectsBtn:Collapsed()
    self._wtRenameBtn:Collapsed()
    self._wtPurchaseBtn:Collapsed()
    self._wtMask:Visible()
    if IsHD() then
        self._wtShowPresetPartsIcon:Collapsed()
    end
    if isvalid(self._item) then
        if self._item.itemMainType == EItemType.HeroAccessory then
            self._wtMask:Collapsed() --展示手表时隐藏遮罩
        end
        if self._item.itemMainType ~= EItemType.Weapon then
            self._item = Server.CollectionServer:GetCollectionItemById(self._item.id, self._item.gid) or self._item
            local skinConfigRow = CollectionLogic.GetWeaponSkinDataRow(self._item.id)
            if skinConfigRow then
                self._skinInfo = {
                    VideosName = skinConfigRow.VideosName or {},
                    VideosDes = skinConfigRow.VideosDes or {},
                    VideosPath = skinConfigRow.VideosPath or {},
                }
            end
            if self._item.itemSubType == ItemConfig.EWeaponItemType.Melee then
                self._wtItemDetailView:UpdateItem(self._item)
            else
                if self._item.gid ~= 0  and Server.CollectionServer:IsOwnedWeaponSkin(self._item.id, self._item.gid) == true and self._item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                    self._wtRenameBtn:SelfHitTestInvisible()
                end
                self._wtItemDetailView:UpdateItem(self._item)
            end
            if self._skinInfo ~= nil and self._skinInfo.VideosPath and #self._skinInfo.VideosPath > 0 then
                if IsHD() then
                    self._wtEffectsBtn:Collapsed()
                else
                    self._wtEffectsBtn:SelfHitTestInvisible()
                end
            end
        else
            self._wtItemDetailView:UpdateItem(ItemBase:NewIns(0))
            self._wtItemDetailView:SetItemTitleName(string.format(CollectionConfig.Loc.DefaultSkin, self._item.name))
            self._wtItemDetailView:SetItemDesc(string.format(CollectionConfig.Loc.DefaultSkin, self._item.name))
        end
        self._wtItemDetailView:SetDetailBtnVisible(false)
        self._wtItemDetailView:SelfHitTestInvisible()
        if Server.CollectionServer:IsOwnedWeaponSkin(self._item.id, self._item.gid) == false then
            self.shopData = Server.StoreServer:GetMallGiftWeaponSkinDataByItemID(self._item.id)
            if self.shopData ~= nil then
                local buyLimted = self:GetMallGiftIsBuyLimitedByShopData(self.shopData)
                if buyLimted then
                    -- self._wtPurchaseBtn:BP_SetMainTitle(Module.Store.Config.Loc.RecommendBundleAllItemBuyButtonTip)
                    -- self._wtPurchaseBtn:SetIsEnabled(false)
                else
                    self._wtPurchaseBtn:Visible()
                    self._wtPurchaseBtn:SetIsEnabled(true)

                    local priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal,
                            Module.Currency:GetRichTxtImgByItemId(self.shopData.CurrencyType),
                            MathUtil.GetNumberFormatStr(self.shopData.Price))

                    self._wtPurchaseBtn:BP_SetMainTitle(priceStr)
                end
            end
        end
        self:_RefreshDownloadBtn()
    end
    self:_OnRefreshModel(ESubStage.HallWeaponShow)
end


function CollectionWeaponSkinDetailPage:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
    if moduleName == moduleKey then
        self:_OnRefreshModel(ESubStage.HallWeaponShow)
        self:_RefreshDownloadBtn()
    end
end

function CollectionWeaponSkinDetailPage:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionWeaponSkinDetailPage:_RefreshDownloadBtn()
    if not hasdestroy(self._wtCommonDownload) then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._item and self._item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        if not bDownloaded and isvalid(self._item) then
            self._wtCommonDownload:InitModuleKey(moduleKey)
            self._wtCommonDownload:Visible()
        else
            self._wtCommonDownload:Collapsed()
        end
    end
end


function CollectionWeaponSkinDetailPage:GetMallGiftIsBuyLimitedByShopData(shopData)
    local ret = false

    if shopData == nil or shopData.GoodsId == nil then
        return ret
    end

    local buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(shopData.GoodsId)
    if buyRecord ~= nil then
        if shopData.LimitType ~= nil and shopData.LimitType > 0 and shopData.LimitAmount > 0 then
            local timeStamp =  Server.StoreServer:GetBuyLimitResetTimeByLimitType(shopData.LimitType)
            if timeStamp ~= 0 then
                if buyRecord.num >= shopData.LimitAmount and buyRecord.buy_time > timeStamp then
                    ret = true
                end
            end
        else
            ret = true
        end
    else
        --buyRecord == nil doesn't limited
        ret = false
    end

    return ret
end

function CollectionWeaponSkinDetailPage:_OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallWeaponShow then
        local displayType = IsHD() and "Weapon" or "WeaponM"
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayType", displayType)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetWeaponDisplayType", "")
        --设置背景
        Facade.HallSceneManager:SetDisplayBackground(1, false)
        if isvalid(self._item) then
            local weaponActor
            local weaponDesc, partIndexs
            if self._item.itemMainType == EItemType.Weapon then
                local presetConfig = UGunPresetTableManager.Get():GetGunByItemId(self._item.id)
                partIndexs = {}
                if presetConfig ~= nil then
                    weaponDesc, partIndexs = UAssembleWeaponDataLibrary.GetWeaponDescAndPartIndexsFromPreset(presetConfig, partIndexs)
                end
                if isvalid(weaponDesc) then
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetIsAdapter", false)
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, false)--self._item.itemSubType == ItemConfig.EWeaponItemType.Melee
                    
                end
            else
                if self._item.itemMainType == EItemType.HeroAccessory then --增加了干员商业化周边展示
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayType", "Watch")
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "DestroyWatch")
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayWatch", tostring(self._item.id))

                    Facade.HallSceneManager:SetDisplayBackground(self._item.id, false)
                else
                    weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(self._item.id)
                    if weaponDesc == nil then
                        weaponDesc = self._item:GetRawDescObj()
                    end
                    if isvalid(weaponDesc) then
                        if self._item.gid ~= 0 then
                            WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, self._item:GetRawPropInfo())
                        end
                        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetIsAdapter", false)
                        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, false, false)
                        Facade.HallSceneManager:SetDisplayBackground(tostring(self._item.id), false)
                    end
                end
            end
            if self._item.itemMainType == EItemType.Weapon or self._item.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                weaponActor = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "GetWeaponActor")
            end
            if isvalid(weaponActor) then
                if not weaponDesc then
                    logerror("CollectionWeaponSkinDetailPage: weaponDesc is nil")
                    return
                end
                Module.Gunsmith:OpenSceneSocketMainUIFromCollection(self, self._wtPartsSlot, weaponActor, weaponDesc, partIndexs, CollectionLogic.IsInMp())
                self._bEnableShowPresetPartsCheckBox = false
                local parts = weaponDesc:GetAllParts()
                if partIndexs then
                    for index, partId in ipairs(partIndexs) do
                        if partId > 0 then
                            local node = parts:Get(partId)
                            if node.NodeType == EModularPartNodeType.Adpater then
                                if isvalid(node.ItemID) and UAssembleWeaponDataLibrary.IsVirutalPartItem(node.ItemID) == false then
                                    self._bEnableShowPresetPartsCheckBox = true
                                    break
                                end
                            end
                        end
                    end
                end
                if self._bEnableShowPresetPartsCheckBox == true then
                    if self._bAutoShowPresetParts == true then
                        self._bAutoShowPresetParts = false
                        self._wtShowPresetPartsCheckBox:SetIsChecked(true, false)
                        self:BP_SetType(1)
                        self._wtPartsSlot:SelfHitTestInvisible()
                        LogAnalysisTool.AddClickSkinDefPreset(self._item.id)
                    end
                    self._wtShowPresetPartsCheckBox:Visible()
                    -- BEGIN MODIFICATION @ VIRTUOS : 关闭手柄打开的Tips
                    if IsHD() and WidgetUtil.IsGamepad() then
                        self._wtShowPresetPartsIcon:SelfHitTestInvisible()
                    end
                    -- END MODIFICATION
                    self._wtShowPresetHint:SelfHitTestInvisible()
                end
            end
        end 
    end
end

function CollectionWeaponSkinDetailPage:_OnPurchaseBtnClicked()
    -- self:DoBuyMallGifts()
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData, self.shopData.BundleItems, self.shopData.BundleItems, true)
end

function CollectionWeaponSkinDetailPage:GetSubstituteCurrencyItemID()
    return 17888808888
end


function CollectionWeaponSkinDetailPage:DoBuyMallGifts()
    if self.shopData.IsCash > 0 then
        return
    else
        local goods_id = self.shopData.GoodsId
        local price = self.shopData.Price

        local currency_type = self.shopData.CurrencyType
        local currecny = Module.Currency:GetNumByItemId(currency_type)
        local currency_type_sub = self:GetSubstituteCurrencyItemID()
        local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

        local fee = 0
        local fee_sub = 0
        local bMoneyEnough = false
        if currecny >= price then
            bMoneyEnough = true
            fee = price
        else
            --use currency_type_sub
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub >= price then
                    bMoneyEnough = true
                    fee = currecny
                    fee_sub = price - currecny
                end
            end
        end

        if bMoneyEnough then
            Server.StoreServer:SendShopBuyBuyMallGiftReq(goods_id, currency_type, fee, currency_type_sub, fee_sub, 1)
            self._wtPurchaseBtn:SetIsEnabled(false)
        else
            -- Module.CommonTips:ShowSimpleTip(Module.Store.Config.Loc.NotEnoughMoney)
            loginfo("[ProductPreview] DoBuyMallGifts NotEnoughMoney currecny:" .. currecny)
            local showStr = Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView
            local confirmStr = Module.Store.Config.Loc.StoreMainTabRecharge

            local _rechargeCancelHandle = function()
            end

            local _rechargeConfirmHandle = function()
                Module.Store:ShowStoreRechargeMainPanle()
            end

            Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
            return
        end

    end
end

function CollectionWeaponSkinDetailPage:_ShowSkinEffectsPage()
    local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(self._item.id)
    if skinDataRow then
        Module.Collection:ShowCommonVideoListView(skinDataRow.VideosName, skinDataRow.VideosDes, skinDataRow.VideosPath)
    else
        logerror("[v_dzhanshen] CollectionWeaponSkinDetailPage:_ShowSkinEffectsPage skinDataRow invalid")
    end
end

function CollectionWeaponSkinDetailPage:_OnSkinEffectVideoEnd()
end

function CollectionWeaponSkinDetailPage:_OnShowPresetPartsCheckBoxStateChanged(bChecked)
    if bChecked then
        self:BP_SetType(1)
        self._wtPartsSlot:SelfHitTestInvisible()
        LogAnalysisTool.AddClickSkinDefPreset(self._item.id)
    else
        self:BP_SetType(0)
        self._wtPartsSlot:Collapsed()
    end
    self:_HideTip()
end


function CollectionWeaponSkinDetailPage:_OnRotateModel(bKeyPressed)
    if bKeyPressed == true then
        self._wtItemDetailView:Collapsed()
        self._wtPartsSlot:Collapsed()
        self._wtShowPresetPartsCheckBox:Collapsed()
        self._wtShowPresetHint:Collapsed()
        self._wtRenameBtn:Collapsed()
        if not IsHD() then
            self._wtEffectsBtn:Collapsed()
            Module.CommonBar:SetTopBarVisible(false)
        end

        -- BEGIN MODIFICATION @ VIRTUOS : 关闭手柄打开的Tips
        if IsHD() then
            self:_HideTip()
            self._ShowCount = 0
	    -- BEGIN MODIFICATION @ VIRTUOS : 关闭手柄打开的Tips
            self._wtShowPresetPartsIcon:Collapsed()
	    -- END MODIFICATION
        else
            self:_HideDetailViewTips()
        end
        -- END MODIFICATION
    else
        self._wtItemDetailView:SelfHitTestInvisible()
        if self._item.gid ~= 0 and Server.CollectionServer:IsOwnedWeaponSkin(self._item.id, self._item.gid) == true and self._item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
            self._wtRenameBtn:SelfHitTestInvisible()
        end
        if not IsHD() then
            if self._skinInfo ~= nil and self._skinInfo.VideosPath and #self._skinInfo.VideosPath > 0 then
                self._wtEffectsBtn:SelfHitTestInvisible()
            end
            Module.CommonBar:SetTopBarVisible(true)
        end
        if self._bEnableShowPresetPartsCheckBox == true then
            self._wtShowPresetPartsCheckBox:Visible()
            -- BEGIN MODIFICATION @ VIRTUOS : 关闭手柄打开的Tips
            if IsHD() and WidgetUtil.IsGamepad() then
                self._wtShowPresetPartsIcon:SelfHitTestInvisible()
            end
            -- END MODIFICATION
            self._wtShowPresetHint:SelfHitTestInvisible()
            if self._wtShowPresetPartsCheckBox:IsChecked() == true then
                self._wtPartsSlot:SelfHitTestInvisible()
                self:BP_SetType(1)
            else
                self:BP_SetType(0)
            end
        else
            self:BP_SetType(0)
        end
    end
end


function CollectionWeaponSkinDetailPage:_ShowSkinRenamePage()
    if self._wtRenameBtn:IsVisible() then
        if isvalid(self._item) then
            if self._item.itemMainType == EItemType.WeaponSkin and ItemHelperTool.IsMysticalSkin(self._item.id) and self._item.quality >= 5 then
                Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinRenamePage, nil, nil, self._item)
            end
        end
    end
end

function CollectionWeaponSkinDetailPage:_OnStoreBuyHotRecommendationSuc(dataChange)
    --update buy record
    Server.StoreServer:SendShopGetBuyRecordReq()
    loginfo("[ProductPreview] _OnStoreBuyHotRecommendationSuc")

    local itemList = {}
    if dataChange then
        if dataChange.prop_changes then
            local prop_changes = dataChange.prop_changes
            for _, propChange in ipairs(prop_changes) do
                if propChange.prop then
                    local bShowConsumerCoupon = true
                    if propChange.prop.id == Server.StoreServer:GetTimeLimitComsumeCouponId() then -- 限时消费券只有add时才展示
                        bShowConsumerCoupon = propChange.prop.num > 0
                    end
                    if bShowConsumerCoupon then
                        local item = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                        local weaponpriceature = item:GetFeature(EFeatureType.Weapon)
                        if weaponFeature and weaponFeature:IsWeaponSkin() then
                            -- 蓝图需要手动设置一下枪械信息
                            local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                            local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                            item:SetRawPropInfo(propinfo)
                        else
                            item:SetRawPropInfo(propChange.prop)
                        end
                        table.insert(itemList, item)
                    end
                end
            end
        end

        -- if dataChange.currency_changes then
        --     local currency_changes = dataChange.currency_changes
        --     for _, currencyChange in ipairs(currency_changes) do
        --         if currencyChange.delta ~= 0 then
        --             local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
        --             table.insert(itemList, item)
        --         end
        --     end
        -- end
    end

    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, nil, nil, true)
end


function CollectionWeaponSkinDetailPage:_OnStoreBuyRecordUpdate()
    if self.shopData ~= nil then
        self._wtPurchaseBtn:Visible()
        local buyLimted = self:GetMallGiftIsBuyLimitedByShopData(self.shopData)
        if buyLimted then
            self._wtPurchaseBtn:BP_SetMainTitle(Module.Store.Config.Loc.RecommendBundleAllItemBuyButtonTip)
            self._wtPurchaseBtn:SetIsEnabled(false)
        else
            self._wtPurchaseBtn:SetIsEnabled(true)
            local priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal,
                        Module.Currency:GetRichTxtImgByItemId(self.shopData.CurrencyType),
                        MathUtil.GetNumberFormatStr(self.shopData.Price))

            self._wtPurchaseBtn:BP_SetMainTitle(priceStr)
        end
    end
end

function CollectionWeaponSkinDetailPage:_OnRelayConnected()
    Server.StoreServer:SendShopGetBuyRecordReq()
end

function CollectionWeaponSkinDetailPage:_ToggleTips()
    if not IsHD() then
        return
    end
    if self._wtItemDetailView then
        --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
        self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
        if not self._ItemDetailEquipWeaponMysticalSkin or self._wtShowPresetPartsCheckBox:IsChecked() == true then
            return
        end
        if self._ItemDetailEquipWeaponMysticalSkin and self._ItemDetailEquipWeaponMysticalSkin:IsVisible() then
           self:_UpdatingShowCount()
           self:_UpdatingTipParameters()
            --显示tips
            if self._wtTipsCheckbox and self._wtParent and self._TipsType then
                self:_ShowTip(self._TipsType,self._wtTipsCheckbox,self._wtParent)
            end
        end
       
    end
end

function CollectionWeaponSkinDetailPage:_UpdatingShowCount()
    if not IsHD() then
        return
    end

    if self._ShowCount ~= nil then
        --获取tips参数,并根据当前按下Y的次数打开不同的tips
        self._ShowCount = self._ShowCount + 1
        self._ShowCount = self._ShowCount % 5
        if not self._ItemDetailEquipWeaponMysticalSkin._wtIDParent or not self._ItemDetailEquipWeaponMysticalSkin._wtIDParent:IsVisible() then
            if self._ShowCount == 1 then
            self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtRateParent or not self._ItemDetailEquipWeaponMysticalSkin._wtRateParent:IsVisible() then
            if self._ShowCount == 2 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtWearParent or not self._ItemDetailEquipWeaponMysticalSkin._wtWearParent:IsVisible() then
            if self._ShowCount == 3 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtkillCntParent or not self._ItemDetailEquipWeaponMysticalSkin._wtkillCntParent:IsVisible() then
            if self._ShowCount == 4 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        self._ShowCount = self._ShowCount % 5
    end
    
end

function CollectionWeaponSkinDetailPage:_UpdatingTipParameters()
    if not IsHD() then
        return
    end

    --根据_ShowCount显示不同的tip
    self._wtParent = nil
    self._wtTipsCheckbox = nil
    self._TipsType = nil
    if self._ShowCount == 1 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("SerialNumber", UIWidgetBase)
        self._wtIDPanel =  self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem", UIWidgetBase)
        if self._wtIDPanel then
            self._wtTipsCheckbox = self._wtIDPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinID
    elseif self._ShowCount == 2 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("Rarity", UIWidgetBase)
        self._wtRatePanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_1", UIWidgetBase)
        if self._wtRatePanel then
            self._wtTipsCheckbox = self._wtRatePanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinRare
    elseif self._ShowCount == 3 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("Wear", UIWidgetBase)
        self._wtWearPanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_2", UIWidgetBase)
        if self._wtWearPanel then
            self._wtTipsCheckbox =self._wtWearPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinWear
    elseif self._ShowCount == 4 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("CountQuantity", UIWidgetBase)
        self._wtkillCntPanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_3", UIWidgetBase)
        if self._wtkillCntPanel then
            self._wtTipsCheckbox =self._wtkillCntPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinKill
    else
        --隐藏tip
        self:_HideTip()
        return
    end
end

function CollectionWeaponSkinDetailPage:_ShowTip(TipsType,wtTipsCheckbox1, wtIDParent)
    if not IsHD() then
        return
    end
    self:_HideTip()
    if not self._handle then
        self._handle = Module.ItemDetail:OpenItemCommonMiniTipsByTipsType(TipsType, wtTipsCheckbox1, wtIDParent, true, nil, nil, 40)
    end
end

function CollectionWeaponSkinDetailPage:_HideTip()
    if not IsHD() then
        return
    end
    if self._handle then
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
        self._handle = nil
    end
end

function CollectionWeaponSkinDetailPage:_WeaponRotationX(value)
    if not IsHD() then
        return
    end

    self._RotationVec.X = value * -1 * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end
function CollectionWeaponSkinDetailPage:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function CollectionWeaponSkinDetailPage:_WeaponScaleUp(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function CollectionWeaponSkinDetailPage:_WeaponScaleDown(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * -1 * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()

    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function CollectionWeaponSkinDetailPage:_IsShowedTipBox()
    if self._wtItemDetailView._CollectionWeaponSkinPanel == nil then
        return false
    else
        local ItemDetailWeaponSkinPanel = self._wtItemDetailView._CollectionWeaponSkinPanel
        if ItemDetailWeaponSkinPanel._wtIDParent and ItemDetailWeaponSkinPanel._wtIDParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtRateParent and ItemDetailWeaponSkinPanel._wtRateParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtWearParent and ItemDetailWeaponSkinPanel._wtWearParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtkillCntParent and ItemDetailWeaponSkinPanel._wtkillCntParent:IsVisible() then
            return true
        end
    end

    return false
end

function CollectionWeaponSkinDetailPage:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
    end
end

function CollectionWeaponSkinDetailPage:_HideDetailViewTips()
    self._ItemDetailEquipWeaponMysticalSkin = self._wtItemDetailView._CollectionWeaponSkinPanel
    if self._ItemDetailEquipWeaponMysticalSkin and self._ItemDetailEquipWeaponMysticalSkin.OnBtnTipsUnhoverd then
        self._ItemDetailEquipWeaponMysticalSkin:OnBtnTipsUnhoverd()
    end
end

-- END MODIFICATION

--end
-----------------------------------------------------------------------
return CollectionWeaponSkinDetailPage
