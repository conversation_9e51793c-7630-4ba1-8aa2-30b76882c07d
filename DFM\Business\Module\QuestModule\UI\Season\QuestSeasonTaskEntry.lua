---@class QuestSeasonTaskEntry : LuaUIBaseView

local QuestSeasonTaskEntry = ui("QuestSeasonTaskEntry")

function QuestSeasonTaskEntry:Ctor()   
    self._wtBtn = self:Wnd("DFButton_67", UIButton)
    self._wtBtn:Event("OnClicked", self._OnClickQuest, self)
    self._wtNameText = self:Wnd("DFTextBlock_QuestName", UITextBlock)

    self._questInfo = nil
    self._bIsSelected = false

    if IsHD() then
        self:Event("OnFocusReceivedEvent", self._OnFocusQuest, self)
    end
end

function QuestSeasonTaskEntry:OnShowBegin()
    self:_AddEventListener()
end

function QuestSeasonTaskEntry:OnHide()
    self:RemoveAllLuaEvent()
end

function QuestSeasonTaskEntry:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateSeasonLevel, self._OnQuestStateUpdate, self)
end

function QuestSeasonTaskEntry:RefreshItemWidget(questInfo, bIsSelect)    
    self._questInfo = questInfo
    self._wtNameText:SetText(questInfo.name)
    self:SetIsSelected(bIsSelect)
end

function QuestSeasonTaskEntry:_OnClickQuest()
    loginfo("JackieTest: QuestSeasonTaskEntry: On Click")
    Module.Quest.Config.evtQuestSeasonEntryClicked:Invoke(self._questInfo, self)
end

function QuestSeasonTaskEntry:_OnFocusQuest()
    loginfo("JackieTest: QuestSeasonTaskEntry: On Focus")
    Module.Quest.Config.evtQuestSeasonEntryClicked:Invoke(self._questInfo, self)
end

function QuestSeasonTaskEntry:_OnQuestStateUpdate(questId)
    if questId then 
        if self._questInfo and self._questInfo.id == questId then
            self:_UpdateTaskState()
        end 
    end
end

function QuestSeasonTaskEntry:SetIsSelected(bIsSelect)
    self._bIsSelected = bIsSelect
    self:_UpdateTaskState()    
end

function QuestSeasonTaskEntry:_UpdateTaskState()
    local type = 0
    local icon = 0
    if self._questInfo.state == QuestState.Locked then
        if self._bIsSelected then
            type = 5
        else
            type = 4
        end
        icon = 4
    elseif self._questInfo.state == QuestState.Rewarded then
        if self._bIsSelected then
            type = 3
        else
            type = 2
        end
        icon = 0
    else
        if self._bIsSelected then
            type = 1
        else
            type = 0
        end
        if self._questInfo.state == QuestState.Completed then
            icon = 1
        elseif self._questInfo.state == QuestState.Unread or  
                self._questInfo.state == QuestState.Unaccepted then
            icon = 2
        else
            icon = 3
        end
    end
    self:SetIcon(icon)
    self:SetType(type)
end

return QuestSeasonTaskEntry