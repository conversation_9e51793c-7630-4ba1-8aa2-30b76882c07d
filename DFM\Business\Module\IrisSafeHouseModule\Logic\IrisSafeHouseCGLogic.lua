----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------

local IrisSafeHouseCGLogic = {}

-- 判断是否需要播放CG，这里默认调用的时候账号已注册
function IrisSafeHouseCGLogic.ShouldPlayCG()
    local seasonData = Facade.TableManager:GetTable("Season") -- 读取赛季表
    if not seasonData then
        return false
    end

    local now = Facade.ClockManager:GetLocalTimestamp()

    local lastSeasonStartTime = 0
    for rowName, data in pairs(seasonData) do
        local seasonStartTime = data.StartTime
        local ret, startTimeStamp = TimeUtil.GetTimeStampFromString(seasonStartTime)

        if ret then
            -- 比较当前时间和赛季开始时间，看现在在哪个赛季，取最大值
            if startTimeStamp <= now and lastSeasonStartTime <= startTimeStamp then
                lastSeasonStartTime = startTimeStamp
            end
        end
    end

    local lastPlayedTime = Facade.ConfigManager:GetNumber("LastSeasonCGTime", 0)
    if lastPlayedTime <= lastSeasonStartTime then
        logwarning("IrisSafeHouseCGLogic.ShouldPlayCG true", "lastPlayedTime", lastPlayedTime, "lastSeasonStartTime", lastSeasonStartTime)
        return true -- 当上一次播放视频的时间小于当前赛季时间的时候，意味着需要再播一次赛季视频
    end
    logwarning("IrisSafeHouseCGLogic.ShouldPlayCG false, do not play!", "lastPlayedTime", lastPlayedTime, "lastSeasonStartTime", lastSeasonStartTime)
    return false
end

function IrisSafeHouseCGLogic.UpdatePlayedCGTime()
    local now = Facade.ClockManager:GetLocalTimestamp()
    Facade.ConfigManager:SetNumber("LastSeasonCGTime", now)
    logwarning("IrisSafeHouseCGLogic.UpdatePlayedCGTime, now:", now)
end

function IrisSafeHouseCGLogic.WarmupCGSubtitleFile()
    local MediaResTable = Facade.TableManager:GetTable("MediaResTable")
    if not MediaResTable then
        logerror("[WarmupCGSubtitleFile]Invalid Media Res Table!")
        return
    end

    local videoConfigData = MediaResTable["StartCG"]
    if not videoConfigData then
        logerror("[WarmupCGSubtitleFile] Do not find row named StartCG!")
        return
    end

    local videoConfig = videoConfigData.MediaResList:Get(0)
    local subtitlePath = FLuaHelper.SoftObjectPtrToString(videoConfig.SubtitleAsset)
    logerror("[WarmupCGSubtitleFile]", subtitlePath)
    Facade.ResourceManager:AsyncLoadResource(nil, subtitlePath)
end


return IrisSafeHouseCGLogic