----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



--请补充下面的配置信息
--name:功能模块的名称（自定义）
--subTries:功能模块下根据数据不同，需要构建的数据树（以数据为核心，每层处理数据的方法一致）所有子树作为同级，作为主树根节点下的子节点
    --rootName：该子树的根节点前缀名称
    --rootCheckFunc：子树根节点的检查方法
    --keyNames:与根具有相同前缀的子节点，与层级个数一致（以下模板中展示两级子节点）同一级子节点的key通过%s区分
    --checkFuncNames:检查方法名称，与层级个数一致，同一层的数据节点共享一个方法，参数为childNodes中的id
    --childNodes:子节点，从根节点下的子节点分布，按层级分布，每个节点的key作为该节点的id(此id可区分keyName以及作为checkFuncName的参数)
--dynamicEnums: 动态数据类型枚举，为功能模块中的动态数据节点进行分类，同一类型的数据节点以列表的形式存储在DataObserver文件中, 共同刷新与反注册
local CollectionManifest =
{
    name = "Collection",
    subTries =
    {
        [1] =
        {
            rootName = "CollectionUnlock",
            rootCheckFunc = "CheckCollectionUnlockList",
            keyNames = {"CollectionUnlock_Props_MainTab%s", "CollectionUnlock_Props_MainTab%s_SubTab%s"}, --这里的%s个数需要做校验
            checkFuncNames = {"CheckCollectionUnlockListByMainTab", "CheckCollectionUnlockListBySubTab"},
            childNodes =
            {
                [1] =
                {
                    childNodes =
                    {

                    }
                },
                [2] =
                {
                    childNodes =
                    {

                    }
                },
                [3] =
                {
                    childNodes =
                    {

                    }
                },
                [4] =
                {
                    childNodes =
                    {

                    }
                },
                [5] =
                {
                    childNodes =
                    {

                    }
                },
                [5] =
                {
                    childNodes =
                    {

                    }
                },
                [6] =
                {
                    childNodes =
                    {

                    }
                },
                [7] =
                {
                    childNodes =
                    {

                    }
                },
                [8] =
                {
                    childNodes =
                    {
                        [801] = {},
                        [802] = {}
                    }
                },
            }
        },
    },
    dynamicEnums = {"SystemMail","NoticeMail","MessageMail"}
}

return CollectionManifest