----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local EMouseCursor = import "EMouseCursor"

---@class CollectionRoomSwitchCabinet : LuaUIBaseView
local CollectionRoomSwitchCabinet = ui("CollectionRoomSwitchCabinet")

function CollectionRoomSwitchCabinet:Ctor()
    self._wtScrollView = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_108", self._OnGetCabinetCount, self._OnProcessItemWidget)
    self._wtButton = self:Wnd("DFMButton_216", DFButtonOnly)
    self._wtButton:Event("OnClicked", self._OnBgButton, self)
    self._wtButton:SetCursor(EMouseCursor.Default)

    self._currentCabinetType = EShowCabinetType.None
    self._currentCabinetId = 0
    ---@type CollectionRoomPanel
    self._parent = nil
end

function CollectionRoomSwitchCabinet:OnShowBegin()
    LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.SwitchDevice)
    if IsHD() then
        self:_EnableGamePadFeature()
    end
end

function CollectionRoomSwitchCabinet:OnHide()
    self:RemoveAllLuaEvent()
    if self._parent and self._parent.OnSwitchCabinetHide then
        self._parent:OnSwitchCabinetHide()
    end
    if IsHD() then
        self:_DisableGamePadFeature()
    end
end

function CollectionRoomSwitchCabinet:RefreshEntrancesList()
    self._wtScrollView:RefreshAllItems()
end

function CollectionRoomSwitchCabinet:SetParentAndCurrentCabinet(parent, cabinetType, cabinetId)
    self._parent = parent
    self._currentCabinetType = cabinetType
    self._currentCabinetId = cabinetId
end

function CollectionRoomSwitchCabinet:GetParentAndCurrentCabinet()
    return self._parent, self._currentCabinetType, self._currentCabinetId
end

function CollectionRoomSwitchCabinet:_OnGetCabinetCount()
    return 2 + CollectionRoomLogic.GetDIYCabinetNum()
end

---@param itemWidget CollectionRoomCabinetEntranceItem
function CollectionRoomSwitchCabinet:_OnProcessItemWidget(position, itemWidget)
    local cabinetType, cabinetId
    if position == 1 then
        cabinetType = EShowCabinetType.Display
        cabinetId = 1
    elseif position == 2 then
        cabinetType = EShowCabinetType.Special
        cabinetId = 1
    else
        cabinetType = EShowCabinetType.DIY
        cabinetId = position - 2
    end
    itemWidget:SetCabinetTypeAndIdAndParent(cabinetType, cabinetId, self)
end

function CollectionRoomSwitchCabinet:HideSwitchCabinetSide()
    Facade.UIManager:CloseUI(self)
end

function CollectionRoomSwitchCabinet:_OnBgButton()
   self:HideSwitchCabinetSide()
end

function CollectionRoomSwitchCabinet:OnNativeOnMouseButtonUp(inGeometry, inGestureEvent)
    self:HideSwitchCabinetSide()
end
--region 手柄适配
function CollectionRoomSwitchCabinet:_EnableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RegisterNavGroup()
    self:_BindDisplayAction()
    
end

function CollectionRoomSwitchCabinet:_DisableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RemoveNavGroup()
    self:_UnBindDisplayAction()

end

function CollectionRoomSwitchCabinet:_RegisterNavGroup()
    if not IsHD() then
        return
    end
    if not self._navGroup then
        local wtSidePanelRoot = self:Wnd("DFCanvasPanel_15", UIWidgetBase)
        self._navGroup = WidgetUtil.RegisterNavigationGroup(wtSidePanelRoot, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(wtSidePanelRoot)
            self._navGroup:AddNavWidgetToArray(self._wtScrollView)
        end
    end

    WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
end

function CollectionRoomSwitchCabinet:_RemoveNavGroup()
    if not IsHD() then
        return
    end
    WidgetUtil.RemoveNavigationGroup(self)
    self._navGroup = nil
end

function CollectionRoomSwitchCabinet:_BindDisplayAction()
    if not IsHD() then
        return
    end

    if not self._closePanelHandler then
        self._closePanelHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self.HideSwitchCabinetSide, self, EDisplayInputActionPriority.UI_Pop)
    end
    
end

function CollectionRoomSwitchCabinet:_UnBindDisplayAction()
	if not IsHD() then
        return
    end

    if self._closePanelHandler then
        self:RemoveInputActionBinding(self._closePanelHandler)
        self._closePanelHandler = nil
    end
end

return CollectionRoomSwitchCabinet