----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local WarehouseEquipSlotView = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView"
local InvSlotView            = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local CommonWidgetConfig     = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ECheckBoxState         = import "ECheckBoxState"

local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

--BEGIN MODIFICATION @ VIRTUOS : 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ETipsAttachMode = import "ETipsAttachMode"
--END MODIFICATION

---@class WareHouseContainerBox_HD : LuaUIBaseView
local WareHouseContainerBox_HD = ui("WareHouseContainerBox_HD")

local function log(...)
    loginfo("[WareHouseContainerBox_HD]", ...)
end

function WareHouseContainerBox_HD:Ctor()
    self.bindContainerSlot = nil
    self.bindEquipSlot = nil
    self.hoverHandle = nil
    self.fixHoverHandle = nil
    self.hoverDescription = InventoryConfig.Loc.HoverSafeSpaceDes

    self._wtCapacityText = self:Wnd("wtBoxCapacity", UITextBlock)
    self._wtEquipSlotView = self:Wnd("wtEquipSlotView", WarehouseEquipSlotView)
    self._wtFixedCheckBox = self:Wnd("wtFixedCheckBox", UICheckBox)
    self._wtFixedCheckBox:Event("OnCheckStateChanged", self.ChangeBoxState, self)
    self._wtBoxTitle = self:Wnd("wtBoxTitle", UITextBlock)
    self._wtBoxSubSlotView = self:Wnd("wtBoxSubSlotView", InvSlotView)
    self._wtSubSlotViewContent = self:Wnd("DFNamedSlot_Mask", UINamedSlot)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._TipsOnHover, self._TipsOnUnhovered)
    self._wtDFTipsAnchor_1 = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor_1", self._CheckOnHover, self._CheckOnUnhovered)

    self._wtTipsCheckBox = self:Wnd("wtTipsCheckBox", DFCommonCheckButtonOnly)
    -- self._wtTipsCheckBox:Event("OnUncheckedHovered", self._TipsOnHover, self)
    -- self._wtTipsCheckBox:Event("OnCheckedHovered", self._TipsOnHover, self)
    -- self._wtTipsCheckBox:Event("OnUncheckedUnhovered", self._TipsOnUnhovered, self)
    -- self._wtTipsCheckBox:Event("OnCheckedUnhovered", self._TipsOnUnhovered, self)

    -- self._wtFixedCheckBox:Event("OnUncheckedHovered", self._CheckOnHover, self)
    -- self._wtFixedCheckBox:Event("OnCheckedHovered", self._CheckOnHover, self)
    -- self._wtFixedCheckBox:Event("OnUncheckedUnhovered", self._CheckOnUnhovered, self)
    -- self._wtFixedCheckBox:Event("OnCheckedUnhovered", self._CheckOnUnhovered, self)
    
    --BEGIN MODIFICATION @ VIRTUOS : 记录TipAnchor初始的AttachMode设置
    if self._wtDFTipsAnchor.AttachModeHD then
        self._TipAnchorAttachMode = self._wtDFTipsAnchor.AttachModeHD
    end
	--END MODIFICATION
end

-----------------------------------------------------------------------
--region 生命周期

function WareHouseContainerBox_HD:OnOpen()

end

function WareHouseContainerBox_HD:OnClose()

end

function WareHouseContainerBox_HD:OnShow()
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(InventoryConfig.Events.evtFlipFlopSafeBoxPinState, self.FlipBoxState, self)
    self:InitContainerBox(ESlotType.SafeBoxContainer)
    self:SetSafeBoxState()
    self:_CreateRefreshTimer()
    self:RefreshView()
    self:SetCheckBoxState()
end

function WareHouseContainerBox_HD:OnHide()
    self:RemoveAllLuaEvent()
    self:ReleaseTimer()
end

--BEGIN MODIFICATION @ VIRTUOS : 新增InitExtraData数据，使得能在新创建UI时能修改CheckBox的IsFocusable
function WareHouseContainerBox_HD:OnInitExtraData(InitData)
    if InitData then
        if InitData.bIsFocusable == false then
            local wtTipsCheckBox = self._wtTipsCheckBox:Wnd("DFCheckBox_Icon", UICheckBox)
            if wtTipsCheckBox then
                wtTipsCheckBox:SetCppValue("IsFocusable", false)
            end
        end
    end
end
--END MODIFICATION
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private Function

function WareHouseContainerBox_HD:_OnItemMove(moveItemInfo)
    local oldLoc = moveItemInfo.OldLoc
    local newLoc = moveItemInfo.NewLoc
    if (oldLoc and oldLoc.ItemSlot == self.bindContainerSlot) or (newLoc and newLoc.ItemSlot == self.bindContainerSlot) 
    or (newLoc and newLoc.ItemSlot == self.bindEquipSlot) then
        self:_RefreshCapacity()
        self:SetSafeBoxState()
        self:ReleaseTimer()
        self:_CreateRefreshTimer()
    end
end

function WareHouseContainerBox_HD:_RefreshCapacity()
    if self.bindContainerSlot then
        local capacity = self.bindContainerSlot:GetUsedCapacity()
        local totalCapacity = self.bindContainerSlot:GetTotalCapacity()

        if totalCapacity > 0 then
            self._wtCapacityText:SelfHitTestInvisible()
            self._wtCapacityText:SetText(string.format(InventoryConfig.Loc.ContainerPanelCapacityText, capacity, totalCapacity))
        else
            self._wtCapacityText:Collapsed()
        end
    end
end

function WareHouseContainerBox_HD:_TipsOnHover()

    --BEGIN MODIFICATION @ VIRTUOS : 鼠标触发Hover时，先恢复初始的AttachMode配置
    if self._wtDFTipsAnchor and WidgetUtil.IsGamepad() == false then
        if self._TipAnchorAttachMode then
            self._wtDFTipsAnchor:SetCppValue("AttachModeHD", self._TipAnchorAttachMode)
        else
            self._wtDFTipsAnchor:SetCppValue("AttachModeHD", ETipsAttachMode.Mouse)
        end
    end
	--END MODIFICATION

    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = self.hoverDescription}
    }}, self._wtDFTipsAnchor)
end

function WareHouseContainerBox_HD:_TipsOnUnhovered()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 
function WareHouseContainerBox_HD:OnHoverTipByGamepad()
    -- 使用手柄时需要将TipAnchor的AttachMode改成 Static
    if WidgetUtil.IsGamepad() then
        if self._wtDFTipsAnchor then
            self._wtDFTipsAnchor:SetCppValue("AttachModeHD", ETipsAttachMode.Static)
        end
    end

    self:_TipsOnHover()
end

function WareHouseContainerBox_HD:OnUnHoverTipByGamepad()
    self:_TipsOnUnhovered()
end
--END MODIFICATION

function WareHouseContainerBox_HD:_CheckOnHover()
    if self.fixHoverHandle and self._wtDFTipsAnchor_1 then
        Module.CommonTips:RemoveAssembledTips(self.fixHoverHandle, self._wtDFTipsAnchor_1)
        self.fixHoverHandle = nil
    end

    local tips
    local fixCheckState = self._wtFixedCheckBox:GetCheckedState()
    if fixCheckState == ECheckBoxState.Checked then
        tips = InventoryConfig.Loc.UnFixSafeBoxTips
    else
        tips = InventoryConfig.Loc.FixSafeBoxTips
    end
    self.fixHoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = tips}
    }}, self._wtDFTipsAnchor_1)
end

function WareHouseContainerBox_HD:_CheckOnUnhovered()
    if self.fixHoverHandle and self._wtDFTipsAnchor_1 then
        Module.CommonTips:RemoveAssembledTips(self.fixHoverHandle, self._wtDFTipsAnchor_1)
        self.fixHoverHandle = nil
    end
end

function WareHouseContainerBox_HD:_CreateRefreshTimer()
    local equipItem = self.bindEquipSlot and self.bindEquipSlot:GetEquipItem()
    if not equipItem then
        return
    end
    local equipItemFeature = equipItem:GetFeature(EFeatureType.Equipment)
    -- 永久和已过期或者无权限的道具不需要定时刷新
    if not equipItemFeature:GetExpiredStatus() and equipItemFeature._expireTimes and equipItemFeature._expireTimes > 0 then
        self._timer = Timer:NewIns(1, 0)
        self._timer:AddListener(self._UpdateStatus, self)
        self._timer:Start()
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public Function

function WareHouseContainerBox_HD:InitContainerBox(containerSlotType)
    self.bindContainerSlot = Server.InventoryServer:GetSlot(containerSlotType)
    assert(self.bindContainerSlot:IsContainerSlot())
    self._wtBoxSubSlotView:InitServerSlot(containerSlotType)

    local equipSlotType = self.bindContainerSlot:GetContainerType()
    self.bindEquipSlot = Server.InventoryServer:GetSlot(equipSlotType)
    self._wtEquipSlotView:InitServerSlot(equipSlotType, ESlotGroup.Player)
end

function WareHouseContainerBox_HD:RefreshView()
    self._wtEquipSlotView:RefreshView()
    self._wtBoxSubSlotView:RefreshView()
    self:_RefreshCapacity()
end

function WareHouseContainerBox_HD:RefreshCapacity()
    self:_RefreshCapacity()
end

function WareHouseContainerBox_HD:GetEquipSlotView()
    return self._wtEquipSlotView
end

function WareHouseContainerBox_HD:GetContainerSlotView()
    return self._wtBoxSubSlotView
end

function WareHouseContainerBox_HD:SetBoxCapacity(curNum, maxNum)
    self._wtBoxCapacity:SetText(string.format("%d/%d", curNum, maxNum))
end

function WareHouseContainerBox_HD:SetBoxTitle(title)
    self._wtBoxTitle:SetText(title)
end

function WareHouseContainerBox_HD:SetEquipSlotViewType(type)
    self._wtEquipSlotView:InitSlotType(type)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 安全箱商业化相关

function WareHouseContainerBox_HD:SetSafeBoxState()
    -- 获取当前槽位的装备
    local equipItem = self.bindEquipSlot:GetEquipItem()
    local equipmentFeature = equipItem and equipItem:GetFeature()
    -- local bExpire = equipmentFeature and equipmentFeature._expireTimes and equipmentFeature:GetExpiredStatus()
    local bExpire = equipmentFeature and not equipmentFeature:PermissionCanUse()
    self:_ShowExpiredState(bExpire)
    self:_ShowExpiredGridMask(bExpire, equipItem)
end

function WareHouseContainerBox_HD:_ShowExpiredState(bExpire)
    local wtMainItem = self._wtEquipSlotView:GetMainItemView()
    if not wtMainItem then
        return
    end
    if bExpire then
        wtMainItem:FindOrAdd(EComp.ExpireMask, UIName2ID.IVContainerExpiredComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
        wtMainItem:EnableComponent(EComp.ExpireMask, true)
    else
        wtMainItem:EnableComponent(EComp.ExpireMask, false)

    end
end

function WareHouseContainerBox_HD:_ShowExpiredGridMask(bExpire, item)
    if bExpire then
        self._wtSubSlotViewContent:Visible()
        if not self._safeBoxMask then
            local weakIns = Facade.UIManager:AddSubUI(self, UIName2ID.ContainerMask, self._wtSubSlotViewContent, nil, item)
            self._safeBoxMask = getfromweak(weakIns)
            self._safeBoxMask:BindLeftBtn(SafeCallBack(function()
                    local curItem = self.bindEquipSlot and self.bindEquipSlot:GetEquipItem()
                    if not curItem then
                        loginfo("[WareHouseContainerBox_HD]: SlotItem is nil")
                        return
                    end
                    Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxAndKeyChainMain, nil, nil, curItem.InSlot.SlotType, curItem)
                end))
            self._safeBoxMask:BindRightBtn(SafeCallBack(function()
                    -- 转移全部按钮绑定
                    ItemOperaTool.TransferSlotItems(ESlotType.SafeBoxContainer)
                end))
        end
        if ItemOperaTool.bInSettlement then
            self._safeBoxMask:HideLeftBtn()
        end
        self._wtBoxSubSlotView:ShowGreyMask(true)
        self._safeBoxMask:Visible()
        self._safeBoxMask:InitWidget(item)
    else
        if self._safeBoxMask then
            self._wtSubSlotViewContent:Collapsed()
        end
        self._wtBoxSubSlotView:ShowGreyMask(false)
    end
end

function WareHouseContainerBox_HD:_UpdateStatus()
    local equipItem = self.bindEquipSlot and self.bindEquipSlot:GetEquipItem()
    local equipItemFeature = equipItem and equipItem:GetFeature(EFeatureType.Equipment)
    local equipSlotView = self._wtEquipSlotView:GetMainItemView()
    if equipItemFeature and not equipItemFeature:GetExpiredStatus() and equipItemFeature._expireTimes and equipItemFeature._expireTimes > 0 then
        -- local itemview = self._wtEquipSlotView:GetMainItemView():RefreshView()
        local cutDownComponent = equipSlotView and equipSlotView:GetComponent(EComp.BottomRightIconText)
        if cutDownComponent then
            cutDownComponent:RefreshComponent()
        end
    else
        self:SetSafeBoxState()
        if equipSlotView then
            self._wtEquipSlotView:GetMainItemView():RefreshView()
        end
        self:ReleaseTimer()
    end
end

function WareHouseContainerBox_HD:ReleaseTimer()
    if self._timer then
        self._timer:Release()
        self._timer = nil
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 安全箱固定按钮相关

function WareHouseContainerBox_HD:SetCheckBoxState()
    local bChecked = Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true)
    self._wtFixedCheckBox:SetIsChecked(bChecked, false)
end

function WareHouseContainerBox_HD:ChangeBoxState(bChecked)
    if bChecked then
        Facade.ConfigManager:SetUserBoolean("SafeBoxbFixed", true)
        Module.Inventory.Config.Events.evtChangeSafeBoxState:Invoke(true)
    else
        Facade.ConfigManager:SetUserBoolean("SafeBoxbFixed", false)
        Module.Inventory.Config.Events.evtChangeSafeBoxState:Invoke(false)
    end
end

function WareHouseContainerBox_HD:FlipBoxState()
    local bChecked = Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true)
    if bChecked then
        Facade.ConfigManager:SetUserBoolean("SafeBoxbFixed", false)
        Module.Inventory.Config.Events.evtChangeSafeBoxState:Invoke(false)
    else
        Facade.ConfigManager:SetUserBoolean("SafeBoxbFixed", true)
        Module.Inventory.Config.Events.evtChangeSafeBoxState:Invoke(true)
    end
end

--endregion
-----------------------------------------------------------------------

return WareHouseContainerBox_HD