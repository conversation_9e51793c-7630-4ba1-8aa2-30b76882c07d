----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitField : FieldBase
local RecruitField = class("RecruitField", require"DFM.YxFramework.Managers.Module.FieldBase")

--- Field用于存储加工、排序过的二级数据、缓存数据、或者临时数据
function RecruitField:Ctor()
    self._filterData = {}
    self._lastFilterData = {}
    self._refreshPendingTime = 0
    self._refreshTimerHandle = nil
    self._recruitmentLifeTime = 0
    self._recruitmentTimerHandle = nil
    self._SOLMaps = nil
    self._SOLTargets = nil
    self._MPMaps = nil
    self._bIsRecruiting = false
    self._bHasFilteredRecruitment = false
    self._lastRecommendListInfo = nil
    self._lastMiniChatIndex = nil
    self._recruitmentList = nil
    self._miniChatRecruitmentList = nil
    self._appendedMiniChatRecruitmentList = nil
    self._teamIdsRequireMic = nil
    self._bFirstTimeOpenRecruitmentPanel = true

    -- azhengzheng:记录最后一次队伍ID对应的组队码和过期时间
    self._lastTeamID = 0
    self._lastTeamCode = 0
    self._lastTeamCodeExpireTime = 0
end

--- 当数据改变需要发事件/额外处理（如排序、筛选）时，在Field里统一处理，以免在外面写重复冗余代码
function RecruitField:SetIsRecruitVar(bRecruitVar)
    --- self._bRecruitVar = bRecruitVar
    --- Module.Recruit.Config.evtRecruitVarChanged:Invoke(bRecruitVar)
end

function RecruitField:GetIsRecruitVar()
    --- return self._bRecruitVar
end

--- 当模块需要拿住ui/uiHandle时，存入Field，并在Module阶段改变时记得清除
function RecruitField:SetMainPanel(mainPanel)
    self._mainPanel = mainPanel
end

function RecruitField:GetMainPanel()
    return self._mainPanel
end

--- 对应每个OnGameFlowChangeEnter
--- 调用重建接口
---@overload fun(FieldBase, OnDeclareField)
function RecruitField:OnDeclareField()
end

--- 对应每个OnGameFlowChangeLeave
--- 调用清除接口
---@overload fun(FieldBase, OnClearField)
function RecruitField:OnClearField()
end

--- 虚拟机销毁时
---@overload fun(FieldBase, OnDestroyField)
function RecruitField:OnDestroyField()
end






function RecruitField:SetFilterData(key, data)
    self._filterData[key] = data
end

function RecruitField:GetFilterData(key)
    for keyName, data in pairs(self._filterData) do
        if keyName == key then
            return data
        end
    end
    return nil
end

function RecruitField:SetLastFilterData(key, data)
    self._lastFilterData[key] = data
end

function RecruitField:GetLastFilterData(key)
    for keyName, data in pairs(self._lastFilterData) do
        if keyName == key then
            return data
        end
    end
    return nil
end




function RecruitField:GetRefreshPendingTime()
    return self._refreshPendingTime or 0
end

function RecruitField:SetRefreshPendingTime(seconds)
    self._refreshPendingTime = seconds or 0
end

function RecruitField:GetRefreshTimer()
    return self._refreshTimerHandle
end

function RecruitField:SetRefreshTimer(handle)
    self._refreshTimerHandle = handle
end

function RecruitField:GetRecruitmentLifeTime()
    return self._recruitmentLifeTime or 0
end

function RecruitField:SetRecruitmentLifeTime(seconds)
    self._recruitmentLifeTime = seconds or 0
end

function RecruitField:GetRecruitmentTimer()
    return self._recruitmentTimerHandle
end

function RecruitField:SetRecruitmentTimer(handle)
    self._recruitmentTimerHandle = handle
end

function RecruitField:GetSOLMaps()
    return self._SOLMaps
end

function RecruitField:SetSOLMaps(SOLMaps)
    self._SOLMaps = SOLMaps
end

function RecruitField:GetSOLTargets()
    return self._SOLTargets
end

function RecruitField:SetSOLTargets(SOLTargets)
    self._SOLTargets = SOLTargets
end

function RecruitField:GetMPMaps()
    return self._MPMaps
end

function RecruitField:SetMPMaps(MPMaps)
    self._MPMaps = MPMaps
end

function RecruitField:SetMPLabelGroups(MPLabels)
    self._MPLabels = MPLabels
end

function RecruitField:GetMPLabelGroups()
    return self._MPLabels
end

function RecruitField:SetSOLLabelGroups(SOLLabels)
    self._SOLLabels = SOLLabels
end

function RecruitField:GetSOLLabelGroups()
    return self._SOLLabels
end


function RecruitField:SetIsRecruiting(IsRecruiting)
    self._bIsRecruiting = IsRecruiting or false
end

function RecruitField:GetIsRecruiting()
    return self._bIsRecruiting or false
end

function RecruitField:SetHasFilteredRecruitment(bHasFiltered)
    self._bHasFilteredRecruitment = bHasFiltered or false
end

function RecruitField:GetHasFilteredRecruitment()
    return self._bHasFilteredRecruitment or false
end

function RecruitField:SetLastRecommendListInfo(listInfo)
    self._lastRecommendListInfo = listInfo
end

function RecruitField:GetLastRecommendListInfo()
    return self._lastRecommendListInfo
end

function RecruitField:SetLastMiniChatIndex(index)
    self._lastMiniChatIndex = index
end

function RecruitField:GetLastMiniChatIndex()
    return self._lastMiniChatIndex
end

function RecruitField:SetRecruitmentList(recruitmentList)
    self._recruitmentList = recruitmentList
end

function RecruitField:GetRecruitmentList()
    return self._recruitmentList
end

function RecruitField:ClearRecruitmentList()
    self._recruitmentList = {}
end

function RecruitField:SetMiniChatRecruitmentList(recruitmentList)
    self._miniChatRecruitmentList = recruitmentList
end

function RecruitField:GetMiniChatRecruitmentList()
    return self._miniChatRecruitmentList
end

function RecruitField:SetAppendedMiniChatRecruitmentList(recruitmentList)
    self._appendedMiniChatRecruitmentList = recruitmentList
end

function RecruitField:GetAppendedMiniChatRecruitmentList()
    return self._appendedMiniChatRecruitmentList
end

function RecruitField:ClearMiniChatRecruitmentList()
    self._miniChatRecruitmentList = {}
    self._appendedMiniChatRecruitmentList = {}
end


function RecruitField:AddTeamIdRequireMic(teamId)
    self._teamIdsRequireMic = setdefault(self._teamIdsRequireMic, {})
    self._teamIdsRequireMic[teamId] = true
end

function RecruitField:CheckTeamIdRequireMic()
    self._teamIdsRequireMic = setdefault(self._teamIdsRequireMic, {})
    return self._teamIdsRequireMic[teamId] == true
end

function RecruitField:ClearTeamIdRequireMic()
    self._teamIdsRequireMic = nil
end


function RecruitField:SetFirstTimeOpenRecruitmentPanel(bfirstTime)
    self._bFirstTimeOpenRecruitmentPanel = bfirstTime or false
end

function RecruitField:GetFirstTimeOpenRecruitmentPanel()
    return self._bFirstTimeOpenRecruitmentPanel or false
end

function RecruitField:SetLastTeamCodeInfo(teamID, teamCode, teamCodeExpireTime)
    self._lastTeamID = teamID
    self._lastTeamCode = teamCode
    self._lastTeamCodeExpireTime = teamCodeExpireTime
end

function RecruitField:GetLastTeamCodeInfo()
    return self._lastTeamID, self._lastTeamCode, self._lastTeamCodeExpireTime
end

return RecruitField
