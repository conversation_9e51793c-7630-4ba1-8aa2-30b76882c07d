----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CustomLayoutMain
local CustomLayoutMainBase = require "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutMainBase"
local CustomLayoutMain = class("CustomLayoutMain",CustomLayoutMainBase)
local UClientSettingDelagate = import"ClientSettingDelagate"
local UMobileCustomLayoutDataCenter = import "MobileCustomLayoutDataCenter"
local EGameRule2LayoutName = Module.SystemSetting.Config.EGameRule2LayoutName

function CustomLayoutMain:Ctor()
    --
    self._wtBtnRevoke = self:Wnd("Revoke", UIWidgetBase):Wnd("DFButton_24", UIButton)
    self._wtBtnRevoke:Event("OnClicked", self._OnRevokeBtnClick, self)

    self._wtBtnRestore = self:Wnd("Restore", UIWidgetBase):Wnd("DFButton_24", UIButton)
    self._wtBtnRestore:Event("OnClicked", self._OnRestoreBtnClick, self)

    self._wtBtnResetLayout = self:Wnd("ResetLayout", DFCommonButtonOnly)
    self._wtBtnResetLayout:Event("OnClicked", self._OnResetLayoutBtnClick, self)

    self._wtBtnStore = self:Wnd("Store", DFCommonButtonOnly)
    self._wtBtnStore:Event("OnClicked", self._OnStoreBtnClick, self)

    self._wtBtnManager = self:Wnd("Manager", DFCommonButtonOnly)
    self._wtBtnManager:Event("OnClicked", self._OnManagerBtnClick, self)
    self._wtTxtManager = self:Wnd("Manager", UIWidgetBase):Wnd("RichTextBlock_Common", UITextBlock)

    self._wtBtnQuit = self:Wnd("Quit", UIButton)
    self._wtBtnQuit:Event("OnClicked", self._OnQuitBtnClick, self)

    self._wtBtnBG = self:Wnd("ButtonBG", UIButton)
    self._wtBtnBG:Event("OnClicked", self._OnBGBtnClick, self)

    self._wtShareImage = self:Wnd("wtShareImage", UIButton)
    self._wtShare = self:Wnd("Share", UIButton)
    self._wtShare:Event("OnClicked", self.ShareCustomLayout, self)
    
    self._wtSync = self:Wnd("Sync", UIButton)
    self._wtSync:Event("OnClicked", self._OnSyncClick, self)

    --挂载的按键属性调整菜单
    self._wtAttachPanel = self:Wnd("WBP_Custom_Edit",UIWidgetBase)

    --基础布局蓝图
    self._wtNamedSlotLayout = self:Wnd("NamedSlot_76",UIWidgetBase)

    --顶部菜单
    self._wtTopPanel = self:Wnd("CanvasPanel_3",UIWidgetBase)
    self._wtCheckBoxTop = self:Wnd("CheckBox_219",UICheckBox)
    self._wtCheckBoxTop:SetCallback(self._TopPanelChange, self)
    self._wtTxtLayoutName = self:Wnd("RichTextBlock",UITextBlock)
    self._wtTxtPlayerName = self:Wnd("RichTextBlock_101",UITextBlock)


    --当前布局名
    self._curLayoutName = "SOLLayout"

    self._isSharedLayout =false

    self._bIsChange = false

    self._custLayoutDCCDO = UMobileCustomLayoutDataCenter.Get(GetGameInstance())

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)

    self._LayoutImagePathTbl = {
        ["SOLLayout"] = "PaperSprite'/Game/UI/UIAtlas/System/Custom/BakedSprite/Custom_Icon_0002.Custom_Icon_0002'",
        ["BattleLayout"] = "PaperSprite'/Game/UI/UIAtlas/System/Custom/BakedSprite/Custom_Icon_0001.Custom_Icon_0001'",
    }
end

function CustomLayoutMain:OnOpen()
	self._curLayoutName = EGameRule2LayoutName[Module.SystemSetting.Field:GetGameRule()]
    self._custLayoutDCCDO:UpdateLayoutNameIncustom(self._curLayoutName)
    self._wtBtnManager:Visible()
    self._wtShare:Visible()
    self._wtShareImage:Visible()
    self._wtBtnStore:Collapsed()
    self:AddLuaEvent(Server.SystemSettingServer.Events.evtRecvSearchShareLayout, self._OnRecvSearchShareLayout, self)
    self:AddLuaEvent(Server.SystemSettingServer.Event.evtLoadSchemeData, self._OnRecvSchemeShareLayout, self)
    UClientSettingDelagate.Get(self).OnCusmtomButtonChange:Add(CreateCPlusCallBack(self._OnCusmtomButtonChange, self))
    Module.CommonBar:SetTopBarVisible(false)

    self:FetchLayoutPanel()
    self:RefreshVisibleLayoutName()

    self._wtSync:AsyncSetImageIconPathAllState(self._LayoutImagePathTbl[self._curLayoutName], true)
end

--根据当前模式，load不同的布局
function CustomLayoutMain:FetchLayoutPanel()
    --[[
    self._wtNamedSlotLayout:ClearChildren()
    local function onContentUICreated(uiIns)
        self._wtNamedSlotLayout:AddChild(uiIns)
        self._wtCustomLayout = uiIns
        self._wtCustomBaseLayout = uiIns:Wnd("WBP_CustomBaseTemplate",UIWidgetBase)
        self._wtCustomLayout:SetAttachPanel(self._wtAttachPanel)
    end
    ]]
    Facade.UIManager:RemoveSubUIByParent(self, self._wtNamedSlotLayout)
    if self._curLayoutName == "SOLLayout" then
        --Facade.UIManager:AsyncCreateSubUI(UIName2ID.CustomLayoutSOL, onContentUICreated, nil)
        local weakUIIns ,instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CustomLayoutSOL, self._wtNamedSlotLayout)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            self._wtCustomLayout = newCell
            self._wtCustomLayout:SetAttachPanel(self._wtAttachPanel)
            self._wtCustomBaseLayout = self._wtCustomLayout:Wnd("WBP_CustomBaseTemplate",UIWidgetBase)
        end
    else
        --Facade.UIManager:AsyncCreateSubUI(UIName2ID.CustomLayoutBattle, onContentUICreated, nil)
        local weakUIIns ,instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CustomLayoutBattle, self._wtNamedSlotLayout)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            self._wtCustomLayout = newCell
            self._wtCustomLayout:SetAttachPanel(self._wtAttachPanel)
            self._wtCustomBaseLayout = self._wtCustomLayout:Wnd("WBP_CustomBaseTemplate",UIWidgetBase)
        end
    end
end

function CustomLayoutMain:_OnResetLayoutBtnClick()
    self._wtCustomBaseLayout:OnAttachNewDragBtn(nil)
    local function f()
        self._wtCustomLayout:ResetConfig(self._curLayoutName)
    end
    --展开的武器栏合上后，下一帧它的数据Geometry才是正确的数据，所以延迟
    Timer.DelayCall(0.1, f, self)
    self._wtBtnStore:Visible()
    self._wtBtnManager:Collapsed()
    self._wtShare:HitTestInvisible()
    self._wtShareImage:Collapsed()
    self._bIsChange = true
end

function CustomLayoutMain:_OnStoreBtnClick()
    self._wtAttachPanel:AttachDragBtnPress(nil)
    self._wtCustomBaseLayout:OnAttachNewDragBtn(nil)
    local function f()
        self._wtCustomLayout:SaveConfig(self._curLayoutName)
        Server.SystemSettingServer:SendCustomLayoutInfo(self._curLayoutName)
        local rotationMode = self._wtCustomLayout:GetFunctionBtnRotationMode()
        Module.SystemSetting.Config.Event.evtFunctionBtnRotationModeChange:Invoke(rotationMode)
        self._bIsChange = false
    end
    --展开的武器栏合上后，下一帧它的数据Geometry才是正确的数据，所以延迟
    Timer.DelayCall(0.1, f, self)

    self._wtBtnStore:Collapsed()
    self._wtBtnManager:Visible()
    self._wtShare:Visible()
    self._wtShareImage:Visible()
end

function CustomLayoutMain:ChangeLayout(layoutName)
    self._wtCustomLayout:ClearCustomLayoutStack()
    Server.SystemSettingServer:SendCurrentUsingLayout("BaseLayout", layoutName)
	self._wtCustomLayout:ApplyConfig()
end

function CustomLayoutMain:OnClose()
    loginfo('Close')
end

function CustomLayoutMain:OnShowBegin()
    self:BindBackAction(self._OnBackBtnClick, self)
end

function CustomLayoutMain:OnHideBegin()
    self:UnBindBackAction()
end

function CustomLayoutMain:_OnBackBtnClick()
    Facade.UIManager:CloseUI(self)
end


return CustomLayoutMain