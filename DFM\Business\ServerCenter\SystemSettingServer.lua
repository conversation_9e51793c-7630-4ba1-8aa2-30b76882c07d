----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local UDFMMobileCustomLayoutBPLibrary = import "DFMMobileCustomLayoutBPLibrary"
local UClientSensitivitySetting = import "ClientSensitivitySetting"
local UClientBaseSetting = import "ClientBaseSetting"
local UClientControlSetting = import "ClientControlSetting"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local UMobileCustomLayoutDataCenter = import "MobileCustomLayoutDataCenter"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local ESensitivityMode = import "ESensitivityMode"
local Json = JsonFactory.createJson()

---@class SystemSettingServer : ServerBase
local SystemSettingServer = class("SystemSettingServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function SystemSettingServer:Ctor()
    self._CreditScore = 1200
    self._TagUgc = 0
    self._clientSensitivitySetting = UClientSensitivitySetting.Get(GetWorld())
    self._clientBaseSetting = UClientBaseSetting.Get(GetWorld())
    self._clientControlSetting = UClientControlSetting.Get(GetWorld())
    self.Events = {
        evtRecvSearchShareLayout = LuaEvent:NewIns("SystemSettingServer.evtRecvSearchShareLayout"),
        evtRecvShareLayoutGUID = LuaEvent:NewIns("SystemSettingServer.evtRecvShareLayoutGUID"),
        evtRecvShareSettingGUID = LuaEvent:NewIns("SystemSettingServer.evtRecvShareSettingGUID"),
        evtRecvSearchShareSensitity = LuaEvent:NewIns("SystemSettingServer.evtRecvSearchShareSensitity"),
        evtPlayerCloudSetting = LuaEvent:NewIns("SystemSettingServer.evtPlayerCloudSetting"),
        evtRecvSearchShareBase = LuaEvent:NewIns("SystemSettingServer.evtRecvSearchShareBase"),
        evtInitPlayerBaseSetting = LuaEvent:NewIns("SystemSettingServer.evtInitPlayerBaseSetting"),
        evtInitPlayerSensititySetting = LuaEvent:NewIns("SystemSettingServer.evtInitPlayerSensititySetting"),
        evtInitPlayerPrivacySetting = LuaEvent:NewIns("SystemSettingServer.evtInitPlayerPrivacySetting"),
        evtInitPlayerPrivacyDataList = LuaEvent:NewIns("SystemSettingServer.evtInitPlayerPrivacyDataList"),
        evtLoadSchemeData = LuaEvent:NewIns("SystemSettingServer.evtLoadSchemeData"),
    }

    self._hasShared = nil

    if DFHD_LUA ~= 1 then
        self.CustomLayoutType = "CustomLayout"
    else
        self.CustomLayoutType = "CustomLayoutHD"
    end
    self.CustomManage = "CustomManage"

    self.BaseSettingName = {
        "bIsAimAssistOpen",
        "bSceneFOVCalcOpenCameraFOV",
        "bIsInventoryAutoOrganize",
        "ShotGunFireMode",
        "SRFireMode",
        "AbilityItemFireMode",
        "bSRInstantFire",
        "ScopeOpenMode",
        "QuickScopeOpen",
        "QuickScopeopen_MP",
        "QuickScopeOpen_Overseas",
        "QuickScopeOpenAbilityItem_Overseas",
        "QuickScopeOpenFixedWeapon_Overseas",
        "QuickScopeOpen_MP_Overseas",
        "QuickScopeOpenAbilityItem_MP_Overseas",
        "QuickScopeOpenFixedWeapon_MP_Overseas",
        "bCanFireOnQuickScopeOpen",
        "QuickScopeOpenMap",
        "QuickScopeOpenAbilityItem",
        "bEnableReloadOnAiming",
        "bEnableAutoFire",
        "FPPViewRange",
        "TPPViewRange",
        "bCanSwitchXPP",
        "FireBreakReload",
        "bRunBreakReload",
        "bAutoHoldBreathOnZoom",
        "QuickScopeOpenFixedWeapon",
    }

    self.ControlSettingName = {
        "LeftJoyStickMode",
        "RightFireMode",
        "FunctionBtnRotationMode",
        "bAutoUpRun",
        "bShowDamageHitHud",
        "bCanLeanPeek",
        "PeekOpenMode",
        "bPeekAutoScopeOpen",
        "SilentWalkInputMode",
        "SprintTriggerSensitivity",
        "SensitivityChangeMode",
        "bVaultTriggerMode",
        "GyroScopeOpenMode",
        "bGyroScopeReverseX",
        "bGyroScopeReverseY",
        "bFireBtnRotated",
        "bAimBtnRotated",
        "bPeekBtnRotated",
        "bCrouchBtnRotated",
        "SprintTriggerSensitivityMP",
        "bAutoUpRunMP",
    }
end

function SystemSettingServer:OnInitServer()
    local gameInst = GetGameInstance()
    self._custLayoutDCCDO = UMobileCustomLayoutDataCenter.Get(gameInst)
    UDFMGameHudDelegates.Get(gameInst).OnGameSendSearchGUID:Add(CreateCPlusCallBack(self.SendSearchGUID, self))
    UDFMGameHudDelegates.Get(gameInst).OnGameShareCustomLayoutInfo:Add(CreateCPlusCallBack(self.ShareCustomLayoutInfo, self))
end

function SystemSettingServer:OnDestroyServer()
end

function SystemSettingServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse then
        self:_OnGetCreditScore()
    end
end

function SystemSettingServer:_OnGetCreditScore()
    if _WITH_EDITOR == 1 then
        local OnCSTSSQueryPlayerCreditScoreRes = function (res)
            if res.result == 0 then
                self._CreditScore = res.score
                self._TagUgc = tag_ugc
            end
        end
        local Req = pb.CSTSSQueryPlayerCreditScoreReq:New()
        Req:Request(OnCSTSSQueryPlayerCreditScoreRes)
    end
end

function SystemSettingServer:GetCreditScore()
    return self._CreditScore
end

function SystemSettingServer:GetTagUgc()
    return self._TagUgc
end

--------------------------------------------------------
---自定义布局
--------------------------------------------------------
--保存当前布局
function SystemSettingServer:SendCustomLayoutInfo(LayoutName)
    local extraData = {}
    extraData["NickName"] = Server.SDKInfoServer:GetUserName()
    local newLayoutInfo = UDFMMobileCustomLayoutBPLibrary.SerializeCustomLayout(LayoutName, extraData)

    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SaveCustomLayoutComplete, 2)
        end
    end
    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = tostring(LayoutName), type = self.CustomLayoutType, value = tostring(newLayoutInfo)}
    Req:Request(onPresetPutKeyValueRes)
end

--切换布局
function SystemSettingServer:SendCurrentUsingLayout(Key, Value, bShowTip)
    bShowTip = setdefault(bShowTip,true)
    if not isvalid(self._custLayoutDCCDO) then
        local gameInst = GetGameInstance()
        self._custLayoutDCCDO = UMobileCustomLayoutDataCenter.Get(gameInst)
    end
    self._custLayoutDCCDO:DeserializeString(Key, Value)

    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
            local LayoutName = ServerTipCode.LayoutNameTable[Value]
            if bShowTip and LayoutName then
                local Tip = string.format(ServerTipCode.SwitchCustomLayoutComplete, tostring(LayoutName))
                LuaGlobalEvents.evtServerShowTip:Invoke(Tip, 2)
            end
        end
    end
    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = tostring(Key), type = self.CustomLayoutType, value = tostring(Value)}
    Req:Request(onPresetPutKeyValueRes,{bEnableHighFrequency = true})
end

function SystemSettingServer:SendSearchGUID(GUID)
    local onPresetGetSharedValueRes = function(res)
        if res.result == 0 then
            self.Events.evtRecvSearchShareLayout:Invoke(res.kv.key, res.kv.value)
        end
    end
    local Req = pb.CSSettingGetSharedValueReq:New()
    Req.share_code = tostring(GUID)
    Req:Request(onPresetGetSharedValueRes)
end

function SystemSettingServer:ShareCustomLayoutInfo(LayoutName)
    if not self._hasShared then
        local onPresetPutKeyValueRes = function(res)
            self._hasShared = true
            self:DoPresetShareKeyValueReq(LayoutName)
        end
        local Req = pb.CSSettingPutKeyValueReq:New()
        Req.kv = {key = "HasShared", type = self.CustomLayoutType, value = "1"}
        Req:Request(onPresetPutKeyValueRes)
    else
        self:DoPresetShareKeyValueReq(LayoutName)
    end
end

function SystemSettingServer:DoPresetShareKeyValueReq(LayoutName, title)
    local onPresetShareKeyValueRes = function(res)
        if res.result == 0 then
            self.Events.evtRecvShareLayoutGUID:Invoke(res.share_code,LayoutName, title)
        end
    end
    local Req = pb.CSSettingGenSharCodeReq:New()
    Req.key = tostring(LayoutName)
    Req:Request(onPresetShareKeyValueRes)
end

function SystemSettingServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function SystemSettingServer:FetchServerData()
    if DFHD_LUA == 0 then
        self:FetchCustomLayoutInfo()
    end
end

function SystemSettingServer:FetchCustomLayoutInfo()
    --先重置到默认布局，再加载服务器布局

    loginfo("SystemSettingServer:FetchCustomLayoutInfo() Begin")
    local onCSPresetGetValuesByTypeRes = function(Res)
        if Res.result == 0 then
            local recvNames = {}
            self._custLayoutDCCDO:ResetAllLayoutToDefault()
            for _, info in ipairs(Res.kv_array) do
                if info.key == "HasShared" then
                    self._hasShared = true
                else
                    UDFMMobileCustomLayoutBPLibrary.ReceiveCustomLayoutInfo(info.key, info.value)
                    table.insert(recvNames, info.key)
                end
            end
            loginfo("SystemSettingServer:FetchCustomLayoutInfo() end")
            -- local allDefaultLayoutNames = UDFMMobileCustomLayoutBPLibrary.GetAllLayoutNames()
            -- for i, name in pairs(allDefaultLayoutNames) do
            --     local canFound = false
            --     for j, recvName in ipairs(recvNames) do
            --         if tostring(name) == recvName then
            --             canFound = true
            --             break
            --         end
            --     end

            --     if canFound == false then
            --         local extraData = {}
            --         extraData["NickName"] = Server.SDKInfoServer:GetUserName()
            --         local newLayoutInfo = UDFMMobileCustomLayoutBPLibrary.SerializeCustomLayout(name, extraData)

            --         local complementReq = pb.CSSettingPutKeyValueReq:New()
            --         complementReq.kv = {key = tostring(name), type = self.CustomLayoutType, value = tostring(newLayoutInfo)}
            --         complementReq:Send()
            --     end
            -- end
            -- UDFMMobileCustomLayoutBPLibrary.FetchAllCustomLayoutAppearance(GetWorld())
        end
    end

    local Req = pb.CSSettingGetValuesByTypeReq:New()
    Req.type = self.CustomLayoutType
    Req:Request(onCSPresetGetValuesByTypeRes)
end

function SystemSettingServer:GetHasShared()
    return self._hasShared
end

--保存当前布局到预设
function SystemSettingServer:SaveCustomLayoutToManage(LayoutName, CustomTitle, index)
    local extraData = {}
    extraData["NickName"] = Server.SDKInfoServer:GetUserName()
    local newLayoutInfo = UDFMMobileCustomLayoutBPLibrary.SerializeCustomLayout(LayoutName, extraData)

    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SaveCustomSchemeSuccess, 2)
            
        end
    end
    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = LayoutName .."_" .. index, type = LayoutName .. self.CustomManage, title = CustomTitle, value = tostring(newLayoutInfo)}
    Req:Request(onPresetPutKeyValueRes)
end

--加载当前布局到预设
function SystemSettingServer:LoadCustomLayoutManage(LayoutName, callback)
    local OnCSSettingGetValuesByTypeRes = function(res)
        if res.result == 0 then
            if callback then
                callback(res.kv_array)
            end
        end
    end
    local Req = pb.CSSettingGetValuesByTypeReq:New()
    Req.type = LayoutName .. self.CustomManage
    Req:Request(OnCSSettingGetValuesByTypeRes)
end

--加载某布局到当前自定义布局
function SystemSettingServer:LoadSelfCustomLayout(LayoutName)
    local OnCSSettingGetValueByKeyRes = function(res)
        if res.result == 0 then
            self.Event.evtLoadSchemeData:Invoke(LayoutName, res.kv)
        end
    end
    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = LayoutName
    Req:Request(OnCSSettingGetValueByKeyRes)
end

--加载预设布局
function SystemSettingServer:LoadCustomScheme(layoutName,customValue)
    self.Event.evtLoadSchemeData:Invoke(layoutName, customValue)
end

-------------------------------------------------
--灵敏度设置
-------------------------------------------------

--得到灵敏度设置table
function SystemSettingServer:GetSensitityStr()
    local tab_SliderValue = {}
    tab_SliderValue.fireValueList = self._clientSensitivitySetting:GetCurrentCustomSensitivityArray(true)
    tab_SliderValue.normalValueList = self._clientSensitivitySetting:GetCurrentCustomSensitivityArray(false)
    tab_SliderValue.normalDefaultValue  = self._clientSensitivitySetting:GetCurrentCustomDefaultSensitivity(false)
    tab_SliderValue.fireDefaultValue    = self._clientSensitivitySetting:GetCurrentCustomDefaultSensitivity(true)
    
    tab_SliderValue.normalGroyList  = self._clientSensitivitySetting:GetCurrentGyroCustomSensitivityArray(false)
    tab_SliderValue.fireGroyList    = self._clientSensitivitySetting:GetCurrentGyroCustomSensitivityArray(true)
    tab_SliderValue.normalGroyValue  = self._clientSensitivitySetting:GetCurrentGyroCustomDefaultSensitivity(false)
    tab_SliderValue.fireGroyValue    = self._clientSensitivitySetting:GetCurrentGyroCustomDefaultSensitivity(true)

    local FireSensitityRotationList = {}
    table.insert(FireSensitityRotationList, tab_SliderValue.fireDefaultValue)
    for i = 0, table.nums(tab_SliderValue.fireValueList) - 1 do
        table.insert(FireSensitityRotationList, tab_SliderValue.fireValueList:Get(i))
    end

    local NormalSensitityRotationList = {}
    table.insert(NormalSensitityRotationList, tab_SliderValue.normalDefaultValue)
    for i = 0, table.nums(tab_SliderValue.normalValueList) - 1 do
        table.insert(NormalSensitityRotationList, tab_SliderValue.normalValueList:Get(i))
    end

    local FireGroyRotationList = {}
    table.insert(FireGroyRotationList, tab_SliderValue.fireGroyValue)
    for i = 0, table.nums(tab_SliderValue.fireGroyList) - 1 do
        table.insert(FireGroyRotationList, tab_SliderValue.fireGroyList:Get(i))
    end

    local NormalGroyRotationList = {}
    table.insert(NormalGroyRotationList, tab_SliderValue.normalGroyValue)
    for i = 0, table.nums(tab_SliderValue.normalGroyList) - 1 do
        table.insert(NormalGroyRotationList, tab_SliderValue.normalGroyList:Get(i))
    end

    local accValueNormal = 0
    local accValueFire = 0
    if self._clientSensitivitySetting.RotationSensitivityMode == ESensitivityMode.SpeedAcc then
        accValueNormal     = self._clientSensitivitySetting:GetCustomSpeedAccExtraAngle(false)
        accValueFire       = self._clientSensitivitySetting:GetCustomSpeedAccExtraAngle(true)
    end

    local SensitityInfo = {

        self._clientSensitivitySetting.RotationSensitivityMode,
        self._clientSensitivitySetting.CategoryLevel_Sensitivity,
        self._clientSensitivitySetting.CategoryLevel_GyroSensitivity,
        Json.encode(FireSensitityRotationList),
        Json.encode(NormalSensitityRotationList),
        Json.encode(FireGroyRotationList),
        Json.encode(NormalGroyRotationList),
        accValueNormal,
        accValueFire,
        self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomSensitivityFactor,
        self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomZoomratedMDVFactor:Get(0),
        self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomVerticalSensitivityFactor,
        self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomHorizontalSensitivityFactor,
        self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomSensitivityFactor,
        self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomZoomratedMDVFactor:Get(0),
        self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomVerticalSensitivityFactor,
        self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomHorizontalSensitivityFactor,

        self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomSensitivityFactor,
        self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Get(0),
        self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomVerticalSensitivityFactor,
        self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomHorizontalSensitivityFactor,
        self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomSensitivityFactor ,
        self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Get(0),
        self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomVerticalSensitivityFactor,
        self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomHorizontalSensitivityFactor,
    }

    local str = Json.encode(SensitityInfo)
    return str
end

--生成灵敏度设置分享码
function SystemSettingServer:SendSensityInfo()
    local sensitityStr = self:GetSensitityStr()
    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
           self:DoSettingShareKeyValueReq("Sensitity")
        end
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "Sensitity", type = "CustomSensitity", value = tostring(sensitityStr)}
    Req:Request(onPresetPutKeyValueRes)
end

function SystemSettingServer:DoSettingShareKeyValueReq(keyStr)
    local onPresetShareKeyValueRes = function(res)
        if res.result == 0 then
            self.Events.evtRecvShareSettingGUID:Invoke(res.share_code)
        end
    end
    local Req = pb.CSSettingGenSharCodeReq:New()
    Req.key = keyStr
    Req:Request(onPresetShareKeyValueRes)
end

--上传玩家灵敏度设置到云端并设置标题
function SystemSettingServer:SendPlayerSensityInfo(cloudName)
    local sensitityStr = self:GetSensitityStr()
    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SystemSettingSaveCloudSucc)
        end
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "PlayerSensitity", type = "CloudSystemSetting", value = tostring(sensitityStr), title = cloudName}
    Req:Request(onPresetPutKeyValueRes)
end

--上传玩家自定义字段
function SystemSettingServer:SendPlayerCustomInfo(name,customValue)
    local onPutCustomValueRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SystemSettingSaveCloudSucc)
        end
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = name, type = "CustomValue", value = tostring(customValue), title = name}
    Req:Request(onPutCustomValueRes)
end

--获取玩家自定义字段
function SystemSettingServer:GetPlayerCustomInfo(name)
    local onGetCustomValueRes = function(res)
        if res.result == 0 then
            if res.kv.value ~= "" then
                return res.kv.value
            end
        end
    end

    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = name
    Req:Request(onGetCustomValueRes)
end

--通过分享码获取灵敏度设置
function SystemSettingServer:SendSearchSensitityGUID(GUID)
    local onPresetGetSharedValueRes = function(res)
        if res.result == 0 then
            local tbl = Json.decode(res.kv.value)
            tbl[4] = Json.decode(tbl[4])
            tbl[5] = Json.decode(tbl[5])
            tbl[6] = Json.decode(tbl[6])
            tbl[7] = Json.decode(tbl[7])
            self.Events.evtRecvSearchShareSensitity:Invoke(tbl)
        end
    end
    local Req = pb.CSSettingGetSharedValueReq:New()
    Req.share_code = tostring(GUID)
    Req:Request(onPresetGetSharedValueRes)
end

--得到玩家云端灵敏度设置和标题
function SystemSettingServer:GetPlayerSensitityCloud()
    local OnCSSettingGetValueByKeyRes = function(res)
        if res.result == 0 then
            if res.kv.value ~= "" then
                local tbl = Json.decode(res.kv.value)
                tbl[4] = Json.decode(tbl[4])
                tbl[5] = Json.decode(tbl[5])
                tbl[6] = Json.decode(tbl[6])
                tbl[7] = Json.decode(tbl[7])
                self.Events.evtPlayerCloudSetting:Invoke(tbl, res.kv.title)
            end
        end
    end
    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = "PlayerSensitity"
    Req:Request(OnCSSettingGetValueByKeyRes)
end

function SystemSettingServer:GetClassStr(className, SettingNameTbl)
    local str = nil
    local settingTbl = {}
    for _, name in pairs(SettingNameTbl) do
        if type(className[name]) == "userdata" then
            if className[name].__name == "LuaMap" then
                local map = {}
                for index, value in pairs(className[name]) do
                    map[index] = value
                end
                table.insert(settingTbl, Json.encode(map))
            end
        else
            table.insert(settingTbl, className[name])
        end
    end
    str = Json.encode(settingTbl)
    return str
end

function SystemSettingServer:GetBaseSettingStr()
    local baseStr = nil
    local tbl = {}
    tbl[1] = self:GetClassStr(self._clientBaseSetting, self.BaseSettingName)
    tbl[2] = self:GetClassStr(self._clientControlSetting, self.ControlSettingName)
    baseStr = Json.encode(tbl)
    return baseStr
end

--上传玩家基础设置到云端并设置标题
function SystemSettingServer:SendPlayerBaseInfo(cloudName)
    local baseStr = self:GetBaseSettingStr()
    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SystemSettingSaveCloudSucc)
        end
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "PlayerBase", type = "CloudSystemSetting", value = tostring(baseStr), title = cloudName}
    Req:Request(onPresetPutKeyValueRes)
end

--自动帮玩家保存基础设置
function SystemSettingServer:AutoSaveBaseInfo()
    local baseStr = self:GetBaseSettingStr()
    local onPresetPutKeyValueRes = function(res)
    end
    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "SaveBaseSetting", type = "SaveSystemSetting", value = tostring(baseStr)}
    Req:Request(onPresetPutKeyValueRes)
end

--初始化设置模块时通过云端设置一下本地的基础设置数据
function SystemSettingServer:InitPlayerBaseSetting()
    local OnCSSettingGetValueByKeyRes = function(res)
        if res.result == 0 then
            if res.kv.value ~= "" then
                local tbl = Json.decode(res.kv.value)
                self.Events.evtInitPlayerBaseSetting:Invoke(tbl, res.kv.title)
            end
        end
    end
    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = "SaveBaseSetting"
    Req:Request(OnCSSettingGetValueByKeyRes)
end

--上传玩家灵敏度设置到云端并设置标题
function SystemSettingServer:AutoSaveSensityInfo()
    local sensitityStr = self:GetSensitityStr()
    local onPresetPutKeyValueRes = function(res)
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "SaveSensititySetting", type = "SaveSystemSetting", value = tostring(sensitityStr)}
    Req:Request(onPresetPutKeyValueRes)
end

--初始化设置模块时通过云端设置一下本地的灵敏度设置数据
function SystemSettingServer:InitPlayerSensititySetting()
    local OnCSSettingGetValueByKeyRes = function(res)
        if res.result == 0 then
            if res.kv.value ~= "" then
                local tbl = Json.decode(res.kv.value)
                tbl[4] = Json.decode(tbl[4])
                tbl[5] = Json.decode(tbl[5])
                tbl[6] = Json.decode(tbl[6])
                tbl[7] = Json.decode(tbl[7])
                self.Events.evtInitPlayerSensititySetting:Invoke(tbl, res.kv.title)
            end
        end
    end
    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = "SaveSensititySetting"
    Req:Request(OnCSSettingGetValueByKeyRes,{bEnableHighFrequency = true})
end

function SystemSettingServer:AutoSaveSetting()
    self:AutoSaveBaseInfo()
    self:AutoSaveSensityInfo()
end

--得到玩家云端基础设置和标题
function SystemSettingServer:GetPlayerBaseCloud()
    local OnCSSettingGetValueByKeyRes = function(res)
        if res.result == 0 then
            if res.kv.value ~= "" then
                local tbl = Json.decode(res.kv.value)
                self.Events.evtPlayerCloudSetting:Invoke(tbl, res.kv.title)
            end
        end
    end
    local Req = pb.CSSettingGetValueByKeyReq:New()
    Req.key = "PlayerBase"
    Req:Request(OnCSSettingGetValueByKeyRes,{bEnableHighFrequency = true})
end

function SystemSettingServer:SendBaseInfo()
    local baseStr = self:GetBaseSettingStr()
    local onPresetPutKeyValueRes = function(res)
        if res.result == 0 then
           self:DoSettingShareKeyValueReq("Base")
        end
    end

    local Req = pb.CSSettingPutKeyValueReq:New()
    Req.kv = {key = "Base", type = "ShareBase", value = tostring(baseStr)}
    Req:Request(onPresetPutKeyValueRes)
end

--通过分享码获取灵敏度设置
function SystemSettingServer:SendSearchBaseGUID(GUID)
    local onPresetGetSharedValueRes = function(res)
        if res.result == 0 then
            if res.kv.key == "Base" then
                local tbl = Json.decode(res.kv.value)
                self.Events.evtRecvSearchShareBase:Invoke(tbl)
            end
        end
    end
    local Req = pb.CSSettingGetSharedValueReq:New()
    Req.share_code = tostring(GUID)
    Req:Request(onPresetGetSharedValueRes)
end

--初始化隐私数据
function SystemSettingServer:InitPlayerPrivacyData()
    local onGetPrivacyRes = function(res)
        if res.result == 0 then
            self.Events.evtInitPlayerPrivacyDataList:Invoke(res.privacy_data)
        end
    end
    local Req = pb.CSGetPlayerPrivacyDataReq:New()
    Req:Request(onGetPrivacyRes)
end

--获取隐私数据
function SystemSettingServer:GePlayerPrivacyData()
    local onGetPrivacyRes = function(res)
        if res.result == 0 then
            self.Events.evtInitPlayerPrivacySetting:Invoke(res.privacy_data)
        end
    end
    local Req = pb.CSGetPlayerPrivacyDataReq:New()
    Req:Request(onGetPrivacyRes)
end

--上传隐私数据
function SystemSettingServer:SePlayerPrivacyData(privacy_data)

    local Req = pb.CSSetPlayerPrivacyDataReq:New()
    Req.privacy_data = privacy_data
    Req:Request()
end


return SystemSettingServer