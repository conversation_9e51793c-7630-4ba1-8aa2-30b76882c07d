local RentalDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.Rental.RentalDataLogic"
local ItemHelperTool  = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool  = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EIVItemViewMode = Module.CommonWidget.Config.EIVItemViewMode
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class AssemblyHDRentalMainPanel : LuaUIBaseView
local AssemblyHDRentalMainPanel = ui("AssemblyHDRentalMainPanel")

function AssemblyHDRentalMainPanel:Ctor()
    self._curIndex = 1
    self._curType = PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL
    self._customRentalVouchers = {}
    self._selectedPos = -1
    self._selectedCell = nil
    local allRentalItems = self:MultiWnd("WBP_Assembly_RentalPlan_PC")
    self._mapSlotGroup2RentalItems = {}
    for _, rentalItem in ipairs(allRentalItems) do
        local slotGroup = rentalItem:GetSlotGroup()
        self._mapSlotGroup2RentalItems[slotGroup] = rentalItem
    end
    
    self._wtVoucherNumText = {}

    for i = 1, 4 do
        local wtVoucherNum = self:Wnd("wtVoucherNum_" .. i, UITextBlock)
        self._wtVoucherNumText[Module.ArmedForce.Config.RentalConsumableID[i]] = wtVoucherNum
    end

    self._wtWaterfallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_519", self._OnWaterfallCount, self._OnWaterfallWidget)
    
    self._wtNotAnything = self:Wnd("WBP_Common_NoAnything_1", UIWidgetBase)

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._ShowTips, self._CloseTips)

    self._wtDFTipsAnchor2 = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_132", self._ShowTips2, self._CloseTips2)

    -- BEGIN MODIFICATION @ VIRTUOS : 绑定导航组控件
    if IsHD() then
        self._wtRootCanvas = self:Wnd("CanvasPanel_0", UIWidgetBase)
    end
    -- END MODIFICATION
end

function AssemblyHDRentalMainPanel:OnOpen()
end

function AssemblyHDRentalMainPanel:OnShowBegin()
    Module.CommonBar:BindBackHandler(self.CloseMainPanel, self)
    Server.CollectionServer:CSCollectionAutoDistributionReq(true)
    Module.Inventory:RegisterCommonClickBehavior()
    Module.ArmedForce.Field:SetApplyRentalProcessing(false)
    Module.Inventory:UpdateItemUnitSize(130)
    self:AddListeners()

    Module.ArmedForce.Config.evtAssemblyRentalMainPanelOnShowBegin:Invoke()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

function AssemblyHDRentalMainPanel:OnHideBegin()
    Module.Inventory:UnregisterCommonClickBehavior()
    Module.Inventory:InitBaseItemSize()
    self:_ReleaseTimer()
    self:_ReleaseTimer2()
    self:_ShowTipsByIndex()
    self:RemoveAllLuaEvent()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end

function AssemblyHDRentalMainPanel:OnActivate()

end

function AssemblyHDRentalMainPanel:OnDeactivate()
    for index, groupId in ipairs({ESlotGroup.Rental_1, ESlotGroup.Rental_2, ESlotGroup.Rental_3}) do
        Server.InventoryServer:ClearSlotGroup(groupId)
    end
end

function AssemblyHDRentalMainPanel:OnClose()
    for index, groupId in ipairs({ESlotGroup.Rental_1, ESlotGroup.Rental_2, ESlotGroup.Rental_3}) do
        Server.InventoryServer:ClearSlotGroup(groupId)
    end
end

function AssemblyHDRentalMainPanel:OnInitExtraData()
    Module.CommonBar:RegStackUITopBarTitle(self, Module.ArmedForce.Config.Loc.EquipmentRentalTitle)
    local defaultIndex = self:_Inference_UsedTab()
    local topTabGroupRegInfo = self:GetCommonBarRegInfo(defaultIndex)
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTabGroupRegInfo)
end

function AssemblyHDRentalMainPanel:AddListeners()
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceGetRentalFinished, self._RefreshView, self)
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtRentalApplySuccess, self._OnRentalApplySuccess, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionAutoDistributionReqSucess, self._RefreshRentalVoucherNum, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtRentalShowTipsChanged, self._ShowTipsByIndex, self)
end

function AssemblyHDRentalMainPanel:CloseMainPanel()
    if Module.ArmedForce.Field:CheckApplyRentalProcessing() then
        return
    else
        Facade.UIManager:CloseUI(self)
    end
end

function AssemblyHDRentalMainPanel:_OnTabIndexChanged(curIndex, lastIndex)
    Module.ItemDetail:CloseAllPopUI()
    self._curIndex = curIndex
    self._curType = self._curIndex <= 4 and PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL or PropType.PROP_TYPE_UNIQUE_EQUIPMENT_RENTAL
    self:_SwitchType()

    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        if self._curType == PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
        else
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._WaterfallNavGroup)
        end
    end
    -- END MODIFICATION
end

function AssemblyHDRentalMainPanel:_RefreshView(bTimeIsUp)
    self._wtNotAnything:Collapsed()
    local bShowNotAnything = true
    self:_ReleaseTimer()
    local nextRefreshTime = Server.ArmedForceServer:GetCurRentalInfo_NextRefreshTime()
    local senconds = TimeUtil.GetServerRemainTime2Seconds(nextRefreshTime)
    if senconds < 3600 then
        -- 小于一个小时开启倒计时刷新
        self._inTimerHandle = Timer.DelayCall(senconds, function ()
            if self._curType == PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL then
                Module.ArmedForce:ReqRentalDatas(Module.ArmedForce.Config.RentalConsumableID[self._curIndex], true)
            elseif self._curType == PropType.PROP_TYPE_UNIQUE_EQUIPMENT_RENTAL then
                local item = self._customRentalVouchers[self._selectedPos]
                if item then
                    Module.ArmedForce:ReqRentalDatas(item.id, true)
                end
            end
        end, self)
    end
    -- 刷新数据逻辑
    for i = 1, 3, 1 do
        local rentalItem = self._mapSlotGroup2RentalItems[ESlotGroup.MainRental + i]
        if rentalItem then
            if self._curType == PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL then
                -- rentalItem:SetTitleEnable(true) -- 正常态显示方案名
                -- local typeId = (self._curIndex - 1) * 3 + i
                local consumableID = Module.ArmedForce.Config.RentalConsumableID[self._curIndex]
                local rentalData = Server.ArmedForceServer:GetCurRentalInfo_RentalData(consumableID, i)
                if rentalData then
                    rentalItem:Visible()
                    rentalItem:SetRentalData(rentalData)
                    if bTimeIsUp then
                        rentalItem:PlayAnimation(rentalItem.Anim_refresh, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                    else
                        rentalItem:PlayAnimation(rentalItem.WBP_Assembly_RentalPlan_PC_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                    end
                    bShowNotAnything = false
                else
                    rentalItem:Collapsed()
                end
            elseif self._curType == PropType.PROP_TYPE_UNIQUE_EQUIPMENT_RENTAL then
                -- rentalItem:SetTitleEnable(false) -- 专属态不显示方案名
                if i < 3 then -- 因为第一个被隐藏了的，要从第二个开始设置
                    local item = self._customRentalVouchers[self._selectedPos]
                    if item then
                        local rentalData = Server.ArmedForceServer:GetCurRentalInfo_RentalData(item.id, i) -- 但是数据还得是从第一个开始设置
                        if rentalData then
                            rentalItem:Visible()
                            rentalItem:SetRentalData(rentalData)
                            if bTimeIsUp then
                                rentalItem:PlayAnimation(rentalItem.Anim_refresh, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                            else
                                rentalItem:PlayAnimation(rentalItem.WBP_Assembly_RentalPlan_PC_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
                            end
                            bShowNotAnything = false
                        else
                            rentalItem:Collapsed()
                        end
                    else
                        rentalItem:Collapsed()
                    end
                else
                    rentalItem:Collapsed()
                end
            end
        end
    end

    if bShowNotAnything then
        self._wtNotAnything:SelfHitTestInvisible()
    end
    self:_RefreshRentalVoucherNum()
end

function AssemblyHDRentalMainPanel:_ReleaseTimer()
    if self._inTimerHandle then
        Timer.CancelDelay(self._inTimerHandle)
        self._inTimerHandle = nil
    end
end

function AssemblyHDRentalMainPanel:_ReleaseTimer2()
    if self._timerHandle then
        self._timerHandle:Release()
    end
end

function AssemblyHDRentalMainPanel:_OnRentalApplySuccess(rentalData)
    if rentalData then
        Module.ArmedForce.Field:SetApplyRentalProcessing(false)
        Facade.UIManager:CloseUI(self)
    end
end

function AssemblyHDRentalMainPanel:_OnUpdateCollectionData()
    self:_RefreshRentalVoucherNum()
    local topTabGroupRegInfo = self:GetCommonBarRegInfo(self._curIndex)
    self:_SwitchType()
    Module.CommonBar:SetTopTabGroup(topTabGroupRegInfo, 2)
end

function AssemblyHDRentalMainPanel:GetCommonBarRegInfo(defaultIndex)
    defaultIndex = setdefault(defaultIndex, -1)
    local imagePath = Module.ArmedForce.Config.RentalImgPathList
    local tabTxtList = {}
    -- 常规券
    for index, id in ipairs(Module.ArmedForce.Config.RentalConsumableID) do
        local name = Module.ArmedForce.Config.RentalTabTxtList[id]
        local txtStr = ""
        local rentalVoucherCollectionItems = RentalDataLogic.GetRentalVoucherCollectionItemsByFunction(function (v, k)
            return v.id == id
        end)
        local bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(id)
        local rentalVoucherNum = rentalVoucherCollectionItems[1] and rentalVoucherCollectionItems[1].num or 0
        if bCanApply then
            txtStr = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVoucherTitle_Available,{["Name"] = name, ["Count"] = rentalVoucherNum})
        else
            txtStr = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVoucherTitle_Unavailable,{["Name"] = name, ["Count"] = rentalVoucherNum})
        end
        table.insert(tabTxtList, txtStr)
        if defaultIndex == -1 and bCanApply then
            defaultIndex = index
        end
    end

    -- 专属券
    self._customRentalVouchers = RentalDataLogic.GetRentalVoucherCollectionItemsByFunction(function (v, k)
        return not table.contains(Module.ArmedForce.Config.RentalConsumableID, v.id)
    end)

    table.sort(self._customRentalVouchers, function (a, b)
        -- 可用 根据地图判断是否可用，战略版接口
        -- 限时 -》不限时
        -- 结束时间近->远
        local a_bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(a.id)
        local b_bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(b.id)
        if a_bCanApply ~= b_bCanApply then
            return a_bCanApply
        end
        local a_RawExpireInfo = Server.CollectionServer:GetPropExpireInfo(a.id)
        local b_RawExpireInfo = Server.CollectionServer:GetPropExpireInfo(b.id)
        -- 判断是否为限时道具
        local a_isLimited = a_RawExpireInfo ~= nil
        local b_isLimited = b_RawExpireInfo ~= nil
        if a_isLimited and b_isLimited then
            if a_RawExpireInfo[1].expireTime ~= b_RawExpireInfo[1].expireTime then
                return a_RawExpireInfo[1].expireTime < b_RawExpireInfo[1].expireTime
            end
            return a.id < b.id
        elseif not a_isLimited and not b_isLimited then
            -- 如果都不是限时道具，按 id 排序
            return a.id < b.id
        else
            -- 如果一个是限时道具，一个不是，则限时道具优先
            return a_isLimited
        end
    end)

    local name = Module.ArmedForce.Config.RentalTabTxtList[0]
    local txtStr = ""
    local rentalVoucherNum = 0
    local bCustomRentalVouchersCanApply = false
    table.walk(self._customRentalVouchers,
        function (v, k)
            rentalVoucherNum = rentalVoucherNum + v.num
            local bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(v.id)
            if defaultIndex == -1 and bCanApply then
                defaultIndex = index
            end
            if not bCustomRentalVouchersCanApply and bCanApply then
                bCustomRentalVouchersCanApply = true
            end
        end)

    if bCustomRentalVouchersCanApply then
        txtStr = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVoucherTitle_Available,{["Name"] = name, ["Count"] = rentalVoucherNum})
    else
        txtStr = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVoucherTitle_Unavailable,{["Name"] = name, ["Count"] = rentalVoucherNum})
    end

    table.insert(tabTxtList, txtStr)
    if defaultIndex == -1 then
        self._curIndex = 1
    else
        self._curIndex = defaultIndex
    end


    local info = {
        uiNavId = UIName2ID.AssemblyHDRentalMainPanel,
        tabTxtList = tabTxtList,
        imgPathList = imagePath,
        fCallbackIns = SafeCallBack(self._OnTabIndexChanged, self),
        defalutIdx = self._curIndex,
        bTriggerCallback = true,
        bReInitChildWidgets = false,
        bNewReddotTrie = true,
    }
    return info
end

function AssemblyHDRentalMainPanel:_RefreshRentalVoucherNum()
    self:_ReleaseTimer2()
    for index, id in pairs(Module.ArmedForce.Config.RentalConsumableID) do
        local rentalVoucherNum = Server.CollectionServer:GetCollectionItemsNumById(id)
        local quality = ItemConfigTool.GetItemQuality(id)
        local rentalID2RichIconTxt = ""
        if ItemConfig.ERentalQuality2RichIconTxt[quality] then
            rentalID2RichIconTxt = string.format(ItemConfig.ERentalQuality2RichIconTxt[quality], 48,48)
        end
        local maxNum = ItemConfigTool.GetMaxStacksNumById(id)
        local voucherNumTxt = StringUtil.Key2StrFormat("{RichIconTxt}{curNum}/{MaxNum}",{["RichIconTxt"] = rentalID2RichIconTxt, ["curNum"] = rentalVoucherNum, ["MaxNum"] = maxNum})
        if id == Module.ArmedForce.Config.RentalConsumableID[1] and rentalVoucherNum < maxNum then
            local nextRefreshTime = Server.CollectionServer:GetLowRentalNextDistributeTs()
            local function fGetRentalNextDistributeTsStr(voucherNumTxt)
                if nextRefreshTime > 0 then
                    local senconds = TimeUtil.GetLocalRemainTime2Seconds(nextRefreshTime)
                    local countdownTime = TimeUtil.GetSecondsFormatHHMMSSString(senconds)
                    local countdownTimeStr = string.format(" <customstyle color=\"C002\">(%s)</>", countdownTime)
                    return voucherNumTxt .. countdownTimeStr
                else
                    return voucherNumTxt
                end
            end
            local voucherNumStr = fGetRentalNextDistributeTsStr(voucherNumTxt)
            self._wtVoucherNumText[id]:SetText(voucherNumStr)
            local equipmentPresetLimitLevel = DFMGlobalConst.GetGlobalConstNumber("EquipmentPresetLimitLevel", 2)
            if equipmentPresetLimitLevel <= Server.RoleInfoServer.seasonLevel and nextRefreshTime > 0 then
                -- 开启倒计时
                self._timerHandle = Timer:NewIns(0.5, 0)
                self._timerHandle:AddListener(function ()
                    local lowRentalNextDistributeTs = Server.CollectionServer:GetLowRentalNextDistributeTs()
                    if lowRentalNextDistributeTs < Facade.ClockManager:GetLocalTimestamp() then
                        Server.CollectionServer:CSCollectionAutoDistributionReq(true)
                    end
                    local voucherNumStr = fGetRentalNextDistributeTsStr(voucherNumTxt)
                    self._wtVoucherNumText[id]:SetText(voucherNumStr)
                end, self)
                self._timerHandle:Start()
            end
        else
            self._wtVoucherNumText[id]:SetText(voucherNumTxt)
        end
    end
end

function AssemblyHDRentalMainPanel:_ShowTips()
    if self:IsInHideBeginState() or self:IsInHideState() or self:IsInShowBeginState() then
        return
    end
    local datas = {}
    table.insert(datas, {
        textContent = Module.ArmedForce.Config.Loc.RentalViewRefreshTips_Mobile,
        styleRowId = "C002"
    })
	self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtDFTipsAnchor)
end

function AssemblyHDRentalMainPanel:_CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtDFTipsAnchor)
        self._tipHandle = nil
    end
end



function AssemblyHDRentalMainPanel:_ShowTips2()
    if self:IsInHideBeginState() or self:IsInHideState() or self:IsInShowBeginState() then
        return
    end
    local datas = {}
    for index, consumableID in ipairs(Module.ArmedForce.Config.RentalConsumableID) do
        table.insert(datas, {id = UIName2ID.Assembled_CommonMessageTips_V7, data = {itemId = consumableID, textDesc = desc}})
    end
	self._tipHandle2 = Module.CommonTips:ShowAssembledTips(datas,self._wtDFTipsAnchor2, 803)
end

function AssemblyHDRentalMainPanel:_CloseTips2()
    if self._tipHandle2 then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle2, self._wtDFTipsAnchor2)
        self._tipHandle2 = nil
    end
end

function AssemblyHDRentalMainPanel:_ShowTipsByIndex(index)
    self:_CloseTips()
    self:_CloseTips2()
    if index == 1 then
        self:_ShowTips()
    elseif index == 2 then
        self:_ShowTips2()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function AssemblyHDRentalMainPanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootCanvas, self, "Hittest")
        if self._NavGroup then
            local NavStrategy = self._NavGroup:GetOwnerNavStrategy()
            if NavStrategy then
                NavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end
            -- 添加导航子控件
            for i = 1, 3, 1 do
                local rentalItem = self._mapSlotGroup2RentalItems[ESlotGroup.MainRental + i]
                if rentalItem then
                    self._NavGroup:AddNavWidgetToArray(rentalItem)
                end
            end

            self._WaterfallNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterfallList, self, "Hittest")
            if self._WaterfallNavGroup then
                self._WaterfallNavGroup:AddNavWidgetToArray(self._wtWaterfallList)
                self._WaterfallNavGroup:SetScrollRecipient(self._wtWaterfallList)
                self._WaterfallNavGroup:SetNavSelectorWidgetVisibility(true)
                self._WaterfallNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
                WidgetUtil.BindCustomFocusProxy(self._WaterfallNavGroup, self._WaterfallFocusProxyMaker, self._WaterfallFocusProxyResolver, self)
                WidgetUtil.BindCustomBoundaryNavRule(self._WaterfallNavGroup, self._CustomBoundaryRule, self)
                local WaterfallNavStrategy = self._WaterfallNavGroup:GetOwnerNavStrategy()
                if WaterfallNavStrategy then
                    WaterfallNavStrategy:SetHitPadding(4.0)
                    WaterfallNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
                end
            end
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
        end
    end
end

function AssemblyHDRentalMainPanel:SetScrollRecipient(scroll)
    if not IsHD() then
        return 
    end
    if self._NavGroup then
        self._NavGroup:SetScrollRecipient(scroll)
    end
end

function AssemblyHDRentalMainPanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil
end

function AssemblyHDRentalMainPanel:_WaterfallFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_CommonItemTemplate_C")
    local rowIndex = self._wtWaterfallList:GetIndexByItem(row)
    return {rowIndex}
end

function AssemblyHDRentalMainPanel:_WaterfallFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    -- 可能item会在屏幕外，先执行滚动
    -- self._wtWaterFallList:ScrollToIndexToScreen(rowIndex, 0.5, 0.5)
    local row = self._wtWaterfallList:GetItemByIndex(rowIndex)
    if row then
        return row
    end
    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function AssemblyHDRentalMainPanel:_CustomBoundaryRule(direction)
    if direction ==  EUINavigation.Down then
        self._wtWaterfallList:ScrollToIndex(1)
        local item = self._wtWaterfallList:GetItemByIndex(1)
        return item
    elseif direction == EUINavigation.Up then
        local lastIdx = self:_OnWaterfallCount()
        self._wtWaterfallList:ScrollToIndex(lastIdx)
        local item = self._wtWaterfallList:GetItemByIndex(lastIdx)
        return item
    else
        return nil
    end
end
--- END MODIFICATION

-- 常规券和专属券切换：type==> 1--正常态   2--专属态
function AssemblyHDRentalMainPanel:_SwitchType()
    self:BPSetType(self._curType - 1)
    for i = 1, 3, 1 do
        local rentalItem = self._mapSlotGroup2RentalItems[ESlotGroup.MainRental + i]
        if rentalItem then
            rentalItem:Collapsed()
        end
    end
    if self._curType  == PropType.PROP_TYPE_COMMON_EQUIPMENT_RENTAL then -- 正常态
        self._wtWaterfallList:Collapsed()
        Module.ArmedForce:ReqRentalDatas(Module.ArmedForce.Config.RentalConsumableID[self._curIndex], false)
    elseif self._curType  == PropType.PROP_TYPE_UNIQUE_EQUIPMENT_RENTAL then -- 专属态
        self._selectedPos = self:_Inference_UsedSelectedPos()
        self._selectedCell = nil
        self._wtNotAnything:SelfHitTestInvisible()
        self._wtWaterfallList:Visible()
        self._wtWaterfallList:RefreshAllItems()
    end
end

function AssemblyHDRentalMainPanel:_OnWaterfallCount()
    local count = 0
    if self._customRentalVouchers and not table.isempty(self._customRentalVouchers) then
        return table.nums(self._customRentalVouchers)
    end
    return count
end

function AssemblyHDRentalMainPanel:_OnWaterfallWidget(position, itemWidget)
    local item = self._customRentalVouchers[position]
    if item then
        local bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(item.id)
        itemWidget:ChangeDefaultMode(EIVItemViewMode.ListItemView)
        local nameComp = itemWidget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
        local maskLockComp = itemWidget:FindOrAdd(EComp.GreyMask, UIName2ID.IVMaskLockComponent, EIVSlotPos.MaskLayer)
        maskLockComp:SetDescText(Module.ArmedForce.Config.Loc.UnavailableMaskStr)
        maskLockComp:EnableComponent(not bCanApply)
        maskLockComp:IsShowLockIcon(false)
        itemWidget:InitItem(item)
        local fClickCb = CreateCallBack(self._OnCustomRentalVouchersClicked, self, itemWidget, position)
        itemWidget:BindCustomOnClicked(fClickCb)
        local expireInfo = Module.Collection:GetProcessedPropExpireInfo(item.id)
        if expireInfo and #expireInfo > 0 then
            local expirationHintComp = itemWidget:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
            expirationHintComp:ShowIconAndText(
                "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'",
                expireInfo[1].timeLeftText
                )
                expirationHintComp:SetCornerStyle()
                expirationHintComp._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Highlight01"))
                expirationHintComp:SetTextColorAndOpacity(Facade.ColorManager:GetSlateColor("Highlight01"))
        end
        itemWidget:EnableComponent(EComp.BottomLeftIconText, expireInfo ~= nil and expireInfo[1] ~= nil)
    end

    itemWidget:SetAnchorIfOverFlow()
    itemWidget:SetSelected(item, self._selectedPos == position)
    if self._selectedPos == position and not self._selectedCell then -- 此时证明是首次进入界面并默认选中第一个
        self._selectedCell = itemWidget
        self:_OnCustomRentalVouchersClicked(itemWidget, position, true)
        -- WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function AssemblyHDRentalMainPanel:_OnCustomRentalVouchersClicked(itemWidget, position, bForce)
    Module.ItemDetail:CloseAllPopUI()
    if self._selectedPos ~= position or bForce then
        local item = self._customRentalVouchers[position]
        if item then
            if self._selectedCell and self._selectedPos > 0 then
                local preItem = self._customRentalVouchers[self._selectedPos]
                if preItem then
                    self._selectedCell:SetSelected(preItem, false)
                    -- if IsHD() and WidgetUtil.IsGamepad() then 
                    --     self._selectedCell:SetCppValue("bHandleClick" , true)
                    -- end
                end
            end
            self._selectedCell = itemWidget
            self._selectedCell:SetSelected(item, true)
            -- if IsHD() and WidgetUtil.IsGamepad() then 
            --     self._selectedCell:SetCppValue("bHandleClick" , false)
            -- end
            self._selectedPos = position
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            Module.ArmedForce:ReqRentalDatas(item.id, false)
        end
    end
end

function AssemblyHDRentalMainPanel:_Inference_UsedTab()
    if Server.ArmedForceServer:CheckIsRentalStatus() then
        local curRentalPlanConsumableID = Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
        if curRentalPlanConsumableID > 0 then
            for index, consumableID in ipairs(Module.ArmedForce.Config.RentalConsumableID) do
                if curRentalPlanConsumableID == consumableID then
                    return index
                end
            end
            -- 如果不是常规券，则是定制专属券，下标为5
            return 5
        end
    end
    return nil
end

function AssemblyHDRentalMainPanel:_Inference_UsedSelectedPos()
    if Server.ArmedForceServer:CheckIsRentalStatus() then
        local curRentalPlanConsumableID = Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
        if curRentalPlanConsumableID > 0 then
            if not table.contains(Module.ArmedForce.Config.RentalConsumableID, curRentalPlanConsumableID) then -- 如果是专属券
                if self._customRentalVouchers and not table.isempty(self._customRentalVouchers) then
                    for pos, item in pairs(self._customRentalVouchers) do
                        if item.id == curRentalPlanConsumableID then
                            return pos
                        end
                    end
                end
            end
        end
    end
    return 1
end



return AssemblyHDRentalMainPanel