require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.cs_shop_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_lottery_editor_pb"
end

LotteryBoxType = {
InvalidBox = 0,
NormalBox = 1,
IndepBox = 2,
MultipleChoicePackage = 3,
BonusPackage = 4,
InstantAssemblePackage = 5,
}
pb.__pb_CSLotteryPropDrawReq = {
    prop_id = 0,
    prop_gid = 0,
    num = 0,
}
pb.__pb_CSLotteryPropDrawReq.__name = "CSLotteryPropDrawReq"
pb.__pb_CSLotteryPropDrawReq.__index = pb.__pb_CSLotteryPropDrawReq
pb.__pb_CSLotteryPropDrawReq.__pairs = __pb_pairs

pb.CSLotteryPropDrawReq = { __name = "CSLotteryPropDrawReq", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSLotteryPropDrawReq : ProtoBase
---@field public prop_id number
---@field public prop_gid number
---@field public num number

---@return pb_CSLotteryPropDrawReq
function pb.CSLotteryPropDrawReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSLotteryPropDrawRes = {
    result = 0,
}
pb.__pb_CSLotteryPropDrawRes.__name = "CSLotteryPropDrawRes"
pb.__pb_CSLotteryPropDrawRes.__index = pb.__pb_CSLotteryPropDrawRes
pb.__pb_CSLotteryPropDrawRes.__pairs = __pb_pairs

pb.CSLotteryPropDrawRes = { __name = "CSLotteryPropDrawRes", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSLotteryPropDrawRes : ProtoBase
---@field public result number
---@field public data_change pb_DataChange

---@return pb_CSLotteryPropDrawRes
function pb.CSLotteryPropDrawRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSLotteryBlindBoxDrawReq = {
    prop_id = 0,
    prop_gid = 0,
    num = 0,
    condition_id = 0,
}
pb.__pb_CSLotteryBlindBoxDrawReq.__name = "CSLotteryBlindBoxDrawReq"
pb.__pb_CSLotteryBlindBoxDrawReq.__index = pb.__pb_CSLotteryBlindBoxDrawReq
pb.__pb_CSLotteryBlindBoxDrawReq.__pairs = __pb_pairs

pb.CSLotteryBlindBoxDrawReq = { __name = "CSLotteryBlindBoxDrawReq", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSLotteryBlindBoxDrawReq : ProtoBase
---@field public prop_id number
---@field public prop_gid number
---@field public num number
---@field public condition_id number
---@field public box_list pb_LotteryBoxData[]

---@return pb_CSLotteryBlindBoxDrawReq
function pb.CSLotteryBlindBoxDrawReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSLotteryBlindBoxDrawRes = {
    result = 0,
}
pb.__pb_CSLotteryBlindBoxDrawRes.__name = "CSLotteryBlindBoxDrawRes"
pb.__pb_CSLotteryBlindBoxDrawRes.__index = pb.__pb_CSLotteryBlindBoxDrawRes
pb.__pb_CSLotteryBlindBoxDrawRes.__pairs = __pb_pairs

pb.CSLotteryBlindBoxDrawRes = { __name = "CSLotteryBlindBoxDrawRes", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSLotteryBlindBoxDrawRes : ProtoBase
---@field public result number
---@field public data_change pb_DataChange

---@return pb_CSLotteryBlindBoxDrawRes
function pb.CSLotteryBlindBoxDrawRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryResultInfo = {
    lottery_process = "",
    box_id = 0,
    num = 0,
}
pb.__pb_LotteryResultInfo.__name = "LotteryResultInfo"
pb.__pb_LotteryResultInfo.__index = pb.__pb_LotteryResultInfo
pb.__pb_LotteryResultInfo.__pairs = __pb_pairs

pb.LotteryResultInfo = { __name = "LotteryResultInfo", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryResultInfo : ProtoBase
---@field public lottery_process string
---@field public box_id number
---@field public num number

---@return pb_LotteryResultInfo
function pb.LotteryResultInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxPropConfig = {
    prop_id = 0,
    active_flag = false,
    indep_prob = 0,
    prob = 0,
    num = 0,
    restore_flag = false,
    pos = 0,
    send_by_mail = false,
    estimated_probability = "",
    prob_showed = 0,
    acquisition_guaranteed = 0,
    real_prob = 0,
    group_id = 0,
    num_id = 0,
    position = 0,
    bound_flag = 0,
    choose_up = 0,
    un_choose_up = 0,
    begin_time = 0,
    end_time = 0,
}
pb.__pb_LotteryBoxPropConfig.__name = "LotteryBoxPropConfig"
pb.__pb_LotteryBoxPropConfig.__index = pb.__pb_LotteryBoxPropConfig
pb.__pb_LotteryBoxPropConfig.__pairs = __pb_pairs

pb.LotteryBoxPropConfig = { __name = "LotteryBoxPropConfig", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxPropConfig : ProtoBase
---@field public prop_id number
---@field public active_flag boolean
---@field public indep_prob number
---@field public prob number
---@field public num number
---@field public restore_flag boolean
---@field public inventory_type_list number[]
---@field public pos number
---@field public send_by_mail boolean
---@field public result pb_LotteryResultInfo
---@field public estimated_probability string
---@field public prob_showed number
---@field public acquisition_guaranteed number
---@field public real_prob number
---@field public group_id number
---@field public num_id number
---@field public position number
---@field public bound_flag number
---@field public choose_up number
---@field public un_choose_up number
---@field public begin_time number
---@field public end_time number

---@return pb_LotteryBoxPropConfig
function pb.LotteryBoxPropConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxGroupConfig = {
    group_id = 0,
    active_flag = false,
    indep_prob = 0,
    prob = 0,
    restore_flag = false,
    change_start = 0,
    change_rate = 0,
    time_assured = 0,
    core_flag = false,
    real_prob = 0,
    begin_time = 0,
    end_time = 0,
}
pb.__pb_LotteryBoxGroupConfig.__name = "LotteryBoxGroupConfig"
pb.__pb_LotteryBoxGroupConfig.__index = pb.__pb_LotteryBoxGroupConfig
pb.__pb_LotteryBoxGroupConfig.__pairs = __pb_pairs

pb.LotteryBoxGroupConfig = { __name = "LotteryBoxGroupConfig", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxGroupConfig : ProtoBase
---@field public group_id number
---@field public active_flag boolean
---@field public indep_prob number
---@field public prob number
---@field public restore_flag boolean
---@field public change_start number
---@field public change_rate number
---@field public time_assured number
---@field public core_flag boolean
---@field public prop_list pb_LotteryBoxPropConfig[]
---@field public real_prob number
---@field public begin_time number
---@field public end_time number

---@return pb_LotteryBoxGroupConfig
function pb.LotteryBoxGroupConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxConfig = {
    box_id = 0,
    type = 0,
    show_id1 = 0,
    show_id2 = 0,
}
pb.__pb_LotteryBoxConfig.__name = "LotteryBoxConfig"
pb.__pb_LotteryBoxConfig.__index = pb.__pb_LotteryBoxConfig
pb.__pb_LotteryBoxConfig.__pairs = __pb_pairs

pb.LotteryBoxConfig = { __name = "LotteryBoxConfig", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxConfig : ProtoBase
---@field public box_id number
---@field public type number
---@field public group_list pb_LotteryBoxGroupConfig[]
---@field public sol_prop_list pb_LotteryBoxPropConfig[]
---@field public show_id1 number
---@field public show_id2 number
---@field public show_id_list number[]

---@return pb_LotteryBoxConfig
function pb.LotteryBoxConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSOpenBoxReq = {
    instant_assemble = false,
}
pb.__pb_CSOpenBoxReq.__name = "CSOpenBoxReq"
pb.__pb_CSOpenBoxReq.__index = pb.__pb_CSOpenBoxReq
pb.__pb_CSOpenBoxReq.__pairs = __pb_pairs

pb.CSOpenBoxReq = { __name = "CSOpenBoxReq", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSOpenBoxReq : ProtoBase
---@field public box_list pb_LotteryBoxData[]
---@field public instant_assemble boolean

---@return pb_CSOpenBoxReq
function pb.CSOpenBoxReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSOpenBoxRes = {
    result = 0,
    send_by_mail = false,
}
pb.__pb_CSOpenBoxRes.__name = "CSOpenBoxRes"
pb.__pb_CSOpenBoxRes.__index = pb.__pb_CSOpenBoxRes
pb.__pb_CSOpenBoxRes.__pairs = __pb_pairs

pb.CSOpenBoxRes = { __name = "CSOpenBoxRes", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSOpenBoxRes : ProtoBase
---@field public result number
---@field public change pb_DataChange
---@field public send_by_mail boolean

---@return pb_CSOpenBoxRes
function pb.CSOpenBoxRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxPropInfo = {
    prop_id = 0,
    active_flag = false,
    indep_prob = 0,
    prob = 0,
    num = 0,
    restore_flag = false,
    hit_sum = 0,
    pos = 0,
    estimated_probability = "",
    prob_showed = 0,
    real_prob = 0,
    acquisition_guaranteed = 0,
    bound_flag = 0,
    is_chosen = false,
    num_id = 0,
    begin_time = 0,
    end_time = 0,
}
pb.__pb_LotteryBoxPropInfo.__name = "LotteryBoxPropInfo"
pb.__pb_LotteryBoxPropInfo.__index = pb.__pb_LotteryBoxPropInfo
pb.__pb_LotteryBoxPropInfo.__pairs = __pb_pairs

pb.LotteryBoxPropInfo = { __name = "LotteryBoxPropInfo", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxPropInfo : ProtoBase
---@field public prop_id number
---@field public active_flag boolean
---@field public indep_prob number
---@field public prob number
---@field public num number
---@field public restore_flag boolean
---@field public hit_sum number
---@field public pos number
---@field public estimated_probability string
---@field public prob_showed number
---@field public real_prob number
---@field public acquisition_guaranteed number
---@field public prop_info pb_PropInfo
---@field public bound_flag number
---@field public is_chosen boolean
---@field public num_id number
---@field public begin_time number
---@field public end_time number

---@return pb_LotteryBoxPropInfo
function pb.LotteryBoxPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxGroupInfo = {
    group_id = 0,
    active_flag = false,
    indep_prob = 0,
    prob = 0,
    restore_flag = false,
    hit_sum = 0,
    time_assured = 0,
    core_flag = false,
    has_chosen_up = false,
    begin_time = 0,
    end_time = 0,
}
pb.__pb_LotteryBoxGroupInfo.__name = "LotteryBoxGroupInfo"
pb.__pb_LotteryBoxGroupInfo.__index = pb.__pb_LotteryBoxGroupInfo
pb.__pb_LotteryBoxGroupInfo.__pairs = __pb_pairs

pb.LotteryBoxGroupInfo = { __name = "LotteryBoxGroupInfo", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxGroupInfo : ProtoBase
---@field public group_id number
---@field public active_flag boolean
---@field public indep_prob number
---@field public prob number
---@field public restore_flag boolean
---@field public hit_sum number
---@field public time_assured number
---@field public core_flag boolean
---@field public prop_list pb_LotteryBoxPropInfo[]
---@field public has_chosen_up boolean
---@field public begin_time number
---@field public end_time number

---@return pb_LotteryBoxGroupInfo
function pb.LotteryBoxGroupInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MandelRewardInfo = {
    approve_count = 0,
}
pb.__pb_MandelRewardInfo.__name = "MandelRewardInfo"
pb.__pb_MandelRewardInfo.__index = pb.__pb_MandelRewardInfo
pb.__pb_MandelRewardInfo.__pairs = __pb_pairs

pb.MandelRewardInfo = { __name = "MandelRewardInfo", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MandelRewardInfo : ProtoBase
---@field public approve_count number
---@field public props pb_PropInfo[]

---@return pb_MandelRewardInfo
function pb.MandelRewardInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LotteryBoxInfo = {
    box_id = 0,
    type = 0,
    open_count = 0,
    show_id1 = 0,
    show_id2 = 0,
    core_open_count = 0,
    last_core_reward_approve_num = 0,
}
pb.__pb_LotteryBoxInfo.__name = "LotteryBoxInfo"
pb.__pb_LotteryBoxInfo.__index = pb.__pb_LotteryBoxInfo
pb.__pb_LotteryBoxInfo.__pairs = __pb_pairs

pb.LotteryBoxInfo = { __name = "LotteryBoxInfo", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LotteryBoxInfo : ProtoBase
---@field public box_id number
---@field public type number
---@field public group_list pb_LotteryBoxGroupInfo[]
---@field public open_count number
---@field public show_id1 number
---@field public show_id2 number
---@field public collide_props pb_PropInfo[]
---@field public info_list pb_MandelRewardInfo[]
---@field public core_open_count number
---@field public last_core_reward_approve_num number
---@field public show_id_list number[]

---@return pb_LotteryBoxInfo
function pb.LotteryBoxInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSGetBoxInfoReq = {
    source = 0,
}
pb.__pb_CSGetBoxInfoReq.__name = "CSGetBoxInfoReq"
pb.__pb_CSGetBoxInfoReq.__index = pb.__pb_CSGetBoxInfoReq
pb.__pb_CSGetBoxInfoReq.__pairs = __pb_pairs

pb.CSGetBoxInfoReq = { __name = "CSGetBoxInfoReq", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSGetBoxInfoReq : ProtoBase
---@field public id_list number[]
---@field public source number

---@return pb_CSGetBoxInfoReq
function pb.CSGetBoxInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSGetBoxInfoRes = {
    result = 0,
}
pb.__pb_CSGetBoxInfoRes.__name = "CSGetBoxInfoRes"
pb.__pb_CSGetBoxInfoRes.__index = pb.__pb_CSGetBoxInfoRes
pb.__pb_CSGetBoxInfoRes.__pairs = __pb_pairs

pb.CSGetBoxInfoRes = { __name = "CSGetBoxInfoRes", __service="lottery", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSGetBoxInfoRes : ProtoBase
---@field public result number
---@field public info_list pb_LotteryBoxInfo[]
---@field public open_lottery_records pb_OpenLotteryItemRecord[]

---@return pb_CSGetBoxInfoRes
function pb.CSGetBoxInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


