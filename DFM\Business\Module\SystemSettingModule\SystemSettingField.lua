----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingField : FieldBase
local SystemSettingField = class("SystemSettingField", require"DFM.YxFramework.Managers.Module.FieldBase")


function SystemSettingField:Ctor()
    self._currentPaneluiID = nil
    self._currentPaneluiIns = nil

    self._currentPaneTab = nil
    self._currentSubPanelTab = nil
    self._settingDescTable = nil
    self._rootPanel = nil
    self._LIUIRoot = nil

    self._currentTabTypeHD = nil
    self._currentSubTabTypeHD = nil

    self._bHDEntranceUiInsAsyncLoading = false
    self._HDEntranceUiIns = nil
    self._HDSettingsUiIns = nil
    self._HDSubPanels = {}

    self._savedForMiniWorldDisplayStyle = nil

    self._descRootPanelHD = nil

    self._cachedDescPanelsHD = setmetatable({}, weakmeta_value)

    self._currentSettingItemsHD = setmetatable({}, weakmeta_value)

    self._bIsInBHDSetting = false

    self._isChangingGraphicsPrest = false

    self._cachedTeamMembers = nil

    self._trySwitchMode = nil

    self._isDescriptionPanelLocked = false

    self._bShowHistoricalRecords = true

    self.bShowRankingInformation = false

    self.bPermitStrangerPrivateChat = true

    self.bPermitStrangerApplyFriends = true

    self.bInvisible = false

    self._currentSettingItemId = nil

    self._vRampaneluiIns = nil

    self._OverheatWarningCountHD = 0

    self._bOverheatWarningPending = false

    self._OldDisplayModes = nil

    self._FetchedCommonSettingHashHD = nil

    self._pendingOverwriteTaskHD = nil

    self._fetchedKeySettingStr = nil

    -- 当前正在配置键位的干员
    self._currentHero = nil

    self._mobileRootPanel = nil
    self._volumeMusicMobile = nil
end

function SystemSettingField:SetTrySwitchMode(trySwitchMode)
    self._trySwitchMode = trySwitchMode
    Module.SystemSetting.Config.Event.evtSettingSwitchModeChanged:Invoke(trySwitchMode)
end

function SystemSettingField:GetTrySwitchMode()
    return self._trySwitchMode
end

function SystemSettingField:SetSavedForMiniWorldDisplayStyle(style)
    self._savedForMiniWorldDisplayStyle = style
end

function SystemSettingField:GetSavedForMiniWorldDisplayStyle()
    return self._savedForMiniWorldDisplayStyle
end

function SystemSettingField:SetVolumeMusic(value)
    self._volumeMusicMobile = value
end

function SystemSettingField:GetVolumeMusic()
    return self._volumeMusicMobile
end

---获取选中项的描述
function SystemSettingField:GetDetailDesc(rowName)
    if not self._settingDescTable then
        self._settingDescTable = Facade.TableManager:GetTable("UserSystemSetting")
    end
    if self._settingDescTable then
        return self._settingDescTable[rowName]
    end
end

---主面板
function SystemSettingField:SetRootPanel(rootPanel)
    self._rootPanel = makeweak(rootPanel)
end

function SystemSettingField:GetRootPanel()
    return getfromweak(self._rootPanel)
end

--BEGIN MODIFICATION @ VIRTUOS : 显示LI Pass绑定账号UI，需要全屏UI作为根组件
function SystemSettingField:SetLIUIRoot(rootPanel)
    self._LIUIRoot = makeweak(rootPanel)
end

function SystemSettingField:GetLIUIRoot()
    return getfromweak(self._LIUIRoot)
end
--END MODIFICATION

---HD描述面板
function SystemSettingField:SetDescRootPanelHD(rootPanel)
    if rootPanel then
        self._descRootPanelHD = makeweak(rootPanel)
    else
        self._descRootPanelHD = nil
    end
end

function SystemSettingField:GetDescRootPanelHD()
    return getfromweak(self._descRootPanelHD)
end

function SystemSettingField:SetCurrentSettingPanelHD(inPanel)
    if inPanel then
        self._curSettingPanelHD = makeweak(inPanel)
    else
        self._curSettingPanelHD = nil
    end
end

function SystemSettingField:GetCurrentSettingPanelHD()
    return getfromweak(self._curSettingPanelHD)
end

function SystemSettingField:SetCurrentDescHD(instanceID, descData)
    self._descInstIDHD = instanceID
    self._descDataHD = descData
end

function SystemSettingField:GetCurrentDescHD()
    return self._descInstIDHD, self._descDataHD
end

function SystemSettingField:AddDescPanelHD(type,uiIns)
    self._cachedDescPanelsHD[type] = uiIns
end

function SystemSettingField:GetDescPanelHD(type)
    return self._cachedDescPanelsHD[type]
end

function SystemSettingField:ClearDescPanelsHD()
    self._cachedDescPanelsHD = {}
end

function SystemSettingField:AddSettingItemHD(inItem)
    self._currentSettingItemsHD[inItem.ID] = inItem
end

function SystemSettingField:RemoveSettingItemHD(inItem)
    self._currentSettingItemsHD[inItem.ID] = nil
end

function SystemSettingField:GetAllSettingItemHD()
    return self._currentSettingItemsHD
end

function SystemSettingField:SetIsInBHDSetting(inBool)
    self._bIsInBHDSetting = inBool
end

function SystemSettingField:GetIsInBHDSetting()
    return self._bIsInBHDSetting
end

function SystemSettingField:SetIsDescriptionPanelLocked(inState)
    self._isDescriptionPanelLocked = inState
end

function SystemSettingField:GetIsDescriptionPanelLocked()
    return self._isDescriptionPanelLocked
end

function SystemSettingField:SetIsShowHistoricalRecords(inState)
    self._bShowHistoricalRecords = inState
end

function SystemSettingField:GetIsShowHistoricalRecords()
    return self._bShowHistoricalRecords
end

function SystemSettingField:SetIsShowRankingInformation(inState)
    self.bShowRankingInformation = inState
end

function SystemSettingField:GetIsShowRankingInformation()
    return self.bShowRankingInformation
end

function SystemSettingField:SetIsPermitStrangerPrivateChat(inState)
    self.bPermitStrangerPrivateChat = inState
end

function SystemSettingField:GetIsPermitStrangerPrivateChat()
    return self.bPermitStrangerPrivateChat
end

function SystemSettingField:SetIsPermitStrangerApplyFriends(inState)
    self.bPermitStrangerApplyFriends = inState
end

function SystemSettingField:GetIsPermitStrangerApplyFriends()
    return self.bPermitStrangerApplyFriends
end

function SystemSettingField:SetInvisibleState(inState)
    self.bInvisible = inState
end

function SystemSettingField:GetInvisibleState()
    return self.bInvisible
end

---视频设置显存预览
function SystemSettingField:SetCurrentSettingItemId(inId)
    self._currentSettingItemId = inId
end

function SystemSettingField:GetCurrentSettingItemId()
    return self._currentSettingItemId
end

function SystemSettingField:SetVramPanel(inPanelIns)
    self._vRampaneluiIns = makeweak(inPanelIns)
end

function SystemSettingField:GetVramPanel()
    return  getfromweak(self._vRampaneluiIns)
end

---设置当前选中面板的uiIns
function SystemSettingField:SetCurrentPanel(currentPaneluiID,currentPaneluiIns)
    self._currentPaneluiID = currentPaneluiID
    self._currentPaneluiIns = makeweak(currentPaneluiIns)
end

function SystemSettingField:GetCurrentPanel()
    return {uiID = self._currentPaneluiID,uiIns = getfromweak(self._currentPaneluiIns)}
end

---设置当前选中的面板页签
function SystemSettingField:SetCurrentPanelTab(inTab, inSubTab)
    self._currentPaneTab = inTab
    self._currentSubPanelTab = makeweak(inSubTab)
end

function SystemSettingField:GetCurrentPanelTab()
    return self._currentPaneTab, getfromweak(self._currentSubPanelTab)
end

--- HD设置当前选中面板页签
function SystemSettingField:SetCurrentTabTypeHD(inType)
    self._currentTabTypeHD = inType
end

function SystemSettingField:GetCurrentTabTypeHD()
    return self._currentTabTypeHD
end

function SystemSettingField:SetCurrentSubTabTypeHD(inType)
    self._currentSubTabTypeHD = inType
end

function SystemSettingField:GetCurrentSubTabTypeHD()
    return self._currentSubTabTypeHD
end

--- HD获取cached panel ins
function SystemSettingField:SetSubPanel_HD(uiIns, inTab, inSubTab)
    if not self._HDSubPanels[inTab] then
        self._HDSubPanels[inTab] = {}
    end
	inSubTab = setdefault(inSubTab, 1)
    self._HDSubPanels[inTab][inSubTab] = makeweak(uiIns)
end

function SystemSettingField:GetSubPanel_HD(inTab, inSubTab)
    -- @todo 处理缓存
    if inTab == 2 and inSubTab>=2 then
        if self._HDSubPanels[inTab] then
            return getfromweak(self._HDSubPanels[inTab][inSubTab])
        end
    end
    return nil
end

--返回需要close的界面
function SystemSettingField:GetAllPanels()
    return {getfromweak(self._currentPaneluiIns)}

end

---清除所有uiIns
function SystemSettingField:OnClearField()
    self._currentPaneluiID = nil
    self._currentPaneluiIns = nil
    self._settingDescTable = nil

    self._HDEntranceUiIns = nil
    self._HDSettingsUiIns = nil
    self._HDSubPanels = {}
    self._mobileRootPanel = nil
end

function SystemSettingField:ClearMainViewBelongings()
    self._currentPaneluiID = nil
    self._currentPaneluiIns = nil
    self._HDSettingsUiIns = nil
    self._HDSubPanels = {}
end

---DFHD Entrance面板
function SystemSettingField:SetHDEntranceUiIns(uiIns)
    self._HDEntranceUiIns = makeweak(uiIns)
end

function SystemSettingField:GetHDEntranceUiIns()
    return getfromweak(self._HDEntranceUiIns)
end

function SystemSettingField:SetHDSettingsUiIns(uiIns)
    self._HDSettingsUiIns = makeweak(uiIns)
end

function SystemSettingField:GetHDSettingsUiIns()
    return getfromweak(self._HDSettingsUiIns)
end

function SystemSettingField:SetGameRule(inGameRule)
    self._gameRule = inGameRule
end

function SystemSettingField:GetGameRule()
    return self._gameRule or MatchGameRule.SOLGameRule
end

function SystemSettingField:SetIsChangingGraphicsPresetHD(inState)
    self._isChangingGraphicsPrest = inState
end

function SystemSettingField:GetIsChangingGraphicsPresetHD()
    return self._isChangingGraphicsPrest
end

function SystemSettingField:SetCachedTeamMembers(teamMembers)
    self._cachedTeamMembers = teamMembers
end

function SystemSettingField:GetCachedTeamMembers()
    return self._cachedTeamMembers
end

function SystemSettingField:AddOverheatWarningCountHD()
    self._OverheatWarningCountHD = self._OverheatWarningCountHD + 1
end

function SystemSettingField:GetOverheatWarningCountHD()
    return self._OverheatWarningCountHD
end

function SystemSettingField:SetPendingOverheatWarningHD(bPending)
    self._bOverheatWarningPending = bPending
end

function SystemSettingField:GetPendingOverheatWarningHD()
    return self._bOverheatWarningPending
end

function SystemSettingField:SetOldDisplayModes(modes)
    self._OldDisplayModes = modes
end

function SystemSettingField:GetOldDisplayModes()
    return self._OldDisplayModes
end

function SystemSettingField:GetFetchedCommonSettingHashHD()
    return self._FetchedCommonSettingHashHD
end

function SystemSettingField:SetFetchedCommonSettingHashHD(inHash)
    self._FetchedCommonSettingHashHD = inHash
end

function SystemSettingField:GetPendingOverwriteTaskHD()
    return self._pendingOverwriteTaskHD
end

function SystemSettingField:SetPendingOverwriteTaskHD(inTask)
    self._pendingOverwriteTaskHD = inTask
end

function SystemSettingField:GetFetchedKeySettingStr()
    return self._fetchedKeySettingStr
end

function SystemSettingField:SetFetchedKeySettingStr(inTask)
    self._fetchedKeySettingStr = inTask
end

function SystemSettingField:GetCurrentHero()
    return self._currentHero
end

function SystemSettingField:SetCurrentHero(hero)
    self._currentHero = hero
end

function SystemSettingField:SetHeroDesc(uiins)
    self._curHeroDescIns = uiins
end

function SystemSettingField:GetHeroDesc()
    return self._curHeroDescIns
end

function SystemSettingField:SetPrivacyData(privacyDataList)
    self._privacyDataList = privacyDataList
end

function SystemSettingField:GetPrivacyData(privacyDataList)
    return self._privacyDataList
end

---移动端设置主面板
function SystemSettingField:SetMobileRootPanel(rootPanel)
    self._mobileRootPanel = makeweak(rootPanel)
end

function SystemSettingField:GetMobileRootPanel()
    return getfromweak(self._mobileRootPanel)
end

--- 虚拟机销毁时
function SystemSettingField:OnDestroyField()
end

function SystemSettingField:JumpBuyRecharge(isBool)
    if isBool == nil then
        local isBool = self._isBool
        self._isBool = nil
        return isBool
    end
    self._isBool = isBool
end

function SystemSettingField:SetHDDefaultTab(openTabType)
    self._openTabType = openTabType
end

function SystemSettingField:GetHDDefaultTab()
    return self._openTabType
end

return SystemSettingField
