----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerLayer)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class DefaultLayerController : Object
local DefaultLayerController = class("DefaultLayerController", Object)

function DefaultLayerController:Ctor()
    self._mapLayer2UIList = {}
end

function DefaultLayerController:Destroy()
    self._mapLayer2UIList = {}
end

function DefaultLayerController:_InternalResetLayerUIList(layerUIList)
    local bImmediate = true
    local bLuaPendingKill = true
    if layerUIList and next(layerUIList) then
        local length = #layerUIList
        for i = -length, -1 do
            local order = - i
            local layerUI = layerUIList[order]
            if not hasdestroy(layerUI) then
                -- logframe("[ GameFlow Debug ] ******* Lua Clean All UI _InternalResetLayerUIList-------------", self._cname, " Reset FinalCloseUI", layerUI._cname)
                Facade.UIManager:FinalCloseUI(layerUI, bImmediate, bLuaPendingKill)
            end
        end
    end
end

function DefaultLayerController:Reset()
    local bImmediate = true
    for layerType, layerUIList in pairs(self._mapLayer2UIList) do
        self:_InternalResetLayerUIList(layerUIList)
        -- self._mapLayer2UIList[layerType] = {}
    end
    self._mapLayer2UIList = {}
end

--------------------------------------------------------------------------
--- operation
--------------------------------------------------------------------------
function DefaultLayerController:AddUIByLayerType(uiIns, layerType, ...)
    if layerType then
        local layerList = self:GetLayerList(layerType)
        layerList[#layerList + 1] = uiIns

        --- 为非强管理ZOrder的层级增加定义ZOrderOffset的能力
        local uiNavId = uiIns.UINavID
        local uiSettings = UITable[uiNavId]
        local customZOrderOffset = uiSettings and uiSettings.ZOrderOffset or 0
        local zOrder = UILayer2ZOrder[layerType] + #layerList + customZOrderOffset
        
        if uiIns.InitExtraData then
            trycall(uiIns.InitExtraData, uiIns, ...)
        end

        if not uiIns:GetParent() and not uiIns:IsInViewport() then
            Facade.UIManager:AddToFrameRoot(uiIns, zOrder, layerType)
        else
            uiIns:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            uiIns:Show()
            Facade.UIManager:SetZOrderInFrameRoot(uiIns, zOrder)
            logframe('[ DefaultLayerController ] ==================  已经创建过的', uiIns.UINavID, zOrder)
        end
    end
    return uiIns
end

---@param uiIns LuaUIBaseView
function DefaultLayerController:RemoveUIByLayerType(uiIns, layerType)
    self:CloseOrRecycle(uiIns, nil, layerType)
end

---@param uiIns LuaUIBaseView
function DefaultLayerController:CloseOrRecycle(uiIns, bImmediate, layerType)
    --- 无对象池原逻辑
    -- Facade.UIManager:FinalCloseUI(uiIns)
    -- local layerList = self:GetLayerList(layerType)
    -- table.removebyvalue(layerList, uiIns)

    Facade.UIManager:CloseUIObjectByIns(uiIns, bImmediate)
end

function DefaultLayerController:InternalRemoveUI(uiIns, layerType)
    local layerList = self:GetLayerList(layerType)
    table.removebyvalue(layerList, uiIns)
end

--------------------------------------------------------------------------
--- default
--------------------------------------------------------------------------
function DefaultLayerController:GetTopZOrder(layerType)
    local layerList = self:GetLayerList(layerType)
    local zOrder = UILayer2ZOrder[layerType] + #layerList
    return zOrder
end

function DefaultLayerController:GetCurrentViewInLayer(layerType)
    layerType = layerType or EUILayer.Stack
    local layerList = self:GetLayerList(layerType)
    if layerList then
        return layerList[#layerList]
    end
end

function DefaultLayerController:GetCurrentViewIdInLayer(layerType)
    local curView = self:GetCurrentViewInLayer(layerType)
    if curView then
        return curView.UINavID
    end
    return nil
end

--------------------------------------------------------------------------
--- Debug
--------------------------------------------------------------------------
local cacheGroupStack = {}
local function GroupStackIndex(t, k)
    return cacheGroupStack[t][k]
end

local function GroupStackNewIndex(t, k, v)
    -- rawset(t, k, v)
    if cacheGroupStack[t] == nil then
        cacheGroupStack[t] = {}
    end
    local cacheTab = cacheGroupStack[t]

    if v == nil then
        print("Delete View: layerType:", t.layerType, "Name:", cacheTab[k]._cname,  "View:", t)
    else
        print("Add    View: layerType:", t.layerType, "Name:", v._cname,  "View:", t)
    end
    cacheTab[k] = v
    print("      Group table length:", #cacheTab, "table dump:")
    for ck, cv in pairs(cacheTab) do
        print("            >>>>>", ck, cv._cname)
    end
end

local function GroupStackLen(t)
    if cacheGroupStack[t] == nil then
        cacheGroupStack[t] = {}
    end
    return #cacheGroupStack[t]
end

function DefaultLayerController:GetLayerList(layerType)
    if not self._mapLayer2UIList[layerType] then
        -- local metaTable = {
        --     __mode = "kv",
        --     __index = GroupStackIndex,
        --     __newindex = GroupStackNewIndex,
        --     __len = GroupStackLen,
        -- }
        -- self._mapLayer2UIList[layerType] = setmetatable({}, metaTable)
        -- rawset(self._mapLayer2UIList[layerType], "layerType", layerType)
        self._mapLayer2UIList[layerType] = setmetatable({}, weakmeta)
    end
    return self._mapLayer2UIList[layerType]
end


--- 慎用，可能过于大量
function DefaultLayerController:TryGetUIInsByNavID(uiNavId, bIncludeDeactive)
    bIncludeDeactive = setdefault(bIncludeDeactive, false)
    local uiSettings = UITable[uiNavId]
    if uiSettings then
        local layerType = uiSettings.UILayer
        if layerType then
            local layerUIList = self:GetLayerList(layerType)
            if layerUIList and next(layerUIList) then
                for idx, layerUI in ipairs(layerUIList) do
                    if not hasdestroy(layerUI) and layerUI.UINavID == uiNavId then
                        if bIncludeDeactive and not rawget(layerUI, "_is_enable_") then
                            return layerUI
                        else
                            if rawget(layerUI, "_is_enable_") then
                                return layerUI
                            end
                        end
                    end
                end
            else
                return nil
            end
        end
    end
end

return DefaultLayerController
