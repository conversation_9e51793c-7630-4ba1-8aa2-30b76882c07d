---@class BlackSiteProductionLine
BlackSiteProductionLine = class("BlackSiteProductionLine", LuaObject)

function BlackSiteProductionLine:Ctor(data)
    self:ReTime()
    self:UpdateData(data)
end

function BlackSiteProductionLine:UpdateData(data)
    self.productList = {}
    self._realProductList = {}
    local splitProductList = string.split(data.ProductList, ";")
    for _, value in pairs(splitProductList) do
        if not string.isempty(value) then
            local splitValue = string.split(value, ":")
            table.insert(self.productList, {Id = tonumber(splitValue[1]), Num = tonumber(splitValue[2])})
        end
    end

    self.time = data.Time
    self.formulaId = data.Id
    self.defaultSort = data.DefaultSort or 0
    self.deviceId = data.DeviceId
    self.level = data.Level
    self.replacedId = data.ReplacedId
    self.startTimeLimit, self.startTimeLimitString = self:_CalculateTimestamp(data.StartTime)
    self.endTimeLimit, self.endTimeLimitString = self:_CalculateTimestamp(data.EndTime)
    self.materialList = {}
    local splitMaterialList = string.split(data.MaterialList, ";")
    for _, value in ipairs(splitMaterialList) do
        if not string.isempty(value) then
            local splitValue = string.split(value, ":")
            table.insert(self.materialList, {Id = tonumber(splitValue[1]), Num = tonumber(splitValue[2])})
        end
    end

    self:AttemptToCreateATimertoProductionCompletion()
end

function BlackSiteProductionLine:UpdateProto(proto)
    local maxTime = Facade.ClockManager:GetLocalTimestamp() + self.time
    self.startTime = proto.start_time
    self.endTime = maxTime < proto.end_time and maxTime or proto.end_time

    self._realProductList = {}

    for _, value in ipairs(proto.products) do
        table.insert(self._realProductList, {Id = value.prop_id, Num = value.num})
    end

    self:AttemptToCreateATimertoProductionCompletion()
end

function BlackSiteProductionLine:ReTime()
    self.startTime = 0
    self.endTime = 0
    self._realProductList = {}
end

function BlackSiteProductionLine:GetProdeceLineState()
    self.serverTimestamp = Facade.ClockManager:GetLocalTimestamp()
    return self.endTime == 0 and BlackSiteDefine.EBlackSiteProduceLineState.Normal or (self.endTime < self.serverTimestamp and BlackSiteDefine.EBlackSiteProduceLineState.Complete or BlackSiteDefine.EBlackSiteProduceLineState.Producing)
end

function BlackSiteProductionLine:GetProductList()
    return #self._realProductList == 0 and self.productList or self._realProductList
end

function BlackSiteProductionLine:GetRemainTime()
    self.remainTime = self.endTime - self.serverTimestamp
    return self.remainTime
end

function BlackSiteProductionLine:GetSchedule()
    return 1 - self.remainTime / self.time
end

function BlackSiteProductionLine:GetFormulaId()
    return self.formulaId
end

function BlackSiteProductionLine:GetMaterialList()
    return self.materialList
end

function BlackSiteProductionLine:CheckIsOccupy()
    return Server.BlackSiteServer:GetDeviceData(self.deviceId):GetProduceLine()
end

function BlackSiteProductionLine:GetTime()
    return self.time
end

function BlackSiteProductionLine:GetReplacedId()
    return self.replacedId or 0
end

function BlackSiteProductionLine:ProduceReq()
    Server.BlackSiteServer:BlackSiteProduceReq(
        self.deviceId,
        self.formulaId,
        function(res)
            self:ReleaseTimer()
            if res ~= 0 then
                if self:CheckWithinTheTimeFrame() then
                    Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteReqFail)
                else
                    Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteNotWithinTheTimeFrameTip)
                    Module.BlackSite.Config.Events.evtBlackSiteNotWithinTheTimeFrame:Invoke()
                end
            end
        end
    )
    self:RegisterTimer()
end

function BlackSiteProductionLine:ReqFail()
    Server.BlackSiteServer:GetDeviceData(self.deviceId):ReqAndFireDeviceUpgradeSuccessEvent()
end

function BlackSiteProductionLine:ReceiveAward()
    Server.BlackSiteServer:GetDeviceData(self.deviceId):ReceiveAward()
end

function BlackSiteProductionLine:StopProduce()
    Server.BlackSiteServer:BlackSiteProduceTerminateReq(
        self.deviceId,
        function(res)
            self:ReleaseTimer()
            if res == 0 then
                Module.BlackSite.Config.Events.evtBlackSiteStopProduce:Invoke()
                Module.CommonTips:ShowSimpleTip(string.format(Module.BlackSite.Config.Loc.BlackSiteStopProduce, Module.BlackSite.Field:GetItemBase(self.productList[1].Id).name))
            else
                Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteReqFail)
            end
        end
    )
    self:RegisterTimer()
end

function BlackSiteProductionLine:ReleaseTimer()
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
end

function BlackSiteProductionLine:RegisterTimer()
    self.tickHandle = Timer:NewIns(2)
    self.tickHandle:AddListener(self.ReqFail, self)
    self.tickHandle:Start()
end

function BlackSiteProductionLine:IsMaterialEnough()
    for _, material in pairs(self.materialList) do
        if Server.InventoryServer:GetItemNumById(material.Id) < material.Num then
            return false
        end
    end
    return true
end

function BlackSiteProductionLine:GetLevel()
    return self.level
end

function BlackSiteProductionLine:AttemptToCreateATimertoProductionCompletion()
    self:ReleaseProductionCompletionTimer()

    local prodeceLineState = self:GetProdeceLineState()

    if prodeceLineState == BlackSiteDefine.EBlackSiteProduceLineState.Producing then
        self:CreateProductionCompletionTimer()
    elseif prodeceLineState == BlackSiteDefine.EBlackSiteProduceLineState.Complete then
        Server.BlackSiteServer.Events.evtBlackSiteProductionLineStatusChangeNotificationRedDotUpdate:Invoke()
    end
end

function BlackSiteProductionLine:CreateProductionCompletionTimer()
    self._productionCompletionTimerHandle = Timer:NewIns(self:GetRemainTime())
    self._productionCompletionTimerHandle:AddListener(self.AttemptToCreateATimertoProductionCompletion, self)
    self._productionCompletionTimerHandle:Start()
end

function BlackSiteProductionLine:ReleaseProductionCompletionTimer()
    if self._productionCompletionTimerHandle then
        self._productionCompletionTimerHandle:Release()
        self._productionCompletionTimerHandle = nil
    end
end

function BlackSiteProductionLine:GetDeviceId()
    return self.deviceId
end

function BlackSiteProductionLine:OnClose()
    self:_ReleaseProductionCompletionTimer()
end

function BlackSiteProductionLine:_CalculateTimestamp(time)
    if not time then
        return 0
    end

    if type(time) == "string" then
        if time == "" or time == "-1" then
            return 0
        end

        local _, timestamp = TimeUtil.GetTimeStampFromString(time)

        if timestamp == 0 then
            return 0
        end

        return timestamp, TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(timestamp)
    end

    if time == 0 then
        return 0
    end

    return time, TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(time)
end

function BlackSiteProductionLine:CheckIsLimitedTimeProduction()
    return self.startTimeLimit ~= 0 or self.endTimeLimit ~= 0
end

function BlackSiteProductionLine:GetStartTimeLimitString()
    return self.startTimeLimitString or Module.BlackSite.Config.Loc.BlackSiteNoLimit
end

function BlackSiteProductionLine:GetEndTimeLimitString()
    return self.endTimeLimitString or Module.BlackSite.Config.Loc.BlackSiteNoLimit
end

function BlackSiteProductionLine:CheckWithinTheTimeFrame()
    local localTimestamp = Facade.ClockManager:GetLocalTimestamp()
    return self.startTimeLimit <= localTimestamp and (self.endTimeLimit == 0 or localTimestamp <= self.endTimeLimit)
end

return BlackSiteProductionLine