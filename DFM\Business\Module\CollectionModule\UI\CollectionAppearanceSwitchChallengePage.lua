----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionAppearanceSwitchChallengePage = ui("CollectionAppearanceSwitchChallengePage")
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local ItemDetailTitleComp = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailTitleComp"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

function CollectionAppearanceSwitchChallengePage:Ctor()
    self._wtItemDetailTitle = self:Wnd("wtItemDetailTitle", ItemDetailTitleComp)
    self._wtItemDetailTitle:SetDetailBtnVisible(false)
    self._wtCamouflageWaterFallBox = UIUtil.WndWaterfallScrollBox(self, "wtCamouflageWaterFallBox", self._OnGetCamouflageCount, self._OnProcessCamouflageItemWidget)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase) 
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase) 
    self._wtActionBtn = self:Wnd("wtActionBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnActionBtnClicked, self)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarTitle(self, CollectionConfig.Loc.CamouflageChallenge)
    self._selectedCamouflageIndex = -1
    self._selectedCell = nil
    self._camouflageInfoList = {}
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end


function CollectionAppearanceSwitchChallengePage:OnInitExtraData(bIsMasterChallenge, baseWeaponId)
    self._bIsMasterChallenge = bIsMasterChallenge or false
    self._baseWeaponId = baseWeaponId or 0
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionAppearanceSwitchChallengePage:OnOpen()
    self:_AddListeners()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionAppearanceSwitchChallengePage:OnClose()
    self:RemoveAllLuaEvent()
    self._selectedCamouflageSkinItem = nil
    self._selectedCell = nil
    self._selectedCamouflageIndex = -1
    self._bIsMasterChallenge = false
    self._baseWeaponId = 0
    self._camouflageInfoList = {}
    self._bIsActivated = false
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
    local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
    local taskIds = {}
    for taskId, value in pairs(newArrivedTaskRedDotsMap) do
        if self._bIsMasterChallenge then
            if taskId >= 20000 and value == true then
                table.insert(taskIds, taskId)                
            end
        else
            if taskId < 20000 and value == true then
                table.insert(taskIds, taskId)                
            end
        end
    end
    if #taskIds > 0 then
        CollectionLogic.RemoveRedDots(nil, taskIds)
    end
end

function CollectionAppearanceSwitchChallengePage:OnActivate()
    self:_AddListeners()
end


function CollectionAppearanceSwitchChallengePage:OnDeactivate()
    self:RemoveAllLuaEvent()
    self._selectedCamouflageSkinItem = nil
    self._selectedCell = nil
    self._selectedCamouflageIndex = -1
    self._bIsMasterChallenge = false
    self._baseWeaponId = 0
    self._camouflageInfoList = {}
    self._bIsActivated = false
    local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
    local taskIds = {}
    for taskId, value in pairs(newArrivedTaskRedDotsMap) do
        if self._bIsMasterChallenge then
            if taskId >= 20000 and value == true then
                table.insert(taskIds, taskId)                
            end
        else
            if taskId < 20000 and value == true then
                table.insert(taskIds, taskId)                
            end
        end
    end
    if #taskIds > 0 then
        CollectionLogic.RemoveRedDots(nil, taskIds)
    end
end

function CollectionAppearanceSwitchChallengePage:OnShowBegin()
    if not self._bIsActivated then
        self:_RefreshView()
        self._bIsActivated = true
    elseif Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallCollectionNew then
        self:_OnRefreshModel(ESubStage.HallCollectionNew)
    end
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:_EnableGamepadFeature()
end

function CollectionAppearanceSwitchChallengePage:OnHideBegin()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionAppearanceSwitchChallengePage:OnShow()
    if not self._camouflageListcrollStopHandle then
        self._camouflageListcrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtCamouflageWaterFallBox, self)  
    end
end

function CollectionAppearanceSwitchChallengePage:OnHide()
    if self._camouflageListcrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._camouflageListcrollStopHandle)
		self._camouflageListcrollStopHandle = nil
	end
end

function CollectionAppearanceSwitchChallengePage:OnAnimFinished(anim)
    if anim == self.WBP_Collections_SwitchChallenge_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionAppearanceSwitchChallengePage:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtActionBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    self:_AddInputActionForActionBtn()
    if not self._wtNavGroupSkinList then 
        self._wtNavGroupSkinList = WidgetUtil.RegisterNavigationGroup(self._wtCamouflageWaterFallBox, self, "Hittest")
        if self._wtNavGroupSkinList then
            self._wtNavGroupSkinList:AddNavWidgetToArray(self._wtCamouflageWaterFallBox)
            self._wtNavGroupSkinList:SetScrollRecipient(self._wtCamouflageWaterFallBox)
            self._wtNavGroupSkinList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionAppearanceSwitchChallengePage:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RemoveInputActionForActionBtn()
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._wtNavGroupSkinList = nil
    self._wtActionBtnHandle  = nil 
    self._NavConfigHandler = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionAppearanceSwitchChallengePage:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupSkinList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupSkinList)
    end
end

function CollectionAppearanceSwitchChallengePage:_AddInputActionForActionBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._bEnableActionBtn and not self._wtActionBtnHandle then
        self._wtActionBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnActionBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionAppearanceSwitchChallengePage:_RemoveInputActionForActionBtn()
    if  self._wtActionBtnHandle then
        self:RemoveInputActionBinding(self._wtActionBtnHandle)
        self._wtActionBtnHandle = nil 
    end
end

function CollectionAppearanceSwitchChallengePage:_AddListeners()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnRefreshModel, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._RefreshView, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionActivateSkinPattern, self._OnActivateSkinPattern, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
end

function CollectionAppearanceSwitchChallengePage:_RefreshView()
    self:_ShowUI()
    self:_OnRefreshCamouflageItems()
    self._shotcutList = {}
    if IsHD() then
        if self._bHideUI then
            table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
end

function CollectionAppearanceSwitchChallengePage:_OnRefreshCamouflageItems()
    local camouflageStatusInfo = Server.CollectionServer:GetAllCamouflageStatusInfo()
    self._camouflageInfoList = {}
    if self._bIsMasterChallenge then
        self._activatedCamouflageId = camouflageStatusInfo.activatedMasterCamouflageId
        for patternId, camouflagetaskInfo in pairs(camouflageStatusInfo.masterCamouflages) do
            table.insert(self._camouflageInfoList, camouflagetaskInfo)
        end
    else
        self._activatedCamouflageId = camouflageStatusInfo.activatedCamouflageId
        for patternId, camouflagetaskInfo in pairs(camouflageStatusInfo.camouflages) do
            table.insert(self._camouflageInfoList, camouflagetaskInfo)
        end
    end
    self:_UpdateSeasonGroups()
    self._selectedCamouflageSkinItem = nil
    self._selectedCell = nil
    self._selectedCamouflageIndex = -1
    if self._camouflageInfoList and self._camouflageInfoList[1] then
        self._selectedCamouflageIndex = 1
        if isvalid(self._camouflageInfoList[1].skinItems[1]) then
            self._selectedCamouflageSkinItem = self._camouflageInfoList[1].skinItems[1]
            if self._baseWeaponId > 0 then
                for index, skinItem in ipairs(self._camouflageInfoList[1].skinItems) do
                    if CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id) == self._baseWeaponId then
                        self._selectedCamouflageSkinItem = skinItem
                        break
                    end
                end
            end
        end
        local patternId = self._camouflageInfoList[1].patternId
        if Server.CollectionServer:IsTaskWithRedDot(patternId, true) then
            Timer.DelayCall(1, function ()
                CollectionLogic.RemoveRedDots(nil, {patternId})
            end, self)
        end
        self._wtCamouflageWaterFallBox:RefreshAllItems()
    else
        self._wtCamouflageWaterFallBox:RefreshVisibleItems()
    end
    self:_RefreshItemUI()
end

function CollectionAppearanceSwitchChallengePage:_OnGetCamouflageCount()
    return #self._camouflageInfoList or 0
end

function CollectionAppearanceSwitchChallengePage:_OnProcessCamouflageItemWidget(position, camouflageItemWidget)
    local camouflageIndex = position
    camouflageItemWidget:SetSize(548, 254)
    camouflageItemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    camouflageItemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    camouflageItemWidget:SetButtonEnable(not IsHD() or not WidgetUtil.IsGamepad())
    local camouflageInfo = self._camouflageInfoList[camouflageIndex]
    if camouflageInfo then
        camouflageItemWidget:BindClickCallback(CreateCallBack(self._OnCamouflageItemClick, self,camouflageItemWidget, position))
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            return Server.CollectionServer:IsTaskWithRedDot(camouflageInfo.patternId)
        end,self)
        if isvalid(self._redDotInsMap[camouflageItemWidget]) and self._redDotInsMap[camouflageItemWidget]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(camouflageItemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[camouflageItemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[camouflageItemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,camouflageItemWidget, {EReddotType.Normal})
        end
        if self._bIsMasterChallenge then
            camouflageItemWidget:InitCollectionCamouflageItem(
                    camouflageInfo,
                    true,
                    CollectionLogic.IsInMp() and camouflageInfo.MP_TaskInfo.finishedTaskNum or camouflageInfo.SOL_TaskInfo.finishedTaskNum,
                    CollectionLogic.IsInMp() and camouflageInfo.MP_TaskInfo.totalTaskNum or camouflageInfo.SOL_TaskInfo.totalTaskNum,
                    camouflageInfo.patternId == self._activatedCamouflageId)
        else
            camouflageItemWidget:InitCollectionCamouflageItem(
                    camouflageInfo,
                    false,
                    camouflageInfo.unlockedNum,
                    #camouflageInfo.skinItems,
                    camouflageInfo.patternId == self._activatedCamouflageId)
        end
        if self._seasonGroupTitleMap[position] then
            camouflageItemWidget:EnableTitle(true, self._seasonGroupTitleMap[position])
        else
            camouflageItemWidget:EnableTitle(false)
        end
    else
        camouflageItemWidget:EnableTitle(false)
    end
    camouflageItemWidget:SetSelected(self._selectedCamouflageIndex == camouflageIndex)
    if self._selectedCamouflageIndex == camouflageIndex then
        self._selectedCell = camouflageItemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionAppearanceSwitchChallengePage:_OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallCollectionNew then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
        if isvalid(self._selectedCamouflageSkinItem) then
            local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(self._selectedCamouflageSkinItem.id)
            if weaponDesc == nil then
                weaponDesc = self._selectedCamouflageSkinItem:GetRawDescObj()
            end
            if isvalid(weaponDesc) then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, false)
            end
        end
    end
end

function CollectionAppearanceSwitchChallengePage:_OnCamouflageItemClick(camouflageItemWidget, camouflageIndex)
    if self._selectedCamouflageIndex ~= camouflageIndex then
        if self._selectedCell then
            self._selectedCell:SetSelected(false)
            if IsHD() and WidgetUtil.IsGamepad() then 
                self._selectedCell:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
            end
        end
        self._selectedCell = camouflageItemWidget
        self._selectedCamouflageIndex = camouflageIndex
        self._selectedCell:SetCppValue("bHandleClick", false)
        self._selectedCell:SetSelected(true)
        local camouflageInfo = self._camouflageInfoList[self._selectedCamouflageIndex]
        self._selectedCamouflageSkinItem = nil
        if isvalid(camouflageInfo) and isvalid(camouflageInfo.skinItems[1]) then
            self._selectedCamouflageSkinItem = camouflageInfo.skinItems[1]
            if self._baseWeaponId > 0 then
                for index, skinItem in ipairs(camouflageInfo.skinItems) do
                    if CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id) == self._baseWeaponId then
                        self._selectedCamouflageSkinItem = skinItem
                        break
                    end
                end
            end
            if Server.CollectionServer:IsTaskWithRedDot(camouflageInfo.patternId, true) then
                CollectionLogic.RemoveRedDots(nil, {camouflageInfo.patternId})
            end
        end
        self:_RefreshItemUI()
    end
end

function CollectionAppearanceSwitchChallengePage:_RefreshItemUI()
    local camouflageInfo = self._camouflageInfoList[self._selectedCamouflageIndex]
    if isvalid(camouflageInfo) then
        local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", camouflageInfo.patternId)
        self._wtItemDetailTitle:SetInfo(
            camouflageDataRow ~= nil and camouflageDataRow.SkinName or "", 
            camouflageDataRow ~= nil and camouflageDataRow.SkinDesc or "", 
            camouflageDataRow ~= nil and camouflageDataRow.SkinQuality or 1
        )
        self._wtItemDetailTitle:SelfHitTestInvisible()
        local bIsActivated = Server.CollectionServer:CheckIsActivatedCamouflageById(camouflageInfo.patternId)
        self._wtActionBtn:SetMainTitle(bIsActivated and CollectionConfig.Loc.Activated or CollectionConfig.Loc.Activate)
        self._wtActionBtn:SetIsEnabled(not bIsActivated)
        self._wtActionBtn:Visible()
        self._wtAlertHintBox:SelfHitTestInvisible()
        self._bEnableActionBtn = true
    else
        self._wtItemDetailTitle:Collapsed()
        self._wtActionBtn:Collapsed()
        self._wtAlertHintBox:Collapsed()
        self._bEnableActionBtn = false
    end
    if self._bEnableActionBtn then
        self:_AddInputActionForActionBtn()
    else
        self:_RemoveInputActionForActionBtn()
    end
    self:_RefreshDownloadBtn()
    self:_OnRefreshModel(ESubStage.HallCollectionNew)

    if IsHD() then
        self:_DownLoadResources()
    end
end

function CollectionAppearanceSwitchChallengePage:_CheckIsFirstIndexOfCamouflageGroup(index)
    if self._camouflageInfoList and self._camouflageInfoList[index] then
        if self._camouflageInfoList[index-1] then
            if self._camouflageInfoList[index].seasonId ~= self._camouflageInfoList[index-1].seasonId
            and self._camouflageInfoList[index-1].seasonId == Server.BattlePassServer:GetSeasonID() then
                return true
            end
        else
            return true
        end
    end
    return false
end

function CollectionAppearanceSwitchChallengePage:_UpdateSeasonGroups()
    table.sort(self._camouflageInfoList, CollectionLogic.CamouflageSort)
    self._seasonGroupTitleMap = {}
    for index, camouflageInfo in ipairs(self._camouflageInfoList) do
        if self:_CheckIsFirstIndexOfCamouflageGroup(index) then
            if camouflageInfo.seasonId == Server.BattlePassServer:GetSeasonID() then
                self._seasonGroupTitleMap[index] = CollectionConfig.Loc.CurrentSeason
            else
                self._seasonGroupTitleMap[index] = CollectionConfig.Loc.PreviousSeason
            end
        end
    end
end

function CollectionAppearanceSwitchChallengePage:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._selectedCamouflageSkinItem and self._selectedCamouflageSkinItem.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:Visible()
        logerror("[v_dzhanshen] CollectionAppearanceSwitchChallengePage:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionAppearanceSwitchChallengePage:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._selectedCamouflageSkinItem and self._selectedCamouflageSkinItem.id or nil)
        logerror("[v_dzhanshen] CollectionAppearanceSwitchChallengePage:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionAppearanceSwitchChallengePage:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._selectedCamouflageSkinItem and self._selectedCamouflageSkinItem.id or nil)
    logerror("[v_dzhanshen] CollectionAppearanceSwitchChallengePage:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtWeaponSkinGridBox:RefreshVisibleItems()
        self:_OnRefreshModel(ESubStage.HallCollectionNew)
        self:_RefreshDownloadBtn()
    end
end

function CollectionAppearanceSwitchChallengePage:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionAppearanceSwitchChallengePage:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(self._selectedCamouflageSkinItem and self._selectedCamouflageSkinItem.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionAppearanceSwitchChallengePage:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(self._selectedCamouflageSkinItem) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function CollectionAppearanceSwitchChallengePage:_OnActionBtnClicked()
    local camouflageInfo = self._camouflageInfoList[self._selectedCamouflageIndex]
    if isvalid(camouflageInfo) then
        local bIsFinished
        if self._bIsMasterChallenge then
            bIsFinished = #camouflageInfo.MP_TaskInfo.tasks > 0 and camouflageInfo.MP_TaskInfo.finishedTaskNum >= #camouflageInfo.MP_TaskInfo.tasks
                    or #camouflageInfo.SOL_TaskInfo.tasks > 0 and camouflageInfo.SOL_TaskInfo.finishedTaskNum >= #camouflageInfo.SOL_TaskInfo.tasks
        else
            bIsFinished = table.nums(camouflageInfo.skinTasks) > 0 and camouflageInfo.unlockedNum >= table.nums(camouflageInfo.skinTasks)
        end
        if bIsFinished then
            local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", camouflageInfo.patternId)
            Module.CommonTips:ShowConfirmWindow(
                    StringUtil.Key2StrFormat(CollectionConfig.Loc.CamouflageChallengeReActivateTip,
                            {["patternName"] = camouflageDataRow ~= nil and camouflageDataRow.SkinName or CollectionConfig.Loc.Unknown}),
                    function()
                        Server.CollectionServer:ActivateSkinPattern(camouflageInfo.patternId)
                    end,
                    function()

                    end,
                    CollectionConfig.Loc.Cancel,
                    CollectionConfig.Loc.Confirm
            )
        else
            Server.CollectionServer:ActivateSkinPattern(camouflageInfo.patternId)
        end
    end
end

function CollectionAppearanceSwitchChallengePage:_OnActivateSkinPattern(patternId)
    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.Activated)
    self:_OnRefreshCamouflageItems()
end

function CollectionAppearanceSwitchChallengePage:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    else
        self:_HideUI()
    end
end

function CollectionAppearanceSwitchChallengePage:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionAppearanceSwitchChallengePage:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end

function CollectionAppearanceSwitchChallengePage:_OnInputTypeChanged(inputType)
    self._wtCamouflageWaterFallBox:RefreshVisibleItems()
end

function CollectionAppearanceSwitchChallengePage:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end

function CollectionAppearanceSwitchChallengePage:_GetSkinItem(groupIndex, skinIndex)
    local group = self._camouflageInfoList[groupIndex]
    if group then
        return group[skinIndex]
    end
    return nil
end

return CollectionAppearanceSwitchChallengePage
