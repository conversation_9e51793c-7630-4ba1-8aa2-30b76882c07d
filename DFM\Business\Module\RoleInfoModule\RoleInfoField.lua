----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RoleInfoField : FieldBase
local RoleInfoField = class("RoleInfoField", require "DFM.YxFramework.Managers.Module.FieldBase")

function RoleInfoField:Ctor()
    self.curSeletctCard = nil
    self._simpleTips = nil
    self._achievementListIndex = -1
    self._achievementId = -1
    self._socialBadgeId = -1
    self._socialHeroId = -1
    self._socialHeroSyncIds = {}
    self._OpenAchievementList = {}
end

function RoleInfoField:GetSortedListByCardID(list)
    local sortedList = {}
    for _, Row in pairs(list) do
        table.insert(sortedList, Row)
    end
    table.sort(
        sortedList,
        function(a, b)
            return a.card_id < b.card_id
        end
    )
    return sortedList
end

function RoleInfoField:SetCurSeletctCard(card)
    self.curSeletctCard = card
end

function RoleInfoField:SortCardByLock(cardList)
    local result = {}
    local UnLockList = {}
    local lockList = {}
    for k, card in pairs(cardList) do
        if card.is_unlock then --没被锁
            table.insert(UnLockList, card)
        else
            table.insert(lockList, card)
        end
    end
    for k, card in pairs(UnLockList) do
        table.insert(result, card)
    end
    for k, card in pairs(lockList) do
        table.insert(result, card)
    end
    return result
end

function RoleInfoField:SortCardByTime(cardList)
    local sortedList = {}
    for _, Row in pairs(cardList) do
        table.insert(sortedList, Row)
    end
    table.sort(
        sortedList,
        function(a, b)
            return a.unlock_time < b.unlock_time
        end
    )
    return sortedList
end

function RoleInfoField:GetCurSeletctCard()
    return self.curSeletctCard
end

function RoleInfoField:SetSimpleHandle(handle)
    if self._simpleTips then
        Facade.UIManager:CloseUIByHandle(self._simpleTips)
    end
    self._simpleTips = handle
end

function RoleInfoField:SetAcheievementIndex(index)
    if self._achievementListIndex ~= index then
        self._achievementListIndex = index
    end
    Module.RoleInfo.Config.Event.evtAchievementListSelectChange:Invoke()
end

function RoleInfoField:GetAcheievementIndex()
    return self._achievementListIndex
end

function RoleInfoField:SetAcheievementId(id)
    if self._achievementId == id then
        self._achievementId = -1
    else
        self._achievementId = id
    end
    Module.RoleInfo.Config.Event.evtAchievementSelectChange:Invoke()
end

function RoleInfoField:GetAcheievementId()
    return self._achievementId
end

function RoleInfoField:SetSocialBadgeId(id)
    if self._socialBadgeId == id then
        return
    else
        self._socialBadgeId = id
    end
    Module.RoleInfo.Config.Event.evtSocialBadgeSelectChange:Invoke()
end

function RoleInfoField:GetSocialBadgeId()
    return self._socialBadgeId
end

function RoleInfoField:SetSocialHeroId(id)
    if self._socialHeroId == id then
        return
    else
        self._socialHeroId = id
    end
    Module.RoleInfo.Config.Event.evtSocialBadgeHeroChange:Invoke()
end

function RoleInfoField:GetSocialHeroId()
    return self._socialHeroId
end

function RoleInfoField:GetSocialHeroSyncIds()
    return self._socialHeroSyncIds
end

function RoleInfoField:GetOpenAchievementList()
    return self._OpenAchievementList -- 展开的列表
end

return RoleInfoField
