----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RoleInfoModule : ModuleBase
local RoleInfoModule = class("RoleInfoModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local RoleInfoField  = require("DFM.Business.Module.RoleInfoModule.RoleInfoField")

local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local RoleInfoLoadLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLoadLogic"
local matchModeConfig = Facade.TableManager:GetTable("MatchModeDataConfig")
local LoginConfig = require "DFM.Business.Module.LoginModule.LoginConfig"
local RoleInfoConfig = require "DFM.Business.Module.RoleInfoModule.RoleInfoConfig"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"


local SeasonTable = Facade.TableManager:GetTable("SeasonLevel")
local SocialTables = Facade.TableManager:GetTable("SocialAvatarDataTable")

local UIManager = Facade.UIManager
local function xxww_(...)
    loginfo("[xxww] ", ...)
end

function RoleInfoModule:OnInitModule()
    self._maxSeasonLevel = 0
    for key, value in pairs(SeasonTable) do
        self._maxSeasonLevel = math.max(self._maxSeasonLevel, tonumber(key))
    end
    self:AddLuaEvent(LoginConfig.Events.evtOnRegisterFinish, self.OnPlayerRename, self)
    self:AddLuaEvent(Server.HeroServer.Events.evtUnlockBadge, self.ShowNewBadgeGetPop, self)
end

---loadBPRes、TextureRes、private table
function RoleInfoModule:OnLoadModule()
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function RoleInfoModule:OnLoadingLogin2Frontend(gameFlowType)
    RoleInfoLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function RoleInfoModule:OnLoadingGame2Frontend(gameFlowType)
    RoleInfoLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function RoleInfoModule:OnLoadingFrontend2Game(gameFlowType)
    RoleInfoLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

---@param gameFlowType EGameFlowStageType
function RoleInfoModule:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Lobby then
        Server.TournamentServer:UpdateFirstVictoryUniteRed()  -- MP 模式才显示红点
    end
end

function RoleInfoModule:ShowMainPanel(playerId)
    playerId = setdefault(playerId, 0)
    xxww_("RoleInfoModule:ShowMainPanel")
    if playerId == 0 or playerId == Server.AccountServer:GetPlayerId() then
        UIManager:AsyncShowUI(UIName2ID.RoleInfoMainPanelSelf, nil, nil, playerId)
        self:OpenTitleUnlockPop()
    else
        UIManager:AsyncShowUI(UIName2ID.RoleInfoMainPanel, nil, nil, playerId)
    end
end

---@example slua.Do Module.RoleInfo:ShowTitleRewardPop(***********)
function RoleInfoModule:ShowTitleRewardPop(avatarId)
    Facade.UIManager:AsyncShowUI(UIName2ID.RewardSocialTitle, nil, nil, avatarId)
end

--得到某id卡牌的value
function RoleInfoModule:GetSingleRoleInfoQualityCardValue(cardId)
    return RoleInfoLogic.InteralGetSingleRoleInfoQualityCardValue(cardId)
end

--得到某id卡牌的name
function RoleInfoModule:GetSingleRoleInfoQualityCardName(cardId)
    return RoleInfoLogic.InteralGetSingleRoleInfoQualityCardName(cardId)
end

--得到某id卡牌的小图片资源路径
function RoleInfoModule:GetSingleRoleInfoQualityCardImgPath(cardId)
    return RoleInfoLogic.InteralGetSingleRoleInfoQualityCardImgPath(cardId)
end

--得到某id卡牌的大图片资源路径
function RoleInfoModule:GetSingleRoleInfoQualityCardSpritePath(cardId)
    return RoleInfoLogic.InteralGetSingleRoleInfoQualityCardSpritePath(cardId)
end

function RoleInfoModule:GetPassTimeStr(s)
    return MathUtil.GetPreciseDecimal(s / 60, 1) .. Module.RoleInfo.Config.Loc.RoleMinete
end

function RoleInfoModule:GetModuleNameByMatchModeID(id)
    if id == 0 then
        return Module.Social.Config.Loc.NoTarget
    end
    local matchId = tostring(id)
    if matchModeConfig[matchId] then
        return matchModeConfig[matchId].ModuleName
    end
    return "需要配表：" .. tostring(matchId)
end

function RoleInfoModule:GetSeasonLevel()
    return Server.RoleInfoServer:GetSeasonLevel()
end

function RoleInfoModule:Jump()
    local openUINavIdList = {} -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
    self:ShowMainPanel()
    table.insert(openUINavIdList, UIName2ID.RoleInfoMainPanel)
    return openUINavIdList
end

function RoleInfoModule:GetSeasonMaxLevel()
    return self._maxSeasonLevel
end

function RoleInfoModule:GetIsLowMapRes(mapid, mode)
    --logerror("RoleInfoModule:GetIsLowMapRes:mapid:",tostring(mapid),",Gamemode:",tostring(mode))
    --记得判断新手,新手不算 Module.Guide:IsInNewPlayerMatch()
    if _WITH_EDITOR == 1 then
        return false
    end

    local IsInNewPlayerMatch = Module.Guide:IsInNewPlayerMatch()
    local IsMapModeHDResDownloaded = LiteDownloadManager:GetMapModeHDResDownloaded(mapid, mode)
    local IsBaseHDResDownloaded = LiteDownloadManager:GetBaseHDResDownloaded()

    --logerror("RoleInfoModule:GetIsLowMapRes:IsInNewPlayerMatch",IsInNewPlayerMatch,",IsMapModeHDResDownloaded:",IsMapModeHDResDownloaded,",IsBaseHDResDownloaded",IsBaseHDResDownloaded)

    if not IsInNewPlayerMatch then
        if (not IsMapModeHDResDownloaded) or (not IsBaseHDResDownloaded) then
            return true
        end
    end
    return false
end

function RoleInfoModule:GetCurrentMatchInfo(CachedKillerUID)
    local playerIdStr = Server.AccountServer:GetPlayerIdStr()
    local playerIdEncryptStr = self:GOpenIdEncryption(playerIdStr)
    local dsRoomId = Server.MatchServer:GetDsRoomId()
    local dsRoomIdEncryptStr = self:GOpenIdEncryption(ULuautils.GetUInt64String(dsRoomId))
    local bIsShipping = VersionUtil.IsShipping()
    local formatted_time = self:GetCurrentFormattedServerTime()
    local killeridEncry = "";
    if CachedKillerUID > 0 then
        killeridEncry = self:GOpenIdEncryption(ULuautils.GetUInt64String(CachedKillerUID))
    end

    local playerInfoStr = ""
    -- logerror("RoleInfoModule:GetCurrentMatchInfo():",playerIdStr,",GetDsRoomId:",tostring(dsRoomId))
    if bIsShipping then
        playerInfoStr = string.format("%s UID:%s_%s  %s", AppSettingUtil.GetRegionCode(), playerIdEncryptStr,
            formatted_time, killeridEncry)
    else
        playerInfoStr = string.format("%s:%s_%s_%s", AppSettingUtil.GetRegionCode(), playerIdStr,
            ULuautils.GetUInt64String(dsRoomId), tostring(CachedKillerUID))
    end
    -- logerror("RoleInfoModule:GetCurrentMatchInfo()_2 : ",playerInfoStr)
    return playerInfoStr
end

function RoleInfoModule:GetCurrentFormattedServerTime()
    -- GMT	Sat Nov 23 2024 01:35:45 GMT+0000
    -- Your Time Zone	Sat Nov 23 2024 09:35:45 GMT+0800 (香港标准时间)
    -- local TestUTCTimestamp = 1732325745
    local ServerTime = Facade.ClockManager:GetServerTimestamp()
    local ShowTime = ""
    if REGION_CN then
        ShowTime = os.date("%Y%m%d%H%M", ServerTime)
    else
        ShowTime = os.date("!%Y%m%d%H%M", ServerTime)
    end
    --local current_time = os.date("*t")
    --local formatted_time = string.format("%04d%02d%02d%02d%02d", current_time.year, current_time.month, current_time.day, current_time.hour, current_time.min)
    return ShowTime
end

function RoleInfoModule:GOpenIdEncryption(playerId)
    if not playerId then
        return ""
    end
    local playerStr = ""
    local size = #playerId
    local fristStr = string.sub(playerId, 1, 1)
    for i = 1, size do
        playerStr = playerStr .. (9 - tonumber(string.sub(playerId, i, i)))
    end
    return fristStr .. playerStr
end

function RoleInfoModule:GOpenIdDecrypt(playerId)
    if not playerId then
        return ""
    end
    local playerStr = ""
    local size = #playerId
    for i = 2, size do
        playerStr = playerStr .. (9 - tonumber(string.sub(playerId, i, i)))
    end
    return playerStr
end

function RoleInfoModule:OnPlayerRename(playerId, nickname)
    Server.RoleInfoServer:FetchAllRoleInfo()
end

function RoleInfoModule:GetMilitary(avatarId)
    if (not avatarId) or avatarId == 0 then
        return "PaperSprite'/Game/UI/UIAtlas/Texture/BrandAvatar/BrandAvatar_0000.BrandAvatar_0000'"
    else
        local SocialInfo = SocialTables[avatarId]
        if SocialInfo then
            return SocialInfo.ResourceName
        end
    end
    return "PaperSprite'/Game/UI/UIAtlas/Texture/BrandAvatar/BrandAvatar_0000.BrandAvatar_0000'"
end

function RoleInfoModule:GetMilitaryName(avatarId)
    if (not avatarId) or avatarId == 0 then
        return ""
    else
        local SocialInfo = SocialTables[avatarId]
        if SocialInfo then
            return SocialInfo.AvatarName
        end
    end
    return ""
end

function RoleInfoModule:ShowAchevementTips(achievementList, tipsAnchor)
    return RoleInfoLogic.ShowAchevementTips(achievementList, tipsAnchor)
end

---@param avatarId number: idx of `SocialAvatarDataTable`
---@return string|nil avatarName
---@return string|nil resourceName
---@return string|nil optionalResourceName1
---@return string|nil optionalResourceName2
function RoleInfoModule:GetTitleMisc(avatarId)
    if avatarId and avatarId ~= 0 then
        local SocialInfo = SocialTables[avatarId]
        if SocialInfo then
            ensure(SocialInfo.AvatarType == 3)
            return SocialInfo.AvatarName, SocialInfo.ResourceName, SocialInfo.OptionalResourceNames[1],
                SocialInfo.OptionalResourceNames[2]
        end
    end
    return nil, nil, nil, nil
end

function RoleInfoModule:GetTileIcon(titleId)
    if titleId and titleId ~= 0 then
        local SocialInfo = SocialTables[titleId]
        if SocialInfo then
            ensure(SocialInfo.AvatarType == 3)
            return SocialInfo.ResourceName
        end
    end
    return nil
end

function RoleInfoModule:GetTileBigTexture(titleId)
    if titleId and titleId ~= 0 then
        local SocialInfo = SocialTables[titleId]
        if SocialInfo then
            ensure(SocialInfo.AvatarType == 3)
            return SocialInfo.OptionalResourceNames[1]
        end
    end
    return nil
end

function RoleInfoModule:GetTileSmallTexture(titleId)
    if titleId and titleId ~= 0 then
        local SocialInfo = SocialTables[titleId]
        if SocialInfo then
            ensure(SocialInfo.AvatarType == 3)
            return SocialInfo.OptionalResourceNames[2]
        end
    end
    return nil
end

function RoleInfoModule:GetTitleName(titleId)
    if titleId and titleId ~= 0 then
        local SocialInfo = SocialTables[titleId]
        if SocialInfo then
            ensure(SocialInfo.AvatarType == 3)
            return SocialInfo.AvatarName
        end
    end
    return nil
end

-- 判断监狱地图
function RoleInfoModule:CheckPrisonMap(match_mode_id)
    return RoleInfoLogic.InteralCheckPrisonMap(match_mode_id)
end

function RoleInfoModule:SetSocialHeroId(heroId)
    RoleInfoField:SetSocialHeroId(heroId)
end

function RoleInfoModule:GetSocialHeroId()
    return RoleInfoField:GetSocialHeroId()
end

function RoleInfoModule:ShowNewBadgeGetPop(unlockBadgeIds)
    if table.getkeynum(unlockBadgeIds) > 0 then
        Timer.DelayCall(1.5, function()
            Facade.UIManager:AsyncShowUI(UIName2ID.BadgeGetPop, nil, nil, unlockBadgeIds)
        end)
    end
end

function RoleInfoModule:IsInMp()
    return RoleInfoLogic.IsInMp()
end

function RoleInfoModule:OpenTitleUnlockPop()
    RoleInfoLogic.OpenTitleUnlockPop()
end

function RoleInfoModule:GetFirstVictoryUniteRed()
    return self:IsInMp() and Server.TipsRecordServer:GetNumber(Server.TipsRecordServer.keys.FirstVictoryUniteRed) ~= 1 and
        Server.TournamentServer:GetCommanderHasAttended()
end

return RoleInfoModule
