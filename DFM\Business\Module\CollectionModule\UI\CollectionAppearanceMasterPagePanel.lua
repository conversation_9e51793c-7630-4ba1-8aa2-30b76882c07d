----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionAppearanceMasterPagePanel = ui("CollectionAppearanceMasterPagePanel")
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local ItemDetailTitleComp = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailTitleComp"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ETipsTriggerReason = import("ETipsTriggerReason")
local EIVSlotPos = Module.CommonWidget.Config.EIVSlotPos
local EIVCompOrder = Module.CommonWidget.Config.EIVCompOrder
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

function CollectionAppearanceMasterPagePanel:Ctor()
    self._wtCurrentCamouflageItemView = self:Wnd("wtCurrentCamouflageItemView", IVCommonItemTemplate)
    self._wtPreConditionCamouflageItemView = self:Wnd("wtPreConditionCamouflageItemView", IVCommonItemTemplate)
    self._wtPreConditionCamouflageItemView:HitTestInvisible()
    self._wtPreConditionDescTxt = self:Wnd("wtPreConditionDescTxt", UITextBlock)
    self._wtPreConditionProgressNumTxt = self:Wnd("wtPreConditionProgressNumTxt", UITextBlock)
    self._wtWeaponSkinWaterFallBox = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinWaterFallBox", self._OnGetWeaponSkinCount, self._OnProcessWeaponSkinWidget)
    self._wtItemDetailTitle = self:Wnd("wtItemDetailTitle", ItemDetailTitleComp)
    self._wtItemDetailTitle:BindDetailCallback(CreateCallBack(self._JumpToSkinDetailPage, self))
    self._wtUnlockedNumTxt = self:Wnd("wtUnlockedNumTxt", UITextBlock)
    self._wtMasterChallengeTitleTxt = self:Wnd("wtMasterChallengeTitleTxt", UITextBlock)
    self._wtTaskWaterFallBox = UIUtil.WndWaterfallScrollBox(self, "wtTaskWaterFallBox", self._OnGetTaskCount, self._OnProcessTaskWidget)
    self._wtPreConditionPanel = self:Wnd("wtPreConditionPanel", UIWidgetBase)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintBox:SelfHitTestInvisible()
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtTipAnchor:Collapsed()
    self._wtActionBtn = self:Wnd("wtActionBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnActionBtnClicked, self)
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    self._selectedSkinIndex = -1
    self._selectedCell = nil
    self._weaponSkinItems = {}
    self._tasks = {}
    self._redDotInsMap = setmetatable({}, weakmeta_key)
    self._bStopRefreshModel = false
end


function CollectionAppearanceMasterPagePanel:OnInitExtraData()
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionAppearanceMasterPagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionAppearanceMasterPagePanel:OnClose()
    self:RemoveAllLuaEvent()
    self._selectedCell = nil
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
end

function CollectionAppearanceMasterPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionAppearanceMasterPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionAppearanceMasterPagePanel:OnShow()
    if not self._weaponSkinScrollStopHandle then
        self._weaponSkinScrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtWeaponSkinWaterFallBox, self)  
    end
    if not self._taskScrollStopHandle then
        self._taskScrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtTaskWaterFallBox, self)    
    end
    self:DelayScrollToEnd()
end

function CollectionAppearanceMasterPagePanel:OnHide()
    if self._weaponSkinScrollStopHandle then
        UIUtil.RemoveScrollBoxClickStopScroll(self._weaponSkinScrollStopHandle)
        self._weaponSkinScrollStopHandle = nil
    end
    if self._taskScrollStopHandle then
        UIUtil.RemoveScrollBoxClickStopScroll(self._taskScrollStopHandle)
        self._taskScrollStopHandle = nil
    end
    self:RemoveResidualScollEndTimer()
end

function CollectionAppearanceMasterPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_Master_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionAppearanceMasterPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._scrollActionHandler then
        self._scrollActionHandler = self:AddAxisInputActionBinding("Collection_Scroll_Gamepad",self._OnScrollTaskList,self,EDisplayInputActionPriority.UI_Pop)
    end
    self._wtActionBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    if not self._wtNavGroupSkinList then 
        self._wtNavGroupSkinList = WidgetUtil.RegisterNavigationGroup(self._wtWeaponSkinWaterFallBox, self, "Hittest")
        if self._wtNavGroupSkinList then
            self._wtNavGroupSkinList:AddNavWidgetToArray(self._wtWeaponSkinWaterFallBox)
            self._wtNavGroupSkinList:SetScrollRecipient(self._wtWeaponSkinWaterFallBox)
            self._wtNavGroupSkinList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionAppearanceMasterPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RemoveInputActionForActionBtn()
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._wtActionBtnHandle  = nil 
    self._NavConfigHandler = nil
    self._wtNavGroupSkinList = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionAppearanceMasterPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupSkinList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupSkinList)
    end
end

function CollectionAppearanceMasterPagePanel:_DisableGamepadA(FocusedNavGroup)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then 
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionAppearanceMasterPagePanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionAppearanceMasterPagePanel:_AddInputActionForActionBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._bEnableActionBtn and not self._wtActionBtnHandle then
        self._wtActionBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._OnActionBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionAppearanceMasterPagePanel:_RemoveInputActionForActionBtn()
    if  self._wtActionBtnHandle then
        self:RemoveInputActionBinding(self._wtActionBtnHandle)
        self._wtActionBtnHandle = nil 
    end
end

function CollectionAppearanceMasterPagePanel:_OnScrollTaskList(axis)
    if not IsHD() or not WidgetUtil.IsGamepad() or WidgetUtil.IsEquipZero(axis) then
        return
    end
    WidgetUtil.ScrollByAxis(self._wtTaskWaterFallBox, axis)
end
-- END MODIFICATION

function CollectionAppearanceMasterPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        if bFullReset == true then
            self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCamouflageStatusInfo, self._OnLoadCamouflageStatusInfo, self)
            self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionActivateSkinPattern, self._OnLoadCamouflageStatusInfo, self)
            self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
            self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
            self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
            self:AddLuaEvent(Module.Reward.Config.Events.evtOpenWeaponSkinGainPop, self._OnOpenPopUIWithSameScene, self)
        end
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self._selectedCell = nil
            self:RemoveLuaEvent(Server.CollectionServer.Events.evtFetchCamouflageStatusInfo, self._OnLoadCamouflageStatusInfo, self)
            self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionActivateSkinPattern, self._OnLoadCamouflageStatusInfo, self)
            self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
            self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
            self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
            self:RemoveLuaEvent(Module.Reward.Config.Events.evtOpenWeaponSkinGainPop, self._OnOpenPopUIWithSameScene, self)
        end
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self._bHideUI = nil
        self._bStopRefreshModel = false
    end
end

function CollectionAppearanceMasterPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    if bResetTab then
        self:_ShowUI()
    end
    self._wtMasterChallengeTitleTxt:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.MasterChallengeTitle,
                        {["GameMode"] = CollectionLogic.IsInMp() and Module.IrisSafeHouse.Config.Loc.BFModeTitle or Module.IrisSafeHouse.Config.Loc.IrisModeTitle,   
                    }))
    self._activatedMasterCamouflageInfo = Server.CollectionServer:GetActivatedCamouflageInfo(true)
    if self._activatedMasterCamouflageInfo then
        self:_OnLoadCamouflageStatusInfo()
    else
        self:HideUI(true)
        self:OnRefreshModel(ESubStage.HallWeaponShow)
        self:UpdateBackground()
        Server.CollectionServer:FetchCamouflageStatusInfo() 
    end
end

function CollectionAppearanceMasterPagePanel:_OnRefreshWeaponSkinItems(bResetList)
    if bResetList then
        self._selectedCell = nil
        self._selectedSkinIndex = -1
        local targetPropId = Module.Collection.Field:GetSelectedPropId()
        Module.Collection.Field:SetSelectedPropId()
        if targetPropId then
            for skinIndex, skinItem in ipairs(self._weaponSkinItems) do
                if skinItem.id == targetPropId then
                    self._selectedSkinIndex = skinIndex
                    self._wtWeaponSkinWaterFallBox:RemoveEvent('OnProcessItemsUpdateFinished')
                    self._wtWeaponSkinWaterFallBox:Event('OnProcessItemsUpdateFinished', self._OnProcessWidgetUpdateFinished, self)
                    break
                end
            end
        elseif self._weaponSkinItems and self._weaponSkinItems[1] then
            self._selectedSkinIndex = 1
        end
        self._wtWeaponSkinWaterFallBox:RefreshAllItems()
    else
        if #self._weaponSkinItems == 0 then
            self._wtWeaponSkinWaterFallBox:RefreshAllItems()
        else
            self._wtWeaponSkinWaterFallBox:RefreshVisibleItems()
        end
    end
    self._wtItemDetailTitle:SetDetailBtnVisible(#self._weaponSkinItems > 0)
    local bAlreadyRetrieve = #self._weaponSkinItems > 0
    local bCanRetrieve = false
    if self._weaponSkinItems then
        for index, skinItem in ipairs(self._weaponSkinItems) do
            if not Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) then
                bAlreadyRetrieve = false
                if Server.CollectionServer:CheckSkinTaskIsFinishedBySkinId(skinItem.id) then
                    bCanRetrieve = true
                end
            end
        end
    end
    if bAlreadyRetrieve then
        self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.Unlocked)
        self._wtActionBtn:SetIsEnabled(false)
        self._bEnableActionBtn = false
    elseif bCanRetrieve then
        self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.RetrieveAll)
        self._wtActionBtn:SetIsEnabled(true)
        self._bEnableActionBtn = true
    else
        self._wtActionBtn:SetMainTitle(CollectionConfig.Loc.RetrieveAll)
        self._wtActionBtn:SetIsEnabled(false)
        self._bEnableActionBtn = false
    end
    if self._bEnableActionBtn then
        self:_AddInputActionForActionBtn()
    else
        self:_RemoveInputActionForActionBtn()
    end
    self:_RefreshItemUI(bResetList)
end

function CollectionAppearanceMasterPagePanel:_OnLoadCamouflageStatusInfo()
    self._activatedMasterCamouflageInfo = Server.CollectionServer:GetActivatedCamouflageInfo(true)
    if self._activatedMasterCamouflageInfo then
        self._weaponSkinItems = self._activatedMasterCamouflageInfo.skinItems
        table.sort(self._weaponSkinItems, CollectionLogic.WeaponSkinInMasterSort)
        self:HideUI(false)
        self:_RefreshCurrentChallenge()
        self:_RefreshTaskPanel()
        self:_OnRefreshWeaponSkinItems(true)
    end
end

function CollectionAppearanceMasterPagePanel:_RefreshCurrentChallenge()
    local currentModeTaskInfo = CollectionLogic.IsInMp() and self._activatedMasterCamouflageInfo.MP_TaskInfo or self._activatedMasterCamouflageInfo.SOL_TaskInfo
    if not hasdestroy(self._wtUnlockedNumTxt) then
        self._wtUnlockedNumTxt:SetText(tostring(currentModeTaskInfo.finishedTaskNum).."/"..tostring(currentModeTaskInfo.totalTaskNum))
    end
    if not hasdestroy(self._wtCurrentCamouflageItemView) then
        local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", self._activatedMasterCamouflageInfo.patternId)
        local fClickCb = CreateCallBack(self._JumpToSwitchChallengePage, self)
        self._wtCurrentCamouflageItemView:BindCustomOnClicked(fClickCb)
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            local taskRedDotsMap = Server.CollectionServer:GetTaskRedDots()
            for taskId, value in pairs(taskRedDotsMap) do
                if taskId >= 20000 and value == true then
                    return true
                end
            end
            local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
            for taskId, value in pairs(newArrivedTaskRedDotsMap) do
                if taskId >= 20000 and value == true then
                    return true
                end
            end
            return false
        end,self)
        if isvalid(self._redDotInsMap[self._wtCurrentCamouflageItemView]) and self._redDotInsMap[self._wtCurrentCamouflageItemView]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(self._wtCurrentCamouflageItemView, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[self._wtCurrentCamouflageItemView], fCheckFunc, nil)
        else
            self._redDotInsMap[self._wtCurrentCamouflageItemView] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,self._wtCurrentCamouflageItemView, {reddotType=EReddotType.Normal, zOrder=10})
        end
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_SetUp_Icon_0006.CommonHall_SetUp_Icon_0006"
        local qualityColor = ItemConfigTool.GetItemQualityLinearColor(0)
        local name = ""
        if camouflageDataRow then
            if camouflageDataRow.SkinIconPath.AssetPathName ~= "None" then
                iconPath = camouflageDataRow.SkinIconPath
            end
            qualityColor = ItemConfigTool.GetItemQualityLinearColor(camouflageDataRow.SkinQuality, 0.25)
            name = camouflageDataRow.SkinName
        end
        local actualImgComponent = self._wtCurrentCamouflageItemView._wtItemIcon:GetActualIconImg()
        if actualImgComponent then
            actualImgComponent:AsyncSetImagePath(iconPath)
        end
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.ItemIcon, true)
        local nameComp = self._wtCurrentCamouflageItemView:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
        nameComp:SetBaseWeaponName(true)
        nameComp:ClearLinkageType()
        nameComp:SetMainTxt(name)
        nameComp:RefreshComponent()
        nameComp:SetQualityColor(qualityColor)
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.TopRightItemNameText, true)
        local iconTextComp = self._wtCurrentCamouflageItemView:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
        iconTextComp:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Switch.Common_De_Switch")
        iconTextComp:SetCornerStyle() 
        iconTextComp:RefreshComponent()
        self._wtCurrentCamouflageItemView:EnableComponent(EComp.BottomRightIconText, true)
    end
end

function CollectionAppearanceMasterPagePanel:_RefreshTaskPanel()
    local currentModeTaskInfo = CollectionLogic.IsInMp() and self._activatedMasterCamouflageInfo.MP_TaskInfo or self._activatedMasterCamouflageInfo.SOL_TaskInfo
    local bIsFinished = self._activatedMasterCamouflageInfo.MP_TaskInfo.bFinished or self._activatedMasterCamouflageInfo.SOL_TaskInfo.bFinished
    if currentModeTaskInfo.bUnlocked or bIsFinished then
        self._tasks = currentModeTaskInfo.tasks
        table.sort(self._tasks, CollectionLogic.WeaponSkinTaskSort) 
        self._wtTaskWaterFallBox:SelfHitTestInvisible()
        self._wtTaskWaterFallBox:RefreshAllItems()
        self._wtPreConditionPanel:Collapsed()
    else
        local preCamouflageTask = currentModeTaskInfo.pre_CamouflageTask
        local camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", preCamouflageTask.patternId)
        local name = ""
        if not hasdestroy(self._wtPreConditionCamouflageItemView) then
            local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_SetUp_Icon_0006.CommonHall_SetUp_Icon_0006"
            if camouflageDataRow then
                if camouflageDataRow.SkinIconPath.AssetPathName ~= "None" then
                    iconPath = camouflageDataRow.SkinIconPath
                end
                name = camouflageDataRow.SkinName
            end
            local actualImgComponent = self._wtPreConditionCamouflageItemView._wtItemIcon:GetActualIconImg()
            if actualImgComponent then
                actualImgComponent:AsyncSetImagePath(iconPath)
            end
            self._wtPreConditionCamouflageItemView:EnableComponent(EComp.ItemIcon, true)
            local nameComp = self._wtPreConditionCamouflageItemView:FindOrAdd(EComp.TopRightItemNameText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
            nameComp:SetBaseWeaponName(true)
            self._wtPreConditionCamouflageItemView:EnableComponent(EComp.TopRightItemNameText, false)
            local iconTextComp = self._wtPreConditionCamouflageItemView:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight, EIVCompOrder.Order1)
            self._wtPreConditionCamouflageItemView:EnableComponent(EComp.BottomRightIconText, false)
        end
        if not hasdestroy(self._wtPreConditionDescTxt) then
            self._wtPreConditionDescTxt:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.MasterCamouflagePreTaskHint,
                    {["skinNum"] = preCamouflageTask.maxValue,
                    ["patternName"] = name}))
        end
        if not hasdestroy(self._wtPreConditionProgressNumTxt) then
            self._wtPreConditionProgressNumTxt:SetText(tostring(preCamouflageTask.curValue).."/"..tostring(preCamouflageTask.maxValue))
        end
        self._wtTaskWaterFallBox:Collapsed()
        self._wtPreConditionPanel:SelfHitTestInvisible()
    end
end

function CollectionAppearanceMasterPagePanel:_JumpToSwitchChallengePage()
    local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
    local baseWeaponId = 0
    if skinItem then
        baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionAppearanceSwitchChallengePage, nil, self, true, baseWeaponId)
end

function CollectionAppearanceMasterPagePanel:_OnGetWeaponSkinCount()
    return #self._weaponSkinItems
end

function CollectionAppearanceMasterPagePanel:_OnProcessWeaponSkinWidget(position, skinItemWidget)
    local skinIndex = position
    local skinItem = self._weaponSkinItems[skinIndex]
    if skinItem then
        skinItemWidget:StopIVAnimation("WBP_CommonItemTemplate_in_special")
        if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id) then
            skinItemWidget:SetAlreadyGetState()
        else
            if Server.CollectionServer:CheckSkinTaskIsFinishedBySkinId(skinItem.id) then
                skinItemWidget:UnsetAlreadyGetState()
                skinItemWidget:PlayIVAnimation("WBP_CommonItemTemplate_in_special", 0, EUMGSequencePlayMode.Forward, 1, false)
            else
                skinItemWidget:UnsetAlreadyGetState()
            end
        end
        skinItemWidget:SetRootSize(IsHD() and 256 or 192, IsHD() and 256 or 192)
        skinItemWidget:InitItem(skinItem)
        local fClickCb = CreateCallBack(self._OnWeaponSkinItemClick, self, skinItemWidget, skinIndex)
        skinItemWidget:BindCustomOnClicked(fClickCb)
        if self._selectedSkinIndex == skinIndex then
            self._selectedCell = skinItemWidget
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
        skinItemWidget:SetSelected(skinItem, self._selectedSkinIndex == skinIndex)
    end
end

function CollectionAppearanceMasterPagePanel:_OnProcessWidgetUpdateFinished()
    self._wtWeaponSkinWaterFallBox:RemoveEvent('OnProcessItemsUpdateFinished')
    Timer.DelayCall(0.2, function ()
        self._wtWeaponSkinWaterFallBox:ScrollToIndexIfNotInScreen(self._selectedSkinIndex, true)
        for skinIndex, skinItem in ipairs(self._weaponSkinItems) do
            if skinIndex == self._selectedSkinIndex then
                self._wtWeaponSkinWaterFallBox:SetScrollOffset(276 * (skinIndex-1))
                break
            end
        end
    end, self)
end

function CollectionAppearanceMasterPagePanel:_OnGetTaskCount()
    return #self._tasks
end

function CollectionAppearanceMasterPagePanel:_OnProcessTaskWidget(position, taskItemWidget)
    local taskIndex = position
    local taskInfo = self._tasks[taskIndex]
    if isvalid(taskInfo) then
        local goalConfigRow = Facade.TableManager:GetRowByKey("ActivityTaskGoals", taskInfo.goalId)
        local taskDesc = goalConfigRow and goalConfigRow.Remark or Module.CommonWidget.Config.Loc.UnknownMission
        taskDesc = string.isempty(taskDesc) and Module.CommonWidget.Config.Loc.UnknownMission or taskDesc
        taskItemWidget:SetTaskDesc(StringUtil.Key2StrFormat(CollectionConfig.Loc.TaskDescWithProgress,
                {["currentNum"] = tostring(taskInfo.curValue),
                ["maxNum"] = tostring(taskInfo.maxValue),
                ["targetDesc"] = taskDesc}))
    end
end

function CollectionAppearanceMasterPagePanel:OnRefreshModel(curSubStageType)
    local currentStackUI = Facade.UIManager:GetCurrentStackUI()
    if currentStackUI.UINavID == UIName2ID.CollectionMainPanel and not self._bStopRefreshModel and (not curSubStageType or curSubStageType == ESubStage.HallWeaponShow) then
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        local displayType = IsHD() and "Weapon" or "WeaponM"
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayType", displayType)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetWeaponDisplayType", "Guru")
        Facade.HallSceneManager:SetDisplayBackground(1, false)
        if isvalid(skinItem) then
            local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(skinItem.id)
            if weaponDesc == nil then
                weaponDesc = skinItem:GetRawDescObj()
            end
            if isvalid(weaponDesc) then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetIsAdapter", false)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallWeaponShow, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, false, false)
                Facade.HallSceneManager:SetDisplayBackground(tostring(skinItem.id), false)
            end
        end
    end
end

function CollectionAppearanceMasterPagePanel:_OnOpenPopUIWithSameScene(bOpened)
    self._bStopRefreshModel = bOpened
    if not bOpened then
        self:OnRefreshModel(ESubStage.HallWeaponShow)
    end
end

function CollectionAppearanceMasterPagePanel:_OnWeaponSkinItemClick(skinItemWidget, skinIndex)
    self._bStopRefreshModel = false
    if self._selectedSkinIndex ~= skinIndex then
        if self._selectedCell then
            self._selectedCell:SetSelected(nil, false)
            if IsHD() and WidgetUtil.IsGamepad() then
                self._selectedCell:SetCppValue("bHandleClick" , true)
            end
        end
        self._selectedCell = skinItemWidget
        self._selectedSkinIndex = skinIndex
        if IsHD() and WidgetUtil.IsGamepad() then
            self._selectedCell:SetCppValue("bHandleClick" , false)
        end
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        if skinItem then
            self._selectedCell:SetSelected(skinItem, true)
        end
        self:_RefreshItemUI(true)
    end
end



function CollectionAppearanceMasterPagePanel:_RefreshItemUI(bReset)
    local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
    if isvalid(skinItem) then
        local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(skinItem.id)
        self._wtItemDetailTitle:SetInfo(
            skinItem.name, 
            skinDataRow ~= nil and skinDataRow.SkinDescription or "",
            skinItem.quality
        )
    end
    self._shotcutList = {}
    if IsHD() then
        if self._bHideUI then
            table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        if isvalid(skinItem) and WidgetUtil.IsGamepad() then
            table.insert(self._shotcutList, {actionName = "GunSkin_ShowDetail",func = self._JumpToSkinDetailPage, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        table.insert(self._shotcutList, {actionName = "Collection_Tip_Gamepad",func = self._ToggleTip, caller = self ,bUIOnly = false, bHideIcon = false}) 
        table.insert(self._shotcutList, {actionName = "Collection_SwitchChallenge_Gamepad",func = self._JumpToSwitchChallengePage, caller = self ,bUIOnly = false, bHideIcon = false})
        self:_DownLoadResources()
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
    self:_RefreshDownloadBtn()
    self:OnRefreshModel(ESubStage.HallWeaponShow)
    self:UpdateBackground()
end

function CollectionAppearanceMasterPagePanel:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:Visible()
        logerror("[v_dzhanshen] CollectionAppearanceMasterPagePanel:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionAppearanceMasterPagePanel:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        logerror("[v_dzhanshen] CollectionAppearanceMasterPagePanel:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionAppearanceMasterPagePanel:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
    logerror("[v_dzhanshen] CollectionAppearanceMasterPagePanel:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtWeaponSkinGridBox:RefreshVisibleItems()
        self:OnRefreshModel(ESubStage.HallWeaponShow)
        self:_RefreshDownloadBtn()
    end
end

function CollectionAppearanceMasterPagePanel:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function CollectionAppearanceMasterPagePanel:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(skinItem and skinItem.id or nil)
        local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionAppearanceMasterPagePanel:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(skinItem) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function CollectionAppearanceMasterPagePanel:_OnActionBtnClicked()
    Server.CollectionServer:RetrieveWeaponSkinsOfPattern(self._activatedMasterCamouflageInfo.patternId)
end

function CollectionAppearanceMasterPagePanel:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    else
        self:_HideUI()
    end
end

function CollectionAppearanceMasterPagePanel:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionAppearanceMasterPagePanel:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        if isvalid(skinItem) and WidgetUtil.IsGamepad() then
            table.insert(self._shotcutList, {actionName = "GunSkin_ShowDetail",func = self._JumpToSkinDetailPage, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
        table.insert(self._shotcutList, {actionName = "Collection_Tip_Gamepad",func = self._ToggleTip, caller = self ,bUIOnly = false, bHideIcon = false}) 
        table.insert(self._shotcutList, {actionName = "Collection_SwitchChallenge_Gamepad",func = self._JumpToSwitchChallengePage, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end

function CollectionAppearanceMasterPagePanel:_JumpToSkinDetailPage()
    local skinItem = self._weaponSkinItems[self._selectedSkinIndex]
    if isvalid(skinItem) then
        Module.Collection:ShowWeaponSkinDetailPage(skinItem)
    end
end

function CollectionAppearanceMasterPagePanel:ClosePopup()
    self._bStopRefreshModel = false
    self:_OnHideInstruction()
end


function CollectionAppearanceMasterPagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionAppearanceMasterPagePanel:_OnShowInstruction()
    local datas = {}
    table.insert(datas, {
        textContent = CollectionConfig.Loc.MasterChallengeTip,
        styleRowId = "C000"
    })
    self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
end

function CollectionAppearanceMasterPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionAppearanceMasterPagePanel:_ToggleTip()
    if self._tipHandle then
        self:_OnHideInstruction()
    else
        self:_OnShowInstruction()
    end
end

function CollectionAppearanceMasterPagePanel:OnInputTypeChanged(inputType)
    self._wtWeaponSkinWaterFallBox:RefreshVisibleItems()
end

function CollectionAppearanceMasterPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end

function CollectionAppearanceMasterPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

function CollectionAppearanceMasterPagePanel:RemoveResidualScollEndTimer()
    if self._mScrollToEndTimer then
        self._mScrollToEndTimer:Stop()
        self._mScrollToEndTimer:Release()
        self._mScrollToEndTimer = nil
    end
end


function CollectionAppearanceMasterPagePanel:DelayScrollToEnd()
    local TimeArrive = function()
        local index = #self._weaponSkinItems
        if index == 0 then
            return
        end
        self._wtWeaponSkinWaterFallBox:ScrollToEndByIndex(index)
    end

    self:RemoveResidualScollEndTimer()

    self._mScrollToEndTimer = Timer:NewIns(0.01, 1)
    self._mScrollToEndTimer:AddListener(TimeArrive)
    self._mScrollToEndTimer:Start()
end

return CollectionAppearanceMasterPagePanel
