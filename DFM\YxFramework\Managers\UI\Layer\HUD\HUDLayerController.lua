----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class HUDLayerController : FeatureLayerControllerBase
local HUDLayerController = class("HUDLayerController", FeatureLayerControllerBase)

local UGPGameplayGlobalDelegates = import "GPGameplayGlobalDelegates"
local UGPGameHudDelegates = import "GPGameHudDelegates"
local ExtendHUDState = require("DFM.YxFramework.Managers.UI.Layer.HUD.ExtendHUDState")
local UHUDStateManager = UE.HUDStateManager

function HUDLayerController:Ctor()
    self.evtOnHUDStateChanged = LuaEvent:NewIns("evtOnHUDStateChanged")
    self.evtOnBeforeHUDStateChanged = LuaEvent:NewIns("evtOnBeforeHUDStateChanged")

    LuaGlobalEvents.evtGameFlowChangeEnter:AddListener(self._GameFlowChangeEnter, self)
    LuaGlobalEvents.evtGameFlowChangeLeave:AddListener(self._GameFlowChangeLeave, self)
    logwarning("HUDLayerController:Ctor")

    self._fOnBeforeGameHUDStateChanged = CreateCallBack(self._OnBeforeGameHUDStateChanged, self)
    self._gameHudOnGameHudStateChangedHandler = UGPGameHudDelegates.Get(GetGameInstance()).OnBeforeHUDStateChanged:Add(self._fOnBeforeGameHUDStateChanged)

    self._fOnPostGameHUDStateChanged = CreateCallBack(self._OnPostGameHUDStateChanged, self)
    self._gameHudOnGameHudStateChangedHandler = UGPGameHudDelegates.Get(GetGameInstance()).PostOnGameHudStateChanged:Add(self._fOnPostGameHUDStateChanged)
    
    self._targetActor = nil
    self._uis = setmetatable({}, weakmeta_value)
    self.state = ExtendHUDState:NewIns()
    self.beforeState = ExtendHUDState:NewIns()
end

function HUDLayerController:AddState(state, bForceRefresh)
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(state, bForceRefresh)
    end
end

function HUDLayerController:RemoveState(state, bForceRefresh)
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(state, bForceRefresh)
    end
end

function HUDLayerController:Reset()
    FeatureLayerControllerBase.Reset(self)
    self._targetActor = nil
    self._uis = setmetatable({}, weakmeta_value)
    self._layerUIList = {}
end

function HUDLayerController:Clear()
    self:Reset()
end

function HUDLayerController:Destroy()
    self.state:Release()
    self.beforeState:Release()
    logwarning("HUDLayerController:Destroy")

    if isvalid(self._gameHudOnGameHudStateChangedHandler) then
        UGPGameHudDelegates.Get(GetGameInstance()).PostOnGameHudStateChanged:Remove(self._gameHudOnGameHudStateChangedHandler)
    end
    LuaGlobalEvents.evtGameFlowChangeEnter:RemoveListener(self._GameFlowChangeEnter, self)
end

function HUDLayerController:_GameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse or
        gameFlowType == EGameFlowStageType.Game
    then
        self:_OnPostGameHUDStateChanged()
    end
end

function HUDLayerController:_GameFlowChangeLeave(gameFlowType)
end

function HUDLayerController:RefreshHudVisibility()
    self:_OnPostGameHUDStateChanged()
end

function HUDLayerController:_OnBeforeGameHUDStateChanged()
    --logwarning("HUDLayerController:_OnBeforeGameHUDStateChanged ", hudState)
    if GetWorld() == nil then
        return
    end
    local stateArray = UHUDStateManager.Get(GetWorld()):GetStateArray()
    self.beforeState:Reset()
    for _, state in pairs(stateArray) do
        self.beforeState:AddState(state)
    end
    self.evtOnBeforeHUDStateChanged:Invoke(self.beforeState)
end

function HUDLayerController:_OnPostGameHUDStateChanged()
    --同步HUDStateManager的state过来
    if GetWorld() == nil then
        return
    end
    local stateArray = UHUDStateManager.Get(GetWorld()):GetStateArray()
    self.state:Reset()
    -- local hudState = ""
    for _, state in pairs(stateArray) do
        self.state:AddState(state)
        -- hudState = hudState..tostring(state).." "
    end
    -- logwarning("HUDLayerController:_OnPostGameHUDStateChanged ", hudState)
    self.evtOnHUDStateChanged:Invoke(self.state)
end

function HUDLayerController:GetHUDVisibleState(uiIns)
    if not uiIns.bIsHud then
        logwarning(string.format("This ui [%s] should inherit from hud.",uiIns._cname))
    elseif uiIns:GetUISettings().IsControlByUIState ~= false then
        local hudStateManager = UE.HUDStateManager.Get(GetWorld())
        if isvalid(hudStateManager) then
            local bIsVisible = hudStateManager:IsVisible_Lua(uiIns)
            return bIsVisible
        end
    end
    return false
end

local baseOrderToHudLayer = {}
baseOrderToHudLayer[EHUDBaseZorder.None] = EUILayer.HUD_ScreenEffect
baseOrderToHudLayer[EHUDBaseZorder.ScreenEffect] = EUILayer.HUD_ScreenEffect
baseOrderToHudLayer[EHUDBaseZorder.Mark] = EUILayer.HUD_Mark
baseOrderToHudLayer[EHUDBaseZorder.Hint] = EUILayer.HUD_Hint
baseOrderToHudLayer[EHUDBaseZorder.Feedback] = EUILayer.HUD_Feedback
baseOrderToHudLayer[EHUDBaseZorder.Common] = EUILayer.HUD_Common
baseOrderToHudLayer[EHUDBaseZorder.Touch] = EUILayer.HUD_Touch
baseOrderToHudLayer[EHUDBaseZorder.Popup] = EUILayer.HUD_Popup
baseOrderToHudLayer[EHUDBaseZorder.LargePopup] = EUILayer.HUD_LargePopup

function HUDLayerController:AddUI(uiIns, ...)
    --设置ZOrder
    local uiSettings = uiIns:GetUISettings()
    local bInSafeZone = uiSettings.bInSafeZone == nil or uiSettings.bInSafeZone
    local baseHud = UE.BaseHUD.GetHUD(GetWorld())
    if isvalid(baseHud) then
        if uiIns.InitExtraData then
            trycall(uiIns.InitExtraData, uiIns, ...)
        end
        self._uis[uiIns.UINavID] = uiIns
        --层级需要重新梳理，现在Feedback和touch共用一个跟节点，暂时用额外的offset区别开
        table.insert(self._layerUIList, uiIns)
        baseHud:AddUI(uiIns, bInSafeZone, 0)
        Facade.UIManager:AddToFrameRoot(
            uiIns, 
            uiSettings.ZOrderOffset, 
            baseOrderToHudLayer[uiSettings.ZOrderBase]
        )
        Facade.UIManager.Events.evtHUDUICreated:Invoke(uiSettings.HUDName, uiIns)
        return uiIns
    else
        logwarning("HUDLayerController:AddUI GetHUD invalid")
        return nil
    end
end

function HUDLayerController:RemoveUI(uiIns)
    self._uis[uiIns.UINavID] = nil
    table.removebyvalue(self._layerUIList, uiIns)
    if uiIns and uiIns.IsValid and uiIns:IsValid() and uiIns.TryClose then
        Facade.UIManager:FinalCloseUI(uiIns)
    end
end

function HUDLayerController:InternalRemoveUI(uiIns)
    if uiIns.UINavID then
        self._uis[uiIns.UINavID] = nil
    end
    table.removebyvalue(self._layerUIList, uiIns)
end

function HUDLayerController:RemoveUIByID(uiID)
    local uiSetting = UITable[uiID]
    local uiIns = self._uis[uiID]
    self:RemoveUI(uiIns)
end

function HUDLayerController:GetUIByID(uiID)
    return self._uis[uiID]
end

---@param hudName string
---@param panelOwner outer optional
---@param subUIList table optional
function HUDLayerController:ShowHudByName(hudName, panelOwner, subUIList)
    local uiID = self.GetUIIDByName(self._hudConfigs, hudName)
    uiID = uiID or self.GetUIIDByName(self._baseConfigs, hudName)
    if not uiID then
        return
    end
    self:ShowHudByID(uiID, panelOwner, subUIList)
end

---@param uiID number
---@param panelOwner outer optional
---@param subUIList table optional
function HUDLayerController:ShowHudByID(uiID, panelOwner, subUIList)
    local uiIns = self._uis[uiID]
    local uiHandle = nil
    if uiIns then
        if not hasdestroy(uiIns) and not uiIns:IsInShowState() then
            local curState = uiIns._curUIState
            uiIns:Show(false, true)            
            return
        end
    elseif subUIList then
        uiHandle = Facade.UIManager:AsyncShowUICustomlized(uiID, subUIList)
    else
        uiHandle = Facade.UIManager:AsyncShowUI(uiID)
    end
    
    if uiHandle then
        uiHandle:AddPendingDo("SetPanelOwner", panelOwner)
    end
end

---@param hudName string
---@param panelOwner outer optional
---@param subUIList table optional
function HUDLayerController:CreateHudByName(hudName, panelOwner, subUIList)
    local uiID = self.GetUIIDByName(self._hudConfigs, hudName)
    uiID = uiID or self.GetUIIDByName(self._baseConfigs, hudName)
    if not uiID then
        return
    end
    local uiIns = self._uis[uiID]
    local uiHandle = nil
    if not uiIns then
        if subUIList then
            uiHandle = Facade.UIManager:AsyncShowUICustomlized(uiID, subUIList)
        else
            uiHandle = Facade.UIManager:AsyncShowUI(uiID)
        end
    end

    if uiHandle then
        uiHandle:AddPendingDo("SetPanelOwner", panelOwner)
    end
end

function HUDLayerController:HideHudByName(hudName)
    for uiID, uiIns in pairs(self._uis) do
        if UITable[uiID].HUDName and UITable[uiID].HUDName == hudName then
            if not hasdestroy(uiIns) and not uiIns:IsInHideState() then
                uiIns:Hide(false, true)
            end
            break
        end
    end
end

function HUDLayerController:DestroyHudByName(hudName)
    for uiID, uiIns in pairs(self._uis) do
        if UITable[uiID].HUDName and UITable[uiID].HUDName == hudName then
            self:RemoveUI(uiIns)
            break
        end
    end
end

function HUDLayerController:GetHudByName(hudName)
    for uiID, uiIns in pairs(self._uis) do
        if UITable[uiID].HUDName and UITable[uiID].HUDName == hudName then
            return uiIns
        end
    end
    return nil
end
--OnMovePanelToLayer("WBP_ControllerRouletteMed_PC", EHUDBaseZorder.Mark)
function HUDLayerController:OnMovePanelToLayer(hudName, targetLayer)
    local uiIns = self:GetHudByName(hudName)
    if uiIns then
        local uiSettings = uiIns:GetUISettings()
        if isvalid(uiIns) then
            Facade.UIManager:AddToFrameRoot(
                uiIns, 
                uiSettings.ZOrderOffset, 
                baseOrderToHudLayer[targetLayer]
            )
        end
    end
end

function HUDLayerController:PreSetHudSetting(hudConfigs, baseConfigs)
    self._hudConfigs = hudConfigs
    self._baseConfigs = baseConfigs
end

function HUDLayerController:ClearHUDBySettings(hudConfigs, baseConfigs)
    loginfo("ClearHUDBySettings")
end

function HUDLayerController:GetAddRemoveDicts(hudConfigs, baseConfigs)
    local needShowDict = {}
    if baseConfigs then
        for _, uiID in pairs(baseConfigs.HUDConfigIDs) do
            local hudName = UITable[uiID] and UITable[uiID].HUDName or nil
            if hudName then
                needShowDict[hudName] = uiID
            end
        end
    end
    if hudConfigs then
        for _, uiID in pairs(hudConfigs.HUDConfigIDs) do
            local hudName = UITable[uiID] and UITable[uiID].HUDName or nil
            if hudName then
                needShowDict[hudName] = uiID
            end
        end
    end
    local needRemoveDict = {}
    local needAddDict = {}
    for uiID, uiIns in pairs(self._uis) do
        if not needShowDict[UITable[uiID].HUDName] then
            table.insert(needRemoveDict, uiID)
        end
    end
    for uiName, uiID in pairs(needShowDict) do
        if not self._uis[uiID] then
            table.insert(needAddDict, uiID)
        end
    end

    return {needAddDict = needAddDict, needRemoveDict = needRemoveDict}
end

function HUDLayerController:PreloadBySettings(hudConfigs, baseConfigs)
    --lua侧UI处理
    local Dicts = self:GetAddRemoveDicts(hudConfigs, baseConfigs)
    for _, uiID in pairs(Dicts.needAddDict) do
        if not UITable[uiID].bCreateOnDemand then
            Facade.UIManager:AsyncLoadUIAsset(uiID)
        end
    end
end

function HUDLayerController:RefreshBySettings(hudConfigs, baseConfigs)
    self:PreSetHudSetting(hudConfigs, baseConfigs)
    --lua侧UI处理
    local Dicts = self:GetAddRemoveDicts(hudConfigs, baseConfigs)
    for _, uiID in pairs(Dicts.needRemoveDict) do
        self:RemoveUIByID(uiID)
    end
    for _, uiID in pairs(Dicts.needAddDict) do
        if not UITable[uiID].bCreateOnDemand then
            Facade.UIManager:AsyncShowUI(uiID)
        end
    end
end

function HUDLayerController.GetUIIDByName(hudConfigs, hudName)
    if not hudConfigs then
        return nil
    end
    for _, uiID in pairs(hudConfigs.HUDConfigIDs) do
        if UITable[uiID] and UITable[uiID].HUDName and UITable[uiID].HUDName == hudName then
            return uiID
        end
    end
    return nil
end

return HUDLayerController
