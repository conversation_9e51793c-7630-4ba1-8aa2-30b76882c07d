----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LogMusicPlayer)
----- LOG FUNCTION AUTO GENERATE END -----------

local MusicPlayerWidgetLogic = require "DFM.Business.Module.MusicPlayerModule.Logic.MusicPlayerWidgetLogic"
local FHitResult = import "HitResult"

---@class MusicPlayerModule : ModuleBase
local MusicPlayerModule = class("MusicPlayerModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))


local TICK_TIME = 0.05

function MusicPlayerModule:Ctor()
    local a = 1
end

---------------------------------------------------------------------------------
--- Mo<PERSON>le 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function MusicPlayerModule:OnInitModule()
    
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function MusicPlayerModule:OnLoadModule()
    self:RegisterEvents()
    MusicPlayerWidgetLogic.InitMusicList()
end



--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function MusicPlayerModule:OnUnloadModule()
    self:UnregisterEvents()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function MusicPlayerModule:OnDestroyModule()
    local musicInfo = self.Field:GetMusicPlayerWidgetInfo()
    if musicInfo then
        local isPlaying = musicInfo.isPlaying
        self:WriteIsPlaying(isPlaying)
    end
    self:StopMusic()
    self:UnregisterEvents()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function MusicPlayerModule:OnGameFlowChangeLeave(gameFlowType)

end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function MusicPlayerModule:OnGameFlowChangeEnter(gameFlowType)
    self:CheckAndPlayMusic()
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
--- 模块[Loading]加载资源，区分局内外
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function MusicPlayerModule:OnLoadingLogin2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function MusicPlayerModule:OnLoadingGame2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function MusicPlayerModule:OnLoadingFrontend2Game(gameFlowType)
end

function MusicPlayerModule:RegisterEvents()
    

end

function MusicPlayerModule:UnregisterEvents()
    
end

function MusicPlayerModule:OnTimerTick()
    if not MusicPlayerWidgetLogic then
        logerror("[MusicPlayerModule:OnMusicPlayerPlayCompleted] MusicPlayerWidgetLogic is nil")
        return
    end
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    local real_isPlaying = Facade.SoundManager:IsMusicPlaying(curMusicEvtName)
    if real_isPlaying then
        local percent = Facade.SoundManager:GetMusicPlayPercent(curMusicEvtName)
        MusicPlayerWidgetLogic.UpdateMusicPosRecord(curMusicEvtName, percent)
        self.Field:SetCurMusicPercent(percent)
        if  percent and  percent>= 0.99 then
            self:OnMusicPlayerPlayCompleted(curMusicEvtName)
        end
    end
end

function MusicPlayerModule:EnableUpdateCurMuiscPercent(bIsEnable)
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
    if bIsEnable then
        self.tickHandle = Timer:NewIns(TICK_TIME,0)
        self.tickHandle:AddListener(self.OnTimerTick, self)
        self.tickHandle:Start()
    end
end

function MusicPlayerModule:OnMusicPlayerPlayCompleted(evtName)
    if not MusicPlayerWidgetLogic then
        logerror("[MusicPlayerModule:OnMusicPlayerPlayCompleted] MusicPlayerWidgetLogic is nil")
        return
    end

    local info = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local playMode = info.palyMode

    loginfo(string.format("[MusicPlayerModule:OnMusicPlayerPlayCompleted] evtName:%s , playMode:%s ", evtName ,playMode))

    if playMode == 1 then
        local curMusicName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
        if  not curMusicName then
            logerror("[MusicPlayerWidgetLogic.PlayMusic] curMusicName is nil")
            return
        end
        MusicPlayerWidgetLogic.RestartMusic(curMusicName)
    elseif playMode == 2 then
        MusicPlayerWidgetLogic.NextMusic()
    elseif playMode == 3 then
        MusicPlayerWidgetLogic.RandomPlayMusic()
    end
end

local PlayingGameFlowType = {
    [EGameFlowStageType.LoginToLobby] = true,
    [EGameFlowStageType.Lobby] = true,
    [EGameFlowStageType.GameToLobby] = true,
    [EGameFlowStageType.None] = true,
    [EGameFlowStageType.ModeHallToSafeHouse] = true,
    [EGameFlowStageType.ModeHallToLobby] = true,
    [EGameFlowStageType.SafeHouse] = true,
    
}

function MusicPlayerModule:CheckAndPlayMusic()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local binPlayingGameFlow = PlayingGameFlowType[curGameFlow] or false
    local musicInfo = self.Field:GetMusicPlayerWidgetInfo()
    loginfo(string.format("[MusicPlayerModule:CheckAndPlayMusic] curGameFlow:%s", curGameFlow))


    if not binPlayingGameFlow then
        loginfo("[MusicPlayerModule:CheckAndPlayMusic] not in playing gameFlowType, not play music")
        self:StopMusic()
        return
    end


    if not musicInfo then
        logerror("[MusicPlayerModule:CheckAndPlayMusic] musicInfo is nil")
        self:StopMusic()
        return
    end

    if not musicInfo.isPlaying then
        loginfo("[MusicPlayerModule:CheckAndPlayMusic] musicInfo.isPlaying is false, not play music")
        self:StopMusic()
        return
    end

    MusicPlayerWidgetLogic.StopDefaultLobbyBGM()

    local curMusicEvtName =  MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if not curMusicEvtName or curMusicEvtName == "" then
        loginfo("[MusicPlayerModule:CheckAndPlayMusic] curMusicEvtName is nil, play default music")
        return 
    end

    local real_isPlaying = Facade.SoundManager:IsMusicPlaying(curMusicEvtName)
    if not real_isPlaying then
        MusicPlayerWidgetLogic.PlayMusic(curMusicEvtName)
    end
    loginfo(string.format("[MusicPlayerModule:CheckAndPlayMusic] play music:%s", curMusicEvtName))
end

function MusicPlayerModule:StopMusic()
    MusicPlayerWidgetLogic.StopAllMusic()
end

function MusicPlayerModule:IsPlayingMusic()
    local info = self.Field:GetMusicPlayerWidgetInfo()
    return info and info.isPlaying or false
end

function MusicPlayerModule:SetMuiscMode_3D()
    MusicPlayerWidgetLogic.SetPlayTypeByRTPC(1)
end

function MusicPlayerModule:SetMuiscMode_2D()
    MusicPlayerWidgetLogic.SetPlayTypeByRTPC(0)
end

---@param pos Vector3
function MusicPlayerModule:SetLobbyMusicPlayerPos(pos)
    local playerActor =  Facade.SoundManager:GetLobbyMusicPlayerActor()
    if not isvalid(playerActor) then
        logerror("[MusicPlayerModule:SetLobbyMusicPlayerPos] playerActor is nil")
        return
    end
    loginfo(string.format("[MusicPlayerModule:SetLobbyMusicPlayerPos] pos:%s", tostring(pos)))
    playerActor:K2_SetActorLocation(pos,false,FHitResult(),false)
end

function MusicPlayerModule:WriteIsPlaying(bIsPlaying)
    local GConfig = FConfigCacheIni.GetGlobalConfig()
    local GGameIni = FConfigCacheIni.LoadGlobalIni("Game")
    FConfigCacheIni.SetString(GConfig,"MusicPlayerModule", "IsPlaying" , bIsPlaying and "True" or "False" ,GGameIni)
end

function MusicPlayerModule:ReadIsPlaying()
    local GConfig = FConfigCacheIni.GetGlobalConfig()
    local GGameIni = FConfigCacheIni.LoadGlobalIni("Game")
    local isPlaying = FConfigCacheIni.GetString(GConfig,"MusicPlayerModule", "IsPlaying", GGameIni)
    if isPlaying == "True" then
        return true
    else
        return false
    end
end

return MusicPlayerModule
