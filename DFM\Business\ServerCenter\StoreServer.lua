----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSStore)
----- LOG FUNCTION AUTO GENERATE END -----------



local StoreItemStruct = require "DFM.Business.DataStruct.StoreStruct.StoreItemStruct"
local StoreRecommendItem = require "DFM.Business.DataStruct.StoreStruct.StoreRecommendItem"
local StoreMallGiftItem = require "DFM.Business.DataStruct.StoreStruct.StoreMallGiftItem"
local StoreLotteryItem = require "DFM.Business.DataStruct.StoreStruct.StoreLotteryItem"
local StoreBHDBundleStruct = require "DFM.Business.DataStruct.StoreStruct.StoreBHDBundleStruct"
local StoreSpecialBackItemStruct = require "DFM.Business.DataStruct.StoreStruct.StoreSpecialBackItemStruct"
local StoreLotteryProbDistributionItem = require "DFM.Business.DataStruct.StoreStruct.StoreLotteryProbDistributionItem"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ThemeConfigStruct = require "DFM.Business.DataStruct.ThemeStruct.ThemeConfigStruct"

---@class StoreServer : ServerBase
local StoreServer = class("StoreServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function StoreServer:Ctor()
    self.has_cur_special_back = false
    self.recharge_open = true
    ---商品列表
    ---@type table<number, StoreItemStruct>
    self._storeItemList = {}
    ---@type table<number, number> 物品ID 商品ID映射
    self._storeItemIdDic = {}

    self.DictStoreHotRecommendItems = {}
    self.DictStoreMallGiftItems = {}
    self.DictStoreLotteryItems = {}
    self.DictStoreBHDItems = {}
    self.DictStoreSpecialBackItems = {}
    self.DictStoreLotteryProbDistributionItems = {}
    self.lottery_records = {}
    self.DictStoreRedDot = {}
    self.pv_play_list = {}
    self.ThemeConfigDatas = {}

    self.TabName2TabCnt = {}  --页签枚举和页签顺序的映射表

    self.Id2JumpPage = {}   --记录捆绑包或奖池跳转到哪个页面

    self.currentLotteryId = nil -- 当前干员抽奖池子id

    self.bStaffLotteryMainUIOpened = false -- 抽奖主界面是否打开

    self._bFlagNtfFlexiblePaymentLimitRestore = false

    ---@type table<number, CSGetBoxInfoRes>
    self._lotteryPoolId2BoxInfos = {} --开砖奖池信息缓存

    self._lotteryRedDotKey2LotteryID = {} --红点key和奖池ID的映射

    self.ReddotChildNodes = {}

    self._recommendTabId = 1

    self._showMandelExchange = true

    self.timeLimitComsumeCouponId = nil -- 限时三角券ID

    self.Events = {
        evtStoreDataChanged = LuaEvent:NewIns("StoreServer.evtStoreDataChanged"),
        evtStoreBuySuc = LuaEvent:NewIns("StoreServer.evtStoreBuySuc"),
        evtStoreBoxItem = LuaEvent:NewIns("StoreServer.evtStoreBoxItem"),
        evtBoxItemGetId = LuaEvent:NewIns("StoreServer.evtBoxItemGetId"),

        evtStoreMandelLotteryItemBuyChange = LuaEvent:NewIns("StoreServer.evtStoreMandelLotteryItemBuyChange"),
        evtStoreMandelLotteryItemBuyFailed = LuaEvent:NewIns("StoreServer.evtStoreMandelLotteryItemBuyFailed"),
        evtStoreBuyRecordUpdate = LuaEvent:NewIns("StoreServer.evtStoreBuyRecordUpdate"),

        evtStoreBuyHotRecommendationSuc = LuaEvent:NewIns("StoreServer.evtStoreBuyHotRecommendationSuc"),
        evtStoreBuyMallGiftSuc = LuaEvent:NewIns("StoreServer.evtStoreBuyMallGiftSuc"),
        evtStoreBuyMarketSuc = LuaEvent:NewIns("StoreServer.evtStoreBuyMarketSuc"),
        evtStoreBuyMarketUpdate = LuaEvent:NewIns("StoreServer.evtStoreBuyMarketUpdate"),

        evtStoreOpenBoxSuccess = LuaEvent:NewIns("StoreServer.evtStoreOpenBoxSuccess"),
        evtStoreDataRefresh = LuaEvent:NewIns("StoreServer.evtStoreDataRefresh"),
        evtStoreReddotChanged = LuaEvent:NewIns("StoreServer.evtStoreReddotChanged"),

        evtStoreLotteryInfoUpdated = LuaEvent:NewIns("StoreServer.evtStoreLotteryInfoUpdated"),
        evtStoreMandelDrawDataUpdated = LuaEvent:NewIns("StoreServer.evtStoreMandelDrawDataUpdated"),
        evtStoreMandelUpRewardChooseResult = LuaEvent:NewIns("StoreServer.evtStoreMandelUpRewardChooseResult"),

        evtStoreFlexiblePaymentLimitRestoreNtf = LuaEvent:NewIns("StoreServer.evtStoreFlexiblePaymentLimitRestoreNtf"),

        evtStoreBuyLuckyNestUpdate = LuaEvent:NewIns("StoreServer.evtStoreBuyLuckyNestUpdate"),
        evtStoreBuyLuckyNestSuc = LuaEvent:NewIns("StoreServer.evtStoreBuyLuckyNestSuc"),
    }

    self._StoreMainTab = Facade.TableManager:GetTable("StoreMainTab")
    self._StoreSubTab = Facade.TableManager:GetTable("StoreSubTab")

    self._currencyConfig = Facade.TableManager:GetTable("CurrencyConfig")

    self._storeLottertClientConfig = Facade.TableManager:GetTable("StoreLottery")
    self._themeConfig = Facade.TableManager:GetTable("ThemeTabConfig")
    self._hotfixTokenStoreLottery = Facade.DataTableHotfixManager:AddHotfixCallback("StoreLottery",
            self.OnReloadLocalStoreLotteryTable, self)

    self._lotteryProbDistributionClientConfig = Facade.TableManager:GetTable("LotteryProbDistribution")


    self._lotteryBoxConfig = Facade.TableManager:GetTable("LotteryBoxConfig")
    self._lotteryBoxPropConfig = Facade.TableManager:GetTable("LotteryBoxPropConfig")
    self._StoreHotRecommendation = Facade.TableManager:GetTable("StoreHotRecommendation")
    self._StoreMallGiftConfig = Facade.TableManager:GetTable("StoreMallGiftConfig")
    self._SpecialItemBuyConfig = Facade.TableManager:GetTable("SpecialItemBuyConfig")
    self._StoreBHDConfig = Facade.TableManager:GetTable("StoreBHDConfig")
    self._StoreSpecialBackConfig = Facade.TableManager:GetTable("StoreSpecialBackConfig")


    self._StoreCashAreaDiff = Facade.TableManager:GetTable("StoreCashAreaDiff")

    self._lotteryBoxGroupConfig = Facade.TableManager:GetTable("LotteryBoxGroupConfig")
    self._hotfixTokenStoreLotteryBoxGroupConfig = Facade.DataTableHotfixManager:AddHotfixCallback("LotteryBoxGroupConfig"
    , self.OnReloadLocalLotteryBoxGroupConfigTable, self)

    self._FreeMandelBrickConfig = Facade.TableManager:GetTable("FreeMandelBrick")
    self._hotfixTokenFreeMandelBrickConfig = Facade.DataTableHotfixManager:AddHotfixCallback("FreeMandelBrick"
    , self.OnReloadLocalFreeMandelBrickConfigTable, self)

    self._GameItemConfig = Facade.TableManager:GetTable("GameItem")

    self._PaymentRestrictionConfig = Facade.TableManager:GetTable("PaymentRestriction")

    self._LuckyNestEventRewardConfig = Facade.TableManager:GetTable("LuckyNestEventReward")
    self._LuckyNestGoodsBackConfig = Facade.TableManager:GetTable("LuckyNestGoodsBack")

    self._PropCombinationConfig = Facade.TableManager:GetTable("PropCombinationConfig")
end

function StoreServer:OnInitServer()
    self._storeClientConfig = Facade.TableManager:GetTable("StoreItem")
end
function StoreServer:OnDestroyServer()
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenStoreLottery)
    self._hotfixTokenStoreLottery = nil

    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._hotfixTokenStoreLotteryBoxGroupConfig)
    self._hotfixTokenStoreLotteryBoxGroupConfig = nil
end

function StoreServer:FetchServerData()
    self:SendShopNewGetConfigReq()
    self:SendShopGetBuyRecordReq()
    self:SendShopGetClickedRedDotReq()
    self:SendShopGetThemeBundleTimeConfigReq()
    self:SendShopGetLuckyNestConfigReq()
    -- self:SendShopGetSpecialBackInfoReq()
end

function StoreServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function StoreServer:OnLoadingGame2Frontend(gameFlowType)
    self:FetchServerData()
end

function StoreServer:OnLoadingFrontend2Game(gameFlowType)
    self:_InternalResetDatas()
end


---------------new---------------
function StoreServer:GetShopSpecialBackRefreshTime()
    return self.next_special_back_refresh_time
end

function StoreServer:IsShopSpecialBackOpen()
    return self.has_cur_special_back
end

function StoreServer:GetShopSpecialBackConfig()
    return self.specialback_config
end

function StoreServer:GetSpecialBackDataByID(Id)
    local ret = nil
    for k, v in pairs(self.DictStoreSpecialBackItems) do
        if v.RewardItem == Id then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetSpecialBackDatas()
    local ret = {}
    for k, v in pairs(self.DictStoreSpecialBackItems) do
        table.insert(ret, v)
    end

    return ret
end


function StoreServer:OnReloadLocalStoreLotteryTable()
    self._storeLottertClientConfig = Facade.TableManager:GetTable("StoreLottery")
end

function StoreServer:OnReloadLocalLotteryBoxGroupConfigTable()
    self._lotteryBoxGroupConfig = Facade.TableManager:GetTable("LotteryBoxGroupConfig")
end

function StoreServer:OnReloadLocalFreeMandelBrickConfigTable()
    self._FreeMandelBrickConfig = Facade.TableManager:GetTable("FreeMandelBrick")
end

function StoreServer:GetHotRecommendationDatas()
    local ret = {}
    for k, v in pairs(self.DictStoreHotRecommendItems) do
        if v.IsCollaboration == 0 then
            -- 判断是否处于上架时间范围
            local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - v.OfflineTime
            local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - v.OnlineTime
            if remainOfflineTime < 0 and remainOnlineTime > 0 then -- 说明在上架时间范围内
                table.insert(ret, v)
            end
        end
    end

    return ret
end

function StoreServer:GetCollabBundleInfo()
    local res = {}
    for k, v in pairs(self.DictStoreHotRecommendItems) do
        if v.IsCollaboration == self.themeId then
            -- 判断是否处于上架时间范围
            local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - v.OfflineTime
            local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - v.OnlineTime
            if remainOfflineTime < 0 and remainOnlineTime > 0 then -- 说明在上架时间范围内
                table.insert(res, v)
            end
        end
    end
    return res
end

function StoreServer:GetAllBundleInfo()
    local res = {}
    local ret = {}
    for k, v in pairs(self.DictStoreHotRecommendItems) do
        if v.IsCollaboration ~= self.themeId then
            -- 判断是否处于上架时间范围
            local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - v.OfflineTime
            local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - v.OnlineTime
            if remainOfflineTime < 0 and remainOnlineTime > 0 then -- 说明在上架时间范围内
                table.insert(res, v)
            end
        else
            -- 判断是否处于上架时间范围
            local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - v.OfflineTime
            local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - v.OnlineTime
            if remainOfflineTime < 0 and remainOnlineTime > 0 then -- 说明在上架时间范围内
                table.insert(ret, v)
            end
        end
    end
    return res, ret
end


function StoreServer:ParseTime(str)
    local _,_,y,M,d,h,m,s = string.find(str, "(%d+)-(%d+)-(%d+) (%d+):(%d+):(%d+)")
    local t = {
        year    = y,
        month   = M,
        day     = d,
        hour    = h,
        min     = m,
        sec     = s,
    }
    return os.time(t)
end

function StoreServer:StringtoTable(str)
    if str == nil then 
        return {}
    end
    local result = {}
    for item in string.gmatch(str, "([^,]+)") do
        table.insert(result, item)
    end
    return result
end

function StoreServer:GetMainTabTable()
    if self.mainTabInfos == nil then
        self.mainTabInfos = {}
        local infos = {}
        for key, value in pairs(self._StoreMainTab) do
            table.insert(infos, value)
        end
        table.sort(infos, function(a,b)
            return a.SortIndex < b.SortIndex
        end)
        self.mainTabInfos = infos
    end

    return self.mainTabInfos 
end

function StoreServer:GetSubTabTable()
    if self.subTabInfos == nil then
        self.subTabInfos = {} 
        local infos = self._StoreSubTab
        table.sort(infos, function(a,b)
            return a.SortIndex < b.SortIndex
        end)
        self.subTabInfos = infos
        for _, row in ipairs(infos) do
            self.subTabInfos[row.SubTabId] = row
        end
    end
    return self.subTabInfos
end

function StoreServer:GetCurrencyConfig()
    if self.currencyConfig == nil then
        self.currencyConfig = {} 
        local infos = self._currencyConfig

        for _, row in ipairs(infos) do
            self.currencyConfig[row.ItemId] = row.CurrencyId
        end
    end
    return self.currencyConfig
end

function StoreServer:CheckIsCollaborationOn()
    local themeId = nil
    self.themeId = nil
    self.themeTabName = nil
    self.mainTabIcon = nil
    self.subTabIcon = nil
    for k, v in pairs(self.ThemeConfigDatas) do
        local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - tonumber(v.ThemeTabOn)
        local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - tonumber(v.ThemeTabOff)
        if remainOfflineTime < 0 and remainOnlineTime > 0 then
            themeId = v.ThemeId
            self.themeId = v.ThemeId
            self.themeTabName = v.ThemeTabName
            self.mainTabIcon = v.MainTabIcon
            self.subTabIcon = v.SubTabIcon
            break
        end
    end

    return themeId, self.themeTabName
end

function StoreServer:CheckIsLuckyNestOn()
    local res = {}
    local remainOnlineTime = Facade.ClockManager:GetLocalTimestamp() - tonumber(self.nmBeginTime)
    local remainOfflineTime = Facade.ClockManager:GetLocalTimestamp() - tonumber(self.nmEndTime)
    if remainOfflineTime < 0 and remainOnlineTime > 0 then
        res[1] = 1
    end
    return res
end

function StoreServer:IsHiddenBoxRemain()
    if self.nmTimesOfHiddenBox and self.nmTimesOfHiddenBox > 0 then
        return true
    end
    return false
end

function StoreServer:GetLuckyNestGoods()
    local goods = {}
    -- if self.nmBonusItems ~= nil then
    --     table.sort(self.nmBonusItems, function (a, b)
    --         return a.sort_index < b.sort_index
    --     end)
    --     for index, value in ipairs(self.nmBonusItems) do
    --         table.insert(goods, value)
    --     end
    -- end

    if self.nmSpecialItems ~= nil then
        local hasInsert = false
        table.sort(self.nmSpecialItems, function (a, b)
            return a.sort_index < b.sort_index
        end)
        local t
        for index, value in ipairs(self.nmSpecialItems) do
            if value.index_id and self._LuckyNestEventRewardConfig[value.index_id] then
                value.cardBg = self._LuckyNestEventRewardConfig[value.index_id].ItemCardBg
                value.buyBg = self._LuckyNestEventRewardConfig[value.index_id].ItemBuyBg
            end
            if value.prop.id == 16110000020 or value.prop.id == 32320000001 then    -- 曼德尔礼包包含两个道具 特殊处理一下
                if not hasInsert then
                    table.insert(goods, value)
                    t = value
                    hasInsert = true
                else
                    t.needShowBoth = true
                end
            else
                table.insert(goods, value)
            end
        end
    end

    if self.nmBackItems ~= nil then
        table.sort(self.nmBackItems, function (a, b)
            return a.sort_index < b.sort_index
        end)
        for index, value in ipairs(self.nmBackItems) do
            if value.index_id and self._LuckyNestGoodsBackConfig[value.index_id] then
                value.cardBg = self._LuckyNestGoodsBackConfig[value.index_id].ItemBg
                value.imgLogo = self._LuckyNestGoodsBackConfig[value.index_id].ImageSourceLogo
            end
            table.insert(goods, value)
        end
    end

    return goods
end

function StoreServer:GetNMBonusItems()
    return self.nmBonusItems
end

function StoreServer:SetLuckyNestBoughtGoods()
    self.LuckyNestBoughtGoods = {}
    if self.nmBonusItems ~= nil then
        for index, value in ipairs(self.nmBonusItems) do
            if value.bought then
                self.LuckyNestBoughtGoods[value.prop.id] = true
            end
        end
    end

    if self.nmSpecialItems ~= nil then
        for index, value in ipairs(self.nmSpecialItems) do
            if value.bought then
                self.LuckyNestBoughtGoods[value.prop.id] = true
            end
        end
    end

    if self.nmBackItems ~= nil then
        for index, value in ipairs(self.nmBackItems) do
            if value.bought then
                self.LuckyNestBoughtGoods[value.prop.id] = true
            end
        end
    end

    self.Events.evtStoreBuyLuckyNestUpdate:Invoke()
end

function StoreServer:GetLuckyNestBoughtGoods()
    return self.LuckyNestBoughtGoods
end

function StoreServer:GetNMFreeBuff()
    return self.nmFreeBuff
end

function StoreServer:GetNMEndTime()
    return self.nmEndTime
end

function StoreServer:GetNMHasSearched()
    if self.nmBonusItems and #self.nmBonusItems > 0 or 
        self.nmSpecialItems and #self.nmSpecialItems > 0 or 
        self.nmBackItems and #self.nmBackItems > 0 then
            return true
    end
    return false
end

function StoreServer:GetThemeId()
    return self.themeId
end

function StoreServer:getCollabMainTabIcon()
    return self.mainTabIcon
end

function StoreServer:getCollabSubTabIcon()
    return self.subTabIcon
end

function StoreServer:GetCollabTabMediaRowName()
    for k, v in pairs(self.ThemeConfigDatas) do
        if self.themeId == v.ThemeId then
            return v.ThemeTabMedia
        end
    end
    return ""
end

function StoreServer:SetTabNameToTabCnt(tabId, tabCnt)
    if tabId == nil then
        return 
    end
    self.TabName2TabCnt[tabId] = tabCnt
end

function StoreServer:GetTabCnt(id)
    if id == nil then
        return 1
    end
    return self.TabName2TabCnt[id]
end

function StoreServer:SetJumpTab(id, tabCnt, tab2Cnt)
    if id == nil then
        return
    end
    self.Id2JumpPage[id] = tabCnt * 100 + tab2Cnt    
end

function StoreServer:GetJumpTab(id)
    if id == nil or self.Id2JumpPage[id] == nil then
        return 1, 1
    end
    local tab1 = math.floor(self.Id2JumpPage[id] / 100)
    local tab2 = self.Id2JumpPage[id] % 10
    return tab1, tab2
end


function StoreServer:SetRecommendTabId(id)
    self._recommendTabId = id
end

function StoreServer:GetRecommendTabId()
    return self._recommendTabId
end

function StoreServer:GetRelativeData(key, useCollabData)
    local data = {}
    if key == EStoreTab.StaffLottery then
        local info, collabInfo = self:GetDrawByType(3)
        if useCollabData then
            data = collabInfo
        else
            data = info
        end
    elseif key == EStoreTab.HotRecommendation then
        local info, collabInfo = self:GetAllBundleInfo()
        if useCollabData then
            data = collabInfo
        else
            data = info
        end
    elseif key == EStoreTab.MandelLottery then
        local info, collabInfo = self:GetDrawByType(1)
        if useCollabData then
            data = collabInfo
        else
            data = info
        end
    elseif key == EStoreTab.WeaponSkinSales then
        data = self:GetMallGiftDatas()
    end

    return data
end

function StoreServer:SetReddotChildNodes(tabCnt, id)
    if self.ReddotChildNodes[tabCnt] == nil then
        self.ReddotChildNodes[tabCnt] = { childNodes = {} }
    end

    if id ~= nil then
        self.ReddotChildNodes[tabCnt].childNodes[id] = {}
    end
end

function StoreServer:GetReddotChildNodes()
    return self.ReddotChildNodes
end

function StoreServer:CheckAndSetThirdTabReddotByGroupID(groupid, lotteryid, IsCollabration)
    if groupid == nil then
        return
    end

    if groupid ~= 1 and groupid ~= 3 then
        --mandel and staff
        return
    end
    local ret = false
    local datas, collabDatas = self:GetDrawByType(groupid)
    local drawInfo = nil
    local drawDatas = nil
    if IsCollabration then
        drawDatas = collabDatas
    else
        drawDatas = datas
    end

    if drawDatas ~= nil and #drawDatas > 0 then
        for key, value in pairs(drawDatas) do
            if value.LotteryId == lotteryid then
                drawInfo = value
            end
        end

        if drawInfo ~= nil then
            if drawInfo.RedDotKey > 0 then
                ret = self:GetStoreNeedShowRedDot(drawInfo.LotteryId, drawInfo.RedDotKey)
            end
        end
    end

    if ret and drawInfo ~= nil then
        self:ReportStoreClickRedDot(drawInfo.LotteryId, drawInfo.RedDotKey)
    end
end

function StoreServer:GetStoreThirdTabNeedShowRedDotByGroupID(groupid)
    local ret = false
    if groupid == nil then
        return ret
    end

    if groupid > 20100000 and groupid < 20300000 then
        local lotteryID = Server.StoreServer:GetLotteryIDByRedDotKey(groupid)
        if lotteryID then
            ret = Server.StoreServer:IsMandelUpRewardRedDot(lotteryID) or Server.StoreServer:IsMandelContinuousRewardBtnRedDotByLotteryID(lotteryID)
        end
    elseif groupid > 20300000 then
        local drawDatas = self:GetAllDrawByType(3)
        local draw = nil
        if drawDatas ~= nil and #drawDatas > 0 then
            for index, value in ipairs(drawDatas) do
                if groupid == value.LotteryId then
                    draw = value
                    break
                end
            end
            if draw ~= nil then
                ret = self:GetStoreNeedShowRedDot(groupid, draw.RedDotKey)
            end
        end
    else
        ret = self:GetStoreTabNeedShowRedDotByGroupID(groupid, true)
    end

    return ret
end

function StoreServer:GetStoreTabNeedShowRedDotByGroupID(groupid, useCollabData)
    if groupid == nil then
        return false
    end

    if self.mainTabInfos[groupid] == nil or self.mainTabInfos[groupid].SubTabList == nil then
        return false
    end

    local SubTabLists = Server.StoreServer:StringtoTable(self.mainTabInfos[groupid].SubTabList)
    for _, key in ipairs(SubTabLists) do
        local useCollab = self.mainTabInfos[groupid].UseCollabData
        if useCollabData then
            useCollab = true
        end
        local datas = self:GetRelativeData(EStoreTab[key], useCollab)
        for k, v in pairs(datas) do
            if v.RedDotType == 1 and v.RedDotKey > 0 then
                local shopid = v.TabId
                if shopid == nil then
                    shopid = v.GoodsId
                end
                if shopid == nil then
                    shopid = v.LotteryId
                end
                if self:GetStoreNeedShowRedDot(shopid, v.RedDotKey) then
                    return true
                end
            else 
                if EStoreTab[key] == EStoreTab.MandelLottery then
                    -- 判断是否有高概率奖励红点
                    if self:IsMandelUpRewardRedDot(v.LotteryId) or Server.StoreServer:IsMandelContinuousRewardBtnRedDotByLotteryID(v.LotteryId) then
                        return true
                    end
                end
            end
        end
    end
    return false
end

function StoreServer:GetStoreNeedShowRedDot(shopid, RedDotKey)
    if shopid == nil or RedDotKey == nil or RedDotKey <= 0 then
        return false
    end

    local ret = true
    for clickShopid, clickedKeys in pairs(self.DictStoreRedDot) do
        if shopid == clickShopid then
            for k, clickKey in pairs(clickedKeys) do
                if clickKey == RedDotKey then
                    ret = false
                    break
                end
            end
        end
    end

    return ret
end

function StoreServer:GetSpecialBackClientByRewardItemID(RewardItemID)
    local ret = nil
    for k, v in pairs(self._StoreSpecialBackConfig) do
        if RewardItemID  == v.RewardItem then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetHotRecommendationClientByTabID(TabId)
    local ret = nil
    for k, v in pairs(self._StoreHotRecommendation) do
        if TabId  == v.TabId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetMallGiftClientByTabID(GoodsId)
    local ret = nil
    for k, v in pairs(self._StoreMallGiftConfig) do
        if GoodsId  == v.GoodsId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetStoreBHDClientByGoodsID(GoodsId)
    local ret = nil
    for k, v in pairs(self._StoreBHDConfig) do
        if GoodsId  == v.GoodsId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetStoreCashAreaDiffByRegionNumericCode(RegionNumericCode)
    local ret = nil
    for k, v in pairs(self._StoreCashAreaDiff) do
        if RegionNumericCode == v.CountryId then
            ret = v
        end
    end
    return ret
end

function StoreServer:GetStoreCashAreaDiffByCurrencyCode(CurrencyCode)
    local ret = nil
    for k, v in pairs(self._StoreCashAreaDiff) do
        if CurrencyCode == v.CurrencyShort then
            ret = v
        end
    end
    return ret
end

function StoreServer:GetLotteryClientByTabID(LotteryId)
    local ret = nil
    for k, v in pairs(self._storeLottertClientConfig) do
        if LotteryId  == v.LotteryId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetThemeConfigClientByTabID(ThemeId)
    local ret = nil
    for k, v in pairs(self._themeConfig) do
        if ThemeId  == v.ThemeId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetLotteryProbDistributionClientByID(lotteryId, Id)
    local ret = nil
    for _, v in pairs(self._lotteryProbDistributionClientConfig) do
        if lotteryId == v.LotteryId and Id == v.SortIndex then
            ret = v
            break
        end
    end

    return ret
end

function StoreServer:GetLotteryDataByLotteryID(LotteryId)
    local ret = nil
    for k, v in pairs(self.DictStoreLotteryItems) do
        if LotteryId  == v.LotteryId then
            if self:CheckLotteryInTime(v) == true then
                ret = v
                loginfo("[StoreServer] GetLotteryDataByLotteryID get data:" .. v.LotteryId)
            end
        end
    end

    return ret
end

function StoreServer:GetLotteryDatas()
    local ret = {}
    for k, v in pairs(self.DictStoreLotteryItems) do
        if self:CheckLotteryInTime(v) == true then
            table.insert(ret, v)
        end
    end

    return ret
end

function StoreServer:GetMallGiftDatas()
    local ret = {}
    for k, v in pairs(self.DictStoreMallGiftItems) do
        if v.IsBlocked == nil or v.IsBlocked < 1 then
            table.insert(ret, v)
        end
    end

    return ret
end

function StoreServer:GetStoreBHDDatas()
    local ret = {}
    for k, v in pairs(self.DictStoreBHDItems) do
        table.insert(ret, v)
    end

    return ret
end

function StoreServer:GetMallGiftWeaponSkins()
    local ret = {}
    for k, v in pairs(self.DictStoreMallGiftItems) do
        if v.GoodsType == 0 then
            table.insert(ret, v)
        end
    end

    return ret
end

function StoreServer:GetMallGiftDataByGoodsID(GoodsId)
    local ret = nil
    for k, v in pairs(self.DictStoreMallGiftItems) do
        if GoodsId == v.GoodsId then
            ret = v
        end
    end

    return ret
end

function StoreServer:GetMallGiftWeaponSkinDataByItemID(weaponSkinItemID)
    local ret = nil
    for k, v in pairs(self.DictStoreMallGiftItems) do
        if v.GoodsType == 0 and #v.BundleItems > 0 and v.BundleItems[1].id == weaponSkinItemID then
            ret = v
        end
    end

    return ret
end

--请求干员抽奖数据
function StoreServer:SendHeroDrawData()
    if self._isAlreadyRequested then
        return
    end
    --道具集合
    if self._heroPropList == nil then
        self._heroPropList = {}
    end
    --回包处理数据
    local fBoxInfoFunc = function(res)
        if res.result == 0 then
            for i, info in ipairs(res.lottery_pool_info or {}) do
                for r, reward in ipairs(info.lottery_rewards or {}) do
                    for p, prop in pairs(reward.props or {}) do
                        if prop and prop.id then
                            self._heroPropList[prop.id] = true
                            self._isAlreadyRequested = true
                        end
                    end
                end
            end
        end
    end
    --请求干员抽奖数据
    for index, value in ipairs(self.lottery_item_descs or {}) do
        if value and value.lottery_type == 3 and value.lottery_id and value.lottery_id ~= 0 then
            local req = pb.CSShopGetLotteryInfoReq:New()
            req.lottery_id = value.lottery_id
            req:Request(fBoxInfoFunc)
        end
    end
end

-- 请求曼德尔砖抽奖数据
function StoreServer:SendMandelDrawData()
    local MandelInfo = self:GetDrawByType(1)
    if MandelInfo then
        for key, value in pairs(MandelInfo) do
            local fCallBack = CreateCallBack(function()
                self.Events.evtStoreMandelDrawDataUpdated:Invoke()
            end, self)
            self:GetStoreBoxItemWithCallback(value.MandelItemId, fCallBack)
        end
    end
end

-- 请求宝箱数据
function StoreServer:SendBoxData()
    for _, lottery in pairs(self.DictStoreLotteryItems) do
        local mandelItemID = lottery.MandelItemId
        if mandelItemID and mandelItemID > 0 then
            self:GetStoreBoxItem(mandelItemID)
        end
    end
end

--判断商品是否存在
function StoreServer:CheckStorePropExist(itemId)
    if itemId == nil then
        return false
    end
    --奖池id判断
    for index, value in ipairs(self.lottery_item_descs or {}) do
        if value and value.lottery_id == tonumber(itemId) then
            return true
        end
    end
    --礼包id判断
    for index, value in ipairs(self.mall_gift_descs or {}) do
        if value and value.goods_id == tonumber(itemId) then
            return true
        end
    end
    --捆绑包id判断
    for index, value in ipairs(self.hot_recommendation_descs or {}) do
        if value and value.tab_id == tonumber(itemId) then
            return true
        end
    end
    --判断商城礼包道具
    for k, value in pairs(self.DictStoreMallGiftItems or {}) do
        if value and value.IsBlocked ~= 1 then
            for index, item in ipairs(value.BundleItems or {}) do
                if item and item.id == tonumber(itemId) then
                    return true
                end
            end
        end
    end
    --判断商城干员道具
    if self._heroPropList and self._heroPropList[tonumber(itemId)] then
        return true
    end
    return false
end

function StoreServer:GetDrawByType(drawType)
    local ret = {}
    local res = {}
    for k, v in pairs(self.DictStoreLotteryItems) do
        if drawType == v.LotteryType then
            if self:CheckLotteryInTime(v) == true then
                if v.IsCollaboration ~= self.themeId then
                    table.insert(ret, v)
                    loginfo("[StoreServer] GetDrawByType get data:" .. v.LotteryId)
                else
                    table.insert(res, v)
                end
            end
        end
    end

    table.sort(ret, function (a, b)
        return a.SortIndex < b.SortIndex
    end)

    table.sort(res, function (a, b)
        return a.SortIndex < b.SortIndex
    end)

    return ret, res
end

function StoreServer:GetAllDrawByType(drawType)
    local ret = {}
    for k, v in pairs(self.DictStoreLotteryItems) do
        if drawType == v.LotteryType then
            if self:CheckLotteryInTime(v) == true then
                table.insert(ret, v)
                loginfo("[StoreServer] GetDrawByType get data:" .. v.LotteryId)
            end
        end
    end

    table.sort(ret, function (a, b)
        return a.SortIndex < b.SortIndex
    end)

    return ret
end

function StoreServer:GetDrawByLotteryID(LotteryId)

    local ret = nil
    for k, v in pairs(self.DictStoreLotteryItems) do
        if LotteryId == v.LotteryId then
            if self:CheckLotteryInTime(v) == true then
                ret = v
                loginfo("[StoreServer] GetDrawByLotteryID get data:" .. v.LotteryId)
            end
        end
    end

    return ret
end

function StoreServer:GetDrawByMandelID(inMandelID)
    local ret = nil
    for k, v in pairs(self.DictStoreLotteryItems) do
        if inMandelID == v.MandelItemId then
            if self:CheckLotteryInTime(v) == true then
                ret = v
                loginfo("[StoreServer][GetDrawByMandelID] GetDrawByLotteryID get data:" .. v.MandelItemId)
            end
        end
    end

    return ret
end

function StoreServer:CheckMallGiftInTime(mallGiftInfo)
    local ret = false
    if mallGiftInfo ~= nil then
        local nowTime = Facade.ClockManager:GetLocalTimestamp()
        if mallGiftInfo.GoodsType == 1 and nowTime > mallGiftInfo.OnlineTime and nowTime < mallGiftInfo.OfflineTime then
            ret = true
        end
    end
    loginfo("[StoreServer:CheckMallGiftInTime] GoodsId:"..mallGiftInfo.GoodsId..", isIntime:"..tostring(ret))

    return ret
end

function StoreServer:CheckLotteryInTime(lotteryInfo)
    local ret = false
    if lotteryInfo ~= nil then
        local nowTime = Facade.ClockManager:GetLocalTimestamp()
        if nowTime > lotteryInfo.BeginTime and nowTime < lotteryInfo.EndTime then
            ret = true
        end
    end

    return ret
end

function StoreServer:GetLotteryBoxConfigByID(boxID)
    for k, v in pairs(self._lotteryBoxConfig) do
        if boxID == v.BoxID then
            loginfo("[StoreServer] GetLotteryBoxConfigByID get data:" .. v.BoxID)
            return v
        end
    end

    return nil
end

function StoreServer:GetLotteryBoxGroupConfigByID(boxID)
    local ret = {}
    for k, v in pairs(self._lotteryBoxGroupConfig) do
        if boxID == v.BoxID then
            table.insert(ret, v)
            loginfo("[StoreServer] GetLotteryBoxGroupConfigByID get data:" .. v.BoxID)
        end
    end

    return ret
end

function StoreServer:GetFreeMandelBrickConfigByID(FreeMandelBrickID)
    if not self._FreeMandelBrickConfig then
        return nil
    end

    for k, v in pairs(self._FreeMandelBrickConfig) do
        if FreeMandelBrickID == v.FreeMandelBrickID then
            return v
        end
    end

    return nil
end

function StoreServer:GetCurrSeasonMandelID()
    if not self._FreeMandelBrickConfig then
        return nil
    end

    local iCurrSeasonID = Server.BattlePassServer:GetSeasonID()
    for k, v in pairs(self._FreeMandelBrickConfig) do
        if iCurrSeasonID == v.SeasonID then
            return v.FreeMandelBrickID
        end
    end

    return 16110001002
end

function StoreServer:GetSeasonByMandelID(mandelID)
    if not self._FreeMandelBrickConfig then
        return nil
    end

    for k, v in pairs(self._FreeMandelBrickConfig) do
        if v.FreeMandelBrickID == mandelID then
            return v.SeasonID
        end
    end

    return 202501
end

function StoreServer:GetLotteryBoxPropConfigByID(groupID)
    local ret = {}
    for k, v in pairs(self._lotteryBoxPropConfig) do
        if groupID == v.GroupID then
            table.insert(ret, v)
            loginfo("[StoreServer] GetLotteryBoxConfigByID get data:" .. v.GroupID)
        end
    end

    return ret
end

function StoreServer:GetLotteryBoxPropSingleConfigByID(groupID, propID)
    for k, v in pairs(self._lotteryBoxPropConfig) do
        if groupID == v.GroupID and propID == v.PropID then
            return v
        end
    end
end

function StoreServer:GetRecommondBuyRecordByTabId(tab_id)
    local ret = nil

    if self.hot_recommendation_records ~= nil then
        for k, v in pairs(self.hot_recommendation_records) do
            if tab_id == v.tab_id then
                ret = v
                loginfo("[StoreServer] GetRecommondBuyRecordByTabId get data:" .. tab_id)
            end
        end
    end

    return ret
end

---@param limit_type number LimitType of mallgift or recommendation
---@return number
function StoreServer:GetBuyLimitResetTimeByLimitType(limit_type)
    local ret = 0
    for k, v in pairs(self.limit_list) do
        if limit_type == v.limit_type then
            ret = v.start_time
        end
    end

    return ret
end


function StoreServer:GetSpecialItemInfoByPresentItemId(presentItemId)
    local ret = nil
    if not self.special_item_list then
        return ret
    end
    for k, v in pairs(self.special_item_list) do
        if presentItemId == v.present_item_id then
            ret = v
            break
        end
    end

    return ret
end


function StoreServer:GetGetMallGiftRecordByGoodsId(GoodsId)
    local ret = nil

    if self.mall_gift_records ~= nil then
        for k, v in pairs(self.mall_gift_records) do
            if GoodsId == v.goods_id then
                ret = v
                loginfo("[StoreServer] GetGetMallGiftRecordByGoodsId get data:" .. GoodsId)
            end
        end
    end

    return ret
end

function StoreServer:GetGetMallBHDRecordByGoodsId(GoodsId)
    local ret = false

    if self.bhd_list ~= nil then
        for k, v in pairs(self.bhd_list) do
            if GoodsId == v then
                ret = true
                loginfo("[StoreServer] GetGetMallBHDRecordByGoodsId get data:" .. GoodsId)
            end
        end
    end

    return ret
end

---@param LotteryId number 奖池id
function StoreServer:GetLotteryRecordsByLotteryId(LotteryId)
    local ret = {}

    if self.lottery_records ~= nil then
        for k, v in pairs(self.lottery_records) do
            if LotteryId == k then
                ret = v
                loginfo("[StoreServer] GetLotteryRecordsByLotteryId get data:" .. LotteryId)
            end
        end
    end

    return ret
end

function StoreServer:GetLotterySpecialItemBuyConfig(ItemID)
    local ret = nil

    if self._SpecialItemBuyConfig ~= nil then
        for k, v in pairs(self._SpecialItemBuyConfig) do
            if ItemID == v.PresentItemID then
                ret = v
                loginfo("[StoreServer] GetLotterySpecialItemBuyConfig get data:" .. ItemID)
            end
        end
    end

    return ret
end

-- 根据附赠物品id，获取数据
function StoreServer:GetLotterySpecialItemBuyConfigByPresentItemId(PresentItemId)
    local ret = nil

    if self._SpecialItemBuyConfig ~= nil then
        for k, v in pairs(self._SpecialItemBuyConfig) do
            if PresentItemId == v.PresentItemID then
                ret = v
                loginfo("[StoreServer] GetLotterySpecialItemBuyConfigByPresentItemId get data:" .. PresentItemId)
            end
        end
    end

    return ret
end

function StoreServer:BuyLotteryItem(itemInfos, isOpenDirectly, lotteryID,fOnBuySuccessCallback, lottery_round, isMandel, giftItemIDs)
    isMandel = setdefault(isMandel, true)
    local fOnBuyLotteryItemRes = function(res)
        if res.result == 0 then
            local reqPurchaseItemidMap = {}
            if giftItemIDs then
                for index, value in ipairs(giftItemIDs) do
                    reqPurchaseItemidMap[value] = true
                end
            end

            --本地判断是否是赠送道具（除了发起请求购买的道具其他都是赠送）
            for index, change in ipairs(res.change.prop_changes) do
                if reqPurchaseItemidMap[change.prop.id] then
                    change.prop.bGiveaway = true
                end
            end

            if fOnBuySuccessCallback then
                fOnBuySuccessCallback(res)
            else
                self.Events.evtStoreMandelLotteryItemBuyChange:Invoke(res.change) --默认行为
            end
        else
            loginfo("[StoreServer] BuyLotteryItem res.result:" .. tostring(res.result))
            self.Events.evtStoreMandelLotteryItemBuyFailed:Invoke(res.result)
        end
    end
    if isMandel then
        local req = pb.CSShopBuyLotteryItemReq:New()
        req.buy_props = itemInfos
        req.is_open_directly = isOpenDirectly
        req.lottery_id = lotteryID
        if lottery_round ~= nil then
            req.lottery_round = lottery_round
        end

        req:Request(fOnBuyLotteryItemRes)
    else
        local req = pb.CSShopOpenLotteryItemReq:New()
        req.lottery_id = lotteryID
        req.buy_prop = itemInfos[1]
        req.round = lottery_round
        req:Request(fOnBuyLotteryItemRes)
    end
end

----商城曼德尔砖的开砖使用的是RewardServer:OpenBlindBox接口
function StoreServer:OpenMandelBox(items, num, instant_assemble)
    local fOnCSOpenBoxRes = function(res)
        if res.result == 0 then
            if res.send_by_mail == true then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryIsFull)
            end

            self.Events.evtStoreOpenBoxSuccess:Invoke(res.change)
        else
            loginfo("[StoreServer] OpenMandelBox res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSOpenBoxReq:New()
    req.box_list = {}

    for i, item in ipairs(items) do
        local boxId = 0
        local itemInfo = ItemConfigTool.GetItemConfigById(item.id)
        if itemInfo then
            boxId = itemInfo.ConnectedPool or 0
        end
        local lotteryBoxData = pb.LotteryBoxData:New()
        lotteryBoxData.box_id = boxId
        lotteryBoxData.num = num

        lotteryBoxData.opened_prop_id = item.id
        lotteryBoxData.opened_prop_gid = item.gid
        lotteryBoxData.opened_prop_num =  item.num
        table.insert(req.box_list, lotteryBoxData)

        self:ClearBoxInfoCache(boxId)

        loginfo("[StoreServer] OpenMandelBox item.id:" .. item.id)
    end

    req.instant_assemble = instant_assemble

    req:Request(fOnCSOpenBoxRes)
end

function StoreServer:GetTimeLimitComsumeCouponId()
    if self.timeLimitComsumeCouponId == nil then
        local propStr = nil
        for index, value in ipairs(self._PropCombinationConfig) do
            if value.CombinationID == 1 then
                propStr = value.PropArray
            end
        end
        local list = {}
        for num in string.gmatch(propStr, "%d+") do
            table.insert(list, tonumber(num))
        end
        self.timeLimitComsumeCouponId = list[1]
    end
    return self.timeLimitComsumeCouponId
end

function StoreServer:RefreshServerThemeItemInfo()
    self:CheckIsCollaborationOn()

    for k, v in pairs(self.ThemeBundleList) do
        if self.DictStoreHotRecommendItems[v.id] then
            self.DictStoreHotRecommendItems[v.id]:RefreshServerThemeInfo(v)
        end
    end

    for k, v in pairs(self.ThemeTabList) do
        if self.ThemeConfigDatas[v.id] then
            self.ThemeConfigDatas[v.id]:RefreshServerInfo(v)
        else
            local client_config = self:GetThemeConfigClientByTabID(v.id)
            if client_config ~= nil then
                self.ThemeConfigDatas[v.id] = ThemeConfigStruct:New(v, client_config)
            end
        end
    end
end

function StoreServer:RefreshServerItemInfo()
    --hot_recommendation_descs
    for k, v in pairs(self.hot_recommendation_descs) do
        if self.DictStoreHotRecommendItems[v.tab_id] then
            self.DictStoreHotRecommendItems[v.tab_id]:RefreshServerInfo(v)
        else
            local client_config = self:GetHotRecommendationClientByTabID(v.tab_id)
            if client_config ~= nil then
                self.DictStoreHotRecommendItems[v.tab_id] = StoreRecommendItem:New(v, client_config)
            end
        end
    end

    --mall_gift_descs
    for k, v in pairs(self.mall_gift_descs) do
        if self.DictStoreMallGiftItems[v.goods_id] then
            self.DictStoreMallGiftItems[v.goods_id]:RefreshServerInfo(v)
        else
            local client_config = self:GetMallGiftClientByTabID(v.goods_id)
            if client_config ~= nil then
                self.DictStoreMallGiftItems[v.goods_id] = StoreMallGiftItem:New(v, client_config)
            end
        end
    end

    --lottery_item_descs
    for k, v in pairs(self.lottery_item_descs) do
        if self.DictStoreLotteryItems[v.lottery_id] then
            self.DictStoreLotteryItems[v.lottery_id]:RefreshServerInfo(v)
        else
            local client_config = self:GetLotteryClientByTabID(v.lottery_id)
            if client_config ~= nil then
                self.DictStoreLotteryItems[v.lottery_id] = StoreLotteryItem:New(v, client_config)

                --- BEGIN MODIFICATION @ VIRTUOS: TRC - forcibly block Dpad like icon "Store_Icon_11" on PS5
                if IsPS5() then
                    local blockIconPath = "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_11.Store_Icon_11'"
                    if self.DictStoreLotteryItems[v.lottery_id].LotteryIcon == blockIconPath then
                        self.DictStoreLotteryItems[v.lottery_id].LotteryIcon = "PaperSprite'/Game/UI_HD/UIAtlas/Common_Console/BakedSprite/Icon_Controller.Icon_Controller'"
                    end
                end
                --- END MODIFICATION
            end
        end
    end
    -- 请求当前奖池的数据
    local NormalLotteryDataList, LotteryDataList = self:GetDrawByType(3)
    if #LotteryDataList > 0 or #NormalLotteryDataList > 0 then
        if LotteryDataList[1] and LotteryDataList[1].IsCollaboration == self.themeId then
            self.currentLotteryId = LotteryDataList[1].LotteryId
        else
            self.currentLotteryId = NormalLotteryDataList[1].LotteryId
        end
        
        if self.currentLotteryId then
            -- Timer.DelayCall(1.0, function()
            --     self:GetServerLotteryInfo(self.currentLotteryId)
            -- end)
            self:GetServerLotteryInfo(self.currentLotteryId)
        end
    end

    -- --bhd
    -- for k, v in pairs(self.bhd_item_list) do
    --     if self.DictStoreBHDItems[v.goods_id] then
    --         self.DictStoreBHDItems[v.goods_id]:RefreshServerInfo(v)
    --     else
    --         local client_config = self:GetStoreBHDClientByGoodsID(v.goods_id)
    --         if client_config ~= nil then
    --             self.DictStoreBHDItems[v.goods_id] = StoreBHDBundleStruct:New(v, client_config)
    --         end
    --     end
    -- end

    -- 刷新曼德尔砖抽奖缓存
    self:RefreshLotteryRedDotKey2LotteryID()
end

function StoreServer:SendShopGetThemeBundleTimeConfigReq()
    local fOnShopGetThemeBundleTimeConfigRes = function(res)
        loginfo("[StoreServer] fOnShopGetThemeBundleTimeConfigRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.ThemeTabList = res.theme_tab_list
            self.ThemeBundleList = res.bundle_list

            self:RefreshServerThemeItemInfo()
        else
            logerror("[StoreServer] fOnShopGetThemeBundleTimeConfigRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopGetThemeBundleTimeConfigReq:New()
    req:Request(fOnShopGetThemeBundleTimeConfigRes)
end

function StoreServer:SendShopNewGetConfigReq()
    local fOnShopNewGetConfigRes = function(res)
        loginfo("[StoreServer] fOnShopNewGetConfigRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.hot_recommendation_descs = res.hot_recommendation_descs
            self.mall_gift_descs = res.mall_gift_descs
            self.lottery_item_descs = res.lottery_item_descs
            self.limit_list = res.limit_list
            self.special_item_list = res.special_item_list
            self.has_cur_special_back = res.has_cur_special_back
            self.next_special_back_refresh_time = res.next_special_back_refresh_time
            -- self.bhd_item_list = res.bhd_item_list

            self:RefreshServerItemInfo()

            -- 请求曼德尔砖抽奖数据
            self:SendMandelDrawData()

            -- 请求宝箱数据
            self:SendBoxData()

            self.Events.evtStoreDataRefresh:Invoke()

            --请求干员抽奖数据
            self:SendHeroDrawData()
        else
            logerror("[StoreServer] fOnShopNewGetConfigRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopNewGetConfigReq:New()
    req:Request(fOnShopNewGetConfigRes)
end

function StoreServer:SendShopGetLuckyNestConfigReq()
    local fOnShopGetLuckyNestConfigRes = function(res)
        loginfo("[StoreServer] fOnShopGetLuckyNestConfigRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.nmTimesOfHiddenBox     = res.TimesOfHiddenBox
            self.nmRefreshLimit         = res.RefreshLimit
            self.nmBeginTime            = res.BeginTimeSt
            self.nmEndTime              = res.EndTimeSt
            self.nmBonusItems           = res.bonus_items
            self.nmSpecialItems         = res.special_item_list
            self.nmBackItems            = res.back_items
            self.nmFreeBuff             = res.free_buff

            self:SetLuckyNestBoughtGoods()
        else
            logerror("[StoreServer] fOnShopGetLuckyNestConfigRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopGetLuckyNestConfigReq:New()
    req:Request(fOnShopGetLuckyNestConfigRes)
end

function StoreServer:SendShopRaffleLuckyNestReq()
    local fOnShopRaffleLuckyNestRes = function(res)
        loginfo("[StoreServer] fOnShopRaffleLuckyNestRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.nmTimesOfHiddenBox     = res.times_of_hidden_box
            self.nmRefreshLimit         = res.refresh_limit
            self.nmBonusItems           = res.bonus_items
            self.nmSpecialItems         = res.special_item_list
            self.nmBackItems            = res.back_items
            self.nmFreeBuff             = res.free_buff
        else
            logerror("[StoreServer] fOnShopRaffleLuckyNestRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopRaffleLuckyNestReq:New()
    req:Request(fOnShopRaffleLuckyNestRes)
end

function StoreServer:SendShopGetSpecialBackInfoReq()
    if self.has_cur_special_back ~= true then
        logerror("[StoreServer] SendShopGetSpecialBackInfoReq has_cur_special_back == false:")
        return
    end

    local fOnShopGetSpecialBackInfoRes = function(res)
        loginfo("[StoreServer] SendShopGetSpecialBackInfo res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.specialback_config = res.cur_special_back_config
            self.specialback_cur_items = res.cur_items

            self:RefreshServerSpecialBackInfo()

            self.Events.evtStoreBuyMarketUpdate:Invoke()
        else
            logerror("[StoreServer] SendShopGetSpecialBackInfo res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopGetSpecialBackInfoReq:New()
    req:Request(fOnShopGetSpecialBackInfoRes)
end

function StoreServer:RefreshServerSpecialBackInfo()
    self.DictStoreSpecialBackItems = {}
    for k, v in pairs(self.specialback_cur_items) do
        if self.DictStoreSpecialBackItems[v.RewardItem] then
            self.DictStoreSpecialBackItems[v.RewardItem]:RefreshServerInfo(v)
        else
            local client_config = self:GetSpecialBackClientByRewardItemID(v.RewardItem)
            if client_config ~= nil then
                self.DictStoreSpecialBackItems[v.RewardItem] = StoreSpecialBackItemStruct:New(v, client_config)
            end
        end
    end
end

function StoreServer:SendShopGetClickedRedDotReq()
    local fOnShopGetClickedRedDotRes = function(res)
        loginfo("[StoreServer] fOnShopGetClickedRedDotRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            -- self.DictStoreRedDot = {}
            for key, value in pairs(res.red_dot_list) do
                if value.shop_id > 0 then
                    self.DictStoreRedDot[value.shop_id] = value.red_dot_list
                end

                -- logerror("[StoreServer] fOnShopGetClickedRedDotRes add clicked redpoint , shop_id:" .. tostring(value.shop_id))
            end

            self.Events.evtStoreReddotChanged:Invoke()
        else
            logerror("[StoreServer] fOnShopGetClickedRedDotRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopGetClickedRedDotReq:New()
    req:Request(fOnShopGetClickedRedDotRes)
end

function StoreServer:ReportStoreClickRedDot(ShopID, RedDotKey)
    if ShopID == nil or RedDotKey == nil then
        return
    end

    if self.DictStoreRedDot[ShopID] == nil then
        self.DictStoreRedDot[ShopID] = {}
    end

    table.insert(self.DictStoreRedDot[ShopID], RedDotKey)
    local fOnShopClickRedDotRes = function(res)
        if res.result == 0 then
            loginfo("[StoreServer] fOnShopClickRedDotRes res.result:" .. tostring(res.result))
        else
            logerror("[StoreServer] fOnShopClickRedDotRes res.result:" .. tostring(res.result))
        end

        self.Events.evtStoreReddotChanged:Invoke()
    end

    local req = pb.CSShopClickRedDotReq:New()
    req.shop_id = ShopID
    req.red_dot_key = RedDotKey
    req:Request(fOnShopClickRedDotRes)
end


function StoreServer:GetAllStoreProductInfo()
    local needCheckProductID = {}

    if self.DictStoreMallGiftItems ~= nil then
        for k, v in pairs(self.DictStoreMallGiftItems) do
            if v.IsCash == 1 and v.ProductID ~= nil and v.IsBlocked ~= 1 then
                table.insert(needCheckProductID, v.ProductID)
            end
        end
    end

    return needCheckProductID
end

function StoreServer:SendShopGetBuyRecordReq()
    local fOnCSShopGetBuyRecordRes = function(res)
        logerror("[StoreServer] fOnCSShopGetBuyRecordRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.hot_recommendation_records = res.hot_recommendation_records
            self.mall_gift_records = res.mall_gift_records
            self.open_lottery_records = res.open_lottery_records
            self.bhd_list = res.bhd_list
            self.pv_play_list = res.pv_play_list

            self.Events.evtStoreBuyRecordUpdate:Invoke()
        else
            logerror("[StoreServer] fOnCSShopGetBuyRecordRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopGetBuyRecordReq:New()
    req:Request(fOnCSShopGetBuyRecordRes)
end

function StoreServer:SendShopBuyLuckyNestItemReq(item_id, currency_type, price, currency_type_substitute, price_substitute)
    local fOnCSShopBuyLuckyNestItemRes = function(res)
        logerror("[StoreServer] fOnCSShopBuyLuckyNestItemRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            if not self.LuckyNestBuyRecord then
                self.LuckyNestBuyRecord = {}
            end

            -- if res.change and res.change.prop_changes then
            --     local prop_changes = res.change.prop_changes
            --     for _, propChange in ipairs(prop_changes) do
            --         if propChange.prop then
            --             self.LuckyNestBuyRecord[propChange.prop.id] = true
            --         end
            --     end
            -- end

            self:SendShopGetLuckyNestConfigReq()

            self.Events.evtStoreBuyLuckyNestSuc:Invoke(res.change)
        else
            logerror("[StoreServer] fOnCSShopBuyLuckyNestItemRes res.result:" .. tostring(res.result))
        end
    end

    local req = pb.CSShopBuyLuckyNestItemReq:New()
    req.item_id = item_id
    req.currency_type = currency_type
    req.price = price
    req.currency_type_substitute = currency_type_substitute
    req.price_substitute = price_substitute
    req:Request(fOnCSShopBuyLuckyNestItemRes)
end

-- function StoreServer:GetLuckyNestBuyRecord()
--     return self.LuckyNestBuyRecord
-- end

--获取当前PV是否已经播放
function StoreServer:GetIsStorePvPlayed(VideoName)
    local hasPlayed = false
    if self.pv_play_list ~= nil then
        for _, value in ipairs(self.pv_play_list) do
            if VideoName == value or VideoName == tostring(value) then
                hasPlayed = true
                break
            end
        end
    end
    return hasPlayed
end

--添加已经播放的视频列表
function StoreServer:AddPlayedPV2List(VideoName)
    local fOnCSShopAddPVPlayListRes = function(res)
        loginfo("[StoreServer] fOnCSShopAddPVPlayListRes res.result:" .. tostring(res.result))
        if res.result == 0 then

        else
            logerror("[StoreServer] fOnCSShopAddPVPlayListRes res.result:" .. tostring(res.result))
        end
    end
    if self.pv_play_list == nil then
        self.pv_play_list = {}
        self.exists = {}
    elseif self.exists == nil then
        self.exists = {}
        for _, value in ipairs(self.pv_play_list) do
            self.exists[value] = true
        end
    end

    if not self.exists[VideoName] then
        table.insert(self.pv_play_list, VideoName)
        self.exists[VideoName] = true

        local req = pb.CSShopAddPVPlayListReq:New()
        req.tab_id_list = self.pv_play_list
        req:Request(fOnCSShopAddPVPlayListRes)
    end
end

---@param tab_id number 商品序列ID
---@param banner_type number 商品类型，1为礼包，2为抽奖
---@param item_ids any 单独购买物品的ID列表
---@param currency_type any 货币类型
---@param price any 价格
---@param currency_type_substitute any 货币类型
---@param price_substitute any 价格
function StoreServer:SendShopBuyHotRecommendationReq(tab_id, banner_type, item_ids, currency_type, price, currency_type_substitute, price_substitute)
    local fOnCSShopBuyHotRecommendationRes = function(res)
        loginfo("[StoreServer] fOnCSShopBuyHotRecommendationRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.Events.evtStoreBuyHotRecommendationSuc:Invoke(res.change, false, nil)

            --request new buy record
            self:SendShopGetBuyRecordReq()
        else
            if res.result == 156015 then
                -- maybe last req not received res
                self:SendShopGetBuyRecordReq()
                logerror("[StoreServer] fOnCSShopBuyHotRecommendationRes self:SendShopGetBuyRecordReq()")
            end

            -- logerror("[StoreServer] fOnCSShopBuyHotRecommendationRes res.result:" .. tostring(res.result))
        end
    end

    loginfo(string.format("[StoreServer] SendShopBuyHotRecommendationReq tab_id:%d currency_type:%d price:%d price_substitute:%d", tab_id, currency_type, price, price_substitute))
    local req = pb.CSShopBuyHotRecommendationReq:New()
    req.tab_id = tab_id
    req.banner_type = banner_type
    if item_ids ~= nil and #item_ids > 0 then
        req.item_ids = item_ids
    end
    req.currency_type = currency_type
    req.price = price
    req.currency_type_substitute = currency_type_substitute
    req.price_substitute = price_substitute

    req:Request(fOnCSShopBuyHotRecommendationRes)
end

---@param goods_id number 商品ID
---@param currency_type any 货币类型
---@param price any 价格
---@param currency_type_substitute any 货币类型
---@param price_substitute any 价格
function StoreServer:SendShopBuyBuyMallGiftReq(goods_id, currency_type, price, currency_type_substitute, price_substitute, num)
    local fOnCSShopBuyMallGiftRes = function(res)
        loginfo("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.Events.evtStoreBuyMallGiftSuc:Invoke(res.change, false, nil)
            --request new buy record
            self:SendShopGetBuyRecordReq()
        else
            -- logerror("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
        end
    end

    loginfo(string.format("[StoreServer] SendShopBuyBuyMallGiftReq goods_id:%d currency_type:%d price:%d price_substitute:%d", goods_id, currency_type, price, price_substitute))
    local req = pb.CSShopBuyMallGiftReq:New()
    req.goods_id = goods_id
    req.currency_type = currency_type
    req.price = price
    req.currency_type_substitute = currency_type_substitute
    req.price_substitute = price_substitute
    req.num = num or 1
    req.is_cash_buy = false

    req:Request(fOnCSShopBuyMallGiftRes)
end


---@param goods_id number 商品ID
---@param product_id any productId
---@param businessID any businessID
---@param payChannel any payChannel
function StoreServer:SendShopBuyBuyMallGiftUseCashReq(goods_id, product_id, businessID, payChannel)
    local fOnCSShopBuyMallGiftRes = function(res)
        loginfo("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            -- tb.midas_result_info = pb.pb_MidasResulinfoDecode(decoder:getsubmsg(3))
            -- tb.is_cash_buy = decoder:getbool(4)
            self.Events.evtStoreBuyMallGiftSuc:Invoke(res.change, res.is_cash_buy, res.midas_result_info)
            --request new buy record
            self:SendShopGetBuyRecordReq()
        else
            -- logerror("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
            Server.PayServer.Events.evtOnPayServerError:Invoke(res.result)
        end
    end

    loginfo(string.format("[StoreServer] SendShopBuyBuyMallGiftUseCashReq goods_id:%d ", goods_id))
    local req = pb.CSShopBuyMallGiftReq:New()
    req.goods_id = goods_id
    req.currency_type = 0
    req.price = 0
    req.currency_type_substitute = 0
    req.price_substitute = 0
    req.num = 1

    local need_riskctl_check = (Server.SDKInfoServer:GetPlayerAdultState() <= 0) and (Server.SDKInfoServer:IsRegionJapan() or Server.SDKInfoServer:IsRegionKorea())

    local buyGoodsReq = pb.CSBuyGoodsReq:New()
    buyGoodsReq.product_id = product_id
    buyGoodsReq.quantity = 1
    buyGoodsReq.discount = 0
    buyGoodsReq.provide_midas_appid = businessID
    buyGoodsReq.pay_channel = payChannel
    buyGoodsReq.delta = 0
    buyGoodsReq.need_riskctl_check = need_riskctl_check

    req.CashBuyReq = buyGoodsReq
    req.is_cash_buy = true

    req:Request(fOnCSShopBuyMallGiftRes)
end

---@param SpecialBackId number 返场ID
---@param item_id number 商品ID
---@param currency_type any 货币类型
---@param price any 价格
---@param currency_type_substitute any 货币类型
---@param price_substitute any 价格
function StoreServer:SendShopBuyMarketReq(SpecialBackId, item_id, currency_type, price, currency_type_substitute, price_substitute)
    local fOnCSShopBuySpecialBackRes = function(res)
        loginfo("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.Events.evtStoreBuyMarketSuc:Invoke(res.change, false, nil)
            --request new specialbackinfo
            self:SendShopGetSpecialBackInfoReq()
        else
            -- logerror("[StoreServer] fOnCSShopBuyMallGiftRes res.result:" .. tostring(res.result))
        end
    end

    loginfo(string.format("[StoreServer] SendShopBuyMarketReq item_id:%d currency_type:%d price:%d price_substitute:%d", item_id, currency_type, price, price_substitute))
    local req = pb.CSShopBuySpecialBackItemReq:New()
    req.SpecialBackId = SpecialBackId
    req.item_id = item_id
    req.currency_type = currency_type
    req.price = price
    req.currency_type_substitute = currency_type_substitute
    req.price_substitute = price_substitute

    req:Request(fOnCSShopBuySpecialBackRes)
end

function StoreServer:SendShopBuyBHDReq(goods_id, currency_type, price, currency_type_substitute, price_substitute, num)
    local fOnCSShopBuyMallBHDRes = function(res)
        loginfo("[StoreServer] fOnCSShopBuyHotRecommendationRes res.result:" .. tostring(res.result))
        if res.result == 0 then
            self.Events.evtStoreBuyMallGiftSuc:Invoke(res.change, false, nil)
            self:SendShopGetBuyRecordReq()
        else

        end
    end

    loginfo(string.format("[StoreServer] SendShopBuyBHDReq goods_id:%d currency_type:%d price:%d", goods_id, currency_type, price))
    local req = pb.CSShopBuyMallBHDReq:New()
    req.goods_id = goods_id
    req.currency_type = currency_type
    req.price = price
    req.currency_type_substitute = currency_type_substitute
    req.price_substitute = price_substitute
    req.num = num

    req:Request(fOnCSShopBuyMallBHDRes)
end

------------------

function StoreServer:ReqAllStoreItemConfig()
    local fOnGetStoreItemRes = function(res, reqWrapperIns)
        --拉取第一页，如果版本信息和服务器一致，则无需拉取剩余页服务器数据
        if reqWrapperIns.time == 1 and res.version_info ~= nil then
            if res.version_info.ver == self._shopConfigServerVersion then
                reqWrapperIns:Reset()
                --请求购买记录
                self:ReqShopBuyRecord()
                loginfo("[StoreServer] ReqAllStoreItemData fOnPartResCallback version_info not change:",
                        self._shopConfigServerVersion)
                return
            else
                self._shopConfigServerVersion = res.version_info.ver
                loginfo("[StoreServer] ReqAllStoreItemData fOnGetStoreItemRes version_info change:",
                        self._shopConfigServerVersion)
            end
        end

        if res.result == 0 then
            for k, v in ipairs(res.goods_list) do
                if self._storeItemList[v.shop_id] then
                    self._storeItemList[v.shop_id]:RefreshServerInfo(v)
                    self._storeItemIdDic[v.goods_id] = v.shop_id
                else
                    self._storeItemList[v.shop_id] = StoreItemStruct:New(v, self._storeClientConfig[tostring(v.shop_id)])
                    self._storeItemIdDic[v.goods_id] = v.shop_id
                end
            end
        else
            logerror("[StoreServer] ReqAllStoreItemData fOnGetStoreItemRes fail result:", res.result)
            -- logerror('ReqAllStoreItemConfig 协议拉取失败, 已经拉取次数:', reqWrapperIns.time)
        end

        if res.is_finish then
            --请求购买记录
            self:ReqShopBuyRecord()
        end
    end

    local fOnSuccessCallback = function(time)
        logwarning("[StoreServer] ReqAllStoreItemData fOnSuccessCallback total times:", time)
    end

    local fOnFailureCallback = function(time)
        logerror("[StoreServer] ReqAllStoreItemData fOnFailureCallback get times:", time)
    end

    local req = pb.CSShopGetConfigReq:New()
    local partParam = {
        start_index = 0,
        get_num = 100,
        time = 0,
        reqIns = req,
        fOnSuccessCallback = fOnSuccessCallback,
        fOnFailureCallback = fOnFailureCallback,
    }
    req:PartRequest(fOnGetStoreItemRes, { maxWaitTime = 10 }, nil, partParam)
end

--请求购买记录
function StoreServer:ReqShopBuyRecord()
    local fOnCSShopGetRecordRes = function(res)
        if res.result == 0 then
            self:RefreshShopBuyRecord(res.record_list)
        else
            logerror("[StoreServer] ReqShopBuyRecord res.result:" .. tostring(res.result))
        end
        self.Events.evtStoreDataChanged:Invoke()
    end
    local req = pb.CSShopGetRecordReq:New()
    req:Request(fOnCSShopGetRecordRes)
end

--刷新购买记录
function StoreServer:RefreshShopBuyRecord(record_list)
    if record_list == nil then
        return
    end
    for i, buyRecord in ipairs(record_list) do
        if self._storeItemList[buyRecord.shop_id] then
            self._storeItemList[buyRecord.shop_id]:RefreshServerRecordInfo(buyRecord)
        end

        if buyRecord.pay_list ~= nil then
            for j, payRecord in ipairs(buyRecord.pay_list) do
                if payRecord.batch_item_list ~= nil then
                    for k, batchItem in ipairs(payRecord.batch_item_list) do
                        local item = self:GetStoreItemByItemId(batchItem)
                        if item then
                            item:MarkBuyRecord()
                        end
                    end
                end
            end
        end
    end
end

---请求购买商品
---@param storeItemId number 商店物品Id
---@param payment_idx number StoreItemStruct.payment_list 可能有多个支付类型，这里是payment_list中对应支付类型的index，从0开始，默认为0
---@param payment any payment_list中的某一项
---@param callback any 购买后的回调
---@param buyNum any 购买数量
---@param batch_buy_price any 礼包对应批量物品的价格，已经是去掉已得物品的价格
---@param own_list any 已得的物品列表，对于批量购买时，可以减少总价
function StoreServer:ReqBuy(storeItemId, payment_idx, payment, callback, buyNum, batch_buy_price, own_list)
    local fOnCSShopBuyRes = function(res)
        if callback then
            callback(res)
        end
        if res.result == 0 then
            self:RefreshShopBuyRecord(res.record_list)
            self.Events.evtStoreBuySuc:Invoke(res.change)
        else
            logerror("[StoreServer] ReqBuy res.result:" .. tostring(res.result))
        end
    end

    buyNum = setdefault(buyNum, 1)
    batch_buy_price = setdefault(batch_buy_price, 0)
    local req = pb.CSShopBuyReq:New()
    local buyItem = {}
    buyItem.shopid = storeItemId
    buyItem.payment_idx = payment_idx
    buyItem.payment = payment
    buyItem.buy_num = buyNum
    buyItem.batch_buy_price = batch_buy_price
    buyItem.own_list = own_list
    req.buy_list = {
        [1] = buyItem,
    }
    req:Request(fOnCSShopBuyRes)
end

---请求购买多个商品
---@param buyList table
---@param callback function
function StoreServer:ReqMutiBuy(buyList, callback)
    if buyList == nil or #buyList == 0 then
        logerror("[StoreServer] ReqMutiBuy buyList is nil")
        return
    end
    local fOnCSShopBuyRes = function(res)
        if callback then
            callback(res)
        end
        if res.result == 0 then
            self:RefreshShopBuyRecord(res.record_list)
            self.Events.evtStoreBuySuc:Invoke()
        else
            logerror("[StoreServer] ReqMutiBuy res.result:" .. tostring(res.result))
        end
    end
    local req = pb.CSShopBuyReq:New()
    req.buy_list = buyList
    req:Request(fOnCSShopBuyRes)
end

--请求领取每日免费礼包
---@param storeItemId number 商店物品Id
---@param payment_idx number StoreItemStruct.payment_list 可能有多个支付类型，这里是payment_list中对应支付类型的index，从0开始，默认为0
---@param payment any payment_list中的某一项
---@param callback any 购买后的回调
---@param buyNum any 购买数量
---@param batch_buy_price any 礼包对应批量物品的价格，已经是去掉已得物品的价格
---@param own_list any 已得的物品列表，对于批量购买时，可以减少总价
function StoreServer:ReqFreePick(storeItemId, payment_idx, payment, callback, buyNum, batch_buy_price, own_list)
    local fCSShopFreePickRes = function(res)
        if callback then
            callback(res)
        end
        if res.result == 0 then
            self:RefreshShopBuyRecord(res.record_list)
            self.Events.evtStoreBuySuc:Invoke(res.change)
        else
            logerror("[StoreServer] ReqBuy res.result:" .. tostring(res.result))
        end
    end

    buyNum = setdefault(buyNum, 1)
    batch_buy_price = setdefault(batch_buy_price, 0)
    local req = pb.CSShopFreePickReq:New()
    local buyItem = {}
    buyItem.shopid = storeItemId
    buyItem.payment_idx = payment_idx
    buyItem.payment = payment
    buyItem.buy_num = buyNum
    buyItem.batch_buy_price = batch_buy_price
    buyItem.own_list = own_list
    req.pick_list = {
        [1] = buyItem,
    }
    req:Request(fCSShopFreePickRes)
end

function StoreServer:GetStoreItemList()
    return self._storeItemList
end

---通过商品ID获取商品信息
---@param storeItemId number 商城ItemId
---@return StoreItemStruct
function StoreServer:GetStoreItemByStoreItemId(storeItemId)
    return self._storeItemList[storeItemId]
end

---通过商品ID获取商品信息
---@param itemId number 物品ID
---@return StoreItemStruct
function StoreServer:GetStoreItemByItemId(itemId)
    return self:GetStoreItemByStoreItemId(self._storeItemIdDic[itemId])
end

--通过id得到宝箱内的物品
function StoreServer:GetStoreBoxItem(itemId)
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(itemId)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end

    self:GetStoreBoxByBoxID(boxId)
end

function StoreServer:GetStoreBoxByBoxID(boxId)
    local req = pb.CSGetBoxInfoReq:New()

    if boxId == 0 then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.StoreBoxNotFound)
        return
    end

    if self._lotteryPoolId2BoxInfos[boxId] then
        self.Events.evtStoreBoxItem:Invoke(self._lotteryPoolId2BoxInfos[boxId].info_list[1])
        return
    end

    local fOnCSGetBoxInfoRes = function(res)
        if res.result == 0 then
            self.lottery_records[boxId] = {}
            for key, value in pairs(res.open_lottery_records) do
                if self.lottery_records[value.lottery_id] == nil then
                    self.lottery_records[value.lottery_id] = {}
                end
                table.insert(self.lottery_records[value.lottery_id], value)
            end

            --[这里做调整]:没有抽奖记录，就不会存(奖池相关数据,定轨数据) ssy 2025-8-7
            if self._lotteryPoolId2BoxInfos == nil then
                self._lotteryPoolId2BoxInfos = {}
            end
            self._lotteryPoolId2BoxInfos[boxId] = res --缓存开砖奖池信息

            self.Events.evtStoreBoxItem:Invoke(res.info_list[1])
            self.Events.evtStoreReddotChanged:Invoke()
        else
            logerror("[StoreServer] fOnCSGetBoxInfoRes res.result:" .. tostring(res.result))
        end
    end

    req.id_list = { boxId }
    req.source = 1
    req:Request(fOnCSGetBoxInfoRes, { bEnableHighFrequency = true})
end

--通过id得到宝箱内的物品
function StoreServer:GetStoreBoxItemWithCallback(itemId, callback)

    local req = pb.CSGetBoxInfoReq:New()
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(itemId)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end

    if boxId == 0 then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.StoreBoxNotFound)
        return
    end

    if self._lotteryPoolId2BoxInfos[boxId] then
        if callback then
            callback(self._lotteryPoolId2BoxInfos[boxId].info_list[1], itemId)
        end
        return
    end

    local fOnCSGetBoxInfoRes = function(res)
        if res.result == 0 then
            self.lottery_records[boxId] = {}
            for key, value in pairs(res.open_lottery_records) do
                if self.lottery_records[value.lottery_id] == nil then
                    self.lottery_records[value.lottery_id] = {}
                end

                table.insert(self.lottery_records[value.lottery_id], value)
            end

            self._lotteryPoolId2BoxInfos[boxId] = res --缓存开砖奖池信息
            self.Events.evtStoreReddotChanged:Invoke()

            if callback then
                --[[
                if res.info_list[1] and res.info_list[1].group_list then
                    local realPropSum = 0
                    local lastPropInfo
                    for index, giftGroupInfo in ipairs(res.info_list[1].group_list) do
                        for index , propInfo in ipairs(giftGroupInfo.prop_list) do
                            realPropSum = realPropSum + math.ceil(propInfo.real_prob *10000)/100
                            if #res.info_list[1].group_list > 1 then
                                if giftGroupInfo.core_flag == true then
                                    if index == #giftGroupInfo.prop_list then
                                        lastPropInfo = propInfo
                                    end
                                end
                            else
                                if index == #giftGroupInfo.prop_list then
                                    lastPropInfo = propInfo
                                end
                            end
                        end
                    end
                    if lastPropInfo then
                        lastPropInfo.real_prob = math.max(100-(realPropSum-math.ceil(lastPropInfo.real_prob *10000)/100), 0)/100
                    end
                end
                --]]
                callback(res.info_list[1], itemId)
            end
        else            
            if callback then
                callback(nil, itemId)
            end
            logerror("[jobsjunlin][StoreServer] fOnCSGetBoxInfoRes res fail. boxId" ..boxId)
        end
    end

    req.id_list = { boxId }
    req.source = 1
    req:Request(fOnCSGetBoxInfoRes, { bEnableHighFrequency = true})
end

-- 获取干员抽奖券信息
function StoreServer:GetLotteryKeyInfo(lotteryKeyId)
    for _, ItemInfo in pairs(self._GameItemConfig) do
        -- print("ItemInfo.ItemID:", ItemInfo.ItemID)
        -- print("ItemInfo.Name:", ItemInfo.Name)
        if ItemInfo.ItemID == tostring(lotteryKeyId) then -- 干员抽奖券信息
            return ItemInfo
        end
    end
end

-- 设置干员皮肤抽奖主界面是否打开
function StoreServer:SetStaffLotteryMainUIOpened(bStaffLotteryMainUIOpened)
    self.bStaffLotteryMainUIOpened = bStaffLotteryMainUIOpened or false
end
-- 获取当前干员皮肤抽奖主界面是否打开
function StoreServer:GetStaffLotteryMainUIOpened()
    return self.bStaffLotteryMainUIOpened
end

-- 请求干员皮肤抽奖数据
function StoreServer:GetServerLotteryInfo(lotteryId)
    -- 请求奖池相关奖励数据
    local fCSShopGetLotteryInfoRes = function(res)
        loginfo("StoreServer:GetServerLotteryInfo fCSShopGetLotteryInfoRes")
        if res.result == 0 then
            -- 更新数据
            self.lotteryPoolInfoArray = res.lottery_pool_info
            self.lotteryPoolInfoMap = {}
            if self.lotteryPoolInfoArray then
                for _, shopLotteryInfo in pairs(self.lotteryPoolInfoArray) do
                    if shopLotteryInfo.lottery_id then -- 当前奖池
                        -- 更新奖励信息
                        local lottery_id = shopLotteryInfo.lottery_id
                        loginfo("StoreServer:GetServerLotteryInfo lotteryId = ", lottery_id)
                        self.DictStoreLotteryProbDistributionItems[lottery_id] = {}
                        for __, lotteryRewardInfo in pairs(shopLotteryInfo.lottery_rewards) do
                            local client_config = self:GetLotteryProbDistributionClientByID(lottery_id, lotteryRewardInfo.num_id)
                            self.DictStoreLotteryProbDistributionItems[lottery_id][lotteryRewardInfo.num_id] = StoreLotteryProbDistributionItem:New(lotteryRewardInfo, client_config)
                        end
                        -- 更新是否已抽中的信息                        
                        for i = 1, #shopLotteryInfo.prop_num_ids do
                            if self.DictStoreLotteryProbDistributionItems[lottery_id][shopLotteryInfo.prop_num_ids[i]] then -- 已抽中
                                self.DictStoreLotteryProbDistributionItems[lottery_id][shopLotteryInfo.prop_num_ids[i]]:SetIsDrawn(true)
                            end
                        end
                        -- 更新下一抽的cost信息
                        if self.DictStoreLotteryItems[lottery_id] then
                            self.DictStoreLotteryItems[lottery_id]:SetNextDrawCost(shopLotteryInfo.cost_num)
                        end
                    end
                end
            end
            self.Events.evtStoreLotteryInfoUpdated:Invoke(res)
        end
    end

    loginfo("StoreServer:GetServerLotteryInfo StartReq")

    local req = pb.CSShopGetLotteryInfoReq:New()
    -- req.lottery_id = lotteryId
    req:Request(fCSShopGetLotteryInfoRes)
end

--获取时间戳
function StoreServer:GetStartTimeAndEndTime(lotteryId)
    return self.DictStoreLotteryItems[lotteryId].BeginTime, self.DictStoreLotteryItems[lotteryId].EndTime
end

--请求选择头奖
function StoreServer:ReqShopSetMandelBoxUp(boxID, groupID, propID, numID, sourceID)
    local fOnShopSetMandelBoxUpRes = function(res)
        if res.result == 0 then
            Server.StoreServer:ClearBoxInfoCache(boxID)
            self.Events.evtStoreMandelUpRewardChooseResult:Invoke(res.result)
            LogAnalysisTool.ShopSetMandelBoxUpUIFlow(boxID, groupID, propID, sourceID)
        else
            self.Events.evtStoreMandelUpRewardChooseResult:Invoke(res.result)
        end
    end

    local req = pb.CSShopSetMandelBoxUpReq:New()
    req.box_id = boxID
    req.group_id = groupID
    req.prop_id = propID
    req.num_id = numID
    req:Request(fOnShopSetMandelBoxUpRes)
end

function StoreServer:GetCurrentLotteryId()
    return self.currentLotteryId
end

function StoreServer:GetLotteryPoolInfo()
    return self.lotteryPoolInfoArray
end

function StoreServer:GetDictStoreLotteryProbDistributionItems()
    return self.DictStoreLotteryProbDistributionItems
end

function StoreServer:GetMandelIDUpReward(boxID)
    if boxID == nil then
        return nil, nil
    end

    local info = self._lotteryPoolId2BoxInfos[boxID]
    if info == nil or info.info_list == nil or info.info_list[1] == nil then
        return nil, nil
    end

    for index, group in pairs(info.info_list[1].group_list) do
        if group.core_flag then
            for indexItem, item in pairs(group.prop_list) do
                if item.is_chosen then
                    return item.prop_id, item.num_id
                end
            end
        end
    end

    return nil, nil
end

function StoreServer:GetMandelLotteryCoreGroup(boxID)
    if boxID == nil then
        return nil
    end

    local info = self._lotteryPoolId2BoxInfos[boxID]
    if info == nil or info.info_list == nil or info.info_list[1] == nil then
        return nil
    end

    for index, group in pairs(info.info_list[1].group_list) do
        if group.core_flag then
            return group
        end
    end

    return nil
end

function StoreServer:GetDictStoreLotteryProbDistributionItemsByLotteryID(lotteryId)
    local ret = {}
    if self.DictStoreLotteryProbDistributionItems[lotteryId] ~= nil then
        ret = self.DictStoreLotteryProbDistributionItems[lotteryId]
    end

    return ret
end

function StoreServer:SetMandelExchangeShow(needShow)
    self._showMandelExchange = needShow
end

function StoreServer:GetMandelExchangeShow()
    return self._showMandelExchange
end


function StoreServer:_InternalResetDatas()
    self.mall_gift_descs = nil
    self.DictStoreHotRecommendItems = {}
    self.DictStoreMallGiftItems = {}
    self.hot_recommendation_descs = {}
    self.DictStoreLotteryProbDistributionItems = {}
    self.mall_gift_records = {}
    self.bhd_list = {}
    -- self.pv_play_list = {}
    self.hot_recommendation_records = {}
    self.DictStoreLotteryItems = {}
    self.DictStoreBHDItems = {}
    self.lotteryPoolInfoArray = {}
    self.lottery_item_descs = {}
    self.special_item_list = {}
    self._lotteryPoolId2BoxInfos = {}
    self.lottery_records = {}
    self.ThemeConfigDatas = {}
end

---清除藏品开箱信息缓存,根据奖池id
function StoreServer:ClearBoxInfoCache(inBoxId)
    if inBoxId ~= nil then
        self._lotteryPoolId2BoxInfos[inBoxId] = nil
    else
        self._lotteryPoolId2BoxInfos = {}
    end
end

---清除藏品开箱信息缓存,根据曼德尔砖id
function StoreServer:ClearBoxInfoCacheByMandelId(inMandelId)
    if not inMandelId then
        return
    end

    local boxId
    local itemInfo = ItemConfigTool.GetItemConfigById(inMandelId)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end

    if boxId ~= nil then
        self:ClearBoxInfoCache(boxId)
    end
end

function StoreServer:RefreshLotteryRedDotKey2LotteryID()
    for index, v in pairs(self.DictStoreLotteryItems) do
        self._lotteryRedDotKey2LotteryID[index] = v.LotteryId
    end
end

function StoreServer:GetRedDotKeyByLotteryID(LotteryId)
    for key, lotteryID in pairs(self._lotteryRedDotKey2LotteryID) do
        if lotteryID == LotteryId then
            return key
        end
    end
end

function StoreServer:GetLotteryIDByRedDotKey(iKey)
    return self._lotteryRedDotKey2LotteryID[iKey]
end

function StoreServer:IsMandelUpRewardRedDot(LotteryId)
    local LotteryData = self:GetLotteryDataByLotteryID(LotteryId)
    if LotteryData then
        return self:IsMandelUpRewardRedDotByMandelID(LotteryData.MandelItemId)
    else
        return false
    end

    return false
end

function StoreServer:IsMandelUpRewardRedDotByMandelID(mandelID)
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(mandelID)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end

    local coreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(boxId)
    -- 判断是否只有一个核心奖励
    local iCoreCount = 0
    if coreGroup == nil then
        iCoreCount = 0
    else
        for index, item in pairs(coreGroup.prop_list) do
            iCoreCount = iCoreCount + 1
        end
    end
    if iCoreCount <= 1 then
        return false
    end

    local itemID = 0
    local numID = 1
    itemID, numID = self:GetMandelIDUpReward(boxId)

    if itemID == nil then
        return true
    end

    return false
end

---获取上次缓存的boxinfo
function StoreServer:GetBoxInfo(iBoxID)
    return self._lotteryPoolId2BoxInfos[iBoxID]
end

function StoreServer:GetBoxInfoByBoxID(iBoxID)
    local infos = self._lotteryPoolId2BoxInfos[iBoxID]
    if infos == nil or infos.info_list == nil then
        logerror("[StoreServer] GetBoxInfoByBoxID ", iBoxID, "nil")
        return nil
    end
    for _, info in pairs(infos.info_list) do
        if info.box_id == iBoxID then
            return info
        end
    end
    return nil
end

-------------- 支付柔性限制 begin ----------------

-- 根据业务类型判断是否是支付柔性限制状态
function StoreServer:IsPaymentFlexibilityLimitByTypeList(tTypeList)
    for _, type in pairs(tTypeList) do
        if self:IsPaymentFlexibilityLimitByType(type) then
            logerror("[StoreServer] IsPaymentFlexibilityLimitByTypeList type", type, "limit")
            return true
        end
    end

    logerror("[StoreServer] IsPaymentFlexibilityLimitByTypeList no limit")

    return false
end

function StoreServer:IsPaymentFlexibilityLimitByType(iType)
    if self:IsPaymentFlexibilityLimit() == false then
        return false
    end

    local level = self:GetPaymentFlexibilityLimitLevel()
    if level == nil then
        logerror("[StoreServer] IsPaymentFlexibilityLimitByType level nil")
        return false
    end
    local config = self:GetPaymentFlexibilityLimitLevelDTConfig(level)
    if config == nil then
        logerror("[StoreServer] IsPaymentFlexibilityLimitByType config nil")
        return false
    end

    local sLimitType = config.LimitType
    if sLimitType == nil then
        logerror("[StoreServer] IsPaymentFlexibilityLimitByType sLimitType nil")
        return false
    end

    local tTypeList = StringUtil.StringSplit(sLimitType, ",")
    for _, type in pairs(tTypeList) do
        if tonumber(type) == iType then
            logerror("[StoreServer] IsPaymentFlexibilityLimitByType limit type", type)
            return true
        end
    end

    logerror("[StoreServer] IsPaymentFlexibilityLimitByType no limit", iType)
    return false
end

-- 是否当前是支付柔性限制状态
function StoreServer:IsPaymentFlexibilityLimit()
    -- todo
    return false
end

-- 获取限制等级对应的配置项
function StoreServer:GetPaymentFlexibilityLimitLevelDTConfig(level)
    for _, config in pairs(self._PaymentRestrictionConfig) do
        if config.Level == level then
            return config
        end
    end
    return nil
end

-- 获取当前限制等级
function StoreServer:GetPaymentFlexibilityLimitLevel()
    -- todo
    return 3
end

-- 获取SOL完成局数和要求局数
function StoreServer:GetPaymentFlexibilityLimitSOLInfo()
    -- todo
    local numDone = 2

    local level = self:GetPaymentFlexibilityLimitLevel()
    if level == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond level nil")
        return 0,0
    end
    local config = self:GetPaymentFlexibilityLimitLevelDTConfig(level)
    if config == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond config nil")
        return 0,0
    end
    local numMax = config.SOLNum

    return numDone, numMax
end

-- 获取MP完成局数和要求局数
function StoreServer:GetPaymentFlexibilityLimitMPInfo()
    -- todo
    local numDone = 3

    local level = self:GetPaymentFlexibilityLimitLevel()
    if level == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond level nil")
        return 0,0
    end
    local config = self:GetPaymentFlexibilityLimitLevelDTConfig(level)
    if config == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond config nil")
        return 0,0
    end
    local numMax = config.MPNum

    return numDone, numMax
end

-- 获取柔性支付限制逻辑条件
-- return 0 or , 1 and
function StoreServer:GetPaymentFlexibilityLimitLogicCond()
    local level = self:GetPaymentFlexibilityLimitLevel()
    if level == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond level nil")
        return 0
    end
    local config = self:GetPaymentFlexibilityLimitLevelDTConfig(level)
    if config == nil then
        logerror("[StoreServer] GetPaymentFlexibilityLimitLogicCond config nil")
        return 0
    end

    return config.CoordinatingRelationship
end

-- 支付柔性限制解除播报
function StoreServer:SetFlagNtfFlexiblePaymentLimitRestore(bFlag)
    self._bFlagNtfFlexiblePaymentLimitRestore = bFlag
    logerror("[StoreServer] SetFlagNtfFlexiblePaymentLimitRestore flag", self._bFlagNtfFlexiblePaymentLimitRestore)
end

function StoreServer:IsFlagNtfFlexiblePaymentLimitRestore()
    logerror("[StoreServer] IsFlagNtfFlexiblePaymentLimitRestore flag", self._bFlagNtfFlexiblePaymentLimitRestore)
    return self._bFlagNtfFlexiblePaymentLimitRestore
end

function StoreServer:NotifyNtfFlexiblePaymentLimitRestore()
    -- 根据是否在局内进行处理
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        -- 局内 
        self:SetFlagNtfFlexiblePaymentLimitRestore(true)
    else
        -- 局外

        -- 展示提示
        self.Events.evtStoreFlexiblePaymentLimitRestoreNtf:Invoke()

        self:SetFlagNtfFlexiblePaymentLimitRestore(false)
    end
end

function StoreServer:IsMandelContinuousRewardBtnRedDot(boxID)
    local info = self:GetBoxInfoByBoxID(boxID)
    if info == nil then
        return false
    end

    for _, rewardInfo in pairs(info.info_list) do
        if rewardInfo.approve_count > info.last_core_reward_approve_num and rewardInfo.approve_count <= info.core_open_count then
            return true
        end
    end

    return false
end

function StoreServer:IsMandelContinuousRewardBtnRedDotByLotteryID(lotteryID)
    local lottery = self.DictStoreLotteryItems[lotteryID]
    if lottery == nil then
        return false
    end

    local mandelID = lottery.MandelItemId
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(mandelID)
    if itemInfo == nil then
        return false
    end

    boxId = itemInfo.ConnectedPool or 0

    return self:IsMandelContinuousRewardBtnRedDot(boxId)
end

function StoreServer:ReqShopGetMandelOpenedReward(boxID, fCallback)
    local fOnCSShopGetMandelOpenedRewardRes = function(res)
        if res.result == 0 then
            fCallback(res)
        else
            logerror("[StoreServer] CSShopGetMandelOpenedRewardReq res error", res.result)
        end
    end
    local req = pb.CSShopGetMandelOpenedRewardReq:New()
    req.box_id = boxID
    req:Request(fOnCSShopGetMandelOpenedRewardRes)
end

-------------- 支付柔性限制 end ----------------

return StoreServer
