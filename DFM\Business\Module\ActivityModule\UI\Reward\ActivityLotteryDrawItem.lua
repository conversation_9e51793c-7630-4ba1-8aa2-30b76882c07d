----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--活动抽奖系统:奖励列表
---@class ActivityLotteryDrawItem : LuaUIBaseView
local ActivityLotteryDrawItem = ui("ActivityLotteryDrawItem")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

function ActivityLotteryDrawItem:Ctor()
    self._wtTabGroupBox = UIUtil.WndTabGroupBox(self, "wtDFTabV3GroupBoxClass3Dynamic", self._OnTabCount, self._OnTabWidget, self._OnTabIndexChanged)
    self._wtScrollBox = UIUtil.WndWaterfallScrollBox(self, "wtPrizesScrollBox", self._OnScrollCount, self._OnScrollWidget, nil, self._OnScrollSize)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        -- Initialize tabs group gamepad switch instructions.
        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Left:SetOnlyDisplayOnGamepad(true)
        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Left:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Left:Visible()

        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Right:SetOnlyDisplayOnGamepad(true)
        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Right:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
        self._wtTabGroupBox.WBP_GamepadKeyIconBox_Right:Visible()
    end
    --- END MODIFICATION

    self._wtRewardPanel = self:Wnd("DFVerticalBox_241", UIWidgetBase)
    self._wtRewardBox = self:Wnd("DFScrollBox_189", UIWidgetBase)

    self._wtTipePanel = self:Wnd("DFCanvasPanel_4", UIWidgetBase)
    self._wtEmptySizeBox = self:Wnd("PlatformSizeBox_0", UIWidgetBase)
    self._wtEmptySizeBox:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    self._wtEmptyPanel = self:Wnd("DFNamedSlot_191", UIWidgetBase)

    self._wtEmptyNoAny = self:Wnd("WBP_Common_NoAnything", UIWidgetBase)

    self._wtTipeIcon = self:Wnd("wtCannotRestoreHintImg", UIImage)
    self._wtTipeDsc = self:Wnd("wtCannotRestoreHintText", UITextBlock)

    -- Using "DFCommonCheckButtonOnly" instead of "DFCommonButtonOnly".
    self._wtDFButton = self:Wnd("wtProbOverallCheckBtn", DFCommonCheckButtonOnly)
    self._wtDFButton:Event("OnCheckedClicked", self._OnClickBtn, self)
    self._wtDFButton:Event("OnUncheckedClicked", self._OnClickBtn, self)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtDFButton:SetDisplayInputAction("Common_ButtonLeft_Gamepad", false, 0.0, true)
    end
    --- END MODIFICATION
end

function ActivityLotteryDrawItem:InitItemData(rewardArr, scanArr, index)
    --按钮
    self._tabList = {
        [1] = Module.Activity.Config.Loc.ActivityReward,--奖励
        [2] = Module.Activity.Config.Loc.ActivityScanHistory,--扫描历史
    }
    self._reward = rewardArr
    self._scan = scanArr
    --奖励组表
    self._rewardList = {}
    --扫描历史
    self._scanList = {}
    --刷新列表/设置下标
    if index then
        self._index = index
    end
    self._index = self._index or 1

    self._wtTabGroupBox:RefreshTab()
    self._wtTabGroupBox:SetTabIndex(self._index - 1)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() and index == 1 then
        self:_InitGamepadInputs()
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS: Called by "ActivityLotteryDrawPanel:_OnOpenRewardClicked(isBool)" to fix OnShow/Hide lifecycle missing issue.
function ActivityLotteryDrawItem:OnPanelClosed()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end
--- END MODIFICATION

function ActivityLotteryDrawItem:_OnTabCount()
    if self._tabList then
        return #self._tabList
    end
    return 0
end

function ActivityLotteryDrawItem:_OnTabWidget(position, itemWidget)
    local index = position + 1
    local name = self._tabList and self._tabList[index]
    itemWidget:SetMainTitle(name or "")
end

function ActivityLotteryDrawItem:_OnTabIndexChanged(position)
    self._index = position + 1
    --奖励不可重复提示
    if self:_IsTips() then
        self._wtTipeIcon:AsyncSetImagePath(Module.Activity.Config.IconPath[15] or "", true)
        self._wtTipeIcon:Visible()
        self._wtTipeDsc:SetText(Module.Activity.Config.Loc.MandelTabItemOwnedTip)
    else
        self._wtTipeIcon:Collapsed()
        self._wtTipeDsc:SetText(Module.Activity.Config.Loc.MandelTabItemCouldRepeatTip)
        --限时处理
        for key, group in ipairs(self._reward or {}) do
            if group.core_flag and group.start_time ~= group.end_time then
                local time1 = TimeUtil.TransTimestamp2AYYMMDDStr(group.start_time or 0)
                local time2 = TimeUtil.TransTimestamp2AYYMMDDStr(group.end_time or 0)
                self._wtTipeIcon:AsyncSetImagePath(Module.Activity.Config.IconPath[16] or "", true)
                self._wtTipeIcon:Visible()
                self._wtTipeDsc:SetText(General.GetText(Module.Activity.Config.Loc.TimesLimitedReturn, time1, time2))
                break
            end
        end
    end
    --box锁定
    if self._wtRewardBox.SetScrollOffset then
        self._wtRewardBox:SetScrollOffset(0)
    end
    if self._wtScrollBox.SetScrollOffset then
        self._wtScrollBox:SetScrollOffset(0)
    end
    self._wtRewardPanel:Collapsed()
    self._wtScrollBox:Collapsed()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableProbOverallGamepad()
    end
    --- END MODIFICATION
    --清空奖励
    self:_GetDrawItem(-1)
    
    if self._index == 1 then
        self._wtRewardPanel:Visible()
        self._rewardList = self._reward
        --延迟一帧刷新(解决：控件被显示,垂直框排布,不能立刻生效问题)
        for index, value in ipairs(self._rewardList or {}) do
            local itemWidget = self:_GetDrawItem(1, index)
            if itemWidget then
                itemWidget:SetInfo(value.items, value.title_main, nil, 1, idx == 2, 2, true)
            end
        end
        --空提示显示动画
        self:_EmptyTipeAnim(self._rewardList == nil or #self._rewardList == 0)
        --设置按钮状态
        self._wtDFButton:SetIsChecked(false)
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsHD() then
            self:_InitProbOverallGamepad()
        end
        --- END MODIFICATION
    else
        self._wtScrollBox:Visible()
        self._scanList = self._scan
        --延迟一帧刷新(解决：控件被隐藏,垂直框排布,不能立刻生效问题)
        Timer.DelayCall(0, self._RefreshRewards, self)
        --空提示显示动画
        self:_EmptyTipeAnim(self:_OnScrollCount() == 0)
    end
end

--刷新两次,延迟刷新一次(解决重叠问题)
function ActivityLotteryDrawItem:_RefreshRewards()
    local rewardFunc = function()
        if self._wtScrollBox == nil then
            return
        end
        self._wtScrollBox:RefreshAllItems()
    end
    rewardFunc()
    Timer.DelayCall(0, rewardFunc, self)
end

function ActivityLotteryDrawItem:_IsTips()
    for key, group in ipairs(self._reward or {}) do
        for index, item in ipairs(group.items or {}) do
            if item.restore_flag == false then
                return true
            end
        end
    end
    return false
end

function ActivityLotteryDrawItem:_EmptyTipeAnim(isBool)
    if isBool == nil then
        return
    end
    if self._btn == nil then
        self._btn = self:_GetEmptyPanel(1)
    end
    if self._btn then
        local btnTxt = self._btn:Wnd("RichTextBlock_50", UITextBlock)
        if btnTxt then
            btnTxt:SetText(Module.Activity.Config.Loc.UnlockEmpty)
        end
        local aimName = self._btn.WBP_Common_NoAnything_in
        local mode = EUMGSequencePlayMode.Forward
        if isBool then
            self._btn:StopAnimation(aimName)
            --延迟一帧执行:解决动画不播放问题
            Timer.DelayCall(0, function()
                if self._btn and aimName and mode then
                    self._btn:PlayAnimation(aimName, 0, 1, mode, 1, false)
                end
            end, self)
            self._btn:SetVisibility(ESlateVisibility.HitTestInvisible)
        else
            self._btn:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
end

--获取empty
function ActivityLotteryDrawItem:_GetEmptyPanel(itemType)
    local uiNavId = UIName2ID.CommonEmptyContent
    local container = self._wtEmptyPanel
    return ActivityLogic.GetSubUiInst(self, uiNavId, container, itemType)
end

function ActivityLotteryDrawItem:_OnScrollCount()
    if self._scanList then
        return #self._scanList
    end
    return 0
end

function ActivityLotteryDrawItem:_OnScrollSize(position)
    local travel = 1
    local width = IsHD() and 720 or 600
    if self._scanList and self._scanList[position] then
        local group = self._scanList[position]
        local lineNum = #self._scanList - (position - 1)
        if lineNum and group and group.historyInfo and group.prop then
            local length = Module.Store:GetStrLength(lineNum, group.historyInfo, group.prop, 1)
            if length then
                local count = IsHD() and 64 or 51
                travel = math.ceil(length/count)
            end
        end
    end
    local endLength = 0
    if #self._scanList == position then
        endLength = 58
    end
    local height = 104 + 46 * travel - endLength

    return FVector2D(width, height)
end

function ActivityLotteryDrawItem:_OnScrollWidget(position, itemWidget)
    if self._scanList and self._scanList[position] then
        local group = self._scanList[position]
        local lineNum = #self._scanList - (position - 1)
        if lineNum and group and group.historyInfo and group.prop then
            itemWidget:SetInfo(lineNum, group.historyInfo, group.prop, 1)
        end
    end
end

--对象池(获取/隐藏/清除)
function ActivityLotteryDrawItem:_GetDrawItem(itemType, index)
    local uiNavId = UIName2ID.StorePrizesList
    local container = self._wtRewardBox
    return ActivityLogic.GetSubUiInst(self, uiNavId, container, itemType, index)
end

function ActivityLotteryDrawItem:_OnClickBtn()
    --设置按钮状态
    self._wtDFButton:SetIsChecked(false)
    --打开商城奖励爆率界面
    if self._rewardList then
        Module.Store.OpenStorePrizePopPanel(nil, nil,nil,true,self._rewardList)
    end
end

function ActivityLotteryDrawItem:OnClose()
    self._btn = nil
    self:_GetDrawItem()
    self:_GetEmptyPanel()
    self._index = nil
end

--- BEGIN MODIFICATION @ VIRTUOS
function ActivityLotteryDrawItem:_InitGamepadInputs()
    if not self._navGroups then
        self._navGroups = {
            rewards = WidgetUtil.RegisterNavigationGroup(self._wtRewardBox, self, "Hittest"),
            history = WidgetUtil.RegisterNavigationGroup(self._wtScrollBox, self, "Hittest"),
        }
        if self._navGroups.rewards then
            self._navGroups.rewards:AddNavWidgetToArray(self._wtRewardBox)
            self._navGroups.rewards:SetScrollRecipient(self._wtRewardBox)
        end

        if self._navGroups.history then
            self._navGroups.history:AddNavWidgetToArray(self._wtScrollBox)
            self._navGroups.history:SetScrollRecipient(self._wtScrollBox)
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups.rewards)
    end

    if not self._gamepadHandles then
        self._gamepadHandles =
        {
            prevTab = self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed, self._wtTabGroupBox.OnPrev, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop),
            nextTab = self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed, self._wtTabGroupBox.OnNext, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop),
            singleDrawBlock = self:AddInputActionBinding("SingleDraw_ActivityPrize", EInputEvent.IE_Pressed, self._wtTabGroupBox.OnPrev, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop),
            multiDrawBlock = self:AddInputActionBinding("MultiDraw_ActivityPrize", EInputEvent.IE_Pressed, self._wtTabGroupBox.OnNext, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop),
        }
    end
end

function ActivityLotteryDrawItem:_DisableGamepadInputs()
    if self._navGroups then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroups = nil
    end

    if self._gamepadHandles then
        for key, handle in pairs(self._gamepadHandles) do
            self:RemoveInputActionBinding(handle)
        end
        self._gamepadHandles = nil
    end

    self:_DisableProbOverallGamepad()
end

function ActivityLotteryDrawItem:_InitProbOverallGamepad()
    if not self._probOverallHandle then
        self._probOverallHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self._wtDFButton.SelfClick, self._wtDFButton, EDisplayInputActionPriority.UI_Pop)
    end
end

function ActivityLotteryDrawItem:_DisableProbOverallGamepad()
    if self._probOverallHandle then
        self:RemoveInputActionBinding(self._probOverallHandle)
        self._probOverallHandle = nil
    end
end
--- END MODIFICATION

return ActivityLotteryDrawItem
