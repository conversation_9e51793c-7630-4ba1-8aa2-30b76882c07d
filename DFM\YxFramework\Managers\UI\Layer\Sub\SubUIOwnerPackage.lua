----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerSub)
----- LOG FUNCTION AUTO GENERATE END -----------


---------------------------------------------------------------------------------
--- SubUIOwnerPack 新版逻辑（性能更优）
---------------------------------------------------------------------------------
---@class SubUIOwnerPack : Object
local SubUIOwnerPack = class("SubUIOwnerPack", Object)
local SubUIHelper = require "DFM.YxFramework.Managers.UI.Layer.Sub.SubUIHelper"
local RefUtil = require "DFM.YxFramework.Util.RefUtil"
local UDFCanvasPanel = import("DFCanvasPanel")
local UPanelWidget = import("PanelWidget")
local ULAI = import "LAI"

local logw = function(...)
    logwarning('[ SubUIOwnerPack 子UI归属管理 ] ',...)
end

local loge = function(...)
    logerror('[ SubUIOwnerPack 子UI归属管理 ] ',...)
end

local log = function(...)
    if not VersionUtil.IsShipping() then
        loginfo('[ SubUIOwnerPack 子UI归属管理 ] ',...)
    end
end

---@enum EChildrenType
EChildrenType = {
    Exclusive = 1,  --- 互斥, instanceID 都为0
    Coexist = 2,    --- 共存
}

local ExclusiveStr = " *互斥* "
local CoexistStr = " *共存* "

function SubUIOwnerPack:Ctor(ownerUIIns)
    self.weakOwnerUIIns = makeweak(ownerUIIns)
    self.ownerCName = ownerUIIns and ownerUIIns._cname or nil

    self._wtCanvasStation = UDFCanvasPanel()
    self._wtCanvasStation:SetVisibility(ESlateVisibility.Hidden)
    RefUtil.AddRef(self._wtCanvasStation)

    ---@type UINavID[]
    self._exclusiveUINavIDList = {}
    ---@type number
    self._preExclusiveIdx = 0

    ---@type table<UINavID, number>
    self._mapUINavID2bHistory = {}

--------------------------------------------------------------------------
--- Public 数据重构
--------------------------------------------------------------------------
    --- New
    --- 1.Close/Mute时才删除的数据
    --- 仍然未关闭的UI（包含激活和非激活）
    --- 开始关闭的时候就置空
    ---@type table<tokenId, weakSubUIIns> 子UI TokenId 索引
    self._mapTokenId2SubUIIns = setmetatable({}, weakmeta_value)

    --- NeedUpdate
    --- 在池子里的时候，parent == self._wtCanvasStation, ui:GetIsEnabled() == false
    --- 不在池子的时候，parent == targetParent, ui:GetIsEnabled() == true
    --- Mute触发的时候才置空（晚一些）
    ---@class TokenInfo
    ---@field uiNavID number UINavID
    ---@field instanceID number instanceID custom or gen by GidBase + 1
    ---@field eChildrenType EChildrenType enum AddSubUI时设置
    ---@field weakParentTable table getfromweak
    ---@field oriVisibility ESlateVisibility reset when create from UIManager

    ---@type table<weakParentUIIns, table<weakSubUIIns, TokenInfo>>
    self._mapTokenId2TokenInfo = {}

    --- 2.池子取出和放回时时改变的数据
    --- 在池子里的（非激活）
    ---@type table<UINavID, table>
    self.mapUIId2PoolInstanceIdList = {}

    --- 3.更换父节点时刷新的数据
    ---@type table<weakParentUIIns, table<tokenId, boolean>>
    self._mapWeakParent2TokenIdExistMap = setmetatable({}, weakmeta_key)
end


--- 子UI跟随Owner引用计数
local bLogPrint = false
function SubUIOwnerPack:IsAnyInsNeedResRef(uiNavID)
    --- instanceIdList存在未激活可复用的子UI
    local instanceIdList = self.mapUIId2PoolInstanceIdList and self.mapUIId2PoolInstanceIdList[uiNavID] or nil
    local bDeactiveExist = instanceIdList and #instanceIdList > 0
    if bDeactiveExist then
        if bLogPrint and not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
            local uiSettings = UITable[uiNavID]
            if uiSettings then  
                logwarning("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef instanceIdList Exist SubUI subUIPackIns > 0 ", uiSettings.FullPath, self.ownerCName)
            end
        end
        return bDeactiveExist
    end
    
    --- 存在仍在使用的激活子UI
    if self._mapTokenId2SubUIIns and next(self._mapTokenId2SubUIIns) then
        if self.mapUINavID2GidBase then
            local curGid = self.mapUINavID2GidBase[uiNavID]
            if curGid and type(curGid) == "number" then
                for i = 1, curGid do
                    local tokenId = SubUIHelper.Encode2TokenId(uiNavID, curGid)
                    local uiIns = self._mapTokenId2SubUIIns[tokenId]
                    if not is_ui_invalid(uiIns) then
                        if bLogPrint and not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                            local uiSettings = UITable[uiNavID]
                            if uiSettings then  
                                logwarning("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef self._mapTokenId2SubUIIns Exist SubUI subUIPackIns > 0 ", uiSettings.FullPath, self.ownerCName)
                            end
                        end
                        return true
                    end
                end
            end
        end
    end

    --- 存在历史记录可能仍需被Owner创建的子UI
    if self._mapUINavID2bHistory and self._mapUINavID2bHistory[uiNavID] then
        if bLogPrint and not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
            local uiSettings = UITable[uiNavID]
            if uiSettings then  
                logwarning("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef self._mapUINavID2bHistory Exist SubUI subUIPackIns > 0 ", uiSettings.FullPath, self.ownerCName)
            end
        end
        return true
    end
    return false
end

function SubUIOwnerPack:IsOwnerValid()
    local ownerUIIns = getfromweak(self.weakOwnerUIIns)
    local bValid = ownerUIIns and not hasdestroy(ownerUIIns) and isvalid(ownerUIIns)
    if not bValid then
        logw('SubUIOwnerPack SubUIOwnerPack:IsOwnerValid() Owner已经失效 ownerUIIns:', ownerUIIns, 'subUIOwnerPackIns', bValid)
    end
    return bValid
end

function SubUIOwnerPack:SetNewOwner(newOwnerUIIns)
    local bValid = newOwnerUIIns and not hasdestroy(newOwnerUIIns) and isvalid(newOwnerUIIns)
    if not bValid then
        logw('SubUIOwnerPack SubUIOwnerPack:SetNewOwner() Owner已经失效 newOwnerUIIns:', newOwnerUIIns, 'subUIOwnerPackIns', bValid)
    else
        self.weakOwnerUIIns = makeweak(newOwnerUIIns)
        self.ownerCName = newOwnerUIIns and newOwnerUIIns._cname or nil
        logw('SubUIOwnerPack SubUIOwnerPack:SetNewOwner(newOwnerUIIns) newOwnerUIIns:', newOwnerUIIns, self.ownerCName)
    end
    return bValid
end


function SubUIOwnerPack:Reset()
    self:UnRegSwitchSubUI()
    SubUIHelper.ClearGid(self)

    --- 重置
    ---@type table<weakParentUIIns, table<weakSubUIIns, TokenInfo>>
    self._mapTokenId2TokenInfo = {}
    ---@type table<UINavID, table>
    self.mapUIId2PoolInstanceIdList = {}
    ---@type table<weakParentUIIns, table<tokenId, boolean>>
    self._mapWeakParent2TokenIdExistMap = setmetatable({}, weakmeta_key)

    for tokenId, subUIIns in pairs(self._mapTokenId2SubUIIns) do
        if not hasdestroy(subUIIns) and isvalid(subUIIns) then
            if subUIIns.RemoveOpenCallBack then
                subUIIns:RemoveOpenCallBack(self._OnPackSubUIOpened, self)
            end
            if subUIIns.RemoveCloseCallBack then
                subUIIns:RemoveCloseCallBack(self._OnPackSubUIMuted, self)
            end
            Facade.UIManager:CloseUI(subUIIns)
            log("SubUIOwnerPack:Destroy() 清理被引用住的 整包 UI", tokenId, subUIIns)
        end
    end
    self._mapTokenId2SubUIIns = setmetatable({}, weakmeta_value)

    if not self:IsOwnerValid() then
        logw('SubUIOwnerPack:Reset() but ownerUIIns is not valid')
    end
    if self._wtCanvasStation then
        UILightWidget.PureClearChildren(self._wtCanvasStation)
    end

    ---@type table<UINavID, number>
    self._mapUINavID2bHistory = {}
end

function SubUIOwnerPack:Destroy()
    self:Reset()
    if isvalid(self._wtCanvasStation) and self._wtCanvasStation.Release then
        self._wtCanvasStation:Release()
    end
    self._wtCanvasStation = nil
end

--------------------------------------------------------------------------
--- 互斥子UI Public API 注册接口
--- 需要在Stack UI 的Ctor或者OnInitExtraData或者OnActivate中调用
--------------------------------------------------------------------------
function SubUIOwnerPack:RegSwitchSubUI(uiNavIDList)
    self._exclusiveUINavIDList = setdefault(uiNavIDList, {})
    log('当前栈UI实例', self._cname, self.UINavID,  ' 注册 *互斥* 子UI列表为 --------------------')
    for idx, uiNavID in ipairs(self._exclusiveUINavIDList) do
        log('-------互斥 SubUI', uiNavID, UIName2ID.GetBPPathByID(uiNavID),  '--------------------')
        local uiSettings = UITable[uiNavID]
        if uiSettings == nil or uiSettings.UILayer ~= EUILayer.Sub then
            loge("RegSwitchSubUI failed, 注册了无效的UINavID"..tostring(uiNavID))
        end
    end
end

--------------------------------------------------------------------------
--- 互斥子UI Public API 反注册接口
--- 反注册接口需要在Stack UI 的OnClose或者OnDeactivate中调用
--------------------------------------------------------------------------
function SubUIOwnerPack:UnRegSwitchSubUI()
    log('当前栈UI实例 UnRegSwitchSubUI', self._cname, self.UINavID,  ' 反注册 *互斥* 子UI列表 --------------------')
    for idx, uiNavID in ipairs(self._exclusiveUINavIDList) do
        local uiIns = self:RemoveSubUI(uiNavID, 1)
    end
    self._preExclusiveIdx = 0
    self._exclusiveUINavIDList = {}
end

--------------------------------------------------------------------------
--- 互斥子UI Public API 切换显示到父容器接口
--- 切换显示注册的互斥子UI
--------------------------------------------------------------------------
---@param index number
---@param parent uiIns
---@return weakUIIns, number
function SubUIOwnerPack:SwitchSubUIByIndex(index, parent, ...)
    if self._preExclusiveIdx ~= 0 and self._preExclusiveIdx ~= index then
        local uiNavID = self._exclusiveUINavIDList[self._preExclusiveIdx]
        self:RemoveSubUI(uiNavID, 1)
        log('SwitchSubUIByIndex 当前[隐藏] *互斥* 子UI idx 为 ', self._preExclusiveIdx, '对应Group中UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    end

   local uiIns = nil
   if next(self._exclusiveUINavIDList) then
        local uiNavID = self._exclusiveUINavIDList[index]
        if uiNavID and UITable[uiNavID] then
            local targetIdx = nil
            uiIns = self:_GetOrCreateSubUI(uiNavID, targetIdx, parent, EChildrenType.Exclusive, 1, ...)
            if uiIns then
                self._preExclusiveIdx = index
                log('SwitchSubUIByIndex 结束 当前[显示] *互斥* 子UI idx 为 ', index, '对应Group中UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
            end
        end
   end
   UIUtil.SetWidgetToParent_Full(uiIns, parent)
   return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), 0
end

--------------------------------------------------------------------------
--- 共存子UI Public API 显示到父容器接口（替换原生AddChild）
--------------------------------------------------------------------------
---@param uiNavID number
---@param parent uiIns
---@return weakUIIns, number
function SubUIOwnerPack:AddSubUI(uiNavID, parent, instanceID, ...)
    local targetIdx = nil
    local uiIns, instanceID = self:_GetOrCreateSubUI(uiNavID, targetIdx, parent, EChildrenType.Coexist, instanceID, ...)
    return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), instanceID
end

--------------------------------------------------------------------------
--- 共存子UI Public API 显示到父容器接口（替换原生RemoveFromParent）
--------------------------------------------------------------------------
---@param uiNavID number
---@param instanceID number
---@return weakUIIns, number
function SubUIOwnerPack:RemoveSubUI(uiNavID, instanceID, bForceHideWhenRemove)
    local uiIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, false)

    local eChildrenType = EChildrenType.Coexist
    if table.contains(self._exclusiveUINavIDList, uiNavID) then
        eChildrenType = EChildrenType.Exclusive
    end
    local typeStr = eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr
    
    if is_ui_invalid(uiIns) then
        logw('复用 [隐藏]', typeStr, 'RemoveSubUI 子UI 已经失活 或 从未创建 instanceID:', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        return nil
    end

    --- Deactivate
    if uiIns.Deactivate then
        if rawget(uiIns, "_is_enable_") then
            trycall(uiIns.Deactivate, uiIns, true)
        end
    end

    local oldParent = uiIns:GetParent()
    local targetIdx = nil
    if isvalid(oldParent) and ULAI.IsSubClassOf(oldParent, UPanelWidget) then
        bForceHideWhenRemove = setdefault(bForceHideWhenRemove, true)
        if bForceHideWhenRemove and SubUIHelper.IsNoArrangeContainer(oldParent) and oldParent.CanHaveMultipleChildren and oldParent:CanHaveMultipleChildren() then
            log('复用 [隐藏]', typeStr, 'RemoveSubUI 子UI bCanHaveMultipleChildren = true ## 隐藏 ## 逻辑开始', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        else
            log('复用 [隐藏]', typeStr, 'RemoveSubUI 子UI bCanHaveMultipleChildren = false ## 中转 ## 逻辑开始', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
            --- 中转逻辑
            self:_InteralAddChild(uiIns, targetIdx, self._wtCanvasStation, tokenId, instanceID, eChildrenType)
        end
    else
        log('复用 [隐藏]', typeStr, 'RemoveSubUI 子UI oldParent 无效,默认 ## 中转 ## 逻辑开始', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        --- 中转逻辑
        self:_InteralAddChild(uiIns, targetIdx, self._wtCanvasStation, tokenId, instanceID, eChildrenType)
    end

    if eChildrenType == EChildrenType.Exclusive then
        if self._exclusiveUINavIDList[self._preExclusiveIdx] == uiNavID then
            self._preExclusiveIdx = 0
        end
    end

    if bReused then
        log('复用 [隐藏]', typeStr, 'RemoveSubUI 子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    else
        log('新建 [隐藏]', typeStr, 'RemoveSubUI 子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    end

    --- 加入可用队列
    declare_if_nil(self.mapUIId2PoolInstanceIdList, uiNavID, {})
    if not table.contains(self.mapUIId2PoolInstanceIdList[uiNavID], instanceID) then
        table.insert(self.mapUIId2PoolInstanceIdList[uiNavID], instanceID)
        log('SubUIOwnerPack:RemoveSubUI [ InstanceId Calc ] owner: ', self, '放回Pool', instanceID, UIName2ID.GetBPPathByID(uiNavID))
        -- loge('SubUIOwnerPack:RemoveSubUI 加入可用队列', instanceID, UIName2ID.GetBPPathByID(uiNavID))
    end

    return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), instanceID
end


--------------------------------------------------------------------------
--- 共存子UI Public API 显示到父容器指定idx接口（替换原生InsertChildAt）
--------------------------------------------------------------------------
---@param uiNavID number
---@param parent uiIns
---@return weakUIIns, number
function SubUIOwnerPack:AddSubUIAtIndex(uiNavID, targetIdx, parent, instanceID, ...)
    local uiIns, instanceID = self:_GetOrCreateSubUI(uiNavID, targetIdx, parent, EChildrenType.Coexist, instanceID, ...)
    return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), instanceID
end

--------------------------------------------------------------------------
--- 子UI Public API ReturnToStation（当需要高自由度管理中转的节点时，比如红点）
--------------------------------------------------------------------------
function SubUIOwnerPack:ReturnToStation(uiNavID, instanceID)
    local uiIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, false)

    local eChildrenType = EChildrenType.Coexist
    if table.contains(self._exclusiveUINavIDList, uiNavID) then
        eChildrenType = EChildrenType.Exclusive
    end
    local typeStr = eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr
    
    if is_ui_invalid(uiIns) then
        logw('复用 [隐藏]', typeStr, 'ReturnToStation 子UI 已经失活 或 从未创建 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        return nil
    end

    --- Deactivate
    if uiIns.Deactivate then
        if rawget(uiIns, "_is_enable_") then
            trycall(uiIns.Deactivate, uiIns, true)
        end
    end
    
    local targetIdx = nil
    log('复用 [隐藏]', typeStr, 'ReturnToStation 子UI 主动回到中转站 ## 中转 ## 逻辑开始', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    --- 中转逻辑
    self:_InteralAddChild(uiIns, targetIdx, self._wtCanvasStation, tokenId, instanceID, eChildrenType)

    if eChildrenType == EChildrenType.Exclusive then
        if self._exclusiveUINavIDList[self._preExclusiveIdx] == uiNavID then
            self._preExclusiveIdx = 0
        end
    end

    if bReused then
        log('复用 [隐藏]', typeStr, 'ReturnToStation 子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    else
        log('新建 [隐藏]', typeStr, 'ReturnToStation 子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    end

    --- 加入可用队列
    declare_if_nil(self.mapUIId2PoolInstanceIdList, uiNavID, {})
    if not table.contains(self.mapUIId2PoolInstanceIdList[uiNavID], instanceID) then
        table.insert(self.mapUIId2PoolInstanceIdList[uiNavID], instanceID)
        log('SubUIOwnerPack:ReturnToStation [ InstanceId Calc ] owner: ', self, '放回Pool', instanceID, UIName2ID.GetBPPathByID(uiNavID))
        -- loge('SubUIOwnerPack:ReturnToStation 加入可用队列', instanceID, UIName2ID.GetBPPathByID(uiNavID))
    end
    
    return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), instanceID
end

--------------------------------------------------------------------------
--- Public API 假关闭 隐藏子UI父容器接口（替换原生ClearChildren等等，走OnHide）
--------------------------------------------------------------------------
function SubUIOwnerPack:RemoveSubUIByParent(parent)
    if parent and isvalid(parent) then
        local tokenExistFlagMap = self._mapWeakParent2TokenIdExistMap[parent]
        --- 未使用过AddSubUI加入节点的Parent，只能走默认清理
        if tokenExistFlagMap == nil then
            local count =  UILightWidget.PureClearChildren(parent)
            log('RemoveSubUIByParent 清空容器下子UI列表 [UILightWidget.PureClearChildren] count: ', count)
            return
        end
        --- 使用过AddSubUI加入节点的Parent，遍历记录mapWeakSubIns2TokenInfo
        local needClearTokenIdList = {}
        for tokenId, bExist in pairs(tokenExistFlagMap) do
            if bExist then
                table.insert(needClearTokenIdList, tokenId)
            end
        end
        --- 放回池子里
        local count = 0
        local fOperationForSubUIIns = CreateCallBack(function (self, subUIIns, tokenId)
            local tokenInfo = self._mapTokenId2TokenInfo[tokenId]
             --- 清理记录的SubUI（调用关闭，返回给UIManager）
             if tokenInfo then
                if subUIIns and not hasdestroy(subUIIns) then
                    count = count + 1
                    self:RemoveSubUI(tokenInfo.uiNavID, tokenInfo.instanceID)
                    log('RemoveSubUIByParent 隐藏容器下子UI列表', ' 当前uiIns: ', subUIIns, 'uiNavID:', subUIIns.UINavID ,' instanceID:', tokenInfo.instanceID, ' parent:', parent)
                else
                    log('RemoveSubUIByParent 隐藏容器下子UI列表 但UI失效')
                end
            else
                log('RemoveSubUIByParent 隐藏容器下子UI列表 但tokenInfo失效')
            end
        end, self)
        for _, needClearTokenId in ipairs(needClearTokenIdList) do
            self:_OnPackSubUIBeforeRemove_Step1(needClearTokenId, fOperationForSubUIIns)
        end
        log('RemoveSubUIByParent 隐藏容器下子UI列表 [tokenInfoList] count: ', count)
    end
end

--------------------------------------------------------------------------
--- Public API 真关闭 清空父容器接口（替换原生ClearChildren等等，走OnClose）
--------------------------------------------------------------------------
function SubUIOwnerPack:ClearSubUIByParent(parent)
    if parent and isvalid(parent) then
        local tokenExistFlagMap = self._mapWeakParent2TokenIdExistMap[parent]
        --- 未使用过AddSubUI加入节点的Parent，只能走默认清理
        if tokenExistFlagMap == nil then
            local count =  UILightWidget.PureClearChildren(parent)
            log('ClearSubUIByParent 清空容器下子UI列表 [UILightWidget.PureClearChildren] count: ', count)
            return
        end
        --- 使用过AddSubUI加入节点的Parent，遍历记录mapWeakSubIns2TokenInfo
        local needClearTokenIdList = {}
        for tokenId, bExist in pairs(tokenExistFlagMap) do
            if bExist then
                table.insert(needClearTokenIdList, tokenId)
            end
        end

        --- 关闭和清理
        local count = 0
        local fOperationForSubUIIns = CreateCallBack(function (self, subUIIns, tokenId)
            local tokenInfo = self._mapTokenId2TokenInfo[tokenId]
             --- 清理记录的SubUI（调用关闭，返回给UIManager）
             if tokenInfo then
                if subUIIns and not hasdestroy(subUIIns) then
                    count = count + 1
                    if subUIIns.RemoveOpenCallBack then
                        subUIIns:RemoveOpenCallBack(self._OnPackSubUIOpened, self)
                    end
                    if subUIIns.RemoveCloseCallBack then
                        subUIIns:RemoveCloseCallBack(self._OnPackSubUIMuted, self)
                    end
                    Facade.UIManager:CloseUI(subUIIns)
                    log('ClearSubUIByParent 清空容器下子UI列表', ' 当前uiIns: ', subUIIns, 'uiNavID:', subUIIns.UINavID ,' instanceID:', tokenInfo.instanceID, ' parent:', parent)
                else
                    log('ClearSubUIByParent 清空容器下子UI列表 但UI失效')
                end
            else
                log('ClearSubUIByParent 清空容器下子UI列表 但tokenInfo失效')
            end
        end, self)
        for _, needClearTokenId in ipairs(needClearTokenIdList) do
            self:OnPackSubUIClearedByTokenId(needClearTokenId, fOperationForSubUIIns)
        end
    end
end

--------------------------------------------------------------------------
--- Public API 假关闭 此接口适用于一次性把整个Pack的子UI都放回对象池的情况
--- 多个子UI UINavID 受到管理时，需要注意表现是否正常
--------------------------------------------------------------------------
function SubUIOwnerPack:RemoveAllSubUI()
    log('SubUIOwnerPack:RemoveAllSubUI() start 隐藏 所有Owner下的子UI, 此接口谨慎使用')
    for parent, tokenExistFlagMap in pairs(self._mapWeakParent2TokenIdExistMap) do
        self:RemoveSubUIByParent(parent)
    end
    log('SubUIOwnerPack:RemoveAllSubUI() end 隐藏 所有Owner下的子UI, 此接口谨慎使用')
end

--------------------------------------------------------------------------
--- Public API 真关闭 此接口适用于一次性把整个Pack的子UI都强制关闭的情况
--- 多个子UI UINavID 受到管理时，需要注意表现是否正常
--------------------------------------------------------------------------
function SubUIOwnerPack:ClearAllSubUI()
    log('SubUIOwnerPack:ClearAllSubUI() start 真关闭 所有Owner下的子UI, 此接口谨慎使用', self.ownerCName)
    for parent, tokenExistFlagMap in pairs(self._mapWeakParent2TokenIdExistMap) do
        self:ClearSubUIByParent(parent)
    end

    --- 重置
    ---@type table<weakParentUIIns, table<weakSubUIIns, TokenInfo>>
    self._mapTokenId2TokenInfo = {}
    ---@type table<UINavID, table>
    self.mapUIId2PoolInstanceIdList = {}
    ---@type table<weakParentUIIns, table<tokenId, boolean>>
    self._mapWeakParent2TokenIdExistMap = setmetatable({}, weakmeta_key)

    self._mapTokenId2SubUIIns = setmetatable({}, weakmeta_value)

    if not self:IsOwnerValid() then
        logw('SubUIOwnerPack:ClearAllSubUI() but ownerUIIns is not valid')
    end

    if self._wtCanvasStation then
        UILightWidget.PureClearChildren(self._wtCanvasStation)
    end
    log('SubUIOwnerPack:ClearAllSubUI() end 真关闭 所有Owner下的子UI, 此接口谨慎使用', self.ownerCName)
end

--------------------------------------------------------------------------
--- Public API 查询相关
--------------------------------------------------------------------------
---@param uiNavID number
---@param instanceID number
---@return weakUIIns, number
function SubUIOwnerPack:GetSubUI(uiNavID, instanceID)
    local uiIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, false)

    local eChildrenType = EChildrenType.Coexist
    if table.contains(self._exclusiveUINavIDList, uiNavID) then
        eChildrenType = EChildrenType.Exclusive
    end
    local typeStr = eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr

    if is_ui_invalid(uiIns) then
        loge('复用 [查询]', typeStr, uiIns, '子UI 已经失活 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        return nil
    end

    if bReused then
        log('复用 [查询]', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    else
        log('新建 [查询]', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
    end
    return SubUIHelper.GetWeakObjTableByUIIns(self, uiIns), instanceID
end

function SubUIOwnerPack:GetSubUINavIDByIndex(index)
    return self._exclusiveUINavIDList[index]
end

function SubUIOwnerPack:TryFastReleaseChildren()
    -- _mapTokenId2SubUIIns weak ref, free from gc..
    if self._mapTokenId2SubUIIns == nil then
        return
    end

    for key, value in pairs(self._mapTokenId2SubUIIns) do
        if isvalid(value) and not hasdestroy(value) then
            releaseobject(value)
        end
    end
end

--------------------------------------------------------------------------
--- Private API 私有接口 创建相关
--------------------------------------------------------------------------
---@param uiNavID number
---@param parent uiIns
---@param eChildrenType EChildrenType
---@return LuaUIBaseView, number
function SubUIOwnerPack:_GetOrCreateSubUI(uiNavID, targetIdx, parent, eChildrenType, instanceID, ...)
    eChildrenType = setdefault(eChildrenType, EChildrenType.Coexist)
    local typeStr = eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr
    local instanceID = eChildrenType == EChildrenType.Exclusive and 1 or setdefault(instanceID, SubUIHelper.GenInsGid(self, uiNavID))
    local uiIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, true, ...)
    if is_ui_invalid(uiIns) then
        loge('新建和复用 [显示]', typeStr, '子UI 失败 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        return nil, 0
    else
        self:_CallLifeCircle(uiNavID, targetIdx, parent, eChildrenType, instanceID, uiIns, bReused, tokenId, ...)
        return uiIns, instanceID
    end
end

---@param uiNavID number
---@param instanceID number
---@param bInstanceIfNotCached boolean
---@return LuaUIBaseView
---@return boolean
---@return uiIns, boolean, number
function SubUIOwnerPack:_InternalGetChild(uiNavID, instanceID, bInstanceIfNotCached, ...)
    local tokenId = SubUIHelper.Encode2TokenId(uiNavID, instanceID)
    local uiIns = self._mapTokenId2SubUIIns[tokenId]

    local mark4DeleteTimer = self.mapWeakSubUI2TimerHandle and self.mapWeakSubUI2TimerHandle[uiIns] or nil
    local bMarkDelete = false
    if bInstanceIfNotCached then
        bMarkDelete = mark4DeleteTimer ~= nil
    end

    if not is_ui_invalid(uiIns) and not bMarkDelete then
        return uiIns, true, tokenId
    else
        if bMarkDelete and not VersionUtil.IsShipping() then
            loge('注意 此子UI正在被标记删除，不可复用', uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        end
    end

    if not bInstanceIfNotCached then
        log('子UI UINavID为 ', uiNavID, ' 无缓存时是否需要新建: ', bInstanceIfNotCached)
        return nil, false, tokenId
    end
    
    uiIns = Facade.UIManager:CreateSubUI(uiNavID, ...)
    self._mapUINavID2bHistory[uiNavID] = true
    if uiIns then
        uiIns.__subTokenId = tokenId
        uiIns:AddOpenCallBack(self._OnPackSubUIOpened, self)
        uiIns:AddCloseCallBack(self._OnPackSubUIMuted, self)
        self:_OnPackSubUICreated(tokenId, uiNavID, instanceID, uiIns)
    end

    return uiIns, false, tokenId
end

function SubUIOwnerPack:_InternalCreateTokenInfo(tokenId, uiNavID, instanceID, uiIns)
    local tokenInfo =  {
        uiNavID = uiNavID,
        instanceID = instanceID,
        eChildrenType = EChildrenType.Coexist,
        weakParentTable = nil,
        oriVisibility = uiIns.Visibility,
    }
    ---@type table<weakParentUIIns, table<weakSubUIIns, TokenInfo>>
    self._mapTokenId2TokenInfo[tokenId] = tokenInfo
    return tokenInfo
end


function SubUIOwnerPack:_OnPackSubUICreated(tokenId, uiNavID, instanceID, uiIns)
    -- log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUICreated() 子UI被创建完成 ------ start -------', uiIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
    self._mapTokenId2SubUIIns[tokenId] = uiIns
    self:_InternalCreateTokenInfo(tokenId, uiNavID, instanceID, uiIns)
    -- log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUICreated() 子UI被创建完成 ------ end -------', uiIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
end

function SubUIOwnerPack:_OnPackSubUIOpened(subUIIns)
    if not is_ui_invalid(subUIIns) then
        local tokenId = subUIIns.__subTokenId
        local instanceID = SubUIHelper.DecodeTokenIDInstanceID(tokenId)
        local uiNavID = SubUIHelper.DecodeByTokenId(tokenId, instanceID)
        log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIOpened() 子UI被打开 ------ start -------', subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
        if self._mapTokenId2SubUIIns[tokenId] == nil then
            local parent = subUIIns:GetParent()
            local oriTokenInfo = self._mapTokenId2TokenInfo[tokenId]
            if not is_ui_invalid(parent) then
                if oriTokenInfo ~= nil then
                    self:_OnPackSubUIAddToParent(subUIIns, parent, tokenId, instanceID, oriTokenInfo.eChildrenType)
                else
                    self:_OnPackSubUIAddToParent(subUIIns, parent, tokenId, instanceID, EChildrenType.Coexist)
                end
                loge('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIOpened() 子UI被打开并执行tokenInfo存储 ------ _OnPackSubUIAddToParent -------', subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
            end
        end
        log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIOpened() 子UI被打开 ------ end -------', subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
    else
        log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIOpened() 子UI被打开 ------ start - end ------- 失败 subUIIns无效', subUIIns)
    end
end

function SubUIOwnerPack:_OnPackSubUIMuted(subUIIns)
    --- new
    local needClearTokenIdList = {}
    for tokenId, findSubUIIns in pairs(self._mapTokenId2SubUIIns) do
        if findSubUIIns == subUIIns then
            table.insert(needClearTokenIdList, tokenId)
        end
    end

    local tokenId = subUIIns.__subTokenId
    local instanceID = SubUIHelper.DecodeTokenIDInstanceID(tokenId)
    local uiNavID = SubUIHelper.DecodeByTokenId(tokenId, instanceID)
    log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIMuted() 子UI被关闭 ------ start -------', subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
    
    if subUIIns.GetParent and  subUIIns.RemoveFromParent and subUIIns:GetParent() then
        logw('[ 创建和Construct Debug ] RemoveFromParent parent', subUIIns:GetParent(), subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
        subUIIns:RemoveFromParent()
    end

    for _, needClearTokenId in ipairs(needClearTokenIdList) do
        self:OnPackSubUIClearedByTokenId(needClearTokenId)
    end
    log('[ 创建和Construct Debug ] SubUIOwnerPack:_OnPackSubUIMuted() 子UI被关闭 ------ end -------', subUIIns, tokenId, instanceID, UIName2ID.GetBPPathByID(uiNavID))
end

function SubUIOwnerPack:OnPackSubUIClearedByTokenId(needClearTokenId, fOperationForSubUIIns)
    self:_OnPackSubUIBeforeRemove_Step1(needClearTokenId, fOperationForSubUIIns)
    self:_OnPackSubUIRemoveFromParent_Step2(needClearTokenId)
end

--- 放回池子和关闭都需要走这里
function SubUIOwnerPack:_OnPackSubUIBeforeRemove_Step1(tokenId, fOperationForSubUIIns)
    local subUIIns = self._mapTokenId2SubUIIns[tokenId]
    --- subUIIns
    if subUIIns then
        if fOperationForSubUIIns then
            fOperationForSubUIIns(subUIIns, tokenId)
        end
    end
    
    local preUINavID = self._exclusiveUINavIDList[self._preExclusiveIdx]
    local tokenInfo = self._mapTokenId2TokenInfo[tokenId]
    if tokenInfo and tokenInfo.uiNavID == preUINavID and tokenInfo.instanceID == 1 then
        self._preExclusiveIdx = 0
        logw('_OnPackSubUIBeforeRemove_Step1 清空容器下子UI列表 当前[显示] *互斥* 子UI被其父容器关闭，重置 idx 为 ', 0)
    end
end

function SubUIOwnerPack:_OnPackSubUIRemoveFromParent_Step2(tokenId)
    local tokenInfo = self._mapTokenId2TokenInfo[tokenId]
    local subUIIns = self._mapTokenId2SubUIIns[tokenId]
    if tokenInfo and not is_ui_invalid(subUIIns) then
        local typeStr = tokenInfo.eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr
        log('注意 此子UI从父容器被移除，当前引用解开', typeStr, subUIIns, '子UI instanceID 为 ', tokenInfo.instanceID, ' tokenId:', tokenId, ' UINavID为 ', subUIIns.UINavID)
                
        local poolInstanceIdList = self.mapUIId2PoolInstanceIdList and self.mapUIId2PoolInstanceIdList[tokenInfo.uiNavID] or {}
        if poolInstanceIdList and next(poolInstanceIdList) then
            table.removebyvalue(poolInstanceIdList, tokenInfo.instanceID)
            log('SubUIOwnerPack:_OnPackSubUIRemoveFromParent_Step2 [ InstanceId Calc ] owner: ', self, 'UI Muted，移除Pool中的 instanceID', tokenInfo.instanceID, UIName2ID.GetBPPathByID(subUIIns.UINavID))
        end
        -- self:_UpdateTokenInfo(subUIIns, nil, tokenId, tokenInfo.instanceID)
        declare_if_nil(self, "mapWeakSubUI2TimerHandle", setmetatable({}, weakmeta_key))
        local preHandle = self.mapWeakSubUI2TimerHandle[subUIIns]
        if preHandle then
            Timer.CancelDelay(preHandle)
            self.mapWeakSubUI2TimerHandle[subUIIns] = nil
        end

        local uiTokenID = subUIIns.UITokenID
        if Facade.UIManager:IsLuaPendingKill(uiTokenID) and LUA_PENDING_KILL_ENABLE then
            self:_OnPackSubUIClosed_Step3(subUIIns, tokenId, tokenInfo, true)
        else
            Facade.UIManager:CacheUIForCurrentFrame(subUIIns)
            self.mapWeakSubUI2TimerHandle[subUIIns] = Timer.DelayCall(0.01, self._OnPackSubUIClosed_Step3, self, subUIIns, tokenId, tokenInfo, false)
        end
    end
end

--- 彻底关闭走这里
function SubUIOwnerPack:_OnPackSubUIClosed_Step3(subUIIns, tokenId, tokenInfo, bLuaPendingKill)
    --- 回到没有父节点的状态
    --- 重置父节点flagMap中bool为nil
    if tokenInfo then
        self:_UpdateTokenInfo(subUIIns, nil, tokenId, tokenInfo.instanceID)
        self._mapTokenId2SubUIIns[tokenId] = nil
        if bLuaPendingKill then
            log('_OnPackSubUIClosed_Step3 [ InstanceId Calc - LuaPendingKill ] owner: ', self, 'self._mapTokenId2SubUIIns[tokenId] = nil --------------Immediate Call', tokenInfo.instanceID, UIName2ID.GetBPPathByID(tokenInfo.uiNavID))
        else
            log('_OnPackSubUIClosed_Step3 [ InstanceId Calc ] owner: ', self, 'self._mapTokenId2SubUIIns[tokenId] = nil --------------Timer Call', tokenInfo.instanceID, UIName2ID.GetBPPathByID(tokenInfo.uiNavID))
        end
    end
    if subUIIns then
        self.mapWeakSubUI2TimerHandle[subUIIns] = nil
    end
end

function SubUIOwnerPack:_OnPackSubUIAddToParent(uiIns, parent, tokenId, instanceID, eChildrenType)
    --- 重新存储在父节点中的状态
    --- 设置父节点flagMap中bool为true
    self._mapTokenId2SubUIIns[tokenId] = uiIns
    declare_if_nil(self, "mapWeakSubUI2TimerHandle", setmetatable({}, weakmeta_key))
    local preHandle = self.mapWeakSubUI2TimerHandle[uiIns]
    if preHandle then
        Timer.CancelDelay(preHandle)
        self.mapWeakSubUI2TimerHandle[uiIns] = nil
    end
    self:_UpdateTokenInfo(uiIns, parent, tokenId, instanceID, eChildrenType)
end

--- 生命周期调用
function SubUIOwnerPack:_CallLifeCircle(uiNavID, targetIdx, parent, eChildrenType, instanceID, uiIns, bReused, tokenId, ...)
    eChildrenType = setdefault(eChildrenType, EChildrenType.Coexist)
    local typeStr = eChildrenType == EChildrenType.Exclusive and ExclusiveStr or CoexistStr
    local oldParent = uiIns:GetParent()

    if bReused then
        log('复用 [显示]', typeStr, '子UI instanceID 为 ', instanceID, ' UINavID为 ', uiNavID)

        --- 遵循先enable为true,再InitExtraData,Show等生命周期
        --- Activate
        if uiIns.Activate then
            if not rawget(uiIns, "_is_enable_") then
                trycall(uiIns.Activate, uiIns)
            end
        end
        --- MS24 cacheExtraDataByIdx
        if uiIns.InitExtraData then
            trycall(uiIns.InitExtraData, uiIns, ...)
            -- if not BOOL_MUTE_IDX_EXTRA_DATA then
            --     trycall(uiIns.CacheExtraDataByIdx, uiIns, targetIdx, ...)
            -- end
        end

        if oldParent and oldParent == parent then
            if not uiIns:IsInShowState() or not UIUtil.CheckVisibilityIsVisible(uiIns.Visibility) then
                local tokenInfo = self._mapTokenId2TokenInfo[tokenId]
                local visibility = tokenInfo.oriVisibility
                if UIUtil.CheckVisibilityIsVisible(visibility) then
                    uiIns:SetVisibility(visibility)
                end
                uiIns:Show()
                log('注意 此子UI已经在同个父节点上，但不在Show状态', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
            else
                --- do nothing
                uiIns:Show()
                log('注意 此子UI已经在同个父节点上且状态正常显示', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
            end
            return uiIns, instanceID
        else
            log('注意 此子UI不在目标父节点上，重新Add', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
            self:_InteralAddChild(uiIns, targetIdx, parent, tokenId, instanceID, eChildrenType)
        end
    else
        log('新建 [显示]', typeStr, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, ' UINavID为 ', uiNavID, UIName2ID.GetBPPathByID(uiNavID))
        self:_InteralAddChild(uiIns, targetIdx, parent, tokenId, instanceID, eChildrenType)
    end
end

function SubUIOwnerPack:_InteralAddChild(uiIns, targetIdx, parent, tokenId, instanceID, eChildrenType)
    eChildrenType = setdefault(eChildrenType, EChildrenType.Coexist)
    if isvalid(parent) then
        local slot
        if targetIdx == nil or type(targetIdx) ~= "number" or targetIdx < 0  then
            slot = parent:AddChild(uiIns)
        else
            slot = parent:InsertChildAt(targetIdx, uiIns)
            logw('_InteralAddChild 注意 此子UI插入到父容器指定idx', uiIns, targetIdx)
        end
        -- if parent ~= self._wtCanvasStation then
        --     UIUtil.SetWidgetToParent_Full(uiIns, parent)
        -- end
        if slot then
            self:_OnPackSubUIAddToParent(uiIns, parent, tokenId, instanceID, eChildrenType)
        else
            loge('_InteralAddChild 注意 此子UI未能成功Add到目标父节点上，请检查是否是单子节点父容器加了多个子UI', uiIns, UIName2ID.GetBPPathByID(uiIns.UINavID))
        end
        Facade.UIManager:CacheUIForCurrentFrame(uiIns)

        --- 暂未开启
        --- 解决部分子UIOnClose错误清空ExtraData的问题
        -- if not BOOL_MUTE_IDX_EXTRA_DATA then
        --     if uiIns.InitExtraData then
        --         local status, cacheInfo = trycall(uiIns.TryGetRestoreCacheInfoByIdx, uiIns, targetIdx)
        --         if status and cacheInfo  and type(cacheInfo) == GlobalStr.TABLE then
        --             cacheInfo.params = cacheInfo.params or {}
        --             local paramLength = cacheInfo.params.n or table.maxn(cacheInfo.params)
        --             trycall(uiIns.InitExtraData, uiIns, unpack(cacheInfo.params, 1, paramLength))
        --         end
        --     end
        -- end
    end
end

--------------------------------------------------------------------------
--- Private API 私有接口 TokenId 与 父容器记录相关
--------------------------------------------------------------------------
function SubUIOwnerPack:_UpdateTokenInfo(uiIns, parent, tokenId, instanceID, eChildrenType)
    if uiIns == nil then
        log('_UpdateTokenInfo 注意 此子UI 更新TokenInfo时，UI已经失效', parent, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId)
        return
    end
    eChildrenType = setdefault(eChildrenType, EChildrenType.Coexist)
    local oriTokenInfo = self._mapTokenId2TokenInfo[tokenId]
    if oriTokenInfo == nil then
        oriTokenInfo = self:_InternalCreateTokenInfo(tokenId, uiIns.UINavID, instanceID, uiIns)
    end
    if isvalid(parent) then
        oriTokenInfo.weakParentTable = makeweak(parent)
        oriTokenInfo.eChildrenType = eChildrenType

        declare_if_nil(self._mapWeakParent2TokenIdExistMap, parent, {})
        local tokenExistFlagMap = self._mapWeakParent2TokenIdExistMap[parent]
        tokenExistFlagMap[tokenId] = true
        log('_UpdateTokenInfo 注意 此子UI【加入】新的父节点, 更新TokenInfo到父节点', parent, uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, UIName2ID.GetBPPathByID(oriTokenInfo.uiNavID))
    else
        if oriTokenInfo.weakParentTable then
            local oldParent = getfromweak(oriTokenInfo.weakParentTable)
            local tokenExistFlagMap = self._mapWeakParent2TokenIdExistMap[oldParent]
            if tokenExistFlagMap then
                tokenExistFlagMap[tokenId] = nil
            end
        end
        oriTokenInfo.weakParentTable = nil
        oriTokenInfo.eChildrenType = eChildrenType
        log('_UpdateTokenInfo 注意 此子UI从父节点【移除】, 暂时更新TokenInfo为无父节点状态', uiIns, '子UI instanceID 为 ', instanceID, ' tokenId:', tokenId, UIName2ID.GetBPPathByID(oriTokenInfo.uiNavID))
    end
    
    return oriTokenInfo
end

--------------------------------------------------------------------------
--- Public API 扩容 扩容对象池内数量（仅操作池内实例，不影响已经加入父节点的子ui）
--------------------------------------------------------------------------
function SubUIOwnerPack:DoPoolExpansion(uiNavID, maxLength, ...)
    local poolInstanceIdList = self.mapUIId2PoolInstanceIdList and self.mapUIId2PoolInstanceIdList[uiNavID] or {}
    local curMaxLength = #poolInstanceIdList
    if maxLength > curMaxLength and maxLength > 0 then
        local needAddCount = maxLength - curMaxLength
        for i = 1, needAddCount do
            local instanceID = SubUIHelper.GenInsGid(self, uiNavID, true)
            local uiIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, true, ...)
            self:ReturnToStation(uiIns.UINavID, instanceID)
        end
        local finalMaxLength = #poolInstanceIdList
        logw('[扩容缩容机制] DoPoolExpansion(uiNavID, maxLength) 扩容最终长度:', finalMaxLength, '目标长度:', maxLength)
    else
        --- do nothing
        logw('[扩容缩容机制] DoPoolExpansion(uiNavID, maxLength) 无需扩容:', curMaxLength, '目标长度:', maxLength)
    end
end

--------------------------------------------------------------------------
--- Public API 缩容 缩容对象池内数量（仅操作池内实例，不影响已经加入父节点的子ui）
--------------------------------------------------------------------------
function SubUIOwnerPack:DoPoolReduction(uiNavID, maxLength)
    local poolInstanceIdList = self.mapUIId2PoolInstanceIdList and self.mapUIId2PoolInstanceIdList[uiNavID] or {}
    local curMaxLength = #poolInstanceIdList
    if curMaxLength > maxLength and curMaxLength > 0 then
        local needClearCount = curMaxLength - maxLength
        local needRemoveInstanceIdList = {}
        for i = 1, needClearCount do
            local instanceID = table.remove(poolInstanceIdList, 1)
            table.insert(needRemoveInstanceIdList, instanceID)
        end
        for idx, instanceID in ipairs(needRemoveInstanceIdList) do
            local subUIIns, bReused, tokenId = self:_InternalGetChild(uiNavID, instanceID, false)
            if subUIIns and not hasdestroy(subUIIns) then
                if subUIIns.RemoveOpenCallBack then
                    subUIIns:RemoveOpenCallBack(self._OnPackSubUIOpened, self)
                end
                if subUIIns.RemoveCloseCallBack then
                    subUIIns:RemoveCloseCallBack(self._OnPackSubUIMuted, self)
                end
                Facade.UIManager:CloseUI(subUIIns)
            end
            log('[扩容缩容机制] DoPoolReduction(uiNavID, maxLength) instanceID', instanceID, '当前清理个数:', idx)
        end
        local finalMaxLength = #poolInstanceIdList
        logw('[扩容缩容机制] DoPoolReduction(uiNavID, maxLength) 缩容最终长度:', finalMaxLength, '目标长度:', maxLength)
    else
        --- do nothing
        logw('[扩容缩容机制] DoPoolReduction(uiNavID, maxLength) 无需缩容:', curMaxLength, '目标长度:', maxLength)
    end
end

return SubUIOwnerPack
