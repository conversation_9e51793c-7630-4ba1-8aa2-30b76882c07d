----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local ClientVehicleSetting = import "ClientVehicleSetting"
local clientVehicleSetting = ClientVehicleSetting.Get(GetWorld())
local UDFMSystemSettingHelper = import "DFMSystemSettingHelper"
local DFMSystemSettingHelper = UDFMSystemSettingHelper.Get(GetWorld())
local EVehicleSensitivityMode = import "EVehicleSensitivityMode"

local VehicleSettingLogic = {}

local VehicleType = {
    vehicle = 1,
	helicopter = 2,
	passenger = 3
}

---载具按键布局
VehicleSettingLogic.ProcessVehicleMode = function(vehicleMode)
    if clientVehicleSetting then
        clientVehicleSetting.VehicleMode = vehicleMode
	    clientVehicleSetting:SaveDataConfig()
    end
end

---战机按键布局
VehicleSettingLogic.ProcessJetMode = function(JetMode)
    if clientVehicleSetting then
        clientVehicleSetting.JetMode = JetMode
	    clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessWeaponVehicleMode = function(vehicleMode)
    if clientVehicleSetting then
        clientVehicleSetting.WeaponVehicleMode = vehicleMode
        clientVehicleSetting:SaveDataConfig()
    end
end

---驾驶视角
VehicleSettingLogic.ProcessDriveAngle = function(driveAngle)
    if clientVehicleSetting then
        clientVehicleSetting.DriveAngle = driveAngle
	    clientVehicleSetting:SaveDataConfig()
    end
end

---快速切换载具武器
VehicleSettingLogic.ProcessEnableFastSwitchVehicleWeapon = function(operation)
    if clientVehicleSetting then
        clientVehicleSetting.SecondWeaponFireBtn = operation
        clientVehicleSetting:SaveDataConfig()
        
    end
end

VehicleSettingLogic.ProcessTankCamFollow = function(isOpen)
    if clientVehicleSetting then
        clientVehicleSetting.TankCamFollow = isOpen
        clientVehicleSetting:SaveDataConfig()
    end
end

---开镜模式
VehicleSettingLogic.ProcessRollCameraMode = function(rollCameraMode)
    if clientVehicleSetting then
        clientVehicleSetting.RollCameraMode = rollCameraMode
	    clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessCannonCamFollow = function(isOpen)
    if clientVehicleSetting then
        clientVehicleSetting.CannonCamFollow = isOpen
        clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessAimAssistMap = function(assistType,isOpen)
    if clientVehicleSetting then
        clientVehicleSetting.VehicleAimAssistModeMap:Add(assistType,isOpen)
        clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessFixedWingOption = function(isOpen)
    if clientVehicleSetting then
        clientVehicleSetting.bFixedWingOption = isOpen
        clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessVehicleCannonLockMode = function(vehicleCannonLockMode)
    if clientVehicleSetting then
        clientVehicleSetting.VehicleCannonLockMode = vehicleCannonLockMode
        clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessVehicleAimAssist = function(vehicleAimAssistMode)
    if clientVehicleSetting then
        clientVehicleSetting.VehicleAimAssistMode = vehicleAimAssistMode
        clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessTankDirection = function(isOpen)
    if clientVehicleSetting then
        clientVehicleSetting.TankDirection = isOpen
        clientVehicleSetting:SaveDataConfig()
    end
end

--载具灵敏度保存
VehicleSettingLogic.ProcessVehicleSensitity = function(value,index,bIsGyro,vehicle,bIsfire)
    if not vehicle then
        return
    end
    value = value/100
    local TPPSensitivitySetting = nil
    local FPPSensitivitySetting = nil
    local TPPFireSensitivitySetting = nil
    local FPPFireSensitivitySetting = nil
    local funMap = {} 
    if clientVehicleSetting then
        if vehicle == VehicleType.vehicle then
            TPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Vehicle_Driver_TPP,bIsGyro)
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Vehicle_Driver_FPP,bIsGyro)
            TPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Vehicle_Driver_TPP,bIsGyro)
            FPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Vehicle_Driver_FPP,bIsGyro)
        elseif vehicle == VehicleType.helicopter then
            TPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Helicopter_Driver_TPP,bIsGyro)
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Helicopter_Driver_FPP,bIsGyro)
            TPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Helicopter_Driver_TPP,bIsGyro)
            FPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Helicopter_Driver_FPP,bIsGyro)
        elseif vehicle == VehicleType.passenger then
            --TPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Vehicle_Weapon_Passenger_TPP,bIsGyro)
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(false,EVehicleSensitivityMode.Vehicle_Weapon_Passenger_FPP,bIsGyro)
            --TPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Vehicle_Weapon_Passenger_TPP,bIsGyro)
            FPPFireSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(true,EVehicleSensitivityMode.Vehicle_Weapon_Passenger_FPP,bIsGyro)
        end

        funMap = {
            [1] = function ()
                if TPPSensitivitySetting then
                    TPPSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                end  
                end ,
            [2] = function ()
                if FPPSensitivitySetting then
                    FPPSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                end
                end ,
            [3] = function ()
                if FPPSensitivitySetting then
                    FPPSensitivitySetting.BaseADSSensitivity = value
                end
                end ,
            [4] = function ()
                if TPPFireSensitivitySetting then
                    TPPFireSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                end
                end ,
            [5] = function ()
                if FPPFireSensitivitySetting then
                    FPPFireSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                end
                end ,
            [6] = function ()
                if FPPSensitivitySetting then
                    FPPSensitivitySetting.SensitivitySetting.CustomZoomratedMDVFactor = {value}
                end
                end ,
        }
        if vehicle == VehicleType.passenger then
            funMap = {

                [1] = function ()
                    if FPPSensitivitySetting then
                        FPPSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                    end
                    end ,
                [2] = function ()
                    if FPPSensitivitySetting then
                        FPPSensitivitySetting.BaseADSSensitivity = value
                    end
                    end ,

                [3] = function ()
                    if FPPFireSensitivitySetting then
                        FPPFireSensitivitySetting.SensitivitySetting.CustomSensitivityFactor = value
                    end
                    end ,
                [4] = function ()
                    if FPPSensitivitySetting then
                        FPPSensitivitySetting.SensitivitySetting.CustomZoomratedMDVFactor = {value}
                    end
                    end ,
            }
        end
        
        funMap[index]()
        if not bIsGyro then
            if vehicle == VehicleType.vehicle then
                clientVehicleSetting.NormalSetting.VehicleDriverTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.VehicleDriverFPPSensitivitySetting = FPPSensitivitySetting
                clientVehicleSetting.FireSetting.VehicleDriverTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.VehicleDriverFPPSensitivitySetting = FPPFireSensitivitySetting
            end
            if vehicle == VehicleType.helicopter then
                clientVehicleSetting.NormalSetting.HelicopterDriverTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.HelicopterDriverFPPSensitivitySetting = FPPSensitivitySetting
                clientVehicleSetting.FireSetting.HelicopterDriverTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.HelicopterDriverFPPSensitivitySetting = FPPFireSensitivitySetting
            end
            if vehicle == VehicleType.passenger then
                --clientVehicleSetting.NormalSetting.VehiclePassengerWeaponTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.VehiclePassengerWeaponFPPSensitivitySetting = FPPSensitivitySetting
                --clientVehicleSetting.FireSetting.VehiclePassengerWeaponTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.VehiclePassengerWeaponFPPSensitivitySetting = FPPFireSensitivitySetting
            end
        else
            if vehicle == VehicleType.vehicle then
                clientVehicleSetting.NormalSetting.GyroVehicleDriverTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.GyroVehicleDriverFPPSensitivitySetting = FPPSensitivitySetting
                clientVehicleSetting.FireSetting.GyroVehicleDriverTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.GyroVehicleDriverFPPSensitivitySetting = FPPFireSensitivitySetting
            end
            if vehicle == VehicleType.helicopter then
                clientVehicleSetting.NormalSetting.GyroHelicopterDriverTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.GyroHelicopterDriverFPPSensitivitySetting = FPPSensitivitySetting
                clientVehicleSetting.FireSetting.GyroHelicopterDriverTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.GyroHelicopterDriverFPPSensitivitySetting = FPPFireSensitivitySetting
            end
            if vehicle == VehicleType.passenger then
                --clientVehicleSetting.NormalSetting.GyroVehiclePassengerWeaponTPPSensitivitySetting = TPPSensitivitySetting
                clientVehicleSetting.NormalSetting.GyroVehiclePassengerWeaponFPPSensitivitySetting = FPPSensitivitySetting
                --clientVehicleSetting.FireSetting.GyroVehiclePassengerWeaponTPPSensitivitySetting = TPPFireSensitivitySetting
                clientVehicleSetting.FireSetting.GyroVehiclePassengerWeaponFPPSensitivitySetting = FPPFireSensitivitySetting
            end
        end
	    clientVehicleSetting:SaveDataConfig()
    end
end

VehicleSettingLogic.ProcessVehicleZoomRate = function(value,index,bIsGyro,vehicle,bIsfire)
    bIsfire = setdefault(bIsfire,false)
    local rateArray = {}
    local vehicelSensitivityMode = EVehicleSensitivityMode.NONE
    if clientVehicleSetting then
        if vehicle == VehicleType.vehicle then
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(bIsfire,EVehicleSensitivityMode.Vehicle_Driver_FPP,bIsGyro)
            vehicelSensitivityMode = EVehicleSensitivityMode.Vehicle_Driver_FPP
        elseif vehicle == VehicleType.helicopter then
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(bIsfire,EVehicleSensitivityMode.Helicopter_Driver_FPP,bIsGyro)
            vehicelSensitivityMode = EVehicleSensitivityMode.Helicopter_Driver_FPP
        elseif vehicle == VehicleType.passenger then
            FPPSensitivitySetting = clientVehicleSetting:GetVehicleSensitivityOuterSetting(bIsfire,EVehicleSensitivityMode.Vehicle_Weapon_Passenger_FPP,bIsGyro)
            vehicelSensitivityMode = EVehicleSensitivityMode.Vehicle_Weapon_Passenger_FPP
        end
        for idx,val in pairs(FPPSensitivitySetting.SensitivitySetting.RotationSensitivityArray_Custom) do
            if idx == index then
                table.insert(rateArray,value)
            else
                table.insert(rateArray,val)
            end
        end
        --FPPSensitivitySetting.SensitivitySetting.RotationSensitivityArray_Custom = rateArray
        clientVehicleSetting:SetSensitivityFactor(
        bIsfire,
        vehicelSensitivityMode,
        bIsGyro,
        -1,
        -1,
        -1,
        rateArray
        )
        clientVehicleSetting:SaveDataConfig()
    end
end



return VehicleSettingLogic
