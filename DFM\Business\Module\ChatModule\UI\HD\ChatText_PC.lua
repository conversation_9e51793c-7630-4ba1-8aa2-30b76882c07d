----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ChatText_PC : LuaUIBaseView
local ChatText_PC = ui("ChatText_PC")
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"

local EChatChannel2XML = {
    [ChatChannelType.WorldChat] = "<Chat_World>%s：</>%s",
    [ChatChannelType.TeamChat] = "<customstyle color=\"Color_Chat_Team\">%s：</>%s",
    [ChatChannelType.PrivateChatFriend] =  "<Chat_Friend>%s：</>%s",
    [ChatChannelType.PrivateChatStanger] = "<Chat_Friend>%s：</>%s",
}

function ChatText_PC:Ctor()
    -- loginfo("ChatText_PC:Ctor 新的ChatText！")
    self._wtText = self:Wnd("RichTextBlock_1", UITextBlock)
    self._lastSocketTime = nil

end

function ChatText_PC:OnShow()
    local function f()
        local geometry = self:GetCachedGeometry()
        local size = geometry:GetLocalSize()
        -- loginfo("ChatText_PC viktorhe : size.Y = ", size.Y)
        if size.Y > 1 then
            Module.Chat.Field:SetChatTextHeight(size.Y)
        end
    end
    Timer.DelayCall(0.1, f, self)
end

function ChatText_PC:OnHide()
    self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState)
end

function ChatText_PC:UpdateTeamInviteState(reason, startIndexVal)
    if self.mMapIndex == nil then
        return
    end

    -- if self.mMapIndex < startIndexVal then
    --     return
    -- end
    self:SetTeamInviteStateTxt()
end

function ChatText_PC:SetInfo(txtInfo, msgPanel)
    if txtInfo.msgType == ChatMsgType.TeamInvite then
        local shouldDisplayQuick = true
        if msgPanel and msgPanel.bInOperation then
            shouldDisplayQuick = false
        end
        self:SetTeamInviteInfo(txtInfo, shouldDisplayQuick)
        return
    else
        self.mMapIndex = nil
        self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState)
    end

    self.txtInfoTable = txtInfo
    local showTxt = ""
    local content = txtInfo.content
    if txtInfo.msg and txtInfo.msg.msg_type == ChatMsgType.PresetStr then
        content = ChatLogic.ResolvePresetChat(txtInfo.msg.preset_msg)
    end
    -- if self.txtInfoTable.senderId == Server.AccountServer:GetPlayerId() then
    --     showTxt = string.format("<customstyle color=\"Color_Highlight01\">%s：</>%s", self.txtInfoTable.senderName, content)
    -- else
    --     if self.txtInfoTable.senderName then
    --         if self.txtInfoTable.channel then
    --             showTxt = string.format(EChatChannel2XML[self.txtInfoTable.channel], self.txtInfoTable.senderName, content)
    --         else
    --             showTxt = string.format("%s：%s", self.txtInfoTable.senderName, content)
    --         end
    --     else
    --         showTxt = content
    --     end
    -- end
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5() then
        local playerOnlineId = self:GetPS5OnlineIdByOpenId(self.txtInfoTable.senderId)
        if playerOnlineId ~= nil and self.txtInfoTable.senderName ~= nil then
            self.txtInfoTable.senderName = playerOnlineId
        end
    end
    if isvalid(msgPanel) then
        -- 局内的聊天样式
        showTxt = self:SetInfoStyle_PCGame(content)
    else
        showTxt = self:SetInfoStyle_PC(content)
    end   
    --END MODIFICATION
    -- waterfall box的process子widget逻辑会改变子widget内容（比如一开始是 A[] B[bbb] C[ccc]，来了新的内容后会变成A[bbb] B[ccc] C[new]）
    local bChangeTxt = self.showTxt ~= nil and self.showTxt ~= showTxt
    self.showTxt = showTxt

    self._wtText:SetText(self.showTxt)

    -- 仅局内传参msgPanel
    if isvalid(msgPanel) then
        self:AddLuaEvent(msgPanel.evtHideOperationPanel, self._OnHideOperationPanel, self)
        self.showTime = msgPanel.newMsgShowTime
        if msgPanel.bInOperation then
            self:BP_ManualAnimShowText(99999, 10)
        else
            -- self:BP_ManualAnimShowText(self.showTime, require("socket").gettime() - self.txtInfoTable.recvTime)
            self:BP_ManualAnimShowText(self.showTime, self:GetAnimShowTextDiff())
        end
    end
end

function ChatText_PC:SetTeamInviteInfo(txtInfo, shouldDisplayQuick)
    self.mMapIndex = txtInfo.mapIndex
    self:AddLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState, self)
    self:SetTeamInviteStateTxt(shouldDisplayQuick)
end

function ChatText_PC:SetTeamInviteStateTxt(shouldDisplayQuick)
    local dataValue = Module.Team.Field.GameTeamInviteViewModel:GetCacheData(self.mMapIndex)
    if dataValue and self._wtText then
        shouldDisplayQuick = dataValue.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes and shouldDisplayQuick or false
        self._wtText:SetText(ChatLogic.InGameInviteChatMsgMake(dataValue, false, shouldDisplayQuick))
    end
end

function ChatText_PC:SetInfoStyle_PCGame(content)

    local chatType = self.txtInfoTable.chatType or DSChatType.DSChatTeam
    local showTxt = ""

    -- TODO 后面颜色代码改成枚举
    local senderName = ChatLogic.GetNameWithTeamPrefixInCommander(
        self.txtInfoTable.senderId, self.txtInfoTable.senderName, chatType == DSChatType.DSChatCamp)
    if chatType == DSChatType.DSChatCamp then
        showTxt = string.format("<customstyle color=\"Color_CampNPC\">%s：</>%s", senderName, content)
    else
        showTxt = string.format("<customstyle color=\"Color_Chat_Team\">%s：</>%s", senderName, content)
    end

    showTxt = ChatLogic.AddCommanderPrefix(self.txtInfoTable.senderId, showTxt, chatType == DSChatType.DSChatCamp)

    return showTxt
end

function ChatText_PC:SetInfoStyle_PC(content)
    local showTxt = ""
    if self.txtInfoTable.senderId == Server.AccountServer:GetPlayerId() then
        showTxt = string.format("<customstyle color=\"Color_Highlight01\">%s：</>%s", self.txtInfoTable.senderName, content)
    else
        if self.txtInfoTable.senderName then
            if self.txtInfoTable.channel then
                showTxt = string.format(EChatChannel2XML[self.txtInfoTable.channel], self.txtInfoTable.senderName, content)
            else
                showTxt = string.format("%s：%s", self.txtInfoTable.senderName, content)
            end
        else
            showTxt = content
        end
    end

    return showTxt
end

function ChatText_PC:_OnHideOperationPanel()
    -- self:BP_ManualAnimShowText(self.showTime, require("socket").gettime() - self.txtInfoTable.recvTime)
    self:BP_ManualAnimShowText(self.showTime, self:GetAnimShowTextDiff())
end

function ChatText_PC:GetAnimShowTextDiff()
    local ok, socket =
        pcall(
        function()
            return require("socket")
        end
    )

    local SpecialHandle = function()
        if self._lastSocketTime then
            return (self._lastSocketTime - self.txtInfoTable.recvTime)
        else
            -- 极端情况 消息展示时间差值默认为没处理
            -- 临时处理 后续查明原因
            return 0
        end
    end

    if (not ok) or (not socket) then
        return SpecialHandle()
    end

    if type(socket.gettime) ~= "function" then
        return SpecialHandle()
    end

    self._lastSocketTime = socket.gettime()
    return SpecialHandle()
end

--BEGIN MODIFICATION @ VIRTUOS : 
function ChatText_PC:GetPS5OnlineIdByOpenId(openId)
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(openId)
            if not string.isempty(PS5OnlineId) then
                return PS5OnlineId
            end
        end
    end
    return nil
end
--END MODIFICATION
return ChatText_PC
