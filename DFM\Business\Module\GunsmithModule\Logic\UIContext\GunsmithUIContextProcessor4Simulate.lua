----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithUIContextProcessor = require "DFM.Business.Module.GunsmithModule.Logic.UIContext.GunsmithUIContextProcessor"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GPModularWeaponDescLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithCPPLogic.GPModularWeaponDescLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local EGunsmithUIState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithUIState"
local GunsmithSolutionMainLogic = require "DFM.Business.Module.GunsmithModule.Logic.Solution.GunsmithSolutionMainLogic"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"

---@type GunsmithUIContextProcessor4Simulate : GunsmithUIContextProcessor
local GunsmithUIContextProcessor4Simulate = class('GunsmithUIContextProcessor4Simulate', GunsmithUIContextProcessor)

function GunsmithUIContextProcessor4Simulate:Ctor()
end

function GunsmithUIContextProcessor4Simulate:OverrideContext()
    local backup = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()

    -- local bUseSolutionSkin = GunsmithSolutionMainLogic.GetUseSolutionSkinState()
    -- if not bUseSolutionSkin then
    --     local skinInfoParam = GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromBackupServer()
    --     GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(backup, skinInfoParam)
    -- end

    self:OnFrontendUpdated(backup)
end

function GunsmithUIContextProcessor4Simulate:OverrideContextFromServerPartUpdated(propinfo4Local)
    ---@doc
    --- 先同步Server数据, 在同步Local数据
    self:InternalOverridePropinfo()
    local propinfo = GunsmithUIContextLogic.GetPropInfo()
    local weaponDescription4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    WeaponAssemblyTool.PropInfo_To_Desc(propinfo, weaponDescription4Server)

    local weaponDescription4BackupLocal = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    WeaponAssemblyTool.PropInfo_To_Desc(propinfo4Local, weaponDescription4BackupLocal)
    GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4BackupLocal)

    self:OnBackupUpdated()
end

function GunsmithUIContextProcessor4Simulate:OverrideContextBackupFromDescription(inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)
    --- 保存预改装整枪数据
    --- 1. 覆盖local
    --- 2. 计算未解锁节点
    --- 3. 同步到frontend
    local weaponDescription4BackupLocal = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    self:_InternalOverrideContextBackupFromDescription(weaponDescription4BackupLocal, inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)

    self:InternalInvokeSimulateDataUpdated()
end

function GunsmithUIContextProcessor4Simulate:SyncAddPartNode(itemID, itemGUID, socketGUID, bUnlock)
    ---@doc
    --- 预改装未解锁道具，同步local数据
    local weaponDescription4Local = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    if not bUnlock then
        self:InternalAddPartNode(itemID, itemGUID, socketGUID, weaponDescription4Local)
        GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4Local)
        self:InternalInvokeSimulateDataUpdated()
        return
    end

    ---@doc
    --- 预改装已解锁道具，并且要装备的道具是已经装备在服务器的。
    --- 场景：
    --- 道具 a 已解锁，道具 b 未解锁
    --- 1. 装备道具 a, 道具 a 显示装备态
    --- 2. 装备道具 b, 道具 b 显示预装备态, a 不显示装备态
    --- 3. 装备道具 a, 道具 a 显示装备态, 道具 b 不显示预装备态
    ---- 此时，需要判断，如果在预安装的状态下，如果装备的道具是服务器已经安装的道具，需要Revert此socket的道具信息
    local weaponDescription4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local serverItemID, serverItemGUID, serverFlag = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription4Server, socketGUID)
    if serverItemGUID == itemGUID then
        self:InternalRevertPartNode(socketGUID)
        self:InternalInvokeSimulateDataUpdated()
        return
    end

    ---@doc
    --- 预改装模式下，装备已解锁道具, 先同步Local数据，在同步Server数据
    local bSuccess = self:InternalAddPartNode(itemID, itemGUID, socketGUID, weaponDescription4Local)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor4Simulate.SyncAddPartNode, local fail:", itemID, itemGUID, socketGUID)
        return
    end

    GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4Local)

    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local weaponDescription4Sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    local result = nil
    bSuccess, result = self:InternalAddPartNode(itemID, itemGUID, socketGUID, weaponDescription4Sync)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor4Simulate.SyncAddPartNode, sync fail:", itemID, itemGUID, socketGUID)
        return
    end
    self:SyncPart2Server(result, itemID, socketGUID)
end

function GunsmithUIContextProcessor4Simulate:SyncRemovePartNode(socketGUID, bUnlock)
    local weaponDescription4Local = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    if not bUnlock then
        self:InternalRevertPartNode(socketGUID)
        self:InternalInvokeSimulateDataUpdated()
        return
    end

    local bSuccess = self:InternalRemovePartNode(socketGUID, weaponDescription4Local)
    GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4Local)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor4Simulate.SyncRemovePartNode, local fail:", socketGUID)
        return
    end

    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local weaponDescription4Sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    local result = nil
    bSuccess, result = self:InternalRemovePartNode(socketGUID, weaponDescription4Sync)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor4Default.SyncRemovePartNode, sync fail:", socketGUID)
        return
    end
    self:SyncPart2Server(result, 0, socketGUID)
end

function GunsmithUIContextProcessor4Simulate:PreProcessSyncTuneNodeValue(socketGUID)
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local backup = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    backup:SeteTuneNodeValueFromDesc(socketGUID, frontend)
end

function GunsmithUIContextProcessor4Simulate:OnSyncTuneNodeValue(socketGUID)
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    -- 同步节点为预安装状态，暂不需要同步
    local bIsSimulate = GPModularWeaponDescLogic.IsSimulateStateBySocketGUID(frontend, socketGUID)
    if bIsSimulate then
        return
    end
    self:SyncPart2Server()
end

-- 安装Frontend可以安装的配件,其中会卸下Server上的配件(即使Frontend中的的配件不能安装)
function GunsmithUIContextProcessor4Simulate:SyncPart2ServerFromFrontend()
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local unlockPartsInfo, lockPartsInfo = GunsmithUIContextLogic.GetIDsContainedFromWeaponDescription(frontend)
    if not table.isempty(unlockPartsInfo) then
        GunsmithUIContextLogic._InternalLinkPartNodeGUIDBySocketGUIDTable(frontend, unlockPartsInfo)
    end

    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    GPModularWeaponDescLogic.BuildFromModularWeaponDesc(sync, frontend)

    if not table.isempty(lockPartsInfo) then
        GunsmithUIContextLogic._InternalRemovePartNodeBySocketGUIDTable(sync, lockPartsInfo)
    end

    -- 空机匣额外处理根节点武器
    self:InternalRelinkWeaponDescriptionRootNodeGUID(sync)

    -- 检查一下是否需要同步到Server
    -- local weaponDescription4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    -- local bIsSame = WeaponHelperTool.CompareWeaponDescription(sync, weaponDescription4Server)
    -- if (not bForce) and bIsSame then
    --     logwarning("GunsmithUIContextProcessor4Simulate:SyncPart2ServerFromFrontend -- No Change")
    --     return
    -- end

    local propinfo4Sync = WeaponAssemblyTool.Desc_To_PropInfo(sync)
    local propinfo4Frontend = WeaponAssemblyTool.Desc_To_PropInfo(frontend)

    self:CSWAssemblyDepositPropUpdateReq(propinfo4Sync, fCallBack, propinfo4Frontend)
end

function GunsmithUIContextProcessor4Simulate:OnUIStateUpdated()
    local weaponDesctipntion4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local backup = GunsmithUIContextLogic.GetBackup()
    backup:SetWeaponDescription(weaponDesctipntion4Server)
end

function GunsmithUIContextProcessor4Simulate:OnOverrideContextFromServerSkinUpdated(skinInfoParam)
    local weaponDescription4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription4Server, skinInfoParam)
end

function GunsmithUIContextProcessor4Simulate:InteralFormatParam4SyncContext()
    local weapinDescription4Sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    local propinfo4Sync = WeaponAssemblyTool.Desc_To_PropInfo(weapinDescription4Sync)

    local weaponDescription4BackupLocal = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()
    local propinfo4Local = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription4BackupLocal)
    return propinfo4Sync, propinfo4Local
end

function GunsmithUIContextProcessor4Simulate:InternalInvokeSimulateDataUpdated(bBroadcastEvent)
    self:OnBackupUpdated()

    bBroadcastEvent = setdefault(bBroadcastEvent, true)
    if not bBroadcastEvent then
        return
    end
    Module.Gunsmith.Config.Events.evtGunsmithOnSimulateDataUpdated:Invoke()
end

function GunsmithUIContextProcessor4Simulate:InternalRevertPartNode(socketGUID)
    local weaponDescription4Server = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local weaponDescription4Local = GunsmithUIContextLogic.GetWeaponDescription4BackupLocal()

    local servetID, servertGUID, serverFlag = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription4Server, socketGUID)
    if servetID == 0 and servertGUID == 0 then
        self:InternalRemovePartNode(socketGUID, weaponDescription4Local)
        GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4Local)
        return
    end

    local itemInfoContext = GPModularWeaponDescLogic.GetPartsDefaultItemInfoFromDesc(servetID, servertGUID, socketGUID, weaponDescription4Server)

    self:InternalAddPartNode(servetID, servertGUID, socketGUID, weaponDescription4Local, itemInfoContext)
    GPModularWeaponDescLogic.RefreshSocketNodeFlag(weaponDescription4Local)
end

return GunsmithUIContextProcessor4Simulate