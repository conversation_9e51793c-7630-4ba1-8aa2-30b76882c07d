----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local ClientSolSetting = import "ClientSolSetting"
local clientSolSetting = ClientSolSetting.Get(GetWorld())
local SolSettingLogic = {}


SolSettingLogic.ProcessPreferMarkItem = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bSafeBoxPreferMarkItem = bIsOpen
        Module.Looting:SetSafeBoxPreferMarkItem(bIsOpen)
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessPreferHighValue = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bSafeBoxPreferHighValueItem = bIsOpen
        Module.Looting:SetSafeBoxPreferHighValueItem(bIsOpen)
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessLootingShare_Team = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bShareSpoilTeam = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessLootingShare_All = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bShareSpoilAll = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessCloseLootPanel = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bHurtCloseLootPanel = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessSprintCloseLootPanel = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bSprintCloseLootPanel = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

--智能药品推荐
SolSettingLogic.ProcessEnableTreat = function(bOpen)
	if clientSolSetting then
        clientSolSetting.bEnableTreat = bOpen
	    clientSolSetting:SaveDataConfig()
    end
end


--回收系统单格价值
SolSettingLogic.ProcessSafeBoxPriceThreshold = function(num)
	if clientSolSetting then
        clientSolSetting.SafeBoxPriceThreshold = num
        Module.Looting:SetSafeBoxPriceThreshold(num)
	    clientSolSetting:SaveDataConfig()
    end
end

--轮盘呼出时间自定义
SolSettingLogic.ProcessOpenRouletteTime = function(num)
	if clientSolSetting then
        clientSolSetting.OpenRouletteTime = num
	    clientSolSetting:SaveDataConfig()
    end
end

--药品耐久度限制
SolSettingLogic.ProcessDrugDuration = function(num)
	if clientSolSetting then
        clientSolSetting.DrugDuration = num
	    clientSolSetting:SaveDataConfig()
    end
end


SolSettingLogic.ProcessShareSpoils = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bShareSpoils = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessAuction = function(bIsOpen)
    if clientSolSetting then
        clientSolSetting.bIsAuctionAutoOrganize = bIsOpen
	    clientSolSetting:SaveDataConfig()
        Module.Auction:SwitchAutoSortMode(bIsOpen)
    end
end

--不同道具换行摆放
SolSettingLogic.ProcessInventoryAutoNewLine = function(bIsOpen)
	if clientSolSetting then
        clientSolSetting.bInventoryAutoNewline = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

--每次进入仓库是否自动整理
SolSettingLogic.ProcessAutoSort = function(bIsOpen)
	if clientSolSetting then
        clientSolSetting.bInventorySortEveryEnter = bIsOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessInfantryVision = function(num)
	if clientSolSetting then
        clientSolSetting.InfantryVision = num
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessVehicleVision = function(num)
	if clientSolSetting then
        clientSolSetting.VehicleVision = num
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessAirPlaneVision = function(num)
	if clientSolSetting then
        clientSolSetting.AirPlaneVision = num
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessMpMapScale = function(num)
	if clientSolSetting then
        clientSolSetting.MpMapScale = num
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessMapAutoRotateSol = function(bOpen)
	if clientSolSetting then
        clientSolSetting.bMapAutoRotateSol = bOpen
	    clientSolSetting:SaveDataConfig()
    end
end

SolSettingLogic.ProcessMapAutoRotateMp = function(bOpen)
	if clientSolSetting then
        clientSolSetting.bMapAutoRotateMp = bOpen
	    clientSolSetting:SaveDataConfig()
    end
end

--获取SOL玩法设置项参数值
SolSettingLogic.GetDataByID = function(id)
	if clientSolSetting then
        return clientSolSetting[id]
    end
end

return SolSettingLogic