----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------



local UIReleaseUtil = {}
local RefUtil = require "DFM.YxFramework.Util.RefUtil"
local PlatformLogic = require "DFM.YxFramework.Managers.UI.Platform.PlatformLogic"

local bMuteRelease = true
local bAutoClearSubUI = true
local bHighMemoryRemoveRef = true
local bLowMemoryRemoveRef = true
local bLowMemoryRemoveChildRef = true
local bForceMemoryRemoveRef = true
local bForceSubUIReleaseAll = true

UIReleaseUtil.MuteRelease = function(self)
    if not (bMuteRelease == true or (self.bFinalClose == true and FINAL_CLOSE_CLEAR_REF)) then
        return
    end
    -- logerror('#[ Lua GC Debug ] *******【UIReleaseUtil】 MuteRelease', self, self._cname, self.__cppinst)
    local uiSettings = self:GetUISettings()
    if uiSettings then
        --- step1 移除父节点
        local layer = uiSettings.UILayer
        if layer ~= EUILayer.Sub then
            local root = Facade.UIManager:GetFrameRoot()
            if isvalid(root) then
                root:RemoveChild_CallFromChild(self)
            end
        end
        --- step2 Release释放逻辑
        local bProcessReleaseLayers = UIReleaseUtil._CheckIsMuteReleaseType(layer, uiSettings.IsMuteRelease)
        if bProcessReleaseLayers then
            UIReleaseUtil._ProcessReleaseUnbindLuaRefs(self, uiSettings)
        else
            --- step3 仅Sub层级FinalClose时，清除Ref
            local bProcessUnRefOnlyLayers = (self.bFinalClose == true and FINAL_CLOSE_CLEAR_REF) and PlatformLogic.IsOutOfAreaType(layer)
            if bProcessUnRefOnlyLayers then
                if bForceSubUIReleaseAll then
                    UIReleaseUtil._ProcessReleaseUnbindLuaRefs(self, uiSettings)
                else
                    UIReleaseUtil._ProcessUnbindLuaRefsOnly(self)
                end
            end
        end
    end
end

--- 检查是否需要参与MuteRelease
function UIReleaseUtil._CheckIsMuteReleaseType(layerType, bIsMuteRelease)
    if bIsMuteRelease ~= nil then
        return bIsMuteRelease
    end
    if layerType and
        not PlatformLogic.IsOutOfAreaType(layerType)
        and not PlatformLogic.IsForceAreaType(layerType)
        and not PlatformLogic.IsHUDAreaType(layerType) then
        return true
    end
    return false
end

__DebugOnly_UILayerName = function(eUILayer)
    if EUILayer then
        for k,v in pairs(EUILayer) do
            if v == eUILayer then
                return k
            end
        end
    end
    return tostring(eUILayer)
end

--------------------------------------------------------------------------
--- 释放并解绑LuaRef相关API
--------------------------------------------------------------------------
UIReleaseUtil.RecursiveReleaseUnbindLuaRefs = function(uiIns, bAutoClearRef, bAutoClearSubUI, recursiveCount)
    recursiveCount = recursiveCount + 1
    local childWidgets = rawget(uiIns, "_childWidgets")
    if childWidgets then
        for i = 1, #childWidgets do
            local childIns = childWidgets[i]
            if not hasdestroy(childIns) then
                if childIns.Release then
                    UIReleaseUtil.RecursiveReleaseUnbindLuaRefs(childIns, bAutoClearRef, bAutoClearSubUI, recursiveCount)
                end
            end
        end
    end

    --- wnd部分
    local wndWidgets = rawget(uiIns, "_wndWidgets")
    if wndWidgets then
        for name, wndUIIns in pairs(wndWidgets) do
            if not hasdestroy(wndUIIns) then
                if wndUIIns.Release then
                    UIReleaseUtil.RecursiveReleaseUnbindLuaRefs(wndUIIns, bAutoClearRef, false, recursiveCount)
                end
            end
        end
    end

    safecall(uiIns.PreReleaseOnUnBindLuaRefs, uiIns)

    if bAutoClearSubUI then
        trycall(Facade.UIManager.ClearAllSubUI, Facade.UIManager, uiIns, false)
    end

    --- 补充生命周期
    if bAutoClearRef and not hasdestroy(uiIns) then
        if uiIns.TryMute then
            uiIns:TryMute()
        elseif uiIns.OnClose then
            uiIns:OnClose()
        end
    end

    if bAutoClearRef then
        if bLowMemoryRemoveChildRef then
            if not VersionUtil.IsShipping() then
                local uiTokenID = uiIns.UITokenID
                if Facade.UIManager:IsLuaPendingKill(uiTokenID) and LUA_PENDING_KILL_ENABLE then
                    logwarning('[Low Memory Log - UIReleaseUtil - LuaPendingKill ] ******* _ProcessReleaseUnbindLuaRefs -----【ChildSubUI】 -------------', uiIns._cname, uiIns.__cppinst, recursiveCount)
                else
                    logwarning('[Low Memory Log - UIReleaseUtil ] ******* _ProcessReleaseUnbindLuaRefs -----【ChildSubUI】 -------------', uiIns._cname, uiIns.__cppinst, recursiveCount)
                end
            end
            rawset(uiIns, "_wtParent", nil)
            UIUtil.UnbindLuaCppRef(uiIns)
        end
    end
    -- logwarning('[Low Memory Log - UIReleaseUtil ] ******* Release 且置空 cpp引用极其luaIns表 -【ChildSubUI】 -------------', uiIns._cname)
    trycall(uiIns.Release, uiIns)
end

function UIReleaseUtil._ProcessReleaseUnbindLuaRefs(self, uiSettings)
    local bAutoClearRef = false
    --- Long1 GM 强制All - 解绑
    if bForceMemoryRemoveRef then
        bAutoClearRef = true
    else
        if Facade.UIManager:GetIsLowMemoryState() then
            if bLowMemoryRemoveRef then
                --- Long1 低内存 - 解绑
                if uiSettings.IsReleaseClearRef then
                    bAutoClearRef = true
                end
            end
        else
            if bHighMemoryRemoveRef then
                --- Long1 高内存 - 解绑
                if uiSettings.IsReleaseClearRef then
                    bAutoClearRef = true
                end
            end
        end
    end

    local childWidgets = rawget(self, "_childWidgets")
    local recursiveCount = 0
    if childWidgets then
        for i = 1, #childWidgets do
            local childIns = childWidgets[i]
            if not hasdestroy(childIns) then
                if childIns.Release then
                    UIReleaseUtil.RecursiveReleaseUnbindLuaRefs(childIns, bAutoClearRef, bAutoClearSubUI, recursiveCount)
                end
            else
                -- logerror('[UIReleaseUtil 查重] _ProcessReleaseUnbindLuaRefs childWidgets has destroy', i, childIns._cname, childIns.UITokenID)
            end
        end
    end
    
    --- wnd部分
    local wndWidgets = rawget(self, "_wndWidgets")
    if wndWidgets then
        for name, wndUIIns in pairs(wndWidgets) do
            if not hasdestroy(wndUIIns) then
                if wndUIIns.Release then
                    UIReleaseUtil.RecursiveReleaseUnbindLuaRefs(wndUIIns, bAutoClearRef, bAutoClearSubUI, recursiveCount)
                end
            else
                -- logerror('[UIReleaseUtil 查重] _ProcessReleaseUnbindLuaRefs wndWidgets has destroy', name, wndUIIns._cname, wndUIIns.UITokenID)
            end
        end
    end

    if bAutoClearSubUI then
        trycall(Facade.UIManager.ClearAllSubUI, Facade.UIManager, self, false)
    end
    if bAutoClearRef then
        local layerName = __DebugOnly_UILayerName(uiSettings.UILayer) or "Unknown"
        logwarning('[Low Memory Log - UIReleaseUtil ] *******  _ProcessReleaseUnbindLuaRefs -【EUILayer.', layerName, 'Owner】 -------------', self._cname, self.__cppinst)
        UIUtil.UnbindLuaCppRef(self)
    end
    -- logwarning('[Low Memory Log - UIReleaseUtil ] ******* Release 且置空 cpp引用极其luaIns表 -【StackPopOwner】 -------------', self._cname)
    self:Release()

    -- if bAutoClearRef then
    --     ULuaSubsystem.Get().FullGC(false)
    -- end
end

--------------------------------------------------------------------------
--- 仅解绑LuaRef相关API
--------------------------------------------------------------------------
UIReleaseUtil.RecursiveUnbindLuaRefsOnly = function(self, recursiveCount)
    recursiveCount = recursiveCount + 1
    local childWidgets = rawget(self, "_childWidgets")
    if childWidgets then
        for i = 1, #childWidgets do
            local childIns = childWidgets[i]
            if not hasdestroy(childIns) then
                if childIns.Release then
                    UIReleaseUtil.RecursiveUnbindLuaRefsOnly(childIns, recursiveCount)
                end
            else
                -- logerror('[UIReleaseUtil 查重] RecursiveUnbindLuaRefsOnly childWidgets has destroy', i, childIns._cname, childIns.UITokenID)
            end
        end
    end

    --- wnd部分
    local wndWidgets = rawget(self, "_wndWidgets")
    if wndWidgets then
        for name, wndUIIns in pairs(wndWidgets) do
            if not hasdestroy(wndUIIns) then
                if wndUIIns.Release then
                    UIReleaseUtil.RecursiveUnbindLuaRefsOnly(wndUIIns, recursiveCount)
                end
            else
                -- logerror('[UIReleaseUtil 查重] RecursiveUnbindLuaRefsOnly wndWidgets has destroy', name, wndUIIns._cname, wndUIIns.UITokenID)
            end
        end
    end
    if not VersionUtil.IsShipping() then
        logwarning('[Low Memory Log - UIReleaseUtil ] ******* RecursiveUnbindLuaRefsOnly -----【ChildSubUI RefUtil.RemoveRef】 -------------', self._cname, self.__cppinst, recursiveCount)
    end
    RefUtil.RemoveRef(self)
end

function UIReleaseUtil._ProcessUnbindLuaRefsOnly(self)
    local recursiveCount = 0
    UIReleaseUtil.RecursiveUnbindLuaRefsOnly(self, recursiveCount)
end

--------------------------------------------------------------------------
--- PendingUnbindLua相关API
--------------------------------------------------------------------------
UIReleaseUtil.LuaPendingKillRelease = function(self)
    local bProcessPendingUnbindLuaLayers = (self.bFinalClose == true and FINAL_CLOSE_CLEAR_REF and LUA_PENDING_KILL_ENABLE)
    if bProcessPendingUnbindLuaLayers then
        --- 互为替代PLAN A
        UIReleaseUtil._ProcessMarkLuaPendingKill(self)
        
        --- 互为替代PLAN B
        -- if not hasdestroy(self) then
        --     --- check condition
        --     if self:IsChildOf(LuaUIBaseView) then
        --         local uiTokenID = self.UITokenID
        --         if uiTokenID then
        --             if not VersionUtil.IsShipping() then
        --                 logwarning('[Low Memory Log - UIReleaseUtil ] ******* Trigger By FinalClose -----【MarkLuaPendingKill】 -------------', self._cname, self.__cppinst)
        --             end
        --             Facade.UIManager:MarkLuaPendingKill(uiTokenID)
        --         end
        --     end
        -- end
    end
end

UIReleaseUtil.RecursiveMarkLuaPendingKill = function(self, recursiveCount)
    recursiveCount = recursiveCount + 1
    local childWidgets = rawget(self, "_childWidgets")
    if childWidgets then
        for i = 1, #childWidgets do
            local childIns = childWidgets[i]
            if not hasdestroy(childIns) then
                --- check condition
                if childIns:IsChildOf(LuaUIBaseView) then
                    UIReleaseUtil.RecursiveMarkLuaPendingKill(childIns, recursiveCount)
                end
            end
        end
    end
    if not VersionUtil.IsShipping() then
        logwarning('[Low Memory Log - UIReleaseUtil ] ******* RecursiveMarkLuaPendingKill -----【ChildSubUI MarkLuaPendingKill】 -------------', self._cname, self.__cppinst, recursiveCount)
    end
    local uiTokenID = self.UITokenID
    if uiTokenID then
        Facade.UIManager:MarkLuaPendingKill(uiTokenID)
    end
end

function UIReleaseUtil._ProcessMarkLuaPendingKill(self)
    local recursiveCount = 0
    UIReleaseUtil.RecursiveMarkLuaPendingKill(self, recursiveCount)
end

--------------------------------------------------------------------------
--- Debug相关API
--------------------------------------------------------------------------
function UIReleaseUtil.ToggleMuteRelease(bToggle)
    if bToggle == 1 or bToggle == true then
        bMuteRelease = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleMuteRelease(bToggle)", tostring(bMuteRelease))
    else
        bMuteRelease = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleMuteRelease(bToggle)", tostring(bMuteRelease))
    end
end

function UIReleaseUtil.ToggleForceMemoryRemoveRef(bToggle)
    if bToggle == 1 or bToggle == true then
        bForceMemoryRemoveRef = true
        bForceSubUIReleaseAll = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleForceMemoryRemoveRef(bToggle)", tostring(bForceMemoryRemoveRef))
    else
        bForceMemoryRemoveRef = false
        bForceSubUIReleaseAll = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleForceMemoryRemoveRef(bToggle)", tostring(bForceMemoryRemoveRef))
    end
end

function UIReleaseUtil.ToggleLowMemoryRemoveRef(bToggle)
    if bToggle == 1 or bToggle == true then
        bLowMemoryRemoveRef = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleLowMemoryRemoveRef(bToggle)", tostring(bLowMemoryRemoveRef))
    else
        bLowMemoryRemoveRef = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleLowMemoryRemoveRef(bToggle)", tostring(bLowMemoryRemoveRef))
    end
end

function UIReleaseUtil.ToggleLowMemoryRemoveChildRef(bToggle)
    if bToggle == 1 or bToggle == true then
        bLowMemoryRemoveChildRef = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleLowMemoryRemoveChildRef(bToggle)", tostring(bLowMemoryRemoveChildRef))
    else
        bLowMemoryRemoveChildRef = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleLowMemoryRemoveChildRef(bToggle)", tostring(bLowMemoryRemoveChildRef))
    end
end

function UIReleaseUtil.ToggleHighMemoryRemoveRef(bToggle)
    if bToggle == 1 or bToggle == true then
        bHighMemoryRemoveRef = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleHighMemoryRemoveRef(bToggle)", tostring(bHighMemoryRemoveRef))
    else
        bHighMemoryRemoveRef = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleHighMemoryRemoveRef(bToggle)", tostring(bHighMemoryRemoveRef))
    end
end

function UIReleaseUtil.ToggleRemoveRefToDefault()
    bMuteRelease = true
    bAutoClearSubUI = true
    bHighMemoryRemoveRef = true
    bLowMemoryRemoveRef = true
    bLowMemoryRemoveChildRef = true
    bForceMemoryRemoveRef = false
end


function UIReleaseUtil.ToggleAutoClearSubUI(bToggle)
    if bToggle == 1 or bToggle == true then
        bAutoClearSubUI = true
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleAutoClearSubUI(bToggle)", tostring(bAutoClearSubUI))
    else
        bAutoClearSubUI = false
        logframe("#[ Lua GC Debug ] *******【UIReleaseUtil】 ToggleAutoClearSubUI(bToggle)", tostring(bAutoClearSubUI))
    end
end


return UIReleaseUtil