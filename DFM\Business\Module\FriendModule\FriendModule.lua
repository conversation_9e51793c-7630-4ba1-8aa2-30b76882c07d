----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class FriendModule : ModuleBase
local FriendModule = class("FriendModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"


function FriendModule:Ctor()
    self.nearTbl = {}
    self.RelationList = nil
    self._index = 0
    self._isInit = true
    self._lbsIsOpen = false
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
---模块Init回调，用于初始化一些数据
function FriendModule:OnInitModule()
    Facade.ProtoManager:AddNtfListener("CSFriendFavChangeNtf", self._OnMsgFriendFavChangeNtf, self)
    --self:AddLuaEvent(Module.LBS.Config.Events.evtOnLBSRelation, self.OnNearPlayerListGet, self)
    self:AddLuaEvent(Server.FriendServer.Events.evtFriendLishChange, self.OnFriendLishChange, self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
        DFMOnlineIdentityManager.OnPlatformUserToUserPermissionCacheResetDelegate:Add(self.SyncPS5BlockListToGame, self)
        self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginSuccess, self.SyncPS5BlockListToGame, self)
        self:AddLuaEvent(Server.FriendServer.Events.evtBlackList, self.SyncPS5BlockListToGame, self)
    end
    -- END MODIFICATION - VIRTUOS
    --Module.LBS:GetNearby()
end

function FriendModule:OnGameFlowChangeEnter(gameFlowType)
    self:_FavChangeMsg(gameFlowType)
end

function FriendModule:OnGameFlowChangeLeave()
end

---若为非懒加载模块，则在Init后调用
---模块默认加载资源（预加载UI蓝图、需要用到的图片等等
function FriendModule:OnLoadModule()
end

---注销LuaEvent、Timer监听
function FriendModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------

--附近的人
function FriendModule:OnNearPlayerListGet(relationRet)
    loginfo("OnNearPlayerListGet :")
    logtable(relationRet)
    self.RelationList = relationRet
    self._index = 0
    if self._isInit then
        self:GetNearPlayerList()
        self._isInit = false
    end
end

--通知聊天，刷新一下数据
function FriendModule:OnFriendLishChange()
    loginfo("OnFriendLishChange")
    Module.Chat:SyncChatDataFromeServer(true)
end

function FriendModule:GetNearPlayerList()
    self.nearTbl = {}
    if not self.RelationList then return end
    local size = self._index + 4
    for i = self._index , size do
        if i >= self.RelationList:Num() then break end
        if self.RelationList:Get(i) then
            local tmp = self.RelationList:Get(i)
            local playerTbl = {
                player_id = UGameplayBlueprintHelper.FStringToInt64(GetWorld(), tmp.openid),
                nick_name = tmp.userName,
                gender = tonumber(tmp.gender) == 1 and 0 or 1,
                pic_url = tmp.pictureUrl,
                distance = tmp.distance,
            }
            loginfo("GetNearPlayerList() :", self._index)
            logtable(playerTbl)
            table.insert(self.nearTbl, playerTbl)
            self._index = self._index + 1
        end
    end
    return self.nearTbl
end

function FriendModule:GetNearTbl()
    return self.nearTbl
end

--添加好友接口
function FriendModule:AddFriend(playerId, applyType, delayCall, roomId, recommendId, platformId)
    if Server.AccountServer:GetPlayerId() == playerId then
        Module.CommonTips:ShowSimpleTip(Module.Friend.Config.addYouSelf)
        return
    end
    if self:CheckIsBlack(playerId) then
        Module.CommonTips:ShowSimpleTip(Module.Friend.Config.playerInYouBlack)
       return
    end
    if self:CheckIsGameFriend(playerId) then
        Module.CommonTips:ShowSimpleTip(Module.Friend.Config.playerInYouFriend)
       return
    end

    -- if platformId ~= nil and Server.AccountServer.isOpenFakePlat() and not Server.AccountServer:IsSamePlatPlayer(platformId) then
    --     -- 只提示发送成功 不真正请求
    --     logerror("FriendModule:AddFriend 跨平台添加好友")
    --     if delayCall then
    --         delayCall()
    --     end
    --     LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendAddApplySuccess)
    --     return
    -- end

    applyType = setdefault(applyType, FriendApplySource.InvalidApply)
    Server.FriendServer:AddFriend(playerId, "", applyType, delayCall, roomId, recommendId)
end

--添加好友接口
function FriendModule:AddFriendInGame(playerId)
    if self.AddFriend then
        self:AddFriend(playerId, FriendApplySource.SettlementRecommendation)
    end
end

--得到好友申请的红点
function FriendModule:GetNewFriendApplyNum()
    return Server.FriendServer:GetNewFriendMsgNum()
end

function FriendModule:_OnMsgFriendFavChangeNtf(res)
    loginfo("FriendModule:_OnMsgFriendFavChangeNtf")
    logtable(res,true)
    local playerIdMine =  Server.AccountServer:GetPlayerId()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    Server.FriendServer:UpdateFavorabilityData(res.friend_id, res.change_value)
    self._msg = string.format(Module.Friend.Config.favChangeMsg[res.reason], res.nick_name, res.change_value)
    if playerIdMine ~= res.source_player_id then self._msg=nil return end
    if curGameFlow == EGameFlowStageType.SafeHouse or curGameFlow == EGameFlowStageType.Lobby then
        Module.CommonTips:ShowSimpleTip(self._msg)
        self._msg = nil
    end
end

function FriendModule:SetFriendRedPoint(widget)
    if widget then
        local num = Server.FriendServer:GetNewFriendMsgNum()
        if num <= 0 then
            widget:SetReddotVisible(false)
        elseif num >= 99 then
            widget:SetReddotVisible(true,EReddotType.Number)
            widget:SetContentText("99")
        else
            widget:SetReddotVisible(true,EReddotType.Number)
            widget:SetContentText(num)
        end
    end
end

function FriendModule:SetFriendRedPointNotNum(widget)
    if widget then
        local num = Server.FriendServer:GetNewFriendMsgNum()
        if num <= 0 then
            widget:SetReddotVisible(false)
        else
            widget:SetReddotVisible(true,EReddotType.Normal)
            loginfo('FriendModule:_RefreshFriendReddot() SetFriendRedPointNotNum')
        end
    end
end

function FriendModule:_FavChangeMsg(curGameFlow)
    if curGameFlow == EGameFlowStageType.SafeHouse and self._msg then
        loginfo("FriendModule:_FavChangeMsg","msg",tostring(self._msg))
        Timer.DelayCall(2, function ()
            Module.CommonTips:ShowSimpleTip(self._msg)
        end, self)
        self._msg = nil
    end
end

--检查次id是否是自己的好友
function FriendModule:CheckIsFriend(playerId)
    return Server.FriendServer:CheckIsFriend(playerId)
end

--检查次id是否是自己的游戏好友
function FriendModule:CheckIsGameFriend(playerId)
    return Server.FriendServer:CheckIsGameFriend(playerId)
end

function FriendModule:CheckIsFriendByNick(nickName)
    return Server.FriendServer:CheckIsFriendByNick(nickName)
end

--得到好友列表，可用playerid访问
function FriendModule:GetFriendList()
    local friendList = Server.FriendServer:GetFriendList()
    return friendList
end

function FriendModule:CheckIsBlack(playerId)
    local blackList = Server.FriendServer:GetBlackList()
    if not blackList[tonumber(playerId)] then
        return false
    end
    return true
end

--得到当前scrollbox可显示的box个数
function FriendModule:GetFriendScrBoxNum(scrollBoxSizeY, boxSizeY, scrBoxSpacingY, boxColumn)
    boxColumn = boxColumn and boxColumn or 1
    local num = math.floor(scrollBoxSizeY / (boxSizeY + scrBoxSpacingY) + 0.5) * boxColumn
    loginfo("FriendModule:GetFriendScrBoxNum:", num, scrollBoxSizeY, boxSizeY)
    return num
end
--189106772081360217717
function FriendModule:is_number(text)
    for i = 1 ,StringUtil.GetRealWidth(text) do
        local a = string.sub(text, i, i)
        if string.byte(a) < 48 or string.byte(a) > 57 then
            return false
        end
    end
    return true
end

function FriendModule:SetPlayerState(stateWidget,friendInfo,Icon)
    FriendLogic.SetPlayerStateCode(stateWidget, friendInfo, Icon)
    return 
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendModule:IsConsolePlayer(platID)
    return FriendLogic.IsConsolePlayer(platID)
end

function FriendModule:GetPlatformIconPath(platID, isBlackVersion, needCheckFake)
    return FriendLogic.GetPlatformIconPath(platID, isBlackVersion, needCheckFake)
end
--- END MODIFICATION

function FriendModule:SortFriendList(friendList)
    FriendLogic.SortFriendList(friendList)
    return 
end

function FriendModule:SyncPS5BlockListToGame()
    if not IsPS5() then
        return
    end
    loginfo("FriendModule:SyncPS5BlockListToGame")
    local blackList = Server.FriendServer:GetBlackListData()
    local blockUsersFromCurrentPlatform = {}
    local checkNum = #blackList
    if checkNum == 0 then
        -- 如果游戏内黑名单中没有玩家，直接将平台黑名单同步到游戏内
        local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
            local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
            local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
            queryRes = UDFMGameUrlGeneratorIns:GenerateQueryPS5PlatformBlockListRes(HttpResponse:GetContentAsString())
            for _, blockPlayerIdOnPlatform in ipairs(queryRes.BlockOpenIdList) do
                local blockPlayerIdOnPlatformAsInt64 = ULuautils.GetUInt64StrToInt64(blockPlayerIdOnPlatform)
                local OnCSFriendBlockPlayerRes = function(res)
                    if res.result == 0 then
                        Server.FriendServer:FetchApplyPlayerList()
                        Server.FriendServer:FetchFriendGetBlackList()
                        if Server.FriendServer:CheckIsFriend(blockPlayerIdOnPlatformAsInt64) then
                            Server.FriendServer:FetchFriendSectionList()
                        end
                    end
                end
                local req = pb.CSFriendBlockPlayerReq:New()
                req.player_id = blockPlayerIdOnPlatformAsInt64
                req:Request(OnCSFriendBlockPlayerRes)
            end
        end
        Server.FriendServer:ReqQueryPS5PlatformBlockList(OnReqQueryRes)
    else
        for _, blockUserDta in pairs(blackList) do
            local req = pb.CSAccountGetPlayerProfileReq:New()
            local blockUserId = blockUserDta.player_id
            req.player_id = blockUserId
            req.client_flag = 0
            req:Request(function(res)
                checkNum = checkNum - 1
                -- 筛选出当前平台的黑名单玩家
                if res.result == 0 and res.plat_id == Server.AccountServer:GetPlatIdType() then
                    table.insert(blockUsersFromCurrentPlatform, blockUserId)
                end
                if checkNum <= 0 then
                    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
                        local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
                        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
                        queryRes = UDFMGameUrlGeneratorIns:GenerateQueryPS5PlatformBlockListRes(HttpResponse:GetContentAsString())
                        Server.FriendServer.platformBlockList = queryRes.BlockOpenIdList
                        if #blockUsersFromCurrentPlatform == 0 then
                            -- 如果游戏内黑名单中没有PS5平台玩家，直接将平台黑名单同步到游戏内
                            for _, blockPlayerIdOnPlatform in ipairs(queryRes.BlockOpenIdList) do
                                local blockPlayerIdOnPlatformAsInt64 = ULuautils.GetUInt64StrToInt64(blockPlayerIdOnPlatform)
                                local OnCSFriendBlockPlayerRes = function(res)
                                    if res.result == 0 then
                                        Server.FriendServer:FetchApplyPlayerList()
                                        Server.FriendServer:FetchFriendGetBlackList()
                                        if Server.FriendServer:CheckIsFriend(blockPlayerIdOnPlatformAsInt64) then
                                            Server.FriendServer:FetchFriendSectionList()
                                        end
                                    end
                                end
                                local req = pb.CSFriendBlockPlayerReq:New()
                                req.player_id = blockPlayerIdOnPlatformAsInt64
                                req:Request(OnCSFriendBlockPlayerRes)
                            end
                            return
                        end
                        for _, blockPlayerIdInGame in pairs(blockUsersFromCurrentPlatform) do
                            local isInPlatformBlockList = false
                            for _, blockPlayerIdOnPlatform in ipairs(queryRes.BlockOpenIdList) do
                                if blockPlayerIdOnPlatform == ULuautils.GetUInt64String(blockPlayerIdInGame) then
                                    isInPlatformBlockList = true
                                end
                                local blockPlayerIdOnPlatformAsInt64 = ULuautils.GetUInt64StrToInt64(blockPlayerIdOnPlatform)
                                -- 如果玩家在平台黑名单但是不在游戏黑名单，则游戏内将其拉黑
                                if Server.FriendServer:CheckIsBlack(blockPlayerIdOnPlatformAsInt64) == false then
                                    local OnCSFriendBlockPlayerRes = function(res)
                                        if res.result == 0 then
                                            Server.FriendServer:FetchApplyPlayerList()
                                            Server.FriendServer:FetchFriendGetBlackList()
                                            if Server.FriendServer:CheckIsFriend(blockPlayerIdOnPlatformAsInt64) then
                                                Server.FriendServer:FetchFriendSectionList()
                                            end
                                        end
                                    end
                                    local req = pb.CSFriendBlockPlayerReq:New()
                                    req.player_id = blockPlayerIdOnPlatformAsInt64
                                    req:Request(OnCSFriendBlockPlayerRes)
                                end
                            end
                            if isInPlatformBlockList == false then
                                -- 如果不在平台黑名单内，则解除拉黑
                                local OnCSFriendRemoveBlackListRes = function(res)
                                    if res.result == 0 then
                                        Server.FriendServer:FetchFriendGetBlackList()
                                    end
                                end
                                local req = pb.CSFriendRemoveBlackListReq:New()
                                req.remove_player = blockPlayerIdInGame
                                req.remove_all = false
                                req:Request(OnCSFriendRemoveBlackListRes)
                            end
                        end
                    end
                    Server.FriendServer:ReqQueryPS5PlatformBlockList(OnReqQueryRes)
                end
            end, {bEnableHighFrequency = true})
        end
    end
end

-- azhengzheng:检查是否可以发送预约
function FriendModule:CheckCanReservation(friendInfo)
    return FriendLogic.CheckCanReservation(friendInfo)
end

--判断自己是否为隐身状态
function FriendModule:GetSelfLoginInvisible()
    return Module.SystemSetting:GetInvisibleState()
end

-- 判断某好友是否离线或隐身
function FriendModule:IsFriendOffLineOrInvisible(state, playerId)
    return FriendLogic.IsFriendOffLineOrInvisible(state, playerId)
end

return FriendModule
