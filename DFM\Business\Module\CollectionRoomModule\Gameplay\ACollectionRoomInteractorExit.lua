----- <PERSON>OG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"

---@class ACollectionRoomInteractorExit : ACollectionRoomInteractorBase
local ACollectionRoomInteractorExit = class("ACollectionRoomInteractorExit", ACollectionRoomInteractorBase)

function ACollectionRoomInteractorExit:Ctor()
    self:log("Ctor")
    self.interactorType = Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomExit
end

function ACollectionRoomInteractorExit:StartInteract()
    CollectionRoomLogic.LeaveCollectionRoom(ECollectionRoomLeaveFrom.Interactor)
end

return ACollectionRoomInteractorExit