----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------

local FAnchors = import "Anchors"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local EDFMGamePlaySubMode = import "EDFMGamePlaySubMode"
local FGPPlayerUIN = import "GPPlayerUIN"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"

local ChatLogic = {}

---@param wrapnum 换行字符数
---@param offset 首行偏移
ChatLogic.CombineTextAndEmoji = function(chatMsg,wrapnum,offset,isSocialButton)
    wrapnum = setdefault(wrapnum,40)
    offset = setdefault(offset,0)
    isSocialButton = setdefault(isSocialButton, false)
    local text = ""
    local startPos = 1
    local endPos = string.len(chatMsg.text)
    if chatMsg.emoji_list then
        local length = offset
        local rownum = 1
        for _,emoji in ipairs(chatMsg.emoji_list) do
            local emojiIndex = emoji.emoji_id
            endPos = emoji.position
            if ChatLogic.CheckEmojiValid(emojiIndex, endPos) then
                local textsplit = string.sub(chatMsg.text, startPos, endPos)
                text = text .. textsplit
                length = length + StringUtil.GetRealWidth(textsplit)
                length = length + 2
                if length > wrapnum * rownum then
                    text = text .. '\n'
                    rownum = rownum + 1
                    length = 0
                end

                -- local originEmojiStr = emoji.emoji_content
                -- local templateStyle = IsHD() and Module.Chat.Config.EEmojiStyleConfig.PC or (
                --     Module.Chat.Config.EEmojiStyleConfig.Mobile)
                -- templateStyle = '%1 ' .. templateStyle
                -- originEmojiStr = string.gsub(originEmojiStr, '<dfmrichtext id="%%%d+"', templateStyle)
                local templateStyle = IsHD() and Module.Chat.Config.EEmojiStyleConfig.NewPC or (
                    Module.Chat.Config.EEmojiStyleConfig.NewMobile)
                local originEmojiStr = StringUtil.Key2StrFormat(templateStyle, {["emojiIndex"] = emojiIndex})

                if isSocialButton then
                    -- originEmojiStr = "</>" .. originEmojiStr .. "<Chat_Default>"
                end

                text = text .. originEmojiStr
                startPos = endPos + 1
            end
        end
    end
    if startPos <= string.len(chatMsg.text) then
        text = text .. string.sub(chatMsg.text, startPos)
    end

    -- return text
    return ChatLogic.CombineTextAndWeaponCode(chatMsg, text, isSocialButton)
end

-- 新版表情包会包含两数据
---@param emojiIndex 表情包在datatable下标
---@param pos 完整信息文本里的表情包起始下标
ChatLogic.CheckEmojiValid = function(emojiIndex, pos)
    if (not emojiIndex) or (not pos) then
        return false
    end

    -- 目前表情下标[1,99]
    local min,max = Module.Chat.Field:GetEmojiIdRange()
    if emojiIndex < min or emojiIndex > max then
        return false
    end

    if pos < 0 then
        return false
    end

    return true
end

ChatLogic.ResolvePresetChat = function(preset_msg)
    local presetID = preset_msg.preset_id
    local tableID = preset_msg.params[1]
    local content = ""
    if Module.Chat.Config.presetChatTable[tableID] and Module.Chat.Config.presetChatTable[tableID][presetID] then
        content = Module.Chat.Config.presetChatTable[tableID][presetID].PresetChat
    else
        logerror(string.format("ChatLogic Can not Find %s in presettabel %s",presetID,tableID))
    end
    if #preset_msg.params > 1 and string.find(content,"%%s") then
        content = string.format(content,preset_msg.params[2])
    else
        logerror(string.format("ChatLogic Can not Find %%s for extraparam in table:%s,presetid%s",tableID,presetID))
    end
    return content
end

ChatLogic.InvalidSpaceMsgCheck = function(chatMsg)
    return string.match(chatMsg, "^%s*$") ~= nil
end

ChatLogic.InvalidMsgCheck = function(chatMsg)
    local checkResult = false
    if ChatLogic.InvalidSpaceMsgCheck(chatMsg) then
        checkResult = true
    end
    return checkResult
end

-- 组合可能存在的改枪码和原始消息
ChatLogic.CombineTextAndWeaponCode = function(chatMsg, originText, isSocialButton)
    local gunSharingCode = chatMsg.gun_sharingcode_text
    if gunSharingCode and gunSharingCode ~= "" then

        local targetStr = ChatLogic.GetWeaponCodeMapKey(chatMsg, gunSharingCode, originText, isSocialButton)
        if targetStr == "" then
            return originText
        end

        chatMsg.custom_chat_weaponCode = gunSharingCode
        -- return targetStr .. originText
        return targetStr
    end

    return originText
end

-- 获取映射键值 以匹配ChatConfig中具体项
ChatLogic.GetWeaponCodeMapKey = function(chatMsg, gunSharingCode, originTextStr, isSocialButton)
    local itemId = chatMsg.prop.id
    local shouldSelectSelf = (chatMsg.sender_id == Server.AccountServer:GetPlayerId())
    if IsHD() then
        shouldSelectSelf = false
    end

    if isSocialButton then
        shouldSelectSelf = false
    end

    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)

    local mapStr = ""
    local EWeaponSmallIconMapConfig = Module.Chat.Config.EWeaponSmallIconMapConfig
    -- 这里直接硬编码
    if itemSubType == ItemConfig.EWeaponItemType.Rifle then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.RifleSelf or EWeaponSmallIconMapConfig.Rifle
    elseif itemSubType == ItemConfig.EWeaponItemType.Submachine then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.SubmachineSelf or EWeaponSmallIconMapConfig.Submachine
    elseif itemSubType == ItemConfig.EWeaponItemType.Shotgun then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.ShotgunSelf or EWeaponSmallIconMapConfig.Shotgun
    elseif itemSubType == ItemConfig.EWeaponItemType.PrecisionShootingRifle then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.PrecisionShootingRifleSelf or EWeaponSmallIconMapConfig.PrecisionShootingRifle
    elseif itemSubType == ItemConfig.EWeaponItemType.LightMachine then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.LightMachineSelf or EWeaponSmallIconMapConfig.LightMachine
    elseif itemSubType == ItemConfig.EWeaponItemType.Sniper then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.SniperSelf or EWeaponSmallIconMapConfig.Sniper
    elseif itemSubType == ItemConfig.EWeaponItemType.Pistol then
        mapStr = shouldSelectSelf and EWeaponSmallIconMapConfig.PistolSelf or EWeaponSmallIconMapConfig.Pistol
    end

    if mapStr == "" then
        return ""
    end

    local targetDisplayCode = ChatLogic.SplitLast(gunSharingCode)

    local templateStr = shouldSelectSelf and EWeaponSmallIconMapConfig.ConstTextSelf or EWeaponSmallIconMapConfig.ConstText
    local extraSymbol = string.len(originTextStr) <= 0 and "" or "-"

    mapStr = StringUtil.Key2StrFormat(
        templateStr,
        {
            ["weaponId"] = mapStr,
            ["weaponName"] = targetDisplayCode,
            ["extraSymbol"] = extraSymbol,
            ["inputText"] = originTextStr,
        }
    )

    return mapStr
end


ChatLogic.SplitLast = function(str, sep)
    sep = sep or "-"  -- 默认分隔符为-
    local last_sep_pos = 0
    local start = 1

    -- 循环查找所有分隔符位置
    while true do
        -- 使用plain模式查找避免正则特殊字符问题
        local s, e = string.find(str, sep, start, true)
        if not s then break end

        last_sep_pos = s  -- 记录最后一个分隔符起始位置
        start = e + 1     -- 更新查找起始位置
    end

    -- 根据最后分隔符位置处理结果
    if last_sep_pos > 0 then
        return string.sub(str, 1, last_sep_pos - 1)
    else
        return ""  -- 没有分隔符时返回空字符串
    end
end


-- 初始化单条玩家数据的等级信息
ChatLogic.InitPlayerLevelInfo = function(thePlayerInfo)
    -- 分别用新变量存储mp和sol下的等级信息
    if thePlayerInfo.custom_solLevel == nil then
        local isFriend = Module.Friend:CheckIsFriend(thePlayerInfo.player_id)
        -- TODO 历史遗留原因 还有和非好友 这个sol等级后端传过来字段不一致
        local tempLevel = isFriend and thePlayerInfo.season_level or thePlayerInfo.season_lvl
        thePlayerInfo.custom_solLevel = tempLevel or 0
    end
    if thePlayerInfo.custom_mpLevel == nil then
        thePlayerInfo.custom_mpLevel = thePlayerInfo.level or 0
    end
    -- 动态初始化当前展示Level
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.SafeHouse then
        thePlayerInfo.level = thePlayerInfo.custom_solLevel
    else
        thePlayerInfo.level = thePlayerInfo.custom_mpLevel
    end
end

ChatLogic.SetRankDataForCommonTips = function(playerInfo)
    -- 一般被你拉黑的人给你发消息时触发这里
    if not playerInfo then
        return
    end

    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.SafeHouse then
        local hasRankAttended = playerInfo.sol_rank_attended or playerInfo.sol_attended
        -- 未参加过sol的排位赛
        if (not hasRankAttended) or (not playerInfo.sol_rank_score) then
            playerInfo.rank_attended = playerInfo.sol_rank_attended
            return
        end

        playerInfo.rank_attended = hasRankAttended
        playerInfo.rank_score = playerInfo.sol_rank_score
    else
        local hasRankAttended = playerInfo.mp_rank_attended or playerInfo.mp_attended
        -- 未参加过mp的积分
        if (not hasRankAttended) or (not playerInfo.mp_rank_score) then
            playerInfo.rank_attended = playerInfo.mp_rank_attended
            return
        end

        playerInfo.rank_attended = hasRankAttended
        playerInfo.rank_score = playerInfo.mp_rank_score
    end
end

local defaultDeltaSpacing = FVector2D(5, 5)
local topLeftAnchor = FAnchors()
topLeftAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
topLeftAnchor.Maximum = LuaGlobalConst.TOP_LEFT_VECTOR

--------------------------------------------------------------------------
--- UI吸附方法
--- * 通常情况下alignAttachAlignment的X和Y分别等于(1- adjustAttachAlignment.X) 和 (1- adjustAttachAlignment.Y)
--- * containerWidget, adjustWidget, alignWidget 均为CanvasSlot上的ui
--------------------------------------------------------------------------
---@param containerWidget 作为共同父容器的widget
---@param adjustWidget 吸附到指定位置的操作widget
---@param alignWidget 吸附所参考的widget
---@param deltaSpacing 吸附保持的间距 同时是和边界保持的间距 optional 默认：defaultDeltaSpacing = FVector2D(5, 5)
---@param adjustAttachAlignment adjust对齐Alignment optional 默认：根据屏幕中线
---@param alignAttachAlignment align对齐Alignment optional 默认：根据屏幕中线
---@param ignoreSafePadding 是否忽略安全区
ChatLogic.AttachWidgetTo = function(containerWidget, adjustWidget, alignWidget, deltaSpacing, adjustAttachAlignment, alignAttachAlignment, ignoreSafePadding)
    assertlog(alignWidget, 'UIUtil.AttachWidgetTo(...) alignWidget is nil, please check')
    assertlog(adjustWidget, 'UIUtil.AttachWidgetTo(...) adjustWidget is nil, please check')
    deltaSpacing = setdefault(deltaSpacing, defaultDeltaSpacing)
    ignoreSafePadding = setdefault(ignoreSafePadding, false)
    --- 容器准备
	local containerGeometry = UWidgetLayoutLibrary.GetViewportWidgetGeometry(containerWidget) -- 当前父容器在Viewport上的Geometry
	local containerAbsPosLT = containerGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    local safePadding = Facade.UIManager:GetFrameSafeZonePadding()
	local localSafePadding = containerGeometry:AbsoluteToLocal(containerAbsPosLT + FVector2D(safePadding.Left, safePadding.Bottom))
    local containerLocalSize = containerGeometry:GetLocalSize()

    --- 吸附准备
    local adjustWidgetSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(adjustWidget)
    --- * 必须
    adjustWidgetSlot:SetAnchors(topLeftAnchor)

    --- 被吸附准备
    local adjustWidgetLocalPos, adjustLocalSize, adjustWidgetAlignment = UIUtil._GenWidgetCalcInfo(containerWidget, adjustWidget)
    local alignWidgetLocalPos, alignLocalSize, alignWidgetAlignment = UIUtil._GenWidgetCalcInfo(containerWidget, alignWidget)

    --- 根据屏幕中线，分别对应两种默认对齐方式
    if alignWidgetLocalPos.X < containerLocalSize.X * 0.5 then
        adjustAttachAlignment = setdefault(adjustAttachAlignment, LuaGlobalConst.TOP_LEFT_VECTOR)
        alignAttachAlignment = setdefault(alignAttachAlignment, LuaGlobalConst.TOP_RIGHT_VECTOR)
    else
        adjustAttachAlignment = setdefault(adjustAttachAlignment, LuaGlobalConst.TOP_RIGHT_VECTOR)
        alignAttachAlignment = setdefault(alignAttachAlignment, LuaGlobalConst.TOP_LEFT_VECTOR)
    end

    --- 要比较的值应该经过标准化处理为左上角
    if IsInEditor() then
        local boundingBoxA = {
            X = adjustWidgetLocalPos.X + (- adjustWidgetAlignment.X * adjustLocalSize.X),
            Y = adjustWidgetLocalPos.Y + (- adjustWidgetAlignment.Y * adjustLocalSize.Y),
            W = (adjustWidgetLocalPos.X + ((1 - adjustWidgetAlignment.X) * adjustLocalSize.X)),
            H = (adjustWidgetLocalPos.Y + ((1 - adjustWidgetAlignment.Y) * adjustLocalSize.Y))
        }

        local boundingBoxB = {
            X = alignWidgetLocalPos.X + (- alignWidgetAlignment.X * alignLocalSize.X),
            Y = alignWidgetLocalPos.Y + (- alignWidgetAlignment.Y * alignLocalSize.Y),
            W = (alignWidgetLocalPos.X + ((1 - alignWidgetAlignment.X) * alignLocalSize.X)),
            H = (alignWidgetLocalPos.Y + ((1 - alignWidgetAlignment.Y) * alignLocalSize.Y))
        }

        loginfo('UIUtil.AttachWidgetTo')
        loginfo('----------------------boundingBoxA：', boundingBoxA.X, boundingBoxA.Y, boundingBoxA.W, boundingBoxA.H)
        loginfo('----------------------boundingBoxB：', boundingBoxB.X, boundingBoxB.Y, boundingBoxB.W, boundingBoxB.H)
    end

    local targetLocalPos = FVector2D(0, 0)
    --- 消除目标widget 原本alignment偏移
    targetLocalPos.X = alignWidgetLocalPos.X - alignWidgetAlignment.X * alignLocalSize.X
    targetLocalPos.Y = alignWidgetLocalPos.Y - alignWidgetAlignment.Y * alignLocalSize.Y

    --- 消除被调整widget 原本alignment偏移
    targetLocalPos.X = targetLocalPos.X + adjustWidgetAlignment.X * adjustLocalSize.X
    targetLocalPos.Y = targetLocalPos.Y + adjustWidgetAlignment.Y * adjustLocalSize.Y

    --- 叠加目标widget 希望attach的alignment偏移 和 deltaSpacing
    targetLocalPos.X = targetLocalPos.X + alignAttachAlignment.X * (alignLocalSize.X + deltaSpacing.X)
    targetLocalPos.Y = targetLocalPos.Y + alignAttachAlignment.Y * (alignLocalSize.Y + deltaSpacing.Y)

    --- 叠加被调整widget 希望attach的alignment偏移 和 deltaSpacing
    targetLocalPos.X = targetLocalPos.X - adjustAttachAlignment.X * (adjustLocalSize.X + deltaSpacing.X)
    targetLocalPos.Y = targetLocalPos.Y - adjustAttachAlignment.Y * (adjustLocalSize.Y + deltaSpacing.Y)

    if not ignoreSafePadding then
        targetLocalPos.X = targetLocalPos.X - localSafePadding.X
        targetLocalPos.Y = targetLocalPos.Y + localSafePadding.Y
    end

    --- 纠正
    -- local widgetCorrectPos, bOverlaped = UIUtil.GetCorrectPosition(adjustWidget, targetLocalPos, 0)
    adjustWidgetSlot:SetPosition(targetLocalPos)
end

--------------------------------------------------------------------------
--- 源自于自定义布局面板中
--- * 自定义布局面板上的每一个控件节点A点触都会打开一个新的二级界面
--- * 拖拽这个控件节点A 其对应的二级界面也会移动并会判断是否屏幕边界 以挪动位置避免其出现在屏幕边界外
--- * 函数返回值会返回新的挂接九宫格 以及 挂接偏移
--------------------------------------------------------------------------
---@param dragBtn 就是上述描述中的控件节点A
---@param secondaryWiget 需要挂接的新的二级界面
---@param bpWidget 控件节点A所处的蓝图最上一级的canvans_panel

ChatLogic.CalTheLegalAttachConfig = function(dragBtn, secondaryWiget, bpWidget, offset)
    local dragBtnGeometry = dragBtn:GetCachedGeometry()
    local dragBtnAbsolutePos = dragBtnGeometry:GetAbsolutePosition()
    -- 将dragBtn的绝对坐标信息转成其在bpWidget里的局部坐标
    local rootGeometry = bpWidget:GetCachedGeometry()
    local rootSize = rootGeometry:GetLocalSize()
    local childInRootPos = USlateBlueprintLibrary.AbsoluteToLocal(rootGeometry, dragBtnAbsolutePos)

    local childGeometry = secondaryWiget:GetCachedGeometry()
    local childSize = childGeometry:GetLocalSize() * secondaryWiget.RenderTransform.Scale

    local center = FVector2D(0.0, 0.5)
    local anchor = FVector2D(0.0, 0.0)
    local offsetX = offset.X
    if childSize.X + offsetX < childInRootPos.X then
        center.X = 0.0
        anchor.X = 1.0
    else
        center.X = 1.0
        anchor.X = 0.0
    end

    local offsetY = offset.Y
    if childSize.Y + offsetY < rootSize.Y - childInRootPos.Y then
        center.Y = 0.5
        anchor.Y = 0.0
    else
        center.Y = 0.5
        anchor.Y = 1.0
    end

    return anchor, center
end

ChatLogic.RemoveUnofficialEmojiStr = function(preSendStr)
    local emoji_pattern = "[\xF0-\xF7][\x90-\xBF][\x80-\xBF][\x80-\xBF]"
    preSendStr = string.gsub(preSendStr, emoji_pattern, "")
    return preSendStr
end


--获取自己的玩家信息
ChatLogic.BuildLocalPlayerInfo = function()
    local playerSimpleInfo = {
        player_id = Server.AccountServer:GetPlayerId(),
        nick_name = Server.RoleInfoServer.nickName,
        pic_url = Server.RoleInfoServer.picUrl,
        level = Server.RoleInfoServer.accountLevel,
        season_lvl = Server.RoleInfoServer.seasonLevel,
        gender = Server.RoleInfoServer.gender,
        safehouse_degree = 0,
        sol_rank_attended = Server.RankingServer:GetHasAttended(),
        sol_rank_score = Server.RankingServer:GetRankScore(),
        mp_rank_attended = Server.TournamentServer:GetHasAttended(),
        mp_rank_score = Server.TournamentServer:GetRankScore(),
    }
    return playerSimpleInfo
end

--提取table中后targetNum数量的元素
ChatLogic.ExtractLastNElements = function(originTable, targetNum)
    local originLen = #originTable
    -- 如果 table 的长度小于等于 目标数量targetNum，则保留所有元素
    if originLen <= targetNum then
        return originTable
    end

    local newTable = {}
    -- 否则，保留后 targetNum 个元素
    for i = originLen - targetNum + 1, originLen do
        table.insert(newTable, originTable[i])
    end

    return newTable
end

--pc环境下提取消息列表中后targetNun数量的元素
ChatLogic.ExtractLastNMsgList_PC = function(msgList, targetNum)
    if not IsHD() then
        return msgList
    end

    return ChatLogic.ExtractLastNElements(msgList, targetNum)
end

-- 模式切换时提取仅包含本模式招募的聊天数据
ChatLogic.ExtractRightRecruitData = function(originDataList)
    if not IsHD() then
        return nil
    end

    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local isSolMode = curGameFlow == EGameFlowStageType.SafeHouse and true or false
    local newTargetList = {}
    for _, value in ipairs(originDataList) do
        if value.custom_chat_channel == ChatChannelType.RecruitmentChat then
            if (not isSolMode) and value.filter.bIsMP then
                table.insert(newTargetList, value)
            elseif isSolMode and (not value.filter.bIsMP) then
                table.insert(newTargetList, value)
            end
        else
            table.insert(newTargetList, value)
        end
    end

    return newTargetList
end

-- 判断当前Stage是否为游戏内
ChatLogic.IsInGameStage = function()
    local gameState = UGameplayStatics.GetGameState(GetWorld())

    -- 此时默认不在游戏内
    if not gameState then
        return false
    end

    local isInGameBool = (gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough) or (
            gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_SOL) or (
                gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Raid) or (
                gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest)

    return isInGameBool
end

-- 看是否应该附加指挥官前缀富文本
ChatLogic.AddCommanderPrefix = function(playerId, strText, isCampChannel)
    if ChatLogic.GetCommanderCharacterId() == playerId then
        if isCampChannel then
            strText = string.format("<dfmrichtext type=\"img\" id=\"Ccommander\" orgsize=\"true\"/>%s", strText)
        else
            strText = string.format("<dfmrichtext type=\"img\" id=\"Ccommander_Team\" orgsize=\"true\"/>%s", strText)
        end
    end

    return strText
end

-- 试图去拿本地玩家所属阵营的指挥官(MP-指挥官模式下)
ChatLogic.GetCommanderCharacterId = function()

    local isCommandMode = Module.GVoice:IsCommanderGameMode()
    if not isCommandMode then
        return nil
    end

    local playerState = Facade.GameFlowManager:GetPlayerState()
    local gameState = UGameplayStatics.GetGameState(GetWorld())

    -- 此时默认不在游戏内
    if (not gameState) or (playerState == nil) then
        return nil
    end

    local targetPlayerId = nil
    local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
    local campValue = playerState:GetCamp()

    -- 一种拿指挥官Id的尝试
    if battleFieldCommanderDataComponent and campValue then
        local commanderCharacter = battleFieldCommanderDataComponent:GetCommanderCharacter(campValue)

        if isvalid(commanderCharacter) and commanderCharacter and commanderCharacter.GetUin then
            targetPlayerId = commanderCharacter:GetUin()
        end
    end

    -- if targetPlayerId == nil then
    --     targetPlayerId = ChatLogic.SecondTryToGetCommanderPlayerId()
    -- end

    return targetPlayerId
end

--获取身份信息
ChatLogic.SecondTryToGetCommanderPlayerId = function()
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if playerState == nil then
        return nil
    end
    local memberInfoList = playerState.CampMemberInfoList
    if memberInfoList then
        for _, memberInfo in pairs(memberInfoList) do
            if isvalid(memberInfo) and isvalid(memberInfo.PS) then
                if memberInfo.PS.IsCommander and memberInfo.PS:IsCommander() then
                    return memberInfo.PlayerUin
                end
            end
        end
    end
    return nil
end

--填充好友备注名
ChatLogic.FullRemarkNameText = function(originNameTxt, remarkName)
    local modeStr = "{remarkName}[{nickName}]"
    local targetTxt = StringUtil.Key2StrFormat(
        modeStr,
        {
            ["remarkName"] = remarkName,
            ["nickName"] = originNameTxt,
        }
    )

    return targetTxt
end

--局内组队邀请拼接
ChatLogic.InGameInviteContentMake = function(locText, modeName, elementState)
    local elementStateTxt = Module.Chat.Config.Loc.InGameInviteStatePending
    if elementState == Module.Team.Config.InGameInviteStateEnum.Agreed then
        elementStateTxt = Module.Chat.Config.Loc.InGameInviteStateAgreed
    elseif elementState == Module.Team.Config.InGameInviteStateEnum.Rejected then
        elementStateTxt = Module.Chat.Config.Loc.InGameInviteStateRejected
    elseif elementState == Module.Team.Config.InGameInviteStateEnum.Expired then
        elementStateTxt = Module.Chat.Config.Loc.InGameInviteStateExpired
    end

    local contentTxt = StringUtil.Key2StrFormat(
        locText,
        {
            ["gameMode"] = modeName,
            ["state"] = elementStateTxt,
        }
    )

    return contentTxt
end

--单条邀请聊天消息拼接
ChatLogic.InGameInviteChatMsgMake = function(dataValue, isAbridget, shouldQuick)
    Abridget = setdefault(Abridget, false)
    shouldQuick = setdefault(shouldQuick, false)
    local modeName = Module.Team.Field.GameTeamInviteViewModel:GetTargetModeStr(dataValue) or ""
    local targetLocText = Module.Chat.Config.Loc.InGamePreBookInviteChat
    if dataValue.inviteType == "TeamApply" then
        targetLocText = Module.Chat.Config.Loc.InGamePreBookApplyChat
    end
    local displayContent = ""
    if shouldQuick then
        local elementStateTxt = StringUtil.Key2StrFormat(
            Module.Chat.Config.Loc.InGamePreBookShotcutKeys,
            {
                ["agreeKey"] = "F4",
                ["rejectKey"] = "F5",
            }
        )

        displayContent = StringUtil.Key2StrFormat(
            targetLocText,
            {
                ["gameMode"] = modeName,
                ["state"] = elementStateTxt
            }
        )
    elseif not isAbridget then
        displayContent = ChatLogic.InGameInviteContentMake(targetLocText, modeName, dataValue.custom_invitestate)
    else
        local elementStateTxt = StringUtil.Key2StrFormat(
            Module.Chat.Config.Loc.InGamePreBookInviteAbridget,
            {
                ["timeValue"] = Module.Social.Config.DEFAULT_NTF_REMAIN_COLD_TIME,
            }
        )

        displayContent = StringUtil.Key2StrFormat(
            targetLocText,
            {
                ["gameMode"] = modeName,
                ["state"] = elementStateTxt,
            }
        )
    end

    local showTxt = string.format(
        "<customstyle color=\"Color_Chat_Friend\">%s：</>%s", ChatLogic.GetInGameInviteOrApplyNickName(dataValue), displayContent)
    showTxt = string.format("<dfmrichtext type=\"img\" width=\"40\" height=\"40\" id=\"MakeAppointment\" orgsize=\"false\"/>%s", showTxt)
    return showTxt
end

ChatLogic.GetInGameInviteOrApplyNickName = function(dataValue)
    local nickName = ""
    if not dataValue or (not dataValue.inviteType) then
        return nickName
    end

    if dataValue.inviteType == "TeamInvite" and (dataValue.Inviter) then
        nickName = dataValue.Inviter.nick_name or ""
    elseif dataValue.apply_player then
        nickName = dataValue.apply_player.nick_name or ""
    end

    return nickName
end

-- 指挥官模式下去拿玩家队伍ID，并组合在昵称之前，没有就返回原始昵称字符串
ChatLogic.GetNameWithTeamPrefixInCommander = function(playerId, originName, isCampChannel)
    -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander000000000")
    -- 非阵营聊天频道
    if not isCampChannel then
        return originName
    end

    -- 非指挥官模式
    -- local isCommandMode = Module.GVoice:IsCommanderGameMode()
    -- if not isCommandMode then
    --     return originName
    -- end

    -- 此时默认不在游戏内
    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if not gameState then
        -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander222222")
        return originName
    end

    local fUIN = FGPPlayerUIN()
    fUIN.Uin = playerId
    local playerState = UGameplayBlueprintHelper.GetPlayerStateByUin(GetWorld(), fUIN)
    if playerState == nil or (not playerState.TeamId) then
        -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander33333333")
        return originName
    end

    -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander4444444444")
    local teamText = ""
    local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()
    if battleFieldCommanderDataComponent then
        teamText = battleFieldCommanderDataComponent:BPGetTeamNameByTeamId(playerState.TeamId) or ""
        -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander55555555555")
        if teamText then
            -- logerror("ChatLogic.GetNameWithTeamPrefixInCommander666666666")
            teamText = StringUtil.Key2StrFormat(
                Module.Chat.Config.Loc.InGameTeamNumPrefix,
                {
                    ["teamText"] = teamText,
                    ["Symbol"] = "·",
                    ["nickName"] = "",
                }
            )
        end
    end

    return teamText .. originName
end

--聊天相关排位积分图标设定
ChatLogic.TestFunc = function(playerInfo, rankWidgetInst)
    local showCommander = playerInfo.show_commander_rank_points
    -- 基于该变量是否为1判断，此时展示指挥官模式段位信息
    if showCommander and  showCommander == 1 then
        return
    end



    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.SafeHouse then
        -- 未参加过sol的排位赛
        local hasRankAttended = playerInfo.sol_rank_attended or playerInfo.sol_attended
        if (not hasRankAttended) or (not playerInfo.sol_rank_score) then
            if rankWidgetInst then
                rankWidgetInst:SetRankIconNone()
            end

            return
        end

        -- 设置sol排位分数图标
        if rankWidgetInst then
            rankWidgetInst:SetRankingIconByScore(playerInfo.sol_rank_score)
        end
    else
        -- 未参加过mp的积分
        local hasRankAttended = playerInfo.mp_rank_attended or playerInfo.mp_attended
        if (not hasRankAttended) or (not playerInfo.mp_rank_score) then
            if rankWidgetInst then
                rankWidgetInst:SetRankIconNone()
            end

            return
        end

        -- 设置sol排位分数图标
        if rankWidgetInst then
            rankWidgetInst:SetTournamentIconByScore(playerInfo.mp_rank_score)
        end
    end
end

return ChatLogic
