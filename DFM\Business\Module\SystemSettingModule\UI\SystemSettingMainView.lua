----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingMainView

local SystemSettingMainView = ui("SystemSettingMainView")
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
--local Config                     = require("DFM.Business.Module.BattlefieldEntryModule.BattlefieldEntryConfig")
local UClientBaseSetting = import "ClientBaseSetting"
local UClientControlSetting = import "ClientControlSetting"
local UClientSensitivitySetting = import "ClientSensitivitySetting"
local UClientVehicleSetting = import "ClientVehicleSetting"
local UGPGameplayStatics = import "GPGameplayStatics"
local ELowFpsType = import "ELowFpsType"
local ELowFpsOP = import "ELowFpsOP"
local UPerfGearFuncLib = import "PerfGearFuncLib"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local ULuaExtension = import("LuaExtension")
local PerfGearInst = UPerfGearFuncLib.GetPerfGearInst()
local SystemConfig = Module.SystemSetting.Config
local ButtonIdConfig = require("DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig")
local UISimulatorUtil = require "DFM.YxFramework.Managers.UI.Util.UISimulatorUtil"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGPGameHudDelegates = import "GPGameHudDelegates"
local EGameHUDState = import "GameHUDSate"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local ERedeployStateFuture = import "ERedeployStateFuture"

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end

function SystemSettingMainView:Ctor()
    loginfo("SystemSettingMainView:Ctor")
    -- 主设置面板
    self._wtRootMain = self:Wnd("wtRootPanel", UIWidgetBase)
    self._tabType = 1
    -- 重置按键
    --self._wtBackGroundAni = self:Wnd("WBP_SetUp_Main1", UIWidgetBase) --暂时屏蔽动画
    self._wtResetPanel = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtBtnReset = self:Wnd("Button_108", DFCommonButtonOnly)
    self._wtBtnReset:Event("OnClicked", self._OnResetBtnClick, self)

    self._wtExitSafehouse3DBtn = self:Wnd("wtExitSafehouse3DBtn", DFCommonButtonOnly)
    self._wtExitSafehouse3DBtn:Event("OnClicked", self._OnExitSafehouse3DBtnClicked, self)

    self._wtResourceFixBtn = self:Wnd("wtResourceFix", DFCommonButtonOnly)
    self._wtResourceFixBtn:Event("OnClicked", self._OnResourceFixedClick, self)

    self._wtExitRangeBtn = self:Wnd("wtExitRangeBtn", DFCommonButtonOnly)
    self._wtExitRangeBtn:Event("OnClicked", self._OnExitRangeBtnClicked, self)
    self._bIsCollectionRoomBtn = false

    self._wtReturnToModeHallBtn = self:Wnd("wtBtn_QuitGameReturn", DFCommonButtonOnly)
    self._wtReturnToModeHallBtn:Event("OnClicked", self._OnReturnToModeHallBtnClicked, self)

    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    self._isOffset = false
    self._wtBtnBack = self:Wnd("WBP_CommonButtonBack", UIWidgetBase):Wnd("BackButton", UIButton)
    self._wtBtnBack:Event("OnClicked", self._OnBackBtnClick, self)
    self:Wnd("WBP_CommonButtonBack", UIWidgetBase):Wnd("Text_Back", UITextBlock):SetText(SystemConfig.Loc.SettingTxt)

    self._wtBugBtn_Disengage = self:Wnd("wtBugBtn_Disengage", DFCommonButtonOnly)
    self._wtBugBtn_Disengage:Event("OnClicked", self._OnDisengage, self)

    self._wtVehicleBreakBtn = self:Wnd("wtVehicleBreakBtn", DFCommonButtonOnly)
    self._wtVehicleBreakBtn:Event("OnClicked", self._OnVehicleBreak, self)

    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        self._wtBugBtn_Disengage:Visible()
    else
        self._wtBugBtn_Disengage:Collapsed()
    end

    self._wtQuitLoginBtn = self:Wnd("wtQuitLoginBtn", DFCommonButtonOnly)
    self._wtQuitLoginBtn:Event("OnClicked", self._OnQuitGameClick, self)
    local WorldName = tostring(UGPGameplayStatics.GetWorldName(GetWorld()))
    if WorldName == "Intro" then
        self._wtQuitLoginBtn:SetVisibility(ESlateVisibility.Collapsed)
    end

    self._wtBugBtn = self:Wnd("wtBugBtn", DFCommonButtonOnly)
    self._wtBugBtn:Event("OnClicked", self._ShowBug, self)

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        if curSubStage == ESubStage.SafeHouse3D then
            self._wtBugBtn:Visible()
        else
            self._wtBugBtn:Collapsed()
        end
    else
        if Module.Guide:IsInNewPlayerMatch() then
            self._wtReturnToModeHallBtn:SelfHitTestInvisible()
        else
            self._wtReturnToModeHallBtn:Collapsed()
        end
        self._wtBugBtn:Collapsed()
    end

    self._wtCloudBtn = self:Wnd("wtCloudBtn", DFCommonButtonOnly)
    self._wtCloudBtn:Event("OnClicked", self._OnOpenCloudPanelClick, self)

    self._wtKillSelf = self:Wnd("wtKillSelf", DFCommonButtonOnly)
    self._wtKillSelf:Event("OnClicked", self._OnBreakthroughKillSelfClick, self)

    local isInGame = curGameFlow == EGameFlowStageType.Game
    self._wtAccountBtn = self:Wnd("wtAccountBtn", DFCommonButtonOnly)
    self._wtAccountBtn:Event("OnClicked", self._OnAccountBtnClicked, self)
    if (not IsBuildRegionCN()) and (IsBuildRegionGlobal() or Module.AccountBind:ShouldShowAccountBind()) and not isInGame then
        self._wtAccountBtn:Visible()
    else
        self._wtAccountBtn:Collapsed()
    end


    self._clientBaseSetting = UClientBaseSetting.Get()
    self._clientControlSetting = UClientControlSetting.Get()
    self._clientSensitivitySetting = UClientSensitivitySetting.Get()
    self._clientVehicleSetting = UClientVehicleSetting.Get()

    self._lastSelectedButton = nil

    self._wtGroupTab = self:Wnd("wtGroupBox", DFCommonMainTabList)

    self._wtCustomerServicesBtn = self:Wnd("WBP_Common_TopBarV1_CustomerService_Item", UIWidgetBase)

    self._selectMap = {
        [SystemConfig.ESystemSettingPanel.SetMode] = SystemConfig.ESystemSettingPanel.SetMode,
        [SystemConfig.ESystemSettingPanel.BaseSetting] = SystemConfig.ESystemSettingPanel.BaseSetting,
        [SystemConfig.ESystemSettingPanel.ControlSetting] = SystemConfig.ESystemSettingPanel.ControlSetting,
        [SystemConfig.ESystemSettingPanel.SensititySetting] = SystemConfig.ESystemSettingPanel.SensititySetting,
        [SystemConfig.ESystemSettingPanel.SystemSetting] = SystemConfig.ESystemSettingPanel.SystemSetting,
        [SystemConfig.ESystemSettingPanel.ComminicateSetting] = SystemConfig.ESystemSettingPanel.ComminicateSetting,
        [SystemConfig.ESystemSettingPanel.DisplaySetting] = SystemConfig.ESystemSettingPanel.DisplaySetting,
        [SystemConfig.ESystemSettingPanel.VeHicleSetting] = SystemConfig.ESystemSettingPanel.VeHicleSetting,
        [SystemConfig.ESystemSettingPanel.PufferDownloadSetting] = SystemConfig.ESystemSettingPanel.PufferDownloadSetting,
        [SystemConfig.ESystemSettingPanel.LanguageSetting] = SystemConfig.ESystemSettingPanel.LanguageSetting,
        [SystemConfig.ESystemSettingPanel.PrivacySetting] = SystemConfig.ESystemSettingPanel.PrivacySetting,
        [SystemConfig.ESystemSettingPanel.OtherSetting] = SystemConfig.ESystemSettingPanel.OtherSetting,
    }

    if Module.Guide:IsInNewPlayerMatch() then
        self._uiNavIDList = {
            UIName2ID.SystemSettingBasePanel,
            UIName2ID.SystemSettingSensitityPanel,
            UIName2ID.SystemSettingSystemSettingPanel,
            UIName2ID.SystemSettingComminicatePanel,
            UIName2ID.SystemSettingDisplayPanel,
            UIName2ID.SystemSettingVehiclePanel,
        }
        self._tabTable = {
            {keyText = SystemConfig.Loc.BaseSettingTxt},
            {keyText = SystemConfig.Loc.SensititySettingTxt},
            {keyText = SystemConfig.Loc.SystemSettingTxt},
            {keyText = SystemConfig.Loc.ComminicateSettingTxt},
            {keyText = SystemConfig.Loc.DisplaySettingTxt},
            {keyText = SystemConfig.Loc.VeHicleSettingTxt},
        }
    else
        self._uiNavIDList = {
            UIName2ID.SystemSettingBasePanel,
            UIName2ID.SystemSettingControlPanel,
            UIName2ID.SystemSettingSensitityPanel,
            UIName2ID.SystemSettingSystemSettingPanel,
            UIName2ID.SystemSettingComminicatePanel,
            UIName2ID.SystemSettingDisplayPanel,
            UIName2ID.SystemSettingVehiclePanel,
        }
        self._tabTable = {
            {keyText = SystemConfig.Loc.BaseSettingTxt},
            {keyText = SystemConfig.Loc.ControlSettingTxt},
            {keyText = SystemConfig.Loc.SensititySettingTxt},
            {keyText = SystemConfig.Loc.SystemSettingTxt},
            {keyText = SystemConfig.Loc.ComminicateSettingTxt},
            {keyText = SystemConfig.Loc.DisplaySettingTxt},
            {keyText = SystemConfig.Loc.VeHicleSettingTxt},
        }
    end

    self:_InitDynamicBtns() -- 创建动态按钮，可能在部分环境下一些按钮存在一些按钮不存在之类的
    self._wtGroupTab:SetCallback(self._FetchSettingSystemByTab, self)

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    end

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        self._wtQuitLoginBtn:SetMainTitle(Module.SystemSetting.Config.Loc.QuitLogin)
    elseif Module.Guide:IsInNewPlayerMatch() then
        self._wtQuitLoginBtn:SetMainTitle(Module.SystemSetting.Config.Loc.ReChallenge)
    else
        self._wtQuitLoginBtn:SetMainTitle(Module.SystemSetting.Config.Loc.BtnQuitGame)
    end

    logwarning("SystemSettingMainView Customer Services")
    local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
    if CustomerServicesEntranceType and CustomerServicesEntranceType.SettingPage then
        if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.SettingPage) then
            self._wtCustomerServicesBtn:Visible()
        else
            self._wtCustomerServicesBtn:Collapsed()
        end
        self._wtCustomerServicesBtn:OnInitExtraData(CustomerServicesEntranceType.SettingPage)
    end

    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtResetSystemSetting, self._UpdateSysetemSettingPanel, self)
end

function SystemSettingMainView:_UpdateSysetemSettingPanel()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRootMain)
    Facade.UIManager:SwitchSubUIByIndex(self, self._tabType, self._wtRootMain)
end

function SystemSettingMainView:_InitDynamicBtns()
    loginfo("SystemSettingMainView:_InitDynamicBtns()")

    --判断是否显示模式切换页签
    local currentGameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if currentGameFlow ~= EGameFlowStageType.ModeHall and curSubStage ~= ESubStage.Range
            and not ItemOperaTool.bIsInCollectionRoom and Facade.GameFlowManager:CheckIsInFrontEnd() then
        --self._selectMap[SystemConfig.ESystemSettingPanel.SetMode] = SystemConfig.ESystemSettingPanel.SetMode
        table.insert(self._tabTable,1,{keyText = SystemConfig.Loc.ModelSettingTxt})
        table.insert(self._uiNavIDList,1,UIName2ID.SystemSettingSetMode)
    else
        self:ResetIndex(SystemConfig.ESystemSettingPanel.SetMode,-1)
    end

    -- 添加下载中心
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        --self._selectMap[SystemConfig.ESystemSettingPanel.PufferDownloadSetting] = SystemConfig.ESystemSettingPanel.PufferDownloadSetting
        table.insert(self._uiNavIDList, UIName2ID.SystemSettingLitePackagePanel)
        table.insert(self._tabTable, {keyText = SystemConfig.Loc.LitePackageSettingTxt})
    else
        self:ResetIndex(SystemConfig.ESystemSettingPanel.PufferDownloadSetting,-1)
    end

    --self._selectMap[SystemConfig.ESystemSettingPanel.LanguageSetting] = SystemConfig.ESystemSettingPanel.LanguageSetting
    -- if not IsBuildRegionCN() then
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            table.insert(self._uiNavIDList, UIName2ID.SystemSettingLanguagePanel)
            table.insert(self._tabTable, {keyText = SystemConfig.Loc.LanguageSettingTxt})
        else
            self:ResetIndex(SystemConfig.ESystemSettingPanel.LanguageSetting,-1)
        end
    -- end

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        table.insert(self._tabTable,{keyText = SystemConfig.Loc.PrivacySettingTxt})
        table.insert(self._uiNavIDList,UIName2ID.SystemSettingPrivacy)
    else
        self:ResetIndex(SystemConfig.ESystemSettingPanel.PrivacySetting,-1)
    end


    -- 添加开发者选项配置
    if not VersionUtil.IsShipping() then
        --self._selectMap[SystemConfig.ESystemSettingPanel.OtherSetting] = SystemConfig.ESystemSettingPanel.OtherSetting  
        table.insert(self._tabTable, {keyText = SystemConfig.Loc.OtherSettingTxt})
        table.insert(self._uiNavIDList, UIName2ID.SystemSettingOtherPanel)
    else
        self:ResetIndex(SystemConfig.ESystemSettingPanel.OtherSetting,-1)
    end

    Facade.UIManager:RegSwitchSubUI(self, self._uiNavIDList)
end

function SystemSettingMainView:_OnDisengage()
    --local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    local character = InGameController:Get():GetGPCharacter()
    character:ClientResetWalkingPosition(false)
    Facade.UIManager:CloseUI(self)
end

function SystemSettingMainView:_OnVehicleBreak()
    local character = InGameController:Get():GetGPCharacter()
    if isvalid(character) then
        character:TryTeleportVehicleToLegalTransform()
        Facade.UIManager:CloseUI(self)
    end
end

function SystemSettingMainView:_OnQuitGameClick()

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        Module.CommonTips:ShowConfirmWindow(
            Module.SystemSetting.Config.Loc.ConfirmQuitLogin,
            function()
                Module.Login:BackToLogin()
            end
        )
    elseif Module.Guide:IsInNewPlayerMatch() then --新手关
        LogAnalysisTool.SignButtonClicked(20150001)
        Module.CommonTips:ShowConfirmWindow(
            Module.SystemSetting.Config.Loc.ConfirmRechallenge,
            function()
                LogAnalysisTool.SignButtonClicked(20150002)
                Module.IrisSafeHouse:SendModeHallLogOnQuit()
                self:BP_QuitGame()
            end
        )
    else
        local quitStr = self:SetQuitWindowInGame()
        Module.CommonTips:ShowConfirmWindow(
            quitStr,
            function()
                Module.IrisSafeHouse:SendModeHallLogOnQuit()
                self:BP_QuitGame()
            end
        )
    end
end

function SystemSettingMainView:_OpenXinyongH5()
    local H5Str = "https://gamecredit.qq.com/static/games/index.htm?rolename=%s"
    local nickName = string.urlencode(Server.RoleInfoServer.nickName)
    local url = string.format(H5Str, nickName)
    Module.GCloudSDK:OpenUrl(url, 3)
end


function SystemSettingMainView:_ShowBug()
    --Module.GCloudSDK:SimpleOpenUrl("https://m.gamer.qq.com/v2/task/detail/842/10068")
    --to bardzhang
    --挪到默认起点
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
        return
    end

    Module.IrisSafeHouse:TeleportTo3DSafeHouseDefaultLoc()
end

function SystemSettingMainView:_ShowCloudBtn()
    self._wtCloudBtn:Collapsed()
end

function SystemSettingMainView:_OnBreakthroughKillSelfClick()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        return
    end
    local dfmCharacter = localCtrl:GetPawn()
    if not isvalid(dfmCharacter) then
        return
    end
    if not dfmCharacter.CanKillSelf then
        return
    end
    if dfmCharacter:CanKillSelf(false)==false then
        return
    end
    self:BP_BreakthroughKillSelf()
    Facade.UIManager:CloseUI(self)
end

function SystemSettingMainView:_OnOpenCloudPanelClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingProgramManage, nil, nil, self._tabType)
end

function SystemSettingMainView:OnInitExtraData(openTabType, isOffset)
    self._isOffset = isOffset
    self._tabType = setdefault(self._selectMap[openTabType], 1)
    --Facade.UIManager:SwitchSubUIByIndex(self, self._tabType, self._wtRootMain, isOffset)
    self:_ShowCloudBtn()
end

function SystemSettingMainView:_FetchSettingSystemByTab(mainIndex)
    self._tabType = mainIndex + 1
    if self._uiNavIDList[ self._tabType] == UIName2ID.SystemSettingSetMode or self._uiNavIDList[ self._tabType] == UIName2ID.SystemSettingLanguagePanel or self._uiNavIDList[ self._tabType] == UIName2ID.SystemSettingPrivacy
    or self._uiNavIDList[ self._tabType] == UIName2ID.SystemSettingLitePackagePanel  then --模式选择界面隐藏重置面板
        self._wtResetPanel:Collapsed()
    else
        self._wtResetPanel:Visible()
    end

    if self._uiNavIDList[ self._tabType] == UIName2ID.SystemSettingLitePackagePanel then --若在下载中心，则显示资源修复按钮，否则隐藏
        self._wtResourceFixBtn:SelfHitTestInvisible()
    else
        self._wtResourceFixBtn:Collapsed()
    end
    self:_ShowCloudBtn()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRootMain)
    Facade.UIManager:SwitchSubUIByIndex(self, mainIndex + 1, self._wtRootMain)
end

function SystemSettingMainView:OnOpen()
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIOpen:Invoke()
    self:SetSelectedModePanel()

    UDFMGameplayDelegates.Get(GetWorld()).BreakthroughOnClientRedeployStateChanged:Add(self.OnClientRedeployStateChanged,self)
end

-- Back Button
function SystemSettingMainView:_OnBackBtnClick()
    Facade.UIManager:CloseUI(self)

    Module.LobbyDisplay.Config.Events.evtResetMobileLobbyMSAA:Invoke()
end

function SystemSettingMainView:OnActivate()
    self:SetSelectedModePanel()
    Facade.UIManager:RegSwitchSubUI(self, self._uiNavIDList)
    --Facade.UIManager:SwitchSubUIByIndex(self, self._tabType, self._wtRootMain)
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        if curSubStage == ESubStage.SafeHouse3D then
            self._wtBugBtn:Visible()
        else
            self._wtBugBtn:Collapsed()
        end
    else
        if Module.Guide:IsInNewPlayerMatch() then
            self._wtReturnToModeHallBtn:SelfHitTestInvisible()
        else
            self._wtReturnToModeHallBtn:Collapsed()
        end
        self._wtBugBtn:Collapsed()
    end
end

function SystemSettingMainView:OnDeactivate()
    Facade.UIManager:UnRegSwitchSubUI(self)
    UClientBaseSetting.NotifyCloseSystemSetting(GetWorld())
end

function SystemSettingMainView:OnShow()
    ULuaExtension.ReportLuaUIShow("SettingUp")
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_EscPanel, true)
    end
end

-- Reset Button
function SystemSettingMainView:_OnResetBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingResetPanel)
end

function SystemSettingMainView:OnClose()
    ULuaExtension.ReportLuaUIClose("SettingUp")

    Server.SystemSettingServer:AutoSaveSetting()
    Facade.UIManager:ClearSubUIByParent(self, self._wtRootMain)

    if self._clientBaseSetting and self._clientControlSetting and SystemSettingLogic.IsLogin() then
        Module.SystemSetting:SendAnalysisDataMobile()
	else
		logerror("LogAnalysisTool Data is nil!")
	end
    --SystemSettingLogic.CloseAllPanel()

    UDFMGameplayDelegates.Get(GetWorld()).BreakthroughOnClientRedeployStateChanged:Remove(self.OnClientRedeployStateChanged,self)
end


function SystemSettingMainView:_OnExitSafehouse3DBtnClicked()
    Facade.UIManager:CloseUI(self)

    Module.IrisSafeHouse:Leave3DSafeHouse(true)
end

function SystemSettingMainView:SetSelectedModePanel()   
    local currentGameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    --遍历列表中查询是否有模式切换页签
    for k,v in ipairs(self._uiNavIDList) do
        if v == UIName2ID.SystemSettingSetMode then
             --若在3D特勤处，则隐藏模式选择
            if curSubStage == ESubStage.SafeHouse3D or curSubStage == ESubStage.Range or ItemOperaTool.bIsInCollectionRoom then
                --self._selectMap[SystemConfig.ESystemSettingPanel.SetMode] = nil
                table.remove(self._uiNavIDList,1)
                table.remove(self._tabTable,1)
                self:ResetIndex(SystemConfig.ESystemSettingPanel.SetMode,-1)
            end
            return
        end
    end

    if currentGameFlow ~= EGameFlowStageType.ModeHall and Facade.GameFlowManager:CheckIsInFrontEnd()
            and curSubStage ~= ESubStage.SafeHouse3D and curSubStage ~= ESubStage.Range and not ItemOperaTool.bIsInCollectionRoom then
        --self._selectMap[SystemConfig.ESystemSettingPanel.SetMode] = SystemConfig.ESystemSettingPanel.SetMode  
        table.insert(self._tabTable,1,{keyText = SystemConfig.Loc.ModelSettingTxt})
        table.insert(self._uiNavIDList,1,UIName2ID.SystemSettingSetMode)
        self:ResetIndex(SystemConfig.ESystemSettingPanel.SetMode,1)
    end

end

function SystemSettingMainView:_OnResourceFixedClick()
    Module.LitePackage:SendDownloadSettingInfo("PackageFix",2)
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingResourceRepair)
end


--change:动态变化的按钮
--changeNum:1或-1，正负分别代表增或减
function SystemSettingMainView:ResetIndex(change,changeNum)
    local bChangeBegin = false

    for key,value in pairs(self._selectMap) do
        if key == change then
            bChangeBegin =true
        end
        if bChangeBegin then
            self._selectMap[key] =self._selectMap[key] + changeNum
        end
    end
end

function SystemSettingMainView:_OnExitRangeBtnClicked()
    Facade.UIManager:CloseUI(self)

    if self._bIsCollectionRoomBtn then
        Module.CollectionRoom:LeaveCollectionRoom(ECollectionRoomLeaveFrom.Setting)
        return
    end
    Module.Range:LeaveRange()
end

function SystemSettingMainView:_OnReturnToModeHallBtnClicked()
    LogAnalysisTool.SignButtonClicked(20150003)
    Module.CommonTips:ShowConfirmWindow(
        Module.SystemSetting.Config.Loc.ConfirmReturnToModeHall,
        function()
            LogAnalysisTool.SignButtonClicked(20150004)
            Module.IrisSafeHouse:EnterModeHallFlow()

        end
    )
end

function SystemSettingMainView:OnShowBegin()
    PerfGearInst:ChangeFpsOp(ELowFpsType.EUI, ELowFpsOP.EHighToLow)
    local WorldName = tostring(UGPGameplayStatics.GetWorldName(GetWorld()))
    if WorldName ~= "Intro" then
        self._wtQuitLoginBtn:SetVisibility(ESlateVisibility.Visible)
    end

    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage == ESubStage.SafeHouse3D then
        self._wtExitSafehouse3DBtn:Visible()
        self._wtQuitLoginBtn:SetVisibility(ESlateVisibility.Collapsed)
    else
        self._wtExitSafehouse3DBtn:Collapsed()
    end
    if curSubStage == ESubStage.Range or ItemOperaTool.bIsInCollectionRoom then
        self._wtExitRangeBtn:Visible()
        self._wtQuitLoginBtn:SetVisibility(ESlateVisibility.Collapsed)
        self._wtKillSelf:Collapsed()
        if ItemOperaTool.bIsInCollectionRoom then
            self._bIsCollectionRoomBtn = true
            self._wtExitRangeBtn:SetMainTitle(Module.SystemSetting.Config.Loc.LeaveCollectionRoom)
        else
            self._bIsCollectionRoomBtn = false
            self._wtExitRangeBtn:SetMainTitle(Module.SystemSetting.Config.Loc.LeaveRange)
        end
    else
        self._wtExitRangeBtn:Collapsed()
    end
    
    self._wtGroupTab:SetTabIndex(self._tabType - 1, true)
    self._wtGroupTab:FreshMainTabByDataList(self._tabTable)
    self:BindBackAction(self._OnBackBtnClick, self)

    local character = InGameController:Get():GetGPCharacter()
    local gameState = UGameplayStatics.GetGameState(GetWorld())
    local bIsMP = (gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough) or (gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest)
    if self._wtVehicleBreakBtn ~= nil then
        if isvalid(character) then
            if character:GetVehicle() ~= nil and bIsMP then
                self._wtVehicleBreakBtn:SetVisibility(ESlateVisibility.Visible)
            else
                self._wtVehicleBreakBtn:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    end

    --刷新重新部署按钮可用状态
    self:UpdateRedeployButtonVisible()
end

function SystemSettingMainView:OnHideBegin()
    ULuaExtension.ReportLuaUIClose("SettingUp")
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRootMain)
    PerfGearInst:ChangeFpsOp(ELowFpsType.EUI, ELowFpsOP.ELowToHigh)
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIClose:Invoke()
    self:UnBindBackAction()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_EscPanel, true)
    end
end

function SystemSettingMainView:_OnAccountBtnClicked()
    if IsBuildRegionGlobal() then
        Module.Login:ShowINTLPanel(Module.Login.Config.EINTLPanelType.AccountCenter)
    elseif IsBuildRegionGA() then
        -- local areaID = Facade.ConfigManager:GetString("lastUserAreaId", "")
        -- local originUrl = "https://binding.deltaforce.garena.sg/2025"
        -- local url = string.format("%s?area_id=%s", originUrl, areaID)
        -- Module.GCloudSDK:OpenUrl(url, 2, false, true)
        Module.AccountBind:ShowMainPanel()
    end
end


--客户端部署状态改变
function SystemSettingMainView:OnClientRedeployStateChanged(InRedeployState)
    self:UpdateRedeployButtonVisible()
end

--刷新重新部署按钮可用状态
function SystemSettingMainView:UpdateRedeployButtonVisible()
    if isvalid(self._wtKillSelf) == false then
        return
    end

    --大战场在FallDown、Focus阶段禁用重新部署按钮
    local bCanClick = self:GetRedeployBtnCanClick()
    self._wtKillSelf:SetIsEnabled(bCanClick)
end

function SystemSettingMainView:GetRedeployBtnCanClick()
    -- if true then
    --     return false
    -- end

    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if isvalid(gameState)==false then
        return false
    end

    local GM = gameState.DFMGamePlayerMode
    if (GM ~= EDFMGamePlayMode.GamePlayMode_Breakthrough and GM ~= EDFMGamePlayMode.GamePlayMode_Conquest) then
        return false
    end

    if Server.IrisSafeHouseServer.bIsInRange then
        return false
    end
    
    --获取UDFMRedeployComponent
    local dfmCharacter = GetInGameController():GetGPCharacter()
    if isinvalid(dfmCharacter) then
        log("UpdateRedeployButtonVisible isinvalid dfmCharacter")
        return false
    end
    local redeployComponent = UE.GameplayBlueprintHelper.FindComponentByClass(dfmCharacter, UE.DFMRedeployComponent)
    if isinvalid(redeployComponent) then
        log("UpdateRedeployButtonVisible isinvalid redeployComponent")
        return false
    end

    -- 倒下到求救阶段就禁用重新部署按钮
    local clientCurState = redeployComponent.ClientCurState

    if clientCurState >= ERedeployStateFuture.FallDown and clientCurState <= ERedeployStateFuture.TurnCameraToSelf then
        return false
    end

    return true
end

function SystemSettingMainView:SetQuitWindowInGame()
    local character = InGameController:Get():GetGPCharacter()
    local gameMode = UGameplayStatics.GetGameState(GetWorld()).DFMGamePlayerMode
    local matchMode=Server.GameModeServer:GetMatchMode()
    local bIsDefaultStr = self:IsDefaultQuitStr(character,gameMode,matchMode) --返回true则不显示处罚
    local quitStr = ""
    if bIsDefaultStr then
        quitStr = Module.SystemSetting.Config.Loc.Quit
        return quitStr
    end
    local param = {
        ["Quit"] = Module.SystemSetting.Config.Loc.Quit,
        ["DeductPoint"] = Module.SystemSetting.Config.Loc.DeductPoint
    }
    quitStr = StringUtil.Key2StrFormat(Module.SystemSetting.Config.Loc.ConfirmQuitGame, param)
    return quitStr
end

function SystemSettingMainView:IsDefaultQuitStr(character,gameMode,matchMode) --是否显示默认退出信息，即不显示退出惩罚
    if not IsBuildRegionCN() then
        return true
    end
    if InGameController:Get():IsVictoryUniteMatch() then
        return false
    end
    if (character and gameMode ==  EDFMGamePlayMode.GamePlayMode_SOL) then
        if  matchMode.game_rule == MatchGameRule.SOLGameRule then 
            if character:IsImpendingDeath() or character:IsDead() then --危险行动模式下玩家死亡或濒死则不触发处罚
                return true
            end
        else --非危险行动模式不触发处罚
            return true
        end
    end
    if character and (gameMode ==  EDFMGamePlayMode.GamePlayMode_Breakthrough or gameMode ==  EDFMGamePlayMode.GamePlayMode_Conquest) then
        return true
    end
    return false

end

return SystemSettingMainView