----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local function log(...)
    print("lyl", "[ChatMsgCell]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end
local EMsgPanelType = Module.Chat.Config.EMsgPanelType
local UDFMGameChat = import "DFMGameChat"
local ChatMsgCell = ui("ChatMsgCell")
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"

function ChatMsgCell:Ctor()
    log("ChatMsgCell:Ctor")
    self._msgType = EMsgPanelType.kConvenientChat
    self._msg = ""
    self._fCallbackName = nil
    self._presetId = nil
    self._tableId = nil

    self._wtContentTB = self:Wnd("ContentTB", UITextBlock)
    self._wtDFMButton = self:Wnd("DFMButton", UIButton)
    self._wtDFMButton:Event("OnClicked", self._OnButtonClick, self)
    self._wtLineImg = self:Wnd("LineImg", UIImage)
end

function ChatMsgCell:OnInitExtraData(txtInfo, panelType)
    if txtInfo.msgType == ChatMsgType.TeamInvite then
        self:SetTeamInviteInfo(txtInfo)
        return
    end

    self.mMapIndex = nil
    self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState)
    self:SetInfo(txtInfo, panelType)
end

function ChatMsgCell:SetTeamInviteInfo(txtInfo)
    self._msgType = EMsgPanelType.kTeamInvite
    self.mMapIndex = txtInfo.mapIndex
    self:AddLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState, self)
    self:SetTeamInviteStateTxt()
end

function ChatMsgCell:UpdateTeamInviteState(reason, startIndexVal)
    if self.mMapIndex == nil then
        return
    end

    -- if self.mMapIndex < startIndexVal then
    --     return
    -- end
    self:SetTeamInviteStateTxt()
end

function ChatMsgCell:SetTeamInviteStateTxt()
    local dataValue = Module.Team.Field.GameTeamInviteViewModel:GetCacheData(self.mMapIndex)
    if dataValue and self._wtContentTB then
        self._wtContentTB:SetText(ChatLogic.InGameInviteChatMsgMake(dataValue))
    end
end


function ChatMsgCell:SetInfo(txtInfo, panelType)
    log("ChatMsgCell:SetInfo")
    if panelType == EMsgPanelType.kChat then
        self:SetChatMsg(txtInfo)
    elseif panelType == EMsgPanelType.kConvenientChat then
        self:SetConvenientMsg(txtInfo)
    end
end


function ChatMsgCell:_OnButtonClick()
    log("ChatMsgCell:_OnButtonClick", self._msgType)

    if self._msgType == EMsgPanelType.kConvenientChat then
        if self._fCallbackName then
            self[self._fCallbackName](self)
        else
            self:_SendLocalTxt()
        end
        Module.Chat.Config.evtOnChatMsgCellClicked:Invoke()
    end
end

function ChatMsgCell:_SendLocalTxt()
    log("ChatMsgCell:_SendLocalTxt")
    local dsRoomId = Server.MatchServer:GetDsRoomId()

    Server.ChatServer:SendChatPreset(dsRoomId, DSChatType.DSChatTeam,self._presetId, nil, {self._tableId}, 4)
end

function ChatMsgCell:SetIsEnabled(bFlag)
    log("ChatMsgCell:SetIsEnabled")
    self._wtDFMButton:Collapsed()
    self._wtLineImg:Collapsed()
end

function ChatMsgCell:SetChatMsg(info)
    self._msgType = EMsgPanelType.kChat
    local chatStruct = {
        dsRoomId = info.dsRoomId,
        timeStamp = info.timeStamp,
        chatType = info.chatType,
        msgType = info.msgType,
        senderId = info.senderId,
        senderName = info.senderName,
        content = info.content,
        presetMsg = info.msg.preset_msg,
    }

    self._msg = chatStruct.content or ""
    local content = ""
    -- if chatStruct.senderId == Server.AccountServer:GetPlayerId() then
    --     content = string.format(Module.Chat.Config.Loc.MsgYellow,chatStruct.senderName..":"..chatStruct.content)
    -- else
    --     content = string.format(Module.Chat.Config.Loc.MsgWhite1, chatStruct.senderName..":"..chatStruct.content)
    -- end
    content = self:GetInfoStyle_InGame(chatStruct, info.chatType, info.senderName, info.senderId)
    log("ChatMsgCell:SetInfo", content)
    self._wtContentTB:SetText(content)
end

function ChatMsgCell:SetConvenientMsg(msg)
    self._msgType = EMsgPanelType.kConvenientChat
    self._msg = msg.content
    self._fCallbackName = msg.fCallbackName
    self._presetId = msg.presetid
    self._tableId = msg.tableid

    local content = string.format("%s", self._msg or "")
    log("ChatMsgCell:SetConvenientMsg", content)
    self._wtContentTB:SetText(content)
end

function ChatMsgCell:GetInfoStyle_InGame(chatStruct, chatType, senderName, senderId)
    local showTxt = ""
    local content = chatStruct.content
    if chatStruct.msgType == ChatMsgType.PresetStr then
        content = ChatLogic.ResolvePresetChat(chatStruct.presetMsg)
    end

    -- TODO 后面颜色代码改成枚举
    senderName = ChatLogic.GetNameWithTeamPrefixInCommander(senderId, senderName, chatType == DSChatType.DSChatCamp)
    if chatType == DSChatType.DSChatCamp then
        showTxt = string.format("<customstyle color=\"Color_CampNPC\">%s：</>%s", senderName, content)
    else
        showTxt = string.format("<customstyle color=\"Color_Chat_Team\">%s：</>%s", senderName, content)
    end

    showTxt = ChatLogic.AddCommanderPrefix(senderId, showTxt, chatType == DSChatType.DSChatCamp)

    return showTxt
end

function ChatMsgCell:GetInviteStyle_InGame(senderName, displayContent)
    local showTxt = string.format("<customstyle color=\"Color_Chat_Team\">%s：</>%s", senderName, displayContent)
    -- showTxt = ChatLogic.AddCommanderPrefix(senderId, showTxt, chatType == DSChatType.DSChatCamp)

    return showTxt
end

function ChatMsgCell:OnHide()
    self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.UpdateTeamInviteState)
end

-------------------------------------------------------------------------------
---按键功能
-------------------------------------------------------------------------------
function ChatMsgCell:MarkCoord()
    log("ChatMsgCell:MarkCoord")
    local dfmGameChatIns = UDFMGameChat.Get(GetWorld())
    if dfmGameChatIns then
        dfmGameChatIns:MarkCoord(GetWorld())
        self:_SendLocalTxt()
    end
end

function ChatMsgCell:MarkItem()
    log("ChatMsgCell:MarkItem")
    local OnMarkItemCallback = function(itemName)
        log("OnMarkItemCallback")
        local dsRoomId = Server.MatchServer:GetDsRoomId()
        local txt = string.format(Module.Chat.Config.Loc.ThingsOfMine, itemName == "" and Module.Chat.Config.Loc.ItemTxt or string.format("<Yellow>%s</>",itemName))
        Server.ChatServer:SendChat(dsRoomId, DSChatType.DSChatTeam, ChatMsgType.PresetStr, txt , nil , nil , 2)
        Module.CommonTips:ShowSimpleTip(txt)
    end

    local dfmGameChatIns = UDFMGameChat.Get(GetWorld())
    if dfmGameChatIns then
        dfmGameChatIns.FdfmGameChatOnMarkedItemNameChanged:Clear()
        dfmGameChatIns.FdfmGameChatOnMarkedItemNameChanged:Add(OnMarkItemCallback)
        dfmGameChatIns:MarkItem(GetWorld())
    end
end

return ChatMsgCell
