--local socket = require("socket")
TimeUtil = {}
--- ClockManager负责与服务器同步时间，TimeUtil负责时间格式转换

local DFMLocalizationManager = import("DFMLocalizationManager").Get(GetGameInstance())
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

local localizeTimeConfig = nil
local curCulture = nil

TimeUtil.Loc = {
    Month = {
        [1] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_January", "01"),
        [2] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_February", "02"),
        [3] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_March", "03"),
        [4] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_April", "04"),
        [5] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_May", "05"),
        [6] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_June", "06"),
        [7] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_July", "07"),
        [8] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_August", "08"),
        [9] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_September", "09"),
        [10] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_October", "10"),
        [11] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_November", "11"),
        [12] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_December", "12"),
    },
    MonthAbbr = {
        [1] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_JanuaryAbbr", "01"),
        [2] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_FebruaryAbbr", "02"),
        [3] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_MarchAbbr", "03"),
        [4] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_AprilAbbr", "04"),
        [5] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_MayAbbr", "05"),
        [6] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_JuneAbbr", "06"),
        [7] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_JulyAbbr", "07"),
        [8] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_AugustAbbr", "08"),
        [9] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_SeptemberAbbr", "09"),
        [10] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_OctoberAbbr", "10"),
        [11] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_NovemberAbbr", "11"),
        [12] = NSLOCTEXT("GlobalText", "Lua_TimeUtil_DecemberAbbr", "12"),
    },    
}

--- 获取当前系统时间
function TimeUtil.GetCurrentTime()
    return os.time()
end

--- 获取当前系统时间，毫秒
function TimeUtil.GetCurrentTimeMillis()
    -- return socket.gettime()
    return FDateTime.GetTimeMillis()
end

---  获取剩余时间格式(HHMMSS)的字符串（相对本地时间）
function TimeUtil.GetLocalRemainTimeFormatHHMMSSString(endTimestamp)
    local remainTime = endTimestamp - Facade.ClockManager:GetLocalTimestamp()
    if remainTime <= 0 then --避免负数
        remainTime = 0
    end
    return TimeUtil.GetSecondsFormatHHMMSSString(remainTime)
    --return string.format("%d:%d:%d",TimeSpan:GetTotalHours() , TimeSpan:GetMinutes(),TimeSpan:GetSeconds())
end

function TimeUtil.GetServerRemainTimeDHMSAsArray(endTimestamp) --> {D, H, M, S}
    local remainTime = endTimestamp - Facade.ClockManager:GetServerTimestamp()
    if remainTime <= 0 then return {0,0,0,0} end
    local M = 60
    local H = M * 60
    local D = H * 24 
    return {remainTime//D, (remainTime%D)//H, (remainTime%H)//M, (remainTime%M)}
end

---  获取剩余时间格式(HHMMSS)的字符串（相对服务器时间）
function TimeUtil.GetServerRemainTimeFormatHHMMSSString(endTimestamp)
    local remainTime = endTimestamp - Facade.ClockManager:GetServerTimestamp()
    if remainTime <= 0 then --避免负数
        remainTime = 0
    end
    return TimeUtil.GetSecondsFormatHHMMSSString(remainTime)
    --return string.format("%d:%d:%d",TimeSpan:GetTotalHours() , TimeSpan:GetMinutes(),TimeSpan:GetSeconds())
end

--[xxww]获取剩余时间秒数(相对服务器时间)
function TimeUtil.GetServerRemainTime2Seconds(endTimestamp)
    local remainTime = endTimestamp - Facade.ClockManager:GetServerTimestamp()
    if remainTime <= 0 then --避免负数
        remainTime = 0
    end
    return remainTime
end

-- azhengzheng:获取剩余时间秒数（相对本地时间）
function TimeUtil.GetLocalRemainTime2Seconds(endTimestamp)
    local remainTime = endTimestamp - Facade.ClockManager:GetLocalTimestamp()

    return remainTime < 0 and 0 or remainTime
end

------------------------------------------------------------------------------------------
--                            时间函数的使用，注意时区和时间戳                             --
------------------------------------------------------------------------------------------
-- 将UnixTime（0时区1970-01-01自今秒数）时间戳毫秒转换为YYMMDDHHMMSS
function TimeUtil.TransUnixTimestamp2YYMMDDHHMMSS(unixTime)
    local fDateTime = FDateTime.FromUnixTimestamp(unixTime)
    return fDateTime:GetYear(), fDateTime:GetMonth(), fDateTime:GetDay(), fDateTime:GetHour(), fDateTime:GetMinute(), fDateTime:GetSecond()
end

-- 将UnixTime时间戳转换为YYMMDDHHMMSS字符串，为0时区时间
---@param unixTime number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransUnixTimestamp2YYMMDDHHMMSSString(unixTime, formatName)
    --timeFormat = setdefault(timeFormat, "%02i-%02i-%02i %02i:%02i:%02i")
    -- local yy, mm, dd, hh, mu, ss = TimeUtil.TransUnixTimestamp2YYMMDDHHMMSS(unixTime)
    -- return string.format(timeFormat, yy, mm, dd, hh, mu, ss)
    formatName = setdefault(formatName, "YYMMDDHHMMSS")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y-%m-%d %H:%M:%S"
    end
    local str = os.date("!" .. timeFormat, unixTime)
    return TimeUtil.ParseStringWithMonth(str, unixTime, true)
end

-- 0时区时间（0时区1970-01-01自今秒数）时间戳秒转换为YYMMDDHHMM字符串
---@param unixTime number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransUnixTimestamp2YYMMDDHHMMString(unixTime, formatName)
    --timeFormat = setdefault(timeFormat, "%02i-%02i-%02i %02i:%02i:%02i")
    -- local yy, mm, dd, hh, mu, ss = TimeUtil.TransUnixTimestamp2YYMMDDHHMMSS(unixTime)
    -- return string.format(timeFormat, yy, mm, dd, hh, mu, ss)
    formatName = setdefault(formatName, "YYMMDDHHMM")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y/%m/%d %H:%M"
    end
    local str = os.date("!" .. timeFormat, unixTime)
    return TimeUtil.ParseStringWithMonth(str, unixTime, true)
end

-- 将UnixTime时间戳秒转换为YYMMDD
function TimeUtil.TransUnixTimestamp2YYMMDD(unixTime)
    local fDateTime = FDateTime.FromUnixTimestamp(unixTime)
    return fDateTime:GetYear(), fDateTime:GetMonth(), fDateTime:GetDay()
end

-- 将UnixTime时间戳秒转换为YYMMDD字符串，为UTC时间
---@param unixTime number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransUnixTimestamp2YYMMDDStr(unixTime, formatName)
    -- local yy, mm, dd = TimeUtil.TransUnixTimestamp2YYMMDD(unixTime)
    -- timeFormat = setdefault(timeFormat, "%02i-%02i-%02i")
    -- return string.format(timeFormat, yy, mm, dd)
    formatName = setdefault(formatName, "YY-MM-DD")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y-%m-%d"
    end
    local str = os.date("!" .. timeFormat, unixTime)
    return TimeUtil.ParseStringWithMonth(str, unixTime, true)
end

-- 将UnixTime时间戳秒转换为YYMM字符串，为UTC时间
---@param unixTime number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransUnixTimestamp2YYMMStr(unixTime, formatName)
    -- local yy, mm, dd = TimeUtil.TransUnixTimestamp2YYMMDD(unixTime)
    -- timeFormat = setdefault(timeFormat, "%02i-%02i-%02i")
    -- return string.format(timeFormat, yy, mm, dd)
    formatName = setdefault(formatName, "YY/MM")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y/%m"
    end
    local str = os.date("!" .. timeFormat, unixTime)
    return TimeUtil.ParseStringWithMonth(str, unixTime, true)
end


-------------------------------------------------------------------------
-- 将LocalTime（本地时区1970-01-01自今秒数）时间戳秒转换为YYMMDDHHMMSS字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%Y-%m-%d %H:%M:%S")
    formatName = setdefault(formatName, "YYMMDDHHMMSS")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y-%m-%d %H:%M:%S"
    end
    local str = os.date(timeFormat, timestamp)
    return TimeUtil.ParseStringWithMonth(str, timestamp)
end

-- 转换LocalTime（本地时区1970-01-01自今秒数）时间戳为YYMMDDHHMMSS_CN字符串
-- 格式 xxxx年xx月xx日xx时xx分xx秒
---@param timestamp number
---@return string|osdate
function TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(timestamp)
    timestamp = math.floor(timestamp)
    local timeFormat = TimeUtil.GetFormatStrByName("YYMMDDHHMMSS_CN")
    if timeFormat == "" then
        timeFormat = "%d/%m/%Y %H:%M:%S"
    end
    local str = os.date(timeFormat, timestamp)
    return TimeUtil.ParseStringWithMonth(str, timestamp)
end

-- 转换LocalTime（本地时区1970-01-01自今秒数）时间戳为MMDDHHMM_AUCTION字符串
-- 格式 xx月xx日 xx:xx
---@param timestamp number
---@return string|osdate
function TimeUtil.TransTimestamp2MMDDHHMMAUCTIONStr(timestamp)
    timestamp = math.floor(timestamp)
    local timeFormat = TimeUtil.GetFormatStrByName("MMDDHHMM_AUCTION")
    if timeFormat == "" then
        timeFormat = "%m月%d日 %H:%M"
    end
    local str = os.date(timeFormat, timestamp)
    return TimeUtil.ParseStringWithMonth(str, timestamp)
end

-- 将LocalTime（本地时区1970-01-01自今秒数）时间戳秒转换为MMDDHHMM字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2MMDDHHMMStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%m-%d %H:%M")
    formatName = setdefault(formatName, "MMDDHHMM")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%m-%d %H:%M"
    end
    local str = os.date(timeFormat, timestamp)
    return TimeUtil.ParseStringWithMonth(str, timestamp)
end

-- 将LocalTime时间戳秒转换为HHMMSS字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2HHMMSSStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "HHMMSS")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%H:%M:%S"
    end
    return os.date(timeFormat, timestamp)
end

-- 将LocalTime时间戳秒转换为YY.MM.DD字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2AYYMMDDStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "YY.MM.DD")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%Y.%m.%d"
    end
    local str = os.date(timeFormat, timestamp)
    return TimeUtil.ParseStringWithMonth(str, timestamp)
end

-- 将LocalTime时间戳秒转换为HHMMSS_CN字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2AHHMMSS_CNStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "HHMMSS_CN")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%H小时%M分钟%S秒"
    end
    return os.date(timeFormat, timestamp)
end

function TimeUtil.TransSeconds2Str(seconds)
    if seconds < 0 then
        seconds = 0
    end
    if seconds >= 3600 * 24 then
        return TimeUtil.TransTimestamp2DDHH_CNStr(seconds)
    elseif seconds >= 3600 then
        return TimeUtil.TransTimestamp2HHMM_CNStr(seconds)
    else
        return TimeUtil.TransTimestamp2MMSS_CNStr(seconds)
    end
end

-- 将LocalTime时间戳秒转换为DDHH_CN字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2DDHH_CNStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "DDHH_CN")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%d天%H小时"
    end
    local d, h, _, _ = TimeUtil.GetSecondsFormatDDHHMMSS(timestamp)
    local timeStr = timeFormat:gsub("%%d", d)
    timeStr = timeStr:gsub("%%H", h)
    return timeStr
end

-- 将LocalTime时间戳秒转换为HHMM_CN字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2HHMM_CNStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "HHMM_CN")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%H小时%M分钟"
    end
    local _, h, m, _ = TimeUtil.GetSecondsFormatDDHHMMSS(timestamp)
    local timeStr = timeFormat:gsub("%%H", h)
    timeStr = timeStr:gsub("%%M", m)
    return timeStr
end

-- 将LocalTime时间戳秒转换为HHMM_CN字符串
---@param timestamp number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string|osdate
function TimeUtil.TransTimestamp2MMSS_CNStr(timestamp, formatName)
    timestamp = math.floor(timestamp)
    -- timeformat = setdefault(timeformat, "%H:%M:%S")
    formatName = setdefault(formatName, "MMSS_CN")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%M分钟%S秒"
    end
    local _, _, m, s = TimeUtil.GetSecondsFormatDDHHMMSS(timestamp)
    local timeStr = timeFormat:gsub("%%M", m)
    timeStr = timeStr:gsub("%%S", s)
    return timeStr
end

------------------------------------------------------------------------------------------
-- 将秒数转换为DDHHMMSS
function TimeUtil.GetSecondsFormatDDHHMMSS(second)
    local day = math.floor(second / 60 / 60 / 24)
    local hour = math.floor(second / 3600) % 24
    local min = math.floor(second / 60) % 60
    local sec = math.floor(second % 60)
    return day, hour, min, sec
end

---将秒数转换为DDHHMMSS的形式，注意这不是通常意义上的时间，即天数没有上限
---@param second number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string
function TimeUtil.GetSecondsFormatDDHHMMSSString(second, formatName)
    -- local timeFormat = setdefault(timeFormat, "%02i:%02i:%02i:%02i")   
    local formatName = setdefault(formatName, "DDHHMMSS")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)  
    if timeFormat == "" then
        timeFormat = "{day}:{hour}:{min}:{sec}"
    end  
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(second)
    -- return string.format(timeFormat, day, hour, min, sec)
    local param = {
        ["day"] = string.format("%02i", day),
        ["hour"] = string.format("%02i", hour),
        ["min"] = string.format("%02i", min),
        ["sec"] = string.format("%02i", sec),
    }
    return StringUtil.Key2StrFormat(timeFormat, param)
end

-- 将秒数转换为DDHHMM
function TimeUtil.GetSecondsFormatDDHHMM(second)
    local day = math.floor(second / 60 / 60 / 24)
    local hour = math.floor(second / 3600) % 24
    local min = math.floor(second / 60) % 60
    return day, hour, min
end

-- 将秒数转换为MMSS
function TimeUtil.GetSecondsFormatMMSS(second)
    local min = math.floor(second / 60)
    local s = math.floor(second / 360)
    return min, s
end

---将秒数转换为DDHHMM的形式，注意这不是通常意义上的时间，即天数没有上限
---@param second number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string
function TimeUtil.GetSecondsFormatDDHHMMString(second, formatName)
    --local timeFormat = setdefault(timeFormat, "%02i %02i:%02i")
    local formatName = setdefault(formatName, "DDHHMM")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)   
    if timeFormat == "" then
        timeFormat = "{day} {hour}:{min}"
    end
    local day, hour, min = TimeUtil.GetSecondsFormatDDHHMM(second)
    --return string.format(timeFormat, day, hour, min)
    local param = {
        ["day"] = string.format("%02i", day),
        ["hour"] = string.format("%02i", hour),
        ["min"] = string.format("%02i", min),
    }
    return StringUtil.Key2StrFormat(timeFormat, param)
end

-- 将秒数转换为HHMMSS
function TimeUtil.GetSecondsFormatHHMMSS(second)
    -- local TimeSpan = UKismetMathLibrary.FromSeconds(second) --TODO:导出
    -- local str = ULuaExtension.Ext_FTimespan_ToFormatString(TimeSpan,"%02i: %02i: %02i")
    -- local str =  ULuaExtension.Ext_FTimespan_FormatGetText(TimeSpan)
    -- LogUtil.LogInfo("RemainTime",second,",",str)
    local hour = math.floor(second / 3600)
    local min = math.floor(second % 3600 / 60)
    second = second % 60
    return hour, min, second
end

--- 将秒数转换为HHMMSS字符串，注意这不是通常意义上的时间，即小时数没有上限，可能会得到25:01:01的结果
--- second 不能为小数
---@param second number
---@param formatName string|any 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string
function TimeUtil.GetSecondsFormatHHMMSSString(second, formatName)
    --local timeFormat = setdefault(timeFormat, "%02i:%02i:%02i")
    local formatName = setdefault(formatName, "HHMMSS_Second")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "{hour}:{min}:{sec}"
    end
    local hour, min, sec = TimeUtil.GetSecondsFormatHHMMSS(second)
    --return string.format(timeFormat, hour, min, sec)
    local param = {
        ["hour"] = string.format("%02i", hour),
        ["min"] = string.format("%02i", min),
        ["sec"] = string.format("%02i", sec),
    }
    return StringUtil.Key2StrFormat(timeFormat, param)
end

--- 将秒数转换为HHMM字符串，注意这不是通常意义上的时间，即小时数没有上限，可能会得到25:01:01的结果
--- second 不能为小数
---@param second number
---@param formatName string|any 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string
function TimeUtil.GetSecondsFormatHHMMString(second, formatName)
    --local timeFormat = setdefault(timeFormat, "%02i:%02i:%02i")
    local formatName = setdefault(formatName, "HHMM")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "%s:%s"
    else
        timeFormat=string.gsub(timeFormat, 'H', "s")
        timeFormat=string.gsub(timeFormat, 'M', "s")
    end
    local hour, min, sec = TimeUtil.GetSecondsFormatHHMMSS(second)
    local hourStr = string.format("%02i", hour)
    local minStr= string.format("%02i", min)
    return string.format(timeFormat, hourStr, minStr)
end

--- 将秒数转换为MMSS
function TimeUtil.GetSecondsFormatMMSS(sec)
    local min = sec // 60
    sec = sec - min * 60
    return min, sec
end

--- 将秒数转换为MMSS的时间格式字符串，注意这不是通常意义上的时间，即分钟数没有上限，可能会得到61:01的结果
---@param sec number
---@param formatName string 传入的LocalizeTimeConfig表中RowName，取得的是Format格式
---@return string
function TimeUtil.GetSecondsFormatMMSSStr(sec, formatName)
    --timeformat = setdefault(timeformat, "%02d:%02d")
    local formatName = setdefault(formatName, "MMSS")
    local timeFormat = TimeUtil.GetFormatStrByName(formatName)
    if timeFormat == "" then
        timeFormat = "{min}:{sec}"
    end
    sec = math.floor(sec)
    local min, sec = TimeUtil.GetSecondsFormatMMSS(sec)
    --return string.format(timeformat, min, sec)
    local param = {
        ["min"] = string.format("%02d", min),
        ["sec"] = string.format("%02d", sec),
    }
    return StringUtil.Key2StrFormat(timeFormat, param)
end

------------------------------------------------------------------------------------------

--- 当前是否是整点
function TimeUtil.GetIsIntegralHour()
    local timestamp = TimeUtil.GetCurrentTime()
    return TimeUtil.GetCurrentHourByStamp(timestamp)
end

--- 获取当前的小时数
function TimeUtil.GetCurrentHour()
    local timestamp = TimeUtil.GetCurrentTime()
    return TimeUtil.GetCurrentHourByStamp(timestamp)
end

function TimeUtil.GetIsIntegralHourByStamp(timestamp)
    local yy, mm, dd, hh, mu, ss = TimeUtil.TransUnixTimestamp2YYMMDDHHMMSS(timestamp)
    if tonumber(mu) == 0 and tonumber(ss) == 0 then
        return true
    else
        return false
    end
end

function TimeUtil.GetCurrentHourByStamp(timestamp)
    local yy, mm, dd, hh, mu, ss = TimeUtil.TransUnixTimestamp2YYMMDDHHMMSS(timestamp)
    return tonumber(hh)
end

function TimeUtil.GetHourAndMinuteByStamp(timestamp)
    return TimeUtil.TransTimestamp2MMDDHHMMStr(timestamp,'HHMM')
end

---comment [fireicelin] 不超过24小时
---@param minutes any 分钟数
---@param bConsiderTimeZone boolean 是否考虑时区，默认不考虑
---@return string
function TimeUtil.GetHourAndMinuteByMinutes(minutes,bConsiderTimeZone)
    bConsiderTimeZone=setdefault(bConsiderTimeZone,false)
    local seconds=minutes*60
    if bConsiderTimeZone then
        seconds=seconds+TimeUtil.GetTimeOffsetInSeconds()
    end
    local oneDaySecond=86400
    seconds=seconds%oneDaySecond
    return TimeUtil.GetSecondsFormatHHMMString(seconds,'HHMM')
end

--- 获取world delta秒数
function TimeUtil.GetDeltaSeconds(ins)
    if ins == nil then
        error("")
    end
    return UGameplayStatics.GetWorldDeltaSeconds(ins)
end

--- 获取world秒数
function TimeUtil.GetTimeSeconds(ins)
    if ins == nil then
        error("")
    end
    return UGameplayStatics.GetTimeSeconds(ins)
end

-- 转化格式为 yyyy-mm-dd hh:mm:ss 的时间到timestamp
function TimeUtil.GetTimeStame(timeFormat)
    local pattern = "(%d+)-(%d+)-(%d+) (%d+):(%d+):(%d+)"
    local runyear, runmonth, runday, runhour, runminute, runseconds = timeFormat:match(pattern)
    local convertedTimestamp = os.time({year = runyear, month = runmonth, day = runday, hour = runhour, min = runminute, sec = runseconds})
    return convertedTimestamp
end

------------------------------------------------------------------------------------
--- [aidenliao]
--- 获取LocalizeTimeConfig表中配置的格式
---@param inFormatName string 用于Format的字符串
---@return string
function TimeUtil.GetFormatStrByName(inFormatName)
    if localizeTimeConfig == nil then
        localizeTimeConfig = Facade.TableManager:GetTable("Localize/LocalizeTimeConfig")
        if localizeTimeConfig == nil then
            loginfo("TimeUtil:GetTable LocalizeTimeConfig Load Failed!")
            return "" -- 这种情况用各自接口的默认格式
        end
    end

    curCulture = LocalizeTool.GetCurrentCulture()

    -- if curCulture == nil and DFMLocalizationManager then
    --     curCulture = DFMLocalizationManager:GetCurrentCulture()
    -- end

    if localizeTimeConfig[inFormatName] then
        local formatConfigRow = localizeTimeConfig[inFormatName]
        if formatConfigRow.TimeFormatConfig then
            --先看看当前文化的有没有，没有的话再看看默认的存不存在，不存在直接返回表中默认
            local cultureFormat = formatConfigRow.TimeFormatConfig:Get(curCulture) -- 减少一次LuaMap的访问
            if cultureFormat then
                return cultureFormat
            end

            local defaultFormat = formatConfigRow.TimeFormatConfig:Get("Default")
            if defaultFormat then
                return defaultFormat
            end
            return "%Y-%m-%d %H:%M:%S"
        end
    end
    return "%Y-%m-%d %H:%M:%S"
end

function TimeUtil.GetWeekdayByTimestamp(timestamp)
    local weekday = os.date("%w", timestamp)
    weekday=weekday=="0" and "7" or weekday
    return tonumber(weekday)
end

function TimeUtil.GetMonthByTimestamp(timestamp, bIsUTC)
    if bIsUTC then
        return os.date("!%m", timestamp)
    end
    return os.date("%m", timestamp)
end

function TimeUtil.ParseStringWithMonth(str, timestamp, bIsUTC)
    local Month = tonumber(TimeUtil.GetMonthByTimestamp(timestamp, bIsUTC))
    local MonthStr = tostring(TimeUtil.Loc.Month[Month])
    local MonthAbbrStr = tostring(TimeUtil.Loc.MonthAbbr[Month])
    str = string.gsub(str, '{month}', MonthStr)
    str = string.gsub(str, '{monthabbr}', MonthAbbrStr)
    return str
end

function TimeUtil.GetTimeOffsetInSeconds()
    return math.floor(os.difftime(os.time(), os.time(os.date("!*t",os.time()))))
end

function TimeUtil.GetClientTimeZone()
    return TimeUtil.GetTimeOffsetInSeconds() / 3600
end

---将时间字符串转换为时间戳，只接受 2024-10-10 15:20:00 格式的字符串
---@param inTimeStr string 传入的时间字符串
---@return boolean 转换是否成功
---@return integer 转换后时间戳
function TimeUtil.GetTimeStampFromString(inTimeStr)
    local parseRet, dateTime = FDateTime.Parse(inTimeStr)
    if parseRet and dateTime then
        local timestamp = dateTime:ToUnixTimestamp()
        if IsBuildRegionCN() then
            -- 国服按utc+8去算的，因此时间需要减8小时才是真正的生效时间
            timestamp = timestamp - 8 * 3600
        end
        return true, timestamp
    end
    
    return false, 0
end

--将毫秒转成00:00.000分秒毫秒格式的字符串
function TimeUtil.TransMilliSecond2MMSSMsMsStr(ms)
    local totalSeconds = math.floor(ms / 1000)
    local milliseconds = ms % 1000
    -- 计算分钟和秒
    local minutes = math.floor(totalSeconds / 60)
    local seconds = totalSeconds % 60
    local formattedTime = string.format("%02d:%02d.%03d", minutes, seconds, milliseconds)
    return formattedTime
end


function TimeUtil.IsCurrentTimeBetween(startTime, endTime)
    local currentTime = os.time()
    local startTimeStame = TimeUtil.GetTimeStame(startTime)
    local endTimeStame = TimeUtil.GetTimeStame(endTime)
    return currentTime >= startTimeStame and currentTime <= endTimeStame
end

return TimeUtil
