----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local ItemConfigTool      = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EMouseCursor = import "EMouseCursor"

---@class CollectionRoomDisplaySpecialCabinetPanel : LuaUIBaseView
local CollectionRoomDisplaySpecialCabinetPanel = ui("CollectionRoomDisplaySpecialCabinetPanel")

function CollectionRoomDisplaySpecialCabinetPanel:Ctor()
    self._wtRootCanvas = self:Wnd("DFCanvasPanel_19", UIWidgetBase)
    self._wtSwitchToLeftCameraBtn = self:Wnd("DFButton_LeftBtn", DFButtonOnly)
    self._wtSwitchToLeftCameraBtn:Event("OnClicked", self._OnLeftBtnClicked, self)
    self._wtSwitchToRightCameraBtn = self:Wnd("DFButton_RightBtn", DFButtonOnly)
    self._wtSwitchToRightCameraBtn:Event("OnClicked", self._OnRightBtnClicked, self)
    self._wtCameraBtnList = self:MultiWnd("WBP_DFCommonButtonCarousel", DFButtonOnly)
    for index, btn in ipairs(self._wtCameraBtnList) do
        btn:Event("OnClicked", CreateCallBack(self._OnCameraBtnClicked, self, index))
    end
    local wtSwitchCabinetBtnHD = self:Wnd("WBP_CommonButtonV3S1_1", DFButtonOnly)
    wtSwitchCabinetBtnHD:Event("OnClicked", self._OnSwitchCabinetBtnClicked, self)
    local wtSwitchCabinetBtnMobile = self:Wnd("WBP_CommonIconButton", DFButtonOnly)
    wtSwitchCabinetBtnMobile:Event("OnClicked", self._OnSwitchCabinetBtnClicked, self)
    if IsHD() then
        self._wtSwitchCabinetBtn = wtSwitchCabinetBtnHD
        wtSwitchCabinetBtnMobile:Collapsed()
    else
        self._wtSwitchCabinetBtn = wtSwitchCabinetBtnMobile
        wtSwitchCabinetBtnHD:Collapsed()
    end
    self._wtEnterRoomBtn = self:Wnd("WBP_CommonButtonV3S1", DFButtonOnly)
    self._wtEnterRoomBtn:Event("OnClicked", self._EnterRoomBtnClicked, self)

    self._wtSwitchBtnPanel = self:Wnd("DFCanvas_Page", UIWidgetBase)
    -- 一期只有中墙有藏品，先不让玩家切换左右镜头
    self._wtSwitchBtnPanel:Collapsed()
    self._wtEquipBtnPanel = self:Wnd("DFCanvas_Btn", UIWidgetBase)
    self._wtEquipBtnPanel:Collapsed()

    -- ui界面控件
    self._wtItemDetailPanel = self:Wnd("wtItemDetailPanel", UIWidgetBase)
    self._wtItemDetailPanel:Collapsed()
    self._wtCabinetValueTxt = self:Wnd("wtCabinetValueTxt", UITextBlock)
    self._wtValueTxt = self:Wnd("wtValueTxt", UIText)
    self._wtWaterFall = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_108", self._OnGetListCount, self._OnProcessCabinetListWidget)

    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtItemNameTxt = self:Wnd("wtItemName", UITextBlock)
    self._wtCabinetLevelTxt = self:Wnd("wtcabinetLevel", UITextBlock)
    self._wtItemNumTxt = self:Wnd("wtItemNum", UITextBlock)
    self._wtItmeDesc = self:Wnd("wtItemDesc", UITextblock)
    self._wtUpgradeBtn = self:Wnd("WBP_CommonButtonV3S1_91", DFCommonButtonOnly)
    self._wtUpgradeBtn:Event("OnClicked", self.OpenUpgradePanel, self)
    self._wtEquipBtn = self:Wnd("wtCommonButtonV1S1_1", DFCommonButtonOnly)
    self._wtUnEquipBtn = self:Wnd("wtCommonButtonV1S2_1", DFCommonButtonOnly)
    self._wtCanPutInTypeTxt = self:Wnd("DFTextBlock_136", UITextBlock)

    self._curCabinetType = EShowCabinetType.None
    self._curFocusGrid = nil
    self._redDotInsMap = setmetatable({}, weakmeta_key)

    self._wtButton = self:Wnd("DFMButton_216", DFButtonOnly)
    self._wtButton:SetCursor(EMouseCursor.Default)
    if IsHD() then
        self._wtButton:Collapsed()
    end

    self._displayInputHandles = {}

    self:_ResetVariables()
end

function CollectionRoomDisplaySpecialCabinetPanel:_ResetVariables()
    ---@type boolean 当前界面是收藏柜还是珍藏柜
    self._bIsDisplayOrSpecialCabinet = false
    ---@type number 当前使用的收藏柜相机ID
    self._cameraId = 2
    ---@type string 当前使用的DisplayType
    self._curUsedDisplayType = nil
end

function CollectionRoomDisplaySpecialCabinetPanel:OnShowBegin()
    Module.CommonBar:BindPersistentBackHandler(self._OnBackBtnClicked, self)
    if self._curFocusGrid == nil then
        self:RefreshPanel()
    elseif self._curUsedDisplayType then
        CollectionRoomLogic.CallCtrlFunction("SetDisplayType", self._curUsedDisplayType, false, CollectionRoomLogic.CallCtrlFunction("GetDisplayType") == "None")
    end

    self:_EnableGamePadFeature()
end


function CollectionRoomDisplaySpecialCabinetPanel:OnHideBegin()
    Module.CommonBar:BindPersistentBackHandler()
    self:_DisableGamePadFeature()
end

function CollectionRoomDisplaySpecialCabinetPanel:OnOpen()
    self:AddLuaEvent(Server.CollectionRoomServer.Events.evtCollectionRoomCabinetGridChange, self._CanbinetGridChange, self)
end

function CollectionRoomDisplaySpecialCabinetPanel:OnShow()
    self:AddLuaEvent(CollectionRoomConfig.Events.evtFocusDisplayOrSpecialCabinetGrid, self._OnFocusDisplayOrSpecialCabinetGrid, self)
    if IsHD() then
        local firestItem = self._wtWaterFall:GetItemByIndex(1)
        WidgetUtil.SetUserFocusToWidget(firestItem,true)
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:OnHide()
    self._curUsedDisplayType = nil
    if not Facade.UIManager:GetStackUIByUINavId(UIName2ID.CollectionRoomDisplaySpecialCabinetPanel) then
        self:LeaveFocusMode()
    else
        self._curUsedDisplayType = CollectionRoomLogic.CallCtrlFunction("GetDisplayType")
    end
    self._cameraId = 2
    self:_HideSwitchCabinetSide()
    self:UnRegisterDynamicReddot()
end

function CollectionRoomDisplaySpecialCabinetPanel:OnClose()
    self:RemoveAllLuaEvent()
end

---@param bIsDisplayCabinet boolean 是收藏柜/珍藏柜
function CollectionRoomDisplaySpecialCabinetPanel:OnInitExtraData(bIsDisplayCabinet)
    self._bIsDisplayOrSpecialCabinet = bIsDisplayCabinet
    self._curCabinetType = bIsDisplayCabinet and EShowCabinetType.Display or EShowCabinetType.Special
    if bIsDisplayCabinet then
        self:_SetDisplayCabinetCamera(2)
        --self._wtSwitchBtnPanel:SelfHitTestInvisible()
    else
        CollectionRoomLogic.SetViewTargetToNamedCamera(CollectionRoomConfig.CameraConfig.SpecialCabinet.View)
        --self._wtSwitchBtnPanel:Collapsed()
    end
end

---@desc 从收藏柜切换到珍藏柜,或者从珍藏柜切换到收藏柜
function CollectionRoomDisplaySpecialCabinetPanel:SwitchCabinetType(toCabinetType)
    self:_ResetVariables()
    self:OnInitExtraData(toCabinetType == EShowCabinetType.Display)
    self:RefreshPanel()
end

-----------------------------------------------------------------------
--region Camera Func

function CollectionRoomDisplaySpecialCabinetPanel:_OnFocusDisplayOrSpecialCabinetGrid(cabinetType, gridId)
    self:_SetCameraFocusGrid(cabinetType, gridId)
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnLeftBtnClicked()
    if self._cameraId > 1 then
        self:_SetDisplayCabinetCamera(self._cameraId - 1)
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnRightBtnClicked()
    if self._cameraId < #CollectionRoomConfig.CameraConfig.DisplayCabinet.DisplayType then
        self:_SetDisplayCabinetCamera(self._cameraId + 1)
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnCameraBtnClicked(cameraId)
    self:_SetDisplayCabinetCamera(cameraId)
end

function CollectionRoomDisplaySpecialCabinetPanel:_SetDisplayCabinetCamera(cameraId)
    self._cameraId = cameraId
    for index, btn in ipairs(self._wtCameraBtnList) do
        if index == cameraId then
            btn:SetStyle(1)
        else
            btn:SetStyle(0)
        end
    end
    if cameraId == 1 then
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(false)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(true)
    elseif cameraId == #self._wtCameraBtnList then
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(true)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(false)
    else
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(true)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(true)
    end
    CollectionRoomLogic.SetViewTargetToNamedCamera(CollectionRoomConfig.CameraConfig.DisplayCabinet.DisplayType[cameraId].View)
    self:LeaveFocusMode()
end

function CollectionRoomDisplaySpecialCabinetPanel:_SetCameraFocusGrid(cabinetType, gridId)
    CollectionRoomLogic.FocusOnDisplaySpecialCabinetGrid(cabinetType, gridId)
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnSwitchCabinetBtnClicked()
    if not self._switchCabinetHandle then
        ---@param uiIns CollectionRoomSwitchCabinet
        local function fLoadFinCallback(uiIns)
            self._wtSwitchCabinetBtn:Collapsed()
            uiIns:SetParentAndCurrentCabinet(self, self._bIsDisplayOrSpecialCabinet
                    and EShowCabinetType.Display or EShowCabinetType.Special, 1)
            uiIns:RefreshEntrancesList()
        end
        self._switchCabinetHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomSwitchCabinet, fLoadFinCallback)
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_HideSwitchCabinetSide()
    if self._switchCabinetHandle then
        Facade.UIManager:CloseUIByHandle(self._switchCabinetHandle)
        self._switchCabinetHandle = nil
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:OnSwitchCabinetHide()
    self._wtSwitchCabinetBtn:SelfHitTestInvisible()
    self._switchCabinetHandle = nil
end

function CollectionRoomDisplaySpecialCabinetPanel:_EnterRoomBtnClicked()
    CollectionRoomLogic.EnterCollectionRoom()
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private Func

-- 放入和卸下按钮点击
function CollectionRoomDisplaySpecialCabinetPanel:_PutDownBtnClicked()
    -- 根据格子中是否有道具来判断室放入还是卸下
    local itemPropInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, self._curFocusGrid)
    self._wtEquipBtnPanel:Visible()
    self._wtEquipBtn:Collapsed()
    self._wtUnEquipBtn:Collapsed()
    self._wtEquipBtn:RemoveEvent("OnClicked")
    self._wtUnEquipBtn:RemoveEvent("OnClicked")

    if itemPropInfo then
        -- 卸下
        self._wtUnEquipBtn:Visible()
        self._wtUnEquipBtn:SetMainTitle(Module.Inventory.Config.Loc.SelectionUnEquipBtnText)
        self._wtUnEquipBtn:Event("OnClicked", self.UnEquipCollectionFunc, self)
    else
        if not self._curFocusGridItem then
            loginfo("[CollectionRoomDisplaySpecialCabinetPanel:_PutDownBtnClicked] self._curFocusGridItem is nil")
            return
        end
        if self._FocusGridItemNumInWH <= 0 then
            self._wtEquipBtnPanel:Collapsed()
            return
        end
        self._wtEquipBtn:Visible()
        self._wtEquipBtn:SetMainTitle(Module.ArmedForce.Config.Loc.PutInText)
        self._wtEquipBtn:Event("OnClicked", self.PlaceCollectionFunc, self)
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:PlaceCollectionFunc()
    if not self._curFocusGridItem then
        loginfo("[CollectionRoomDisplaySpecialCabinetPanel:PlaceCollectionFunc] self._curFocusGridItem is nil")
        return
    end
    CollectionRoomLogic.PlaceCollectionByItemID(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, self._curFocusGrid, self._curFocusGridItem.id)
end

function CollectionRoomDisplaySpecialCabinetPanel:UnEquipCollectionFunc()
    CollectionRoomLogic.UnEquipItemAnyWhere(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, self._curFocusGrid, self._curFocusGridItem)
end

-- 当选中某个道具时，镜头移至展柜中该道具的格子除，并对当前界面做一些ui的显隐操作
function CollectionRoomDisplaySpecialCabinetPanel:_FocusItemGrid(bFocus)
    if bFocus then
        --self._wtSwitchBtnPanel:Collapsed()
        self._wtSwitchCabinetBtn:Collapsed()
    else
        --self._wtSwitchBtnPanel:Visible()
        self._wtSwitchCabinetBtn:Visible()
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_InitItemDetailPanel(itemInfo, gridID)
    if not itemInfo then return end

    self:_SetItemQualityIcon(itemInfo)
    self._wtItemNameTxt:SetText(itemInfo.name)
    -- 根据gridID获取展位等级，根据itemID获取仓库拥有数量和道具的详细信息
    local gridLevel = Server.CollectionRoomServer:GetCabinetGridLevel(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, gridID)
    gridLevel = gridLevel or 0
    self._wtCabinetLevelTxt:SetText(string.format(CollectionRoomConfig.Loc.CabinetLevel, gridLevel))

    self._FocusGridItemNumInWH = CollectionRoomLogic.GetCollectionItemsNum(itemInfo.id, true)
    self._wtItemNumTxt:SetText(string.format(CollectionRoomConfig.Loc.UnBindNum, self._FocusGridItemNumInWH))
    self._wtItmeDesc:SetText(itemInfo.description)

    self._wtItemDetailPanel:SelfHitTestInvisible()
end

function CollectionRoomDisplaySpecialCabinetPanel:_SetItemQualityIcon(itemInfo)
    self:BpSetQuality(itemInfo.quality)
	local qualityColor = ItemConfigTool.GetItemQualityLinearColor(itemInfo.quality)
	self._wtQualityIcon:SetColorAndOpacity(qualityColor)
end

function CollectionRoomDisplaySpecialCabinetPanel:_CanbinetGridChange(cabinetGridDiffType, cabinetType, cabinetId, gridId, bFraming)
    self:InitPriceTxt()
    -- self:_InitItemDetailPanel(self._curFocusGridItem, self._curFocusGrid)
    self:SelectedItemToggle(self._curFocusGridItem, cabinetType, self._curFocusGrid)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region InitWidget

function CollectionRoomDisplaySpecialCabinetPanel:_OnGetListCount()
    return CollectionRoomLogic.GetItemCapacityByCabinet(self._curCabinetType, nil)
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnProcessCabinetListWidget(position, itemWidget)
    local itemId = CollectionRoomLogic.GetItemInfoByCabinetType(self._curCabinetType, position)
    itemWidget:RefreshItem(itemId, self._curCabinetType, position)
    itemWidget:SetSelected(self._curFocusGridItem, self._curFocusGrid)

    local showReddot = nil
    local fCheckReddot = function()
        -- 是否放入
        local itemPropInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, position)
        if itemPropInfo or CollectionRoomLogic.IsClickRedPoint(itemId, true) then
            return false
        else
            -- local bindCondition = function (caller, item)
            --     return item.id == tonumber(itemId) and not item:CheckIsBind()
            -- end
            -- local itemNumInWH = Server.InventoryServer:GetItemNumByCondition(CreateCallBack(bindCondition, self))
            local itemNumInWH = CollectionRoomLogic.GetCollectionItemsNum(tonumber(itemId), true)

            showReddot = itemNumInWH > 0
            return itemNumInWH > 0
        end
    end
    if isvalid(self._redDotInsMap[itemWidget]) then
        Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._redDotInsMap[itemWidget], fCheckReddot, nil)
    else
        -- {self._curCabinetType, position}
        self._redDotInsMap[itemWidget] =
            Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, fCheckReddot, nil, itemWidget, {EReddotType.Normal})
    end
    itemWidget:ShowRedDotAlready(showReddot, true)
    itemWidget:BindOnClickedFunc(self.SelectedItemToggle, self)
end

function CollectionRoomDisplaySpecialCabinetPanel:RefreshPanel()
    if not IsHD() then
        if self._curCabinetType == EShowCabinetType.Display then
            Module.CommonBar:RegStackUITopBarTitle(self, CollectionRoomConfig.Loc.DisplayCabinetName)
        elseif self._curCabinetType == EShowCabinetType.Special then
            Module.CommonBar:RegStackUITopBarTitle(self, CollectionRoomConfig.Loc.SpecialCabinetName)
        end
    end
    self._wtWaterFall:RefreshAllItems()
    self:InitPriceTxt()
    self:_ResetUIAndCamera()
    self._wtSwitchCabinetBtn:SelfHitTestInvisible()
end

-- 重置摄像机镜头和ui回初始
function CollectionRoomDisplaySpecialCabinetPanel:_ResetUIAndCamera()
    self._wtItemDetailPanel:Collapsed()
    self._wtEquipBtnPanel:Collapsed()
    self._wtSwitchCabinetBtn:SelfHitTestInvisible()
    if self._curCabinetType == EShowCabinetType.Display then
        local cameraId = self._cameraId
        self:_SetDisplayCabinetCamera(cameraId ~= 0 and cameraId or 2)
        self._cameraId = 2
        --self._wtSwitchBtnPanel:SelfHitTestInvisible()
    elseif self._curCabinetType == EShowCabinetType.Special then
        CollectionRoomLogic.SetViewTargetToNamedCamera(CollectionRoomConfig.CameraConfig.SpecialCabinet.View)
        --self._wtSwitchBtnPanel:Collapsed()
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:InitPriceTxt()
    local curCabinetId = CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1
    local price = 0
    local cabinetTitle = ""
    if self._curCabinetType == EShowCabinetType.Special then
        cabinetTitle = CollectionRoomConfig.Loc.SpecialCabinetValue
        price = Server.CollectionRoomServer:GetSpecialCabinetPrice(curCabinetId)
    elseif self._curCabinetType == EShowCabinetType.Display then
        cabinetTitle = CollectionRoomConfig.Loc.DisplayCabinetValue
        -- price = Server.CollectionRoomServer:GetDisplayCabinetPrice(curCabinetId)
        price = CollectionRoomLogic.GetDisplayCabinetValue(curCabinetId)
    end
    local priceTxt = MathUtil.GetNumberFormatStr(price)
    self._wtValueTxt:SetText(priceTxt)
    self._wtCabinetValueTxt:SetText(cabinetTitle)
    self._wtCanPutInTypeTxt:SetText(CollectionRoomConfig.Loc.CanPutInTypeTxt)
end

function CollectionRoomDisplaySpecialCabinetPanel:SelectedItemToggle(itemInfo, cabinetType, gridID)
    self._curFocusGrid = gridID
    self._curFocusGridItem = itemInfo
    self._curCabinetType = cabinetType
    -- 拉近镜头需要把切换视角面板隐藏
    self:_SetCameraFocusGrid(self._curCabinetType, gridID)
    --self._wtSwitchBtnPanel:Collapsed()
    self:_InitItemDetailPanel(itemInfo, gridID)
    self:_FocusItemGrid(true)
    self:_PutDownBtnClicked()
    CollectionRoomConfig.Events.evtShowItemDetailPanel:Invoke(self._curFocusGridItem, self._curFocusGrid)
end

function CollectionRoomDisplaySpecialCabinetPanel:UnRegisterDynamicReddot()
    if not table.isempty(self._redDotInsMap) then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn)
        self._redDotInsMap = {}
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:LeaveFocusMode()
    CollectionRoomLogic.LeaveFocusMode()
    self._curFocusGrid = nil
    self._curFocusGridItem = nil
    CollectionRoomConfig.Events.evtUnSelectAllItems:Invoke()
end

function CollectionRoomDisplaySpecialCabinetPanel:_OnBackBtnClicked()
    if self._curFocusGrid then
        self:LeaveFocusMode()
        self:_ResetUIAndCamera()
    else
        Facade.UIManager:CloseUI(self)
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region UpgradePanel

function CollectionRoomDisplaySpecialCabinetPanel:OpenUpgradePanel()
    CollectionRoomLogic.ShowUpgradePanel(self._curCabinetType, self._curFocusGrid, self._curFocusGridItem)
    LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.ShowUpgradePanel)
end

--endregion
-----------------------------------------------------------------------

function CollectionRoomDisplaySpecialCabinetPanel:_EnableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RegisterNavGroup()

-- 添加快捷键映射
    self._wtEquipBtn:SetDisplayInputAction("CM_PutIn", true, nil, true)
    self._wtUnEquipBtn:SetDisplayInputAction("CM_UnEquip", true, nil, true)

    self:_BindDisplayAction()

end

function CollectionRoomDisplaySpecialCabinetPanel:_DisableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_UnRegisterNavGroup()
    self:_UnBindDisplayAction()
end

function CollectionRoomDisplaySpecialCabinetPanel:_RegisterNavGroup()
    if not IsHD() then
        return
    end
    -- 注册导航组
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterFall, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtWaterFall)
            self._navGroup:SetScrollRecipient(self._wtWaterFall)
            local wtSwitchCabinetBtnHD = self:Wnd("WBP_CommonButtonV3S1_1", DFButtonOnly)
            self._navGroup:AddNavWidgetToArray(wtSwitchCabinetBtnHD)
            self._navGroup:AddNavWidgetToArray(self._wtUpgradeBtn)
        end
    end

    -- if self._navGroup then
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    -- end
end

function CollectionRoomDisplaySpecialCabinetPanel:_UnRegisterNavGroup()
    if not IsHD() then
        return
    end
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_BindDisplayAction()
    if not IsHD() then
        return 
    end

    if self._displayInputHandles then
        self:_UnBindDisplayAction()
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    handle = inputMonitor:AddDisplayActionBinding("CM_PutIn", EInputEvent.IE_Pressed, self._FlipEquip, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- handle = inputMonitor:AddDisplayActionBinding("CM_UnEquip", EInputEvent.IE_Pressed, self.UnEquipCollectionFunc, self, EDisplayInputActionPriority.UI_Stack)
    -- table.insert(self._displayInputHandles, handle)

end

function CollectionRoomDisplaySpecialCabinetPanel:_UnBindDisplayAction()
    if not IsHD() then
        return 
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    if self._displayInputHandles then
        for i, handle in ipairs(self._displayInputHandles) do
            inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
        end
        self._displayInputHandles = {}
    end
end

function CollectionRoomDisplaySpecialCabinetPanel:_FlipEquip()
    local itemPropInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(self._curCabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, self._curFocusGrid)
    if itemPropInfo then
        self:UnEquipCollectionFunc()
    else
        self:PlaceCollectionFunc()
    end
end



return CollectionRoomDisplaySpecialCabinetPanel
