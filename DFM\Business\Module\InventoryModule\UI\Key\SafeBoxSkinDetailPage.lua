----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SafeBoxSkinDetailPage : LuaUIBaseWindow
local SafeBoxSkinDetailPage = ui("SafeBoxSkinDetailPage", require("DFM.YxFramework.Managers.UI.LuaUIBaseWindow"))

local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"


function SafeBoxSkinDetailPage:Ctor()
    self._BtnPanelBox = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
    self._BtnPanelBox:Collapsed()
    self._wtShowPresetHint = self:Wnd("wtShowPresetHint", UIWidgetBase)
    self._wtShowPresetHint:Collapsed()
    self._wtKeyIconBox = self:Wnd("WBP_CommonKeyIconBox", UIWidgetBase)
    if self._wtKeyIconBox then
        self._wtKeyIconBox:Collapsed()
    end
    self._wtShowPresetPartsCheckBox = self:Wnd("wtShowPresetPartsCheckBox", UIWidgetBase)
    self._wtShowPresetPartsCheckBox:Collapsed()

    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)

    
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {ECurrencyClientId.Special, ECurrencyClientId.Diamond})
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Collection.Config.Loc.WeaponSkinDetail)
end

-----------------------------------------------------------------------
--region live func

function SafeBoxSkinDetailPage:OnInitExtraData(item)
    self._item = item
end

---@overload fun(LuaUIBaseView, OnShow)
function SafeBoxSkinDetailPage:OnShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function SafeBoxSkinDetailPage:OnHide()
    self:RemoveAllLuaEvent()
end


function SafeBoxSkinDetailPage:OnShowBegin()
    self:_OnRefreshItemDetail()
    self:_AddListeners()

    local summaryList = {}
    table.insert(summaryList, {actionName = "ViewAction", func = self._EnterPreviewPanel, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList)
end


function SafeBoxSkinDetailPage:OnHideBegin()

end

--endregion
-----------------------------------------------------------------------

function SafeBoxSkinDetailPage:_AddListeners()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self.OnSceneSubLevelLoaded, self)
end

function SafeBoxSkinDetailPage:_OnRefreshItemDetail()
    self._wtItemDetailView:UpdateItem(self._item)
end

function SafeBoxSkinDetailPage:OnSceneSubLevelLoaded(curSubStageType)
    if curSubStageType ~= ESubStage.HallMall then
        return
    end
    self:_OnRefreshModel(curSubStageType, self._item)
end

function SafeBoxSkinDetailPage:_OnRefreshModel(curSubStageType, item)
    if not curSubStageType or curSubStageType == ESubStage.HallMall then
        --设置背景
        Facade.HallSceneManager:SetDisplayBackground(1, false)
        if isvalid(item) then
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "SafeBox")
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayItem", self._item.id)
        end
    end
end

function SafeBoxSkinDetailPage:_EnterPreviewPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxSkinVideoPanel, nil, nil, self._item)
end

return SafeBoxSkinDetailPage