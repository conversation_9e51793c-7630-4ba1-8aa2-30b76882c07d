----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------

local function log(...)
    loginfo("[SafeBoxAndKeyChainMain]", ...)
end

local SafeBoxAndKeyChainMain = class("SafeBoxAndKeyChainMain", LuaUIBaseView)

local InventoryConfig        = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool          = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemHelperTool         = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool         = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local InventoryLogic         = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local EDepartmentType        = Module.Shop.Config.EDepartmentType
local CommonWidgetConfig     = Module.CommonWidget.Config
local EComp                  = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos             = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder           = CommonWidgetConfig.EIVCompOrder

function SafeBoxAndKeyChainMain:Ctor()
    self._wtItemDetailView = self:Wnd("wtItemDetailView", UIWidgetBase)
    self._wtSafeBoxAndKeyChainItem = self:Wnd("wtWeaponSkinScrollView", UIWidgetBase)
    self._wtSafeBpxAndKeyChainList = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinScrollView", self._OnGetItemCount, self._OnProcessItemWidget)

    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtIconTips = self:Wnd("DFImage_86", UIImage)
    self._wtQuestionHintBox:Visible()
    self._wtQuestionHintText:Visible()
    self._wtIconTips:Collapsed()

    -- 按钮
    self._wtApplyBtn = self:Wnd("wtApplyBtn", DFCommonButtonOnly)
    self._wtDetailBtn = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)

    self._wtBgIcon = self:Wnd("Image_Bg", DFCDNImage)
    self._wtItemIcon = self:Wnd("Image_Icon", DFCDNImage)
    self._wtTransformWidget = self:Wnd("DFScaleBox_0", UIWidgetBase)

    self._wtItemWidgetTable = {}
end

-----------------------------------------------------------------------
--region Life function

function SafeBoxAndKeyChainMain:OnInitExtraData(type, item)
    self._type = type
    self._alreadySelectedItem = item
    self._bUsingItem = item

    if not IsHD() then
        Module.CommonBar:RegStackUITopBarTitle(self, InventoryConfig.SlotNameMapping[ESlotType.SafeBox])
    end
end

function SafeBoxAndKeyChainMain:OnOpen()
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.Normal
end

function SafeBoxAndKeyChainMain:OnShowBegin()
    if self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal then
        self:_GetSafeBoxAndKeyChainInfo()
    else
        self:_GotoCustomizeFunc()
    end

    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

function SafeBoxAndKeyChainMain:OnShow()
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionUsePropSucess, self.RefreshWidget, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtEquipSafeBoxSkin, self.RefreshSkinWidget, self)
end

function SafeBoxAndKeyChainMain:OnHide()
    self:RemoveAllLuaEvent()
end

function SafeBoxAndKeyChainMain:OnClose()
    self._wtItemWidgetTable = {}
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private function

function SafeBoxAndKeyChainMain:_OnGetItemCount()
    return self._groupItems and #self._groupItems or 0
end

function SafeBoxAndKeyChainMain:_OnProcessItemWidget(position, itemWidget)
    self._wtItemWidgetTable[position] = itemWidget
    itemWidget:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    itemWidget:InitItem(self._groupItems[position])
    -- 网吧特权需要增加角标
    local equipmentFeature = self._groupItems[position] and self._groupItems[position]:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and equipmentFeature:IsNetBarSafeBox() then
        local rightComponent = itemWidget:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight)
        rightComponent:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_Cybercafe.CommonHall_Icon_Cybercafe'")
        rightComponent:EnableComponent(true)
    else
        itemWidget:EnableComponent(EComp.BottomRightIconText, false)
    end
    local itemNameComp = itemWidget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:RefreshComponent()
    local fClickCb = CreateCallBack(self._SafeBoxItemClicked, self, itemWidget, position)
    itemWidget:BindCustomOnClicked(fClickCb)
    if self._alreadySelectedItem.id % 10 == itemWidget.item.id % 10 then
        self:_SafeBoxItemClicked(itemWidget, position)
        self._selectedCell = itemWidget
    end
end

function SafeBoxAndKeyChainMain:_SafeBoxItemClicked(itemWidget, position)
    if self._selectedPos ~= position then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._alreadySelectedItem, false)
        end
        itemWidget:SetSelected(itemWidget.item, true)
        self._selectedCell = itemWidget
        self._selectedPos = position
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
        self._alreadySelectedItem = itemWidget.item
        self:_RefreshInfoPanel(itemWidget.item)
    end
end

-- 刷新面板
function SafeBoxAndKeyChainMain:_RefreshInfoPanel(item)
    self:InitItemDetailPanel(item)
    if self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal then
        self:_RefreshBtnPanel(item)
    else
        self:_RefreshSkinPanel(item)
    end
    self:_SetBackGroundImg(true, item.id)
end

-- 刷新安全箱面板
function SafeBoxAndKeyChainMain:_RefreshBtnPanel(item)
    local itemFeature = item:GetFeature(EFeatureType.Equipment)

    local cardsTable = ItemConfigTool.FindPermissionActivateCard(item.id)
    -- 获取体验卡道具
    for _, cardID in ipairs(cardsTable) do
        self.activateCard = Server.CollectionServer:GetCollectionItemById(cardID)
        if self.activateCard then
            break
        end
    end

    -- 只有顶级安全箱才有自定义功能
    local itemIdLast = tonumber(self._alreadySelectedItem.id) % 10
    if itemIdLast == 4 then
        self._wtDetailBtn:Visible()
        self._wtDetailBtn:RemoveEvent("OnClicked")
        self._wtDetailBtn:SetMainTitle(InventoryConfig.Loc.CustomizeText)
        self._wtDetailBtn:Event("OnClicked", self._GotoCustomizeFunc, self)
    else
        self._wtDetailBtn:Collapsed()
    end

    self._wtApplyBtn:RemoveEvent("OnClicked")
    if itemFeature:PermissionCanUse() then
        -- 免费和未过期
        local bUsing = self._bUsingItem.id == self._alreadySelectedItem.id

        local timeStr
        if itemFeature:PermissionItemIsFree() then
            timeStr = InventoryConfig.Loc.PermanentText
        else
            if itemFeature:IsNetBarSafeBox() then
                timeStr = InventoryConfig.Loc.NetBarSafeBoxText
            else
                timeStr = InventoryLogic.GetRemainTime(itemFeature._expireTimes)
            end
        end
        local timeStr = string.format(InventoryConfig.Loc.UseTimeText, timeStr)
        self._wtQuestionHintText:SetText(timeStr)
        self._wtApplyBtn:SetBtnEnable(true and not bUsing)
        self._wtApplyBtn:SetMainTitle(bUsing and InventoryConfig.Loc.ItemUsedText or InventoryConfig.Loc.UseContainerText)
        self._wtApplyBtn:Event("OnClicked", self._OnUseButtonClick, self)
    elseif not self.activateCard and not itemFeature:PermissionCanUse() then
        -- 无权限卡且无权限
        self._wtApplyBtn:SetBtnEnable(true)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.GotoGetText)
        self._wtApplyBtn:Event("OnClicked", self._OnRenewalBtnClick, self)

        -- 高级安全箱和顶级安全箱需要显示获取途径
        local getWayTxt
        if Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleBattlePass) then
            local curSeason = Server.BattlePassServer:GetSeasonNum()
            if itemIdLast == 4 then
                getWayTxt = string.format(InventoryConfig.Loc.GetSafeBoxWayTxt_1, curSeason)
            else
                getWayTxt = string.format(InventoryConfig.Loc.GetSafeBoxWayTxt_2, curSeason)
            end
        else
            getWayTxt = Module.Inventory.Config.Loc.BPLockTxt
        end
        self._wtQuestionHintText:SetText(getWayTxt)
    else
        local activateCardTxt = string.format(InventoryConfig.Loc.ActivateCardNum, self.activateCard.num)
        self._wtQuestionHintText:SetText(activateCardTxt)

        self._wtApplyBtn:SetBtnEnable(true)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.ActivatePermissions)
        self._wtApplyBtn:Event("OnClicked", self._OnUseActivateCardFunc, self)
    end
end

-- 刷新安全箱皮肤面板
function SafeBoxAndKeyChainMain:_RefreshSkinPanel(item)
    local equipedSkinId = Server.CollectionServer.safeboxInfoEquiped
    self._wtQuestionHintText:Collapsed()
    self._wtApplyBtn:RemoveEvent("OnClicked")

    local itemFeature = item:GetFeature(EFeatureType.Equipment)
    -- local isDefaultSkin = itemFeature and itemFeature:IsSafeBox()
    local isDefaultSkin = item.id == "33010010001"

    if (equipedSkinId and equipedSkinId == item.id) or (equipedSkinId == 0 and isDefaultSkin) then
        self._wtApplyBtn:SetBtnEnable(false)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.ItemUsedText)
    else
        -- 判断当前安全箱外观是否已拥有, 默认外观允许安装
        if self:_IsHaveSafeBoxSkin(item.id) or isDefaultSkin then
            self._wtApplyBtn:SetBtnEnable(true)
            self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.UseContainerText)
            self._wtApplyBtn:Event("OnClicked", self.UseSafeBoxSkinBtn, self)
        else
            self._wtApplyBtn:SetBtnEnable(false)
            self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.RepairText)
        end
    end
    self._wtDetailBtn:SetMainTitle(InventoryConfig.Loc.DetailCheckTxt)
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OnDetailBtnClick, self)
end

function SafeBoxAndKeyChainMain:_SetBackGroundImg(bShow, propId)
    Module.Collection:SetBackgroundImgByPropId({self._wtBgIcon, self._wtItemIcon}, bShow, Module.CDNIcon.Config.ECdnTagEnum.Collection, nil, propId, self._wtTransformWidget)

    --[[
    local gameItemRow = ItemConfigTool.GetItemConfigById(propId)
    local CDNImagePaths, imageTransforms
    if gameItemRow then
        CDNImagePaths = gameItemRow.Pictures
        imageTransforms = imageTransforms or gameItemRow.PictureTransforms
    end
    if bShow then
        local eCdnTagEnum = Module.CDNIcon.Config.ECdnTagEnum.SafeBox
        local bgCDNPathPrefix = self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal and "Resource/Texture/Collection/MS24/" or "Resource/Texture/Safebox/"
        -- local bgCDNPathPrefix = "Resource/Texture/Safebox/"
        local itemCDNPathPrefix = (self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal or tostring(propId) == "33010010001") and "Resource/Texture/Collection/MS24/" or "Resource/Texture/Safebox/"
        self._wtBgIcon:SetCDNImage(bgCDNPathPrefix..CDNImagePaths[1], false, eCdnTagEnum)
        self._wtBgIcon:SelfHitTestInvisible()

        self._wtItemIcon:SetCDNImage(itemCDNPathPrefix..CDNImagePaths[2], false, eCdnTagEnum)
        self._wtItemIcon:SelfHitTestInvisible()

        if imageTransforms ~= nil and #imageTransforms > 0 then
            if imageTransforms[1] ~= nil then
                if imageTransforms[1].Translation then
                    self._wtTransformWidget:SetRenderTranslation(imageTransforms[1].Translation)
                end
                if imageTransforms[1].Angle then
                    self._wtTransformWidget:SetRenderTransformAngle(imageTransforms[1].Angle)
                end
            end
        else
            self._wtTransformWidget:SetRenderTranslation(FVector2D(0, 0))
            self._wtTransformWidget:SetRenderTransformAngle(0)
        end
    else
        self._wtBgIcon:Collapsed()
        self._wtItemIcon:Collapsed()
        self._wtTransformWidget:Collapsed()
    end
    ]]--
end

-- 使用按钮
function SafeBoxAndKeyChainMain:_OnUseButtonClick()
    log("_OnUseButtonClick")
    ItemOperaTool.DoPlacePermissionItem(self._alreadySelectedItem, self._type)
    self:_OnCloseBtnClick()
end

-- 前往获取按钮
function SafeBoxAndKeyChainMain:_OnRenewalBtnClick()
    log("_OnUseButtonClick")
    -- 根据不同的扩容箱判断前往获取按钮应该跳转到何处
    local equipmentFeature = self._alreadySelectedItem:GetFeature(EFeatureType.Equipment)
    local isSafeBox = equipmentFeature and equipmentFeature:IsSafeBox()
    local isKeyChain = equipmentFeature and equipmentFeature:IsKeyChain()
    local itemIdLast = tonumber(self._alreadySelectedItem.id) % 10
    if isKeyChain then
        if itemIdLast == 2 then
            -- 跳转至部门-任务
            if IsHD() then
                Module.Quest:Jump()
            else
                Module.Shop:OpenChooseDepartmentView(EDepartmentType.Quest)
            end
        elseif itemIdLast == 3 or itemIdLast == 4 then
            -- 跳转至部门-军需处
            Module.Shop:ShowChooseMerchantPanel()
        elseif itemIdLast == 5 then
            if Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleRankSOL) then
                Module.Ranking:ShowMainPanel()
            else
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.RankingSOLUnLockTxt)
            end
        end
    elseif isSafeBox then
        if itemIdLast == 4 then
            -- 部门-任务
            if IsHD() then
                Module.Quest:Jump()
            else
                Module.Shop:OpenChooseDepartmentView(EDepartmentType.Quest)
            end
        else
            -- 通行证界面
            if Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleBattlePass) then
                Module.BattlePass:ShowBattleProcess(UIName2ID.BattlePassMain)
            else
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.BPLockTxt)
            end
        end
    end
    -- self:_OnCloseBtnClick()
end

-- 激活权限
function SafeBoxAndKeyChainMain:_OnUseActivateCardFunc()
    -- local function afterUseActivateCardFunc()
    --     self:RefreshWidget()
    -- end
    -- if self.activateCard then
    --     Server.CollectionServer:DoUseMultiItems({self.activateCard}, {1}, {}, {}, afterUseActivateCardFunc)
    -- end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionActivationCard, nil, self, self.activateCard)
end

-- 前往自定义界面
function SafeBoxAndKeyChainMain:_GotoCustomizeFunc()
    -- 先将原本的groupItems进行替换
    self:_GetSafeBoxSkinInfo()
    local equipSafeBoxSkinID = Server.CollectionServer.safeboxInfoEquiped
    if equipSafeBoxSkinID and equipSafeBoxSkinID ~= 0 then
        for _, skinItem in ipairs(self._groupItems) do
            if skinItem.id == equipSafeBoxSkinID then
                self._alreadySelectedItem = skinItem
            end
        end
    else
        -- 特殊外观，重组一下
        self._alreadySelectedItem = self._groupItems[1]
    end

    if not IsHD() then
        Module.CommonBar:ChangeBackBtnText(InventoryConfig.Loc.CustomizeText)
    end
    Module.CommonBar:BindBackHandler(self._OnTopBarBackBtnClicked, self)
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.SafeBoxSkin
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

-- 查看详情界面
function SafeBoxAndKeyChainMain:_OnDetailBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxSkinDetailPage, nil, self, self._alreadySelectedItem)
end

-- 使用外观接口
function SafeBoxAndKeyChainMain:UseSafeBoxSkinBtn()
    local skinId = self._alreadySelectedItem.id
    if ItemHelperTool.FindSafeBoxSkinByID(skinId) then
        Server.CollectionServer:EquipSafeBoxSkin(tonumber(skinId))
    else
        Server.CollectionServer:EquipSafeBoxSkin(0)
    end
end

function SafeBoxAndKeyChainMain:_OnCloseBtnClick()
    Facade.UIManager:CloseUI(self)
end

function SafeBoxAndKeyChainMain:_AddNoPermissionInfo(ownGroup, allItemsGroup)
    -- 1.赛季道具优先
    -- 2.每种类型的道具只能存在一个
    table.sort(ownGroup, function (a, b)
        return a.id > b.id
    end)
    local actTable = {}
    for key, itemInfo in ipairs(ownGroup) do
        if allItemsGroup[tostring(itemInfo.id)] then
            allItemsGroup[tostring(itemInfo.id)] = nil
        end
        local subStr = itemInfo.id % 10
        if not actTable[subStr] then
            -- 如果道具还有权限则加入，如果没权限则找下一个有权限的,如果都没权限则默认没激活
            local equipFeature = itemInfo:GetFeature(EFeatureType.Equipment)
            if equipFeature and equipFeature:PermissionCanUse() then
                actTable[subStr] = itemInfo
            end
        end
    end

    for key, itemInfo in pairs(allItemsGroup) do
        local type = tonumber(itemInfo.VirtualItemID) % 10
        if itemInfo and not actTable[type] then
            local newItem = ItemBase:New(tonumber(itemInfo.VirtualItemID))
            actTable[type] = newItem
        end
    end

    local function fsortfunc(a, b)
        local id_a = a.id % 10
        local id_b = b.id % 10
        return id_a < id_b
    end
    table.sort(actTable, fsortfunc)
    return actTable
end

function SafeBoxAndKeyChainMain:_AddSafeBoxSkinInfo(allSkinGroup, safeboxSkinList)
    for _, skinInfo in ipairs(safeboxSkinList) do
        local skinItem = ItemBase:New(skinInfo)
        table.insert(self._safeboxSkinList, skinItem)
    end
end

function SafeBoxAndKeyChainMain:_GetSafeBoxAndKeyChainInfo()
    local ownGroup = Server.InventoryServer:GetSafeBoxItems()
    local allItemsGroup = ItemHelperTool.FindPermissonArray(1)
    self._groupItems = self:_AddNoPermissionInfo(ownGroup, allItemsGroup)
end

function SafeBoxAndKeyChainMain:_GetSafeBoxSkinInfo()
    self._groupItems = ItemHelperTool.FindSafeBoxSkinData()
    table.insert(self._groupItems, 1, ItemBase:New("33010010001"))
end

function SafeBoxAndKeyChainMain:_OnTopBarBackBtnClicked()
    Module.CommonBar:BindBackHandler(nil)
    self:_GetSafeBoxAndKeyChainInfo()
    self._alreadySelectedItem = self._groupItems[4]
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.Normal
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

function SafeBoxAndKeyChainMain:_IsHaveSafeBoxSkin(itemID)
    local safeboxSkinList = Server.CollectionServer._propSafeBoxList
    for _, skinID in ipairs(safeboxSkinList) do
        if skinID == itemID then
            return true
        end
    end
end

--endregion
-----------------------------------------------------------------------

-- 激活之后重新刷新
function SafeBoxAndKeyChainMain:RefreshWidget()
    -- 需要重新刷新安全箱的数据并选中上一个选中的安全箱
    self:_GetSafeBoxAndKeyChainInfo()
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_SafeBoxItemClicked(self._wtItemWidgetTable[self._selectedPos], self._selectedPos)
end

-- 使用安全箱外观之后刷新
function SafeBoxAndKeyChainMain:RefreshSkinWidget()
    -- self:_SafeBoxItemClicked(self._wtItemWidgetTable[self._selectedPos], self._selectedPos)
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

-- 初始化详情页界面
function SafeBoxAndKeyChainMain:InitItemDetailPanel(item)
    -- local defaultSkinItem
    -- if self._isCustomizePanel == InventoryConfig.ESafeBoxType.SafeBoxSkin then
    --     local equipFeature = item:GetFeature(EFeatureType.Equipment)
    --     if equipFeature and equipFeature:IsSafeBox() then
    --         defaultSkinItem = ItemBase:New("33010010001")
    --     end
    -- end
    -- item = defaultSkinItem or item
    self._wtItemDetailView:UpdateItem(item, true)
end

return SafeBoxAndKeyChainMain