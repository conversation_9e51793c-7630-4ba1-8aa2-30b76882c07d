----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class VehicleLayoutMain
local CustomLayoutMainBase = require "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutMainBase"
local VehicleLayoutMain = class("VehicleLayoutMain",CustomLayoutMainBase)
local ClientVehicleSetting = import "ClientVehicleSetting"
local UClientSettingDelagate = import"ClientSettingDelagate"
local UMobileCustomLayoutDataCenter = import "MobileCustomLayoutDataCenter"
local EVehicleMode = import "EVehicleMode"
local EBattleVehicle = Module.SystemSetting.Config.EBattleVehicle
local ECustomVehicleType = import "ECustomVehicleType"

function VehicleLayoutMain:Ctor()
    self._clientVehicleSetting = ClientVehicleSetting.Get(GetWorld())
    --
    self._wtBtnRevoke = self:Wnd("Revoke", UIWidgetBase):Wnd("DFButton_24", UIButton)
    self._wtBtnRevoke:Event("OnClicked", self._OnRevokeBtnClick, self)
 
    self._wtBtnRestore = self:Wnd("Restore", UIWidgetBase):Wnd("DFButton_24", UIButton)
    self._wtBtnRestore:Event("OnClicked", self._OnRestoreBtnClick, self)
 
    self._wtBtnResetLayout = self:Wnd("ResetLayout", DFCommonButtonOnly)
    self._wtBtnResetLayout:Event("OnClicked", self._OnResetLayoutBtnClick, self)

    self._wtBtnStore = self:Wnd("Store", DFCommonButtonOnly)
    self._wtBtnStore:Event("OnClicked", self._OnStoreBtnClick, self)

    self._wtBtnManager = self:Wnd("Manager", DFCommonButtonOnly)
    self._wtBtnManager:Event("OnClicked", self._OnManagerBtnClick, self)
    self._wtTxtManager = self:Wnd("Manager", UIWidgetBase):Wnd("RichTextBlock_Common", UITextBlock)
 
    self._wtBtnQuit = self:Wnd("Quit", UIButton)
    self._wtBtnQuit:Event("OnClicked", self._OnQuitBtnClick, self)
 
    self._wtBtnBG = self:Wnd("ButtonBG", UIButton)
    self._wtBtnBG:Event("OnClicked", self._OnBGBtnClick, self)

    self._wtShareImage = self:Wnd("wtShareImage", UIButton)
    self._wtShare = self:Wnd("Share", UIButton)
    self._wtShare:Event("OnClicked", self.ShareCustomLayout, self)


    self._vehicleTxtTbl = {}
    self._wtSelectVehicleBtn = UIUtil.WndDropDownBox(self, "wtSelectVehicleBtn", self._ChangeVehicleDrop, self._vehicleTxtTbl, {}, 0)
    self._VehicleTable = Facade.TableManager:GetTable("Vehicle/VehicleDatatable")
 
    --挂载的按键属性调整菜单
    self._wtAttachPanel = self:Wnd("WBP_Custom_Edit",UIWidgetBase)

    --基础布局蓝图
    self._wtNamedSlotLayout = self:Wnd("NamedSlot_76",UIWidgetBase)

    --放背景
    self._wtNamedSlotBG = self:Wnd("NamedSlot",UIWidgetBase)

    --顶部菜单
    self._wtTopPanel = self:Wnd("CanvasPanel_3",UIWidgetBase)
    self._wtCheckBoxTop = self:Wnd("CheckBox_219",UICheckBox)
    self._wtCheckBoxTop:SetCallback(self._TopPanelChange, self)
    self._wtTxtLayoutName = self:Wnd("RichTextBlock",UITextBlock)
    self._wtTxtPlayerName = self:Wnd("RichTextBlock_101",UITextBlock)

    --当前布局名
    self._curLayoutName = "ButtonStyle"

    self._customVehicleType = ECustomVehicleType.None

    self._isSharedLayout =false

    self._custLayoutDCCDO = UMobileCustomLayoutDataCenter.Get(GetGameInstance())

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
end

function VehicleLayoutMain:OnInitExtraData(inBattleVehicle,controlMode)
    loginfo("VehicleLayoutMain:OnInitExtraData")
    self._battleVehicle = inBattleVehicle
    self._vehicleControlMode = controlMode
end

function VehicleLayoutMain:InitChangeVehicleDrop()
    self._vehicleTxtTbl = {}
    self._vehicleEnumTbl = {}
    table.insert(self._vehicleEnumTbl, 255)
    if self._customVehicleType == ECustomVehicleType.CustomGroundVehicle then
        table.insert(self._vehicleTxtTbl, Module.SystemSetting.Config.Loc.AllCustomGroundVehicleTxt)
    elseif self._customVehicleType == ECustomVehicleType.CustomAirVehicle then
        table.insert(self._vehicleTxtTbl, Module.SystemSetting.Config.Loc.AllCustomAirVehicleTxt)
    end
    --[[
    local vehicleTmpTbl = {}
    for index, vehicleInfo in pairs(self._VehicleTable) do
        if self._customVehicleType == vehicleInfo.CustomVehicleType then
            table.insert(vehicleTmpTbl, vehicleInfo)
        end
    end

    local function SortList(vehicleInfo1, vehicleInfo2)
        return vehicleInfo1.VehicleId < vehicleInfo2.VehicleId
    end
    table.sort(vehicleTmpTbl, SortList)

    for index, info in ipairs(vehicleTmpTbl) do
        table.insert(self._vehicleTxtTbl, info.VehicleName)
        table.insert(self._vehicleEnumTbl, info.CarType)
    end
    ]]
    UIUtil.InitDropDownBox(self._wtSelectVehicleBtn, self._vehicleTxtTbl, {}, 0)
end

function VehicleLayoutMain:_ChangeVehicleDrop(index)
    self._wtCustomLayout:OnVisibilityChangeByVehicleType(self._vehicleEnumTbl[index + 1])
end

function VehicleLayoutMain:OnOpen()
    loginfo("VehicleLayoutMain:OnOpen")
end

function VehicleLayoutMain:OnShow()
    loginfo("VehicleLayoutMain:OnShow")
    local function LoadLayout(uiNavID)

        Facade.UIManager:RemoveSubUIByParent(self, self._wtNamedSlotLayout)
        local weakUIIns ,instanceId = Facade.UIManager:AddSubUI(self, uiNavID, self._wtNamedSlotLayout,nil,self._curLayoutName)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            self._wtCustomLayout = newCell
            self._wtCustomLayout:SetAttachPanel(self._wtAttachPanel)
            self._wtCustomBaseLayout = self._wtCustomLayout:Wnd("WBP_CustomBaseTemplate",UIWidgetBase)
            if not self._battleVehicle then
                self:ChangeVehicleMode()
            end
            self:RefreshVisibleLayoutName()
        end
    end

    self:UpdateLayoutName(self._vehicleControlMode)
    self._custLayoutDCCDO:UpdateLayoutNameIncustom(self._curLayoutName)
    loginfo("VehicleLayoutMain curLayoutName : " .. self._curLayoutName)

    if not self._battleVehicle then
        self._customVehicleType = ECustomVehicleType.CustomGroundVehicle
        LoadLayout(UIName2ID.VehicleLayoutBasePanel)
    else
        if self._battleVehicle == EBattleVehicle.Tank then
            self._customVehicleType = ECustomVehicleType.CustomGroundVehicle
            LoadLayout(UIName2ID.VehicleLayoutTank)
        elseif self._battleVehicle == EBattleVehicle.WeaponVehicleButton then
            self._customVehicleType = ECustomVehicleType.CustomGroundVehicle
            LoadLayout(UIName2ID.VehicleLayoutWeaponVehicleButton)
        elseif self._battleVehicle == EBattleVehicle.Jet then
            self._customVehicleType = ECustomVehicleType.CustomAirVehicle
            LoadLayout(UIName2ID.VehicleLayoutJet)
        else
            self._customVehicleType = ECustomVehicleType.CustomAirVehicle
            LoadLayout(UIName2ID.VehicleLayoutHelicopter)
        end
    end
    self:InitChangeVehicleDrop()
    self._wtBtnStore:Collapsed()
    self:AddLuaEvent(Server.SystemSettingServer.Events.evtRecvSearchShareLayout, self._OnRecvSearchShareLayout, self)
    self:AddLuaEvent(Server.SystemSettingServer.Event.evtLoadSchemeData, self._OnRecvSchemeShareLayout, self)
    UClientSettingDelagate.Get(self).OnCusmtomButtonChange:Add(CreateCPlusCallBack(self._OnCusmtomButtonChange, self))
    --self:FetchBGLayoutPanel()

    Module.CommonBar:SetTopBarVisible(false)
end

--load基础布局当背景
function VehicleLayoutMain:FetchBGLayoutPanel()
    --[[
    self._wtNamedSlotBG:ClearChildren()
    local function onContentUICreated(uiIns)
        self._wtNamedSlotBG:AddChild(uiIns)
    end
    Facade.UIManager:AsyncCreateSubUI(UIName2ID.CustomLayoutBG, onContentUICreated, nil)
    ]]
end

function VehicleLayoutMain:UpdateLayoutName(vehicleControlMode)
    if not self._battleVehicle then
        local vehicleMode = setdefault(vehicleMode,self._clientVehicleSetting.VehicleMode)
        if vehicleMode == EVehicleMode.Button then
            self._curLayoutName = "ButtonStyle"
        elseif vehicleMode == EVehicleMode.Glider then
            self._curLayoutName = "GliderStyle"
        elseif vehicleMode == EVehicleMode.JoyStick then
            self._curLayoutName = "JoyStickStyle"
        end
    elseif vehicleControlMode then
        self._curLayoutName = vehicleControlMode
    else
        self._curLayoutName = self._battleVehicle
    end
end

function VehicleLayoutMain:ChangeVehicleMode(vehicleMode)
    vehicleMode = setdefault(vehicleMode,self._clientVehicleSetting.VehicleMode)
    self._wtCustomLayout:SwitchLayout(vehicleMode)
    self._wtAttachPanel:AttachDragBtnPress(nil)
end

function VehicleLayoutMain:_OnResetLayoutBtnClick()
    self._wtCustomLayout:ResetConfig(self._curLayoutName)
    self._wtAttachPanel:AttachDragBtnPress(nil)
    self._wtBtnStore:Visible()
    self._wtBtnManager:Collapsed()
    self._bIsChange = true
end

function VehicleLayoutMain:_OnStoreBtnClick()
    self._wtAttachPanel:AttachDragBtnPress(nil)
    self._wtCustomLayout:SaveConfig(self._curLayoutName)
    Server.SystemSettingServer:SendCustomLayoutInfo(self._curLayoutName)
    self._wtBtnStore:Collapsed()
    self._wtBtnManager:Visible()
    self._wtShare:Visible()
    self._wtShareImage:Visible()
    self._bIsChange = false
end

function VehicleLayoutMain:OnClose()
end

function VehicleLayoutMain:OnShowBegin()
    self:BindBackAction(self._OnBackBtnClick, self)
end

function VehicleLayoutMain:OnHideBegin()
    self:UnBindBackAction()
end

function VehicleLayoutMain:_OnBackBtnClick()
    Facade.UIManager:CloseUI(self)
end

return VehicleLayoutMain