----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------


local ReddotDotBase = require "DFM.Business.Module.ReddotTrieModule.UI.ReddotDotBase"
local ReddotTrieViewLogic = require "DFM.Business.Module.ReddotTrieModule.Logic.ReddotTrieViewLogic"

---@class ReddotDotNormal : LuaUIBaseView
local ReddotDotNormal = ui("ReddotDotNormal", ReddotDotBase)

function ReddotDotNormal:Ctor()
end

function ReddotDotNormal:OnInitExtraData(params)
    ReddotDotBase.OnInitExtraData(self, params)
    loginfo(self._reddotType == EReddotType.Normal, "ReddotDotNormal should not be "..ReddotTrieViewLogic.GetReddotTypeName(type))
    -- self:SetAnim()
end

function ReddotDotNormal:OnShowBegin()
    ReddotDotBase.OnShowBegin(self)
end

return ReddotDotNormal
