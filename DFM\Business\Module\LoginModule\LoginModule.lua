----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



local LoginModule = class("LoginModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local LoginLogic = require("DFM.Business.Module.LoginModule.LoginLogic")
local LoginInteractLogic = require("DFM.Business.Module.LoginModule.LoginInteractLogic")
local LoginEventLogic = require("DFM.Business.Module.LoginModule.Logic.LoginEventLogic")
local UIManager = Facade.UIManager
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local Json = JsonFactory.createJson()
local UDFMLocalizationManager = import("DFMLocalizationManager")
local LiteDownloadManager         = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
-- BEGIN MODIFICATION - VIRTUOS
local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
local UDFMOnlineIdentityProxy_CheckUserPrivilege = import "DFMOnlineIdentityProxy_CheckUserPrivilege"
-- END MODIFICATION - VIRTUOS

local GameLaunchReportTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.GameLaunchReportTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

local function log(...)
    loginfo("[LoginModule]", ...)
end

function LoginModule:OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.Login then
        LoginEventLogic.RemoveLoginListeners()
        -- [aidenliao]启动登录流程，开始loading进大厅
        local openid = Server.SDKInfoServer:GetOpenIdStr()
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Loading_LoginToModeHall_Start, openid, true, "")
    end

    if gameFlowType == EGameFlowStageType.LoginToModeHall then
        -- [aidenliao]启动登录流程，loading进大厅结束
        local openid = Server.SDKInfoServer:GetOpenIdStr()
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.Loading_LoginToModeHall_End, openid, true, "")
    end
end

function LoginModule:OnGameFlowChangeEnter(gameFlowType)
    loginfo("LoginModule:OnGameFlowChangeEnter", gameFlowType)
    -- if IsHD() then
    --     if gameFlowType == EGameFlowStageType.SafeHouse and not Server.AccountServer:GetPlayerRegisterState() then
    --         Module.Login:ShowRegisterView()
    --     end
    -- end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() then
        local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if gameFlowType == EGameFlowStageType.Login then
            identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)

        elseif gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.Intro then
            if not Server.TeamServer:IsInTeam() or Server.TeamServer:GetTeamNum() < 2 then
                identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
            end
    
        elseif gameFlowType == EGameFlowStageType.Game then
            local bIsTutorial = false
            local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem"
            local DFMIrisEnterSubsystem = UDFMIrisEnterSubsystem.Get(GetWorld())
            if DFMIrisEnterSubsystem then
                bIsTutorial = DFMIrisEnterSubsystem.IsTutorial
            end
            if not bIsTutorial then
                identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, true)
            end
    
        elseif gameFlowType == EGameFlowStageType.SafeHouse or gameFlowType == EGameFlowStageType.Lobby then
            if Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamNum() > 1 then
                identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, true)
            else
                identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
            end
            identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.CrossPlatform, Server.RoleInfoServer.hasCrossPlayPrivilege)
    
        elseif gameFlowType == EGameFlowStageType.LobbyBHD then
            identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
            identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.CrossPlatform, false)
        end
    end
    -- END MODIFICATION - VIRTUOS
    if gameFlowType == EGameFlowStageType.Login then
        LoginEventLogic.AddLoginListeners()
    end
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.LobbyBHD then
        Server.LocalizeServer:FetchServerData()
        local fOnGetStateInfo = function()
            local fURLCallbackIns = CreateCallBack(function(self, lastUrl)
                logerror("LoginModule:fURLCallbackIns, lastUrl:",lastUrl)
                -- 判断非异账号
                local UDFMGameLogin = import "DFMGameLogin"
                local loginIns = UDFMGameLogin.Get(GetGameInstance())
                if loginIns and loginIns:GetHandleGameCenter() then
                    Server.SDKInfoServer:SetLaunchForm(lastUrl)
                    local game_center = Server.SDKInfoServer:GetLaunchChannelId()
                    Server.AccountServer:ReportAccountLaunchId(game_center)
                    Server.SDKInfoServer:SetLaunchForm(0)
                end
                return Module.Team:JoinTeamFromMiniProgram(lastUrl)
            end,self)
            Module.DeepLink:AddDeepLinkCallback(fURLCallbackIns)
            local bInGame = Server.AccountServer:IsPlayerInGame()
            log("LoginModule:OnGameFlowChangeEnter, 模式大厅判断是否还在局内",bInGame)
            -- BEGIN MODIFICATION - VIRTUOS
            local onPlatformPrivilegeCheckCompleted = function()
                if IsConsole() then
                    -- 重新缓存平台权限
                    Server.ChatServer:OnPlatformPrivilegesInitOrReset()
                    -- 主机平台邀请在这里处理
                    local UDFMOnlineSessionManager = import("DFMOnlineSessionManager")
                    local DFMOnlineSessionManager = UDFMOnlineSessionManager.Get(GetWorld())
                    if DFMOnlineSessionManager then
                        DFMOnlineSessionManager:OnPlatformPrivilegesCheckCompleted()
                    end
                end
            -- END MODIFICATION - VIRTUOS
                if bInGame then
                    -- 登录时候，不需要弹窗直接开始连接, 这里建议
                    Module.Reconnect:OpenReconnectPanel()
                elseif Server.AccountServer:IsInTeam() then
                    Server.TeamServer.Events.evtTeamInfosUpdated:AddListener(LoginInteractLogic._OnTeamInfoUpdated)
                elseif Server.AccountServer:IsInPickHeroStage() then
                    logwarning("LoginModule:OnGameFlowChangeEnter, IsInPickHeroStage true")
                    local matchModeInfo = Server.MatchServer:GetMatchModeInfo()
                    local gameMode
                    if not table.isempty(matchModeInfo) then
                        logwarning("LoginModule:OnGameFlowChangeEnter matchModeInfo game_mode:", matchModeInfo.game_mode)
                        gameMode = matchModeInfo.game_mode
                    else
                        logerror("LoginModule:OnGameFlowChangeEnter no matchModeInfo")
                    end
                    if gameMode == MatchGameMode.WorldGameMode then
                        Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:Invoke()
                    elseif gameMode == MatchGameMode.TDMGameMode then
                        Module.IrisSafeHouse.Config.flowEvtToEnterLobby:Invoke()
                    elseif gameMode == MatchGameMode.BlackHawkDown then
                        Module.IrisSafeHouse.Config.flowEvtToEnterLobbyBHD:Invoke()
                    else
                        Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:Invoke()
                    end
                else
                    Module.Team:JoinTeamFromMiniProgram()
                end
            end
            -- BEGIN MODIFICATION - VIRTUOS
            if IsConsole() and self.Field.bHasCheckedPlatformMultiplayerPrivilege == false then
                -- 平台权限检测
                self:CheckPlatformMultiplayerPrivilege(onPlatformPrivilegeCheckCompleted)
            else
                onPlatformPrivilegeCheckCompleted()
            end
            -- END MODIFICATION - VIRTUOS
        end
        -- 查询状态 -- 回调中判断是否要打开重连弹窗
        Module.Login:GetStateInfo(fOnGetStateInfo)
        LoginLogic.BindFirstLoginReward()
    elseif gameFlowType == EGameFlowStageType.GameToSafeHouse or gameFlowType == EGameFlowStageType.GameToLobby then
        logerror("Refresh Token now")
        Module.Login.Config.Events.evtOnRefreshToken:Invoke()
    end
end

function LoginModule:OnInitModule()
    log("LoginModule:OnInitModule")
    LoginLogic.AddListeners()
    LoginInteractLogic.BindCallbacks()
    self:AddLuaEvent(Server.HopeServer.Events.evtOpenUrl, self.OpenUrl, self)
    self:AddLuaEvent(Server.HopeServer.Events.evtShowHopePanel, self.delayCreateHopePanelUI, self)
    self:AddLuaEvent(Server.HopeServer.Events.evtBackToLogin, self.BackToLogin, self)
    self:AddLuaEvent(Server.SDKInfoServer.Events.evtServerAddrNotfound, self.OnServerAddrNotfound, self)
    self:AddLuaEvent(DFMGlobalEvents.evtWorldCleanUp, self.WorldCleanUp, self)
    self:AddLuaEvent(Server.HopeServer.Events.evtRequestAppStoreWindow, self.SetOpenAppStoreFlag, self)
    self:AddLuaEvent(Server.AccountServer.Events.evtCheckParentControl, self.CheckParentControl, self)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnStartReconnected, self.UpdateLoginInfo, self)
    self:PreloadWaitView()
    
    if UDFMLocalizationManager then
        local gameInst = GetGameInstance()
        UDFMLocalizationManager.Get(gameInst).OnCultureChanged:Add(self._OnCultureChanged, self)
    end
end

function LoginModule:OnDestroyModule()
    log("LoginModule:OnDestroyModule")
    LoginLogic.RemoveListens()
    self:RemoveAllLuaEvent()
    LoginInteractLogic.UnbindCallbacks()

    if UDFMLocalizationManager then
        local gameInst = GetGameInstance()
        UDFMLocalizationManager.Get(gameInst).OnCultureChanged:Remove(self._OnCultureChanged, self)
    end
end

function LoginModule:SDKLogin(iChannelType)
    self:PreloadWaitView()
    log("LoginModule:SDKLogin", iChannelType)
    LoginLogic.CallGameLoginFunc("Login", iChannelType)
end

function LoginModule:SDKQRCodeLogin(iChannelType)
    log("LoginModule:SDKQRCodeLogin", iChannelType)
    self:PreloadWaitView()
    LoginLogic.CallGameLoginFunc("QRCodeLogin", iChannelType)
end

function LoginModule:SwitchUser(state)
    log("LoginModule:SwitchUser", state)
    LoginLogic.CallGameLoginFunc("SwitchUser", state)
end

function LoginModule:AutoLogin(iChannelType)
    log("LoginModule:AutoLogin",iChannelType)
    self:PreloadWaitView()
    LoginLogic.CallGameLoginFunc("AutoLogin",iChannelType)
end

function LoginModule:GameAutoLogin()
    self:PreloadWaitView()
    log("LoginModule:GameAutoLogin")
    LoginLogic.GameAutoLogin()
end

function LoginModule:SetLIUIRoot(canvasPanel)
    LoginLogic.SetLIUIRoot(canvasPanel)
end

function LoginModule:OpenLILoginPanel()
    log("LoginModule:OpenLILoginPanel")
    LoginLogic.CallGameLoginFunc("OpenLILoginPanel")
end

-- BEGIN MODIFICATION - Zhang Yan: Add console login entrance
function LoginModule:LoginChannelWithLIPass(eChannel)
    log("LoginModule:LoginChannelWithLIPass")
    LoginLogic.CallGameLoginFunc("LoginChannelWithLIPass", eChannel)
end

function LoginModule:BindChannelWithLIPassQueryInfo()
    log("LoginModule:BindChannelWithLIPassQueryInfo")
    LoginLogic.CallGameLoginFunc("BindChannelWithLIPassQueryInfo")
end
-- END MODIFICATION - VIRTUOS

function LoginModule:OpenLIAccountCenter()
    log("LoginModule:OpenLIAccountCenter")
    LoginLogic.CallGameLoginFunc("OpenLIAccountCenter")
end

function LoginModule:ResetGuest()
    log("LoginModule:ResetGuest")
    LoginLogic.CallGameLoginFunc("ResetGuest")
end

function LoginModule:LoginWithConfirmCode()
    log("LoginModule:LoginWithConfirmCode")
    LoginLogic.CallGameLoginFunc("LoginWithConfirmCode")
end

function LoginModule:Logout()
    log("LoginModule:Logout")
    self:SendModeHallLogOnQuit()
    LoginLogic.CallGameLogoutFunc("Logout")
end

--登出时上报模式大厅数据
function LoginModule:SendModeHallLogOnQuit()
    local lastTimespan=LogAnalysisTool.GetModeHallTimespan()
    if lastTimespan~=0 then
        local curTimespan=Facade.ClockManager:GetServerTimestamp()
        LogAnalysisTool.DoSendModeHallLog(curTimespan)
        LogAnalysisTool.SetModeHallTimespan(0)
    end
end

-- function LoginModule:AccountLogin(userName)
--     log("LoginModule:AccountLogin", userName)
--     LoginLogic.AccountLogin(userName)
-- end

function LoginModule:OpenUrl(url)
    log("LoginModule:OpenUrl", url)
    LoginLogic.CallGameToolsFunc("OpenURL", url)

    if false then
        local UDFMGameBrowser = import "DFMGameBrowser"
        if UDFMGameBrowser and UDFMGameBrowser.OpenGreenUrl then
            UDFMGameBrowser.OpenGreenUrl(url)
        end
    end
end

function LoginModule:WorldCleanUp()
    log("LoginModule:WorldCleanUp")
    --关闭所有持有的UI
    local stackUIHandleList = Module.Login.Field:GetStackUI()
    for index, uiHandle in ipairs(stackUIHandleList) do
        if uiHandle then
            Facade.UIManager:CloseUIByHandle(uiHandle)
        end
    end
    Module.Login.Field:ClearStackUI()
end

-- 供主流程蓝图调用
function LoginModule:CheckStateInfoBeSet()
    if Server.AccountServer:IsPlayerInGame() then
        log("LoginModule:CheckStateInfo", true)
        return true
    else
        return false
    end
end

-- 供主流程蓝图调用
function LoginModule:GetStateInfo(fCallBack, params)
    Server.AccountServer:GetStateInfo(fCallBack, params)
end

function LoginModule:ShowMainPanel()
    LoginLogic.ResetPlayerState()-- TODO:这个状态是做什么用的？好像Loading的时候要判断这个状态？大小也先不改了
    local uiHandle = Facade.UIManager:AsyncShowUI(UIName2ID.LoginInterface)
    Module.Login.Field:AddStackUI(uiHandle)
    trycall(require, "DFM.YxFramework.Util.ApmUtil")  -- 为了支持热更即时生效需要把require后置
    ApmUtil.GetDeviceLevel()
end

---打开INTL面板
---@param intlPanelType EINTLPanelType
function LoginModule:ShowINTLPanel(intlPanelType)
    LoginLogic.ShowINTLPanel(intlPanelType)
end

-- 自动化测试登录MP
---@param bAutoMatch boolean        是否自动入局
---@param ip string         服务器ip
---@param port string         服务器端口
---@param groupId GameModeServer.EnumGroupID     CLASSIC = 1, -- 经典,OFFDEF  = 2, -- 攻防
---@param mapId number 地图id
---@param defaultPlayerId number    玩家id
function LoginModule:HandleAutomationLoginMP(bAutoMatch, ip, port, groupId, mapId, defaultPlayerId)
    LoginLogic.TestAutomationLogin(bAutoMatch, ip, port, MatchGameMode.TDMGameMode, {groupId,mapId}, defaultPlayerId)
end

-- 自动化测试登录
---@param bAutoMatch boolean        是否自动入局
---@param ip string         服务器ip
---@param port string         服务器端口
---@param entranceIdx number        对应WorldEntranceConfig中的EntranceIdx，对应的地图
---@param matchSubMode number       MatchMode.csv中对应的MatchSubMode,一般SOL中PMC=10，SCAV=20
---@param defaultPlayerId number    玩家id
function LoginModule:HandleAutomationLogin(bAutoMatch, ip, port, entranceIdx, matchSubMode, defaultPlayerId)
    LoginLogic.TestAutomationLogin(bAutoMatch, ip, port, MatchGameMode.WorldGameMode, {entranceIdx,matchSubMode}, defaultPlayerId)
end

--MSDK的分享功能 https://docs.msdk.qq.com/v5/zh-CN/Module/Share.html
---@param channel   string    分享渠道  "WeChat"\"QQ"
---@param type      number       分享类型 MSDKFriendReqType
---@param user      string    当使用 gopenid 或者 sopenid 时，必须在extraJson中加上 {\"isFriendInGame\":false}；
---                           当使用 gopenid 时，可以在extraJson中加上 {\"isFriendInGame\":true} (非必须)
---@param title     string    分享的标题，必填
---@param desc      string    分享的内容，选填
---@param link      string    QQ渠道，必填，填写游戏中心详情页
---                           WeChat渠道，Android 为无用字段。iOS选填，如果填写为空，点击消息会拉起游戏。如果填写为其他任意地址，则点击消息会跳转至该地址
---@param thumbPath string    缩略图，支持本地或网络图片，图片大小不超过1M
---@param extraJson string    扩展字段，透传游戏自定义数据，平台会将透传数据做转义处理。其中自定义透传字段 game_data ，Android 为必填项，内容不为空
function LoginModule:FriendSendMessage(channel, type, user, title, desc, link, thumbPath, extraJson)
    LoginLogic.FriendSendMessage(channel, type, user, title, desc, link, thumbPath, extraJson)
end

-- 获取玩家当前语言码
function LoginModule:GetCurrentCulture()
    return LoginLogic.GetCurrentCulture()
end

function LoginModule:OnServerAddrNotfound()
    local confirmText =  Module.Login.Config.Loc.BackToLogin
    if IsHD() and IsWeGameEnabled() then
        confirmText = Module.Login.Config.Loc.ExitClient
    end
    Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
        Module.Login.Config.Loc.ServerInfoNotFound,
        function()
            if IsHD() and IsWeGameEnabled() then
                local UKismetSystemLibrary = import "KismetSystemLibrary"
                local EQuitPreference = import "EQuitPreference"
                UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
            else
                Module.Login.Config.Events.evtOnLoginFailed:Invoke()
                self:BackToLogin()
            end
        end,
        confirmText
    )
end

function LoginModule:delayCreateHopePanelUI(msg, hopeType, title)
    local function delayShowCommonPanel()
        if hopeType == 2 then
            Facade.ProtoManager:SetEnableReconnect(false)
            local confirmFun = CreateCallBack(function ()
                LoginInteractLogic._ReturnToLoginScene()
            end, self)
            Module.GCloudSDK:CreateHopeMainPanel(msg, title, confirmFun)
        else
            Module.GCloudSDK:CreateHopeMainPanel(msg, title)
        end
    end
    Timer.DelayCall(0.1, delayShowCommonPanel)
end

function LoginModule:GetTestAutoLoginMatch()
    return self.Field:GetTestAutoLoginMatch()
end

function LoginModule:GetTestMatchId()
    return self.Field:GetTestMatchId()
end

function LoginModule:SetTestAutoLoginMatch(bTestAutoMatch)
    return self.Field:SetTestAutoLoginMatch(bTestAutoMatch)
end

function LoginModule:GetTestAutoLoginMatchGameMode()
    return self.Field:GetTestAutoLoginMatchGameMode()
end

function LoginModule:GetTestAutoLoginParam()
    return self.Field:GetTestAutoLoginParam()
end

-- BEGIN MODIFICATION @ VIRTUOS
function LoginModule:BackToLogin(isLoadingFailed2Login)
-- END MODIFICATION
    logerror("LoginModule:BackToLogin")
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() then
        local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        identityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
    end
    -- END MODIFICATION - VIRTUOS
    local UGameSDKMgr = import "GameSDKManager"
    local idcSelectorIns = UGameSDKMgr.GetGameIdcSelectorIns(GetGameInstance())
    if idcSelectorIns then
        -- idcSelectorIns:DestroyIdcSelector()
    end
    Facade.ProtoManager:SetReconnectState(false)
    Facade.ProtoManager:SetQuickReconnectState(false)
    self.Field:SetAutoLogin(false)
    Server.SDKInfoServer:SetLastConnectChannelID(0)
    Server.SDKInfoServer:SetLastSubChannelID(0)
    self.Field:SetServiceAgreeFlag(false)
    Facade.ProtoManager:TryDisConnectServer(true)
    Facade.ProtoManager:StopHeartbeatTimer()
    -- 登录态注销
    self:Logout()
    local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager and DFMGameLoadingManager.IsLoading then
        -- BEGIN MODIFICATION @ VIRTUOS
        if isLoadingFailed2Login or DFMGameLoadingManager:IsLoading() then
        -- END MODIFICATION
            DFMGameLoadingManager:BroadcastLoadingFailed2Login() -- Loading的时候不走GameFlow来流转，直接走返回登录流程
        else
            self.Config.flowEvtToEnterLogin:Invoke()
        end
    else
        self.Config.flowEvtToEnterLogin:Invoke()
    end

    --- @todo 后续通过流程正常关闭，目前临时特殊处理
    local hudController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    if hudController then
        hudController:Reset()
    end

    Facade.ProtoManager:ClearWaitingProto()
    -- 清理缓存

    -- return self.Config.flowEvtToEnterLogin:Invoke()
    LiteDownloadManager:CancelAll()
    
end

function LoginModule:GetCurGuideStage()
    return self.Field:GetCurGuideStage()
end

function LoginModule:ShowRegisterView(fCallback)
    local registerState = Server.AccountServer:GetPlayerRegisterState()
    if registerState == false then
        -- local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        -- Module.CommonBar:SetUseStarAppState(true, true)

        -- Timer.DelayCall(1,function()
        --     Facade.UIManager:AsyncShowUI(UIName2ID.RegisterView, nil, nil, nil)
        -- end)
        Facade.UIManager:AsyncShowUI(UIName2ID.RegisterView, nil, nil, nil,nil,fCallback)
    end
end

function LoginModule:PreloadWaitView()
    local UGameSDKManager = import "GameSDKManager"
    local GameSDKManager = UGameSDKManager.Get(GetGameInstance());
    if GameSDKManager then
        GameSDKManager:PreLoadWaitView()
    end
end

function LoginModule:CheckLockNow()
    if self.Field:GetLoginFailedNum() < 50 then
        return false
    end
    return  self.Field:GetForbidLoginTime() - TimeUtil.GetCurrentTime() > 0
end

function LoginModule:UpdateLoginFailedInfo()
    local curflow =  Facade.GameFlowManager:GetCurrentGameFlow() 
    local bIsLoginStep = curflow== EGameFlowStageType.Login or curflow == EGameFlowStageType.LaunchToLogin
    if not bIsLoginStep then
        logerror("[LoginField] UpdateLoginFailedInfo, curflow", curflow)
        return 
    end
    
    local failedNum = self.Field:GetLoginFailedNum() + 1
    self.Field:SetLoginFailedNum(failedNum)
    
    if failedNum >= 3 and failedNum < 6 then
        -- 三次 禁止10s
        local timeSplit = TimeUtil.GetCurrentTime() + 10;
        self.Field:SetForbidLoginTime(timeSplit)
    elseif failedNum >= 6 then
        -- 六次 禁止 300s
        local timeSplit = TimeUtil.GetCurrentTime() + 300;
        self.Field:SetForbidLoginTime(timeSplit)
    end
end

function LoginModule:ResetLoginFailedInfo()
    Facade.ConfigManager:SetNumber("LoginFailedNum", 0)
    Facade.ConfigManager:SetNumber("LoginForbidTime", 0)
end

function LoginModule:IsPlayerKickOut()
    return Module.Login.Field:IsPlayerKickOut()
end

function LoginModule:DeleteAccount()
    LoginLogic.DeleteAccount()
end

function LoginModule:IsServiceAgree()
    if (IsBuildRegionGA()) then
        return true
    end
    return Module.Login.Field:GetServiceAgreeFlag()
end

-- BEGIN MODIFICATION - VIRTUOS
function LoginModule:CheckPlatformMultiplayerPrivilege(fCallback)
    logerror("LoginModule:CheckPlatformMultiplayerPrivilege")
    local identityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
    local checkPlayOnlinePrivilegeProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(identityManager)
    if checkPlayOnlinePrivilegeProxy then
        checkPlayOnlinePrivilegeProxy.OnGetUserPrivilegeCompleteDelegate:Add(function(res)
            self.Field.bHasCheckedPlatformMultiplayerPrivilege = true
            self.Field.bCanEnterMultiplayerMode = res
            if res then
                fCallback()
            else
                local text = IsPS5() and Module.Hall.Config.Loc.MultiplayerResolveWindowTxtPS5 or
                    (IsXSX() and Module.Hall.Config.Loc.MultiplayerResolveWindowTxt or "")
                -- 没有网络游戏权限
                Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
                    text,
                    function()
                        self:CheckPlatformMultiplayerPrivilege(fCallback)
                    end,  
                    Module.Hall.Config.Loc.PrivilegeResolveWindowConfirm)
            end
        end)
        checkPlayOnlinePrivilegeProxy:CheckUserPrivilege(EPlatformUserPrivileges.CanPlayOnline, true)
    end 
end
-- END MODIFICATION - VIRTUOS

function LoginModule:SetOpenAppStoreFlag(triggerType)
    Module.GCloudSDK:SetAppStoreFlag(true,triggerType)
end

function LoginModule:CheckParentControl(delayTime, isFirstTime)
    if self.CheckParentControlHandle then
        Timer.CancelDelay(self.CheckParentControlHandle)
        self.CheckParentControlHandle = nil
    end
    if isFirstTime then
        self.isInGameTip = false
    end
    -- 仅在PC端开启家长中控
    if not IsHD() then
        logerror("LoginModule:CheckParentControl() skip on non PC")
        return
    end
    -- 非韩国地区(410)不启用这个逻辑
    local region = Server.SDKInfoServer:GetRegionNumericCode()
    if tonumber(region) ~= 410 then
        logerror("LoginModule:CheckParentControl() skip when 410 != region ", region)
        return
    end
    local function delayCheckParentControl()
        local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
        if DFMGameLoadingManager:IsLoading() then
            logwarning("LoginModule:CheckParentControl() skip while loading")
            return Module.Login:CheckParentControl(1.0)
        end
        local req = pb.CSHopeCheckParentControlReq:New()
        loginfo("LoginModule:CheckParentControl()")
        logtable(req,true)
        local function OnCSHopeCheckParentControlRes(res)
            loginfo("LoginModule:CheckParentControl() OnCSHopeCheckParentControlRes")
            logtable(res,true)
            -- BEGIN MODIFICATION - VIRTUOS
            -- Console上不能直接退出游戏，只退出登录
            local function QuitGame()
                if IsConsole() then
                    Module.Login:BackToLogin()
                else
                    Module.SystemSetting:RequestQuitGame(true)
                end 
                -- END MODIFICATION - VIRTUOS
            end
            if res.result == 0 then
                local check_result = res.check_result
                if check_result == 0 then
                    self.isInGameTip = true
                    Module.Login:CheckParentControl(res.check_interval or 10.0)
                elseif check_result == 1 then
                    if IsHD() then
                        logerror("CSHopeCheckParentControlRes Kill ue5 now")
                        Module.LobbyBHD:KillUE5(EDFKillBHDReason.ParentPolicy)
                    end
                    Module.CommonTips:ShowConfirmWindowWithSingleBtn(not self.isInGameTip and Module.Login.Config.Loc.TipParentControlOverTimeLimit or Module.Login.Config.Loc.TipParentControlOverTimeLimitInGame, QuitGame)
                    Module.Login:CheckParentControl(60.0)
                elseif check_result == 2 then
                    if IsHD() then
                        logerror("CSHopeCheckParentControlRes Kill ue5 now")
                        Module.LobbyBHD:KillUE5(EDFKillBHDReason.ParentPolicy)
                    end
                    Module.CommonTips:ShowConfirmWindowWithSingleBtn(not self.isInGameTip and Module.Login.Config.Loc.TipParentControlOverNoAccess or Module.Login.Config.Loc.TipParentControlOverNoAccessInGame, QuitGame)
                    Module.Login:CheckParentControl(60.0)
                end
            else
                Module.Login:CheckParentControl(10.0)
            end
        end
        req:Request(OnCSHopeCheckParentControlRes, {bShowErrTip = false})
    end
    if delayTime > 0 then
        self.CheckParentControlHandle = Timer.DelayCall(delayTime, delayCheckParentControl)
    end
end

function LoginModule:_OnCultureChanged(newCulture)
    loginfo("[LoginModule] _OnCultureChanged", newCulture)
    return self:SetLIUILanguage(newCulture)
end

function LoginModule:SetLIUILanguage(language)
    -- 语言特殊处理
    if language == 'zh' then
        language = 'zh-Hans' -- 需要和Intl控制台对应
    end

    if REGION_CN then
        language = 'zh-CN' -- 国服定死zh-CN
    end

    if IsBuildRegionGlobal() or IsBuildRegionGA() then
        UE.LevelInfiniteAPI.SetLanguage(language)
    end
end

function LoginModule:RefreshToken()
    -- CN
    logerror("LoginModule:RefreshToken")
    if IsBuildRegionCN() then
        if IsMobile() then
            -- 手游
            local lastId = Server.SDKInfoServer:GetLastConnectChannelID()
            logerror("LoginModule:RefreshToken AutoLogin lastId:",lastId)
            if lastId then
                -- 这里不能走完整的登录流程，必须劫持登录的回调。
                Module.Login:AutoLogin(lastId)
                Module.Login.Field:SetRefreshTokenFlag(true)
            end
        elseif PLATFORM_WINDOWS and IsWeGameEnabled() then
            -- PC
            local UWeGameManager = nil
            local WeGameManager = nil
	        UWeGameManager = import "WeGameSDKManager"
	        WeGameManager = UWeGameManager.Get(GetGameInstance())
            logerror("LoginModule:RefreshToken GetAccountInfo")
            if WeGameManager then
                local info
                local successful, info = WeGameManager:GetAccountInfo(info)
                logerror("LoginModule:RefreshToken successful:",successful)
                if successful and info then
                    logerror("LoginModule:RefreshToken updateToken:",info.Token)
                    if info.Token and info.Token ~= "" then
                        logerror("LoginModule:RefreshToken update token")
                        Server.SDKInfoServer:SetToken(info.Token)
                        Server.PayServer:ReqAccountUpdatePayToken()
                    else
                        logerror("LoginModule:RefreshToken token is empty")
                    end
                    Server.PayServer:ReqAccountUpdatePayToken()
                end
            end
        end
    elseif IsBuildRegionGlobal() then
        -- BEGIN MODIFICATION - VIRTUOS
        if PLATFORM_PS5 == 1 then
            local permissions = "psn:s2s openid id_token:psn.basic_claims"
            UE.LevelInfiniteAPI.LoginChannelWithLIPass(EChannelType.kChannelPS5, permissions, "{}")
            Module.Login.Field:SetRefreshTokenFlag(true)
        end
        -- END MODIFICATION - VIRTUOS
        Server.PayServer:ReqAccountUpdatePayToken()
    end
end

function LoginModule:SetIsQRCodeLogin(bIsQRCodeLogin)
    logerror("LoginModule:SetIsQRCodeLogin bIsQRCodeLogin:", bIsQRCodeLogin)
    Facade.ConfigManager:SetBoolean("bIsQRCodeLogin", bIsQRCodeLogin)
end

function LoginModule:GOpenIdEncryption(playerId)
    if not playerId then
        return ""
    end
    local playerStr = ""
    local size = #playerId
    local fristStr = string.sub(playerId, 1, 1)
    for i = 1, size do
        playerStr = playerStr .. (9 - tonumber(string.sub(playerId, i, i))) 
    end
    return fristStr .. playerStr
end

function LoginModule:SaveAccountLimitStatus(bIsLimited, showText)
    logerror("LoginModule:SetAccountLimited bIsLimited:", bIsLimited, showText)
    Module.Login.Field:SaveAccountLimitStatus(bIsLimited, showText)
end

function LoginModule:GetAccountLimitStatus()
    return Module.Login.Field:GetAccountLimitStatus()
end


function LoginModule:UpdateLoginInfo()
   -- 仅用于wegame启动，客户端发生重连时更新access_token和ext_str
    if IsHD() and IsWeGameEnabled() then
        UWeGameManager = import "WeGameSDKManager"
        WeGameManager = UWeGameManager.Get(GetGameInstance())
        logerror("LoginModule:UpdateLoginInfo..")
        if WeGameManager then
            local info
            local successful, info = WeGameManager:GetAccountInfo(info)
            if info then
                if info.Token and info.token ~= "" then
                    logerror("LoginModule:UpdateLoginInfo update token", info.Token)
                    Server.SDKInfoServer:SetToken(info.Token)
                else
                    logerror("LoginModule:UpdateLoginInfo token is empty")
                end

                if info.ExtStr and info.ExtStr ~= "" then
                    logerror("LoginModule:UpdateLoginInfo update ext_str:", info.ExtStr)
                    Server.SDKInfoServer:SetWeLoginInfo(info.ExtStr)
                else
                    logerror("LoginModule:UpdateLoginInfo ext_str is empty")
                end
            else
                logerror("LoginModule:UpdateLoginInfo GetAccountInfo failed, info is nil")
            end
        end
    else
        logerror("LoginModule:UpdateLoginInfo not wegame")    
    end
end

return LoginModule
