----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local MPCammanderBigRankUp = ui("MPCammanderBigRankUp")

function MPCammanderBigRankUp:Ctor()
end

function MPCammanderBigRankUp:OnInitExtraData(targetRank)
    self._targetRank = targetRank
    if self._targetRank > 4 then
        self._targetRank = 4
    end
    local curSerial = Server.TournamentServer:GetCurSerial()

    Module.Settlement.Field:SetIsSpecialPopOpen(true)
end

function MPCammanderBigRankUp:OnOpen()
    self:_BindBigRankWidget()
end

function MPCammanderBigRankUp:OnClose()
    Module.Settlement.Field:SetIsSpecialPopOpen()
end

function MPCammanderBigRankUp:_BindBigRankWidget()
    local widget = self:Wnd("WBP_CommanderUpgrades_0" .. self._targetRank, UIWidgetBase)
    self:PlayAnimation(self.WBP_BattlePointMatch_RankUpgrade_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if widget then
        widget:Visible()

        if widget["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"] then
            widget:PlayAnimation(widget["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"], 0, 1, EUMGSequencePlayMode.Forward, 1, false)

            Timer.DelayCall(
                widget["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"]:GetEndTime(),
                function()
                    self:_CloseSelf()
                end,
                self
            )

        else
            self:_CloseSelf()
        end
    else
        self:_CloseSelf()
    end
end
function MPCammanderBigRankUp:_CloseSelf()
    Facade.UIManager:CloseUI(self)
end

function MPCammanderBigRankUp:OnNavBack()
    return true
end

return MPCammanderBigRankUp