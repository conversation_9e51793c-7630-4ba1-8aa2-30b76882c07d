----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMainPanel : LuaUIBaseView
---活动主面板，对应WBP_Activity_MainPanel
local ActivityMainPanel = ui("ActivityMainPanel")


local ActivityConfig                = require "DFM.Business.Module.ActivityModule.ActivityConfig"
local ActivityLogic                 = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local CommonWidgetConfig            = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local ActivityRedDotLogic           = require "DFM.Business.Module.ActivityModule.Logic.ActivityRedDotLogic"
local DFCommonCheckButtonOnly       = require "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonCheckButtonOnly"
local TabUIContentDataV2            = require "DFM.Business.DataStruct.UIDataStruct.TabUIContentDataV2"
local UIThemeUtil                   = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"
local InputBindingAgent             = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local CallFilter                    = require "DFM.Business.DataStruct.Common.Base.CallFilter"
local Table                         = require "DFM.Business.DataStruct.Common.Base.Table"

local EDescendantScrollDestination  = import "EDescendantScrollDestination"
local ETextChecked                  = import "ETextChecked"
local FAnchors                      = import "Anchors"
local UKismetInputLibrary           = import "KismetInputLibrary"
local CustomerServicesEntranceType  = import "ECustomerServicesEntranceType"

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil                    = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates             = import  "GPInputDelegates"
local EGPInputType                  = import  "EGPInputType"
--- END MODIFICATION

-- 用于活动的customParamList，仅用于标记，实际打开活动时主面板替换成实际对应的内容传给活动
local _ACTV_ID_         = {}
local _ACTV_INFO_       = {}
local _GET_CTRL_FUNC_   = {}
local _APP_ID_          = {}
local _OPEN_ARGS_       = {}
local _PANDORA_ARGS_    = {}

-- 上下切换上一个/下一个活动的箭头提示控件
local _ARROW_NONE_      = 0
local _ARROW_PREV_      = 1 << 1
local _ARROW_NEXT_      = 1 << 2

----------------------------------------------------------------------------------------------------------
--#region 子UI活动访问ActivityMainPanel中的公用控件

---@enum EActivityCommonControls
EActivityCommCtrl = {
    ActivityMainPanel       = "",
    MainInfoPaddingBox      = "_wtActivityMainInfoRoot",
    MainInfoVerticalBox     = "_wtActivityMainInfoVBox",
    ActivityNameTextBox     = "_wtActivityName",
    ActivityDescTextBox     = "_wtActivityDesc",

    ToggleDetailButton      = "_wtToggleDetailBtn",
    LotteryShowPoolButton   = "_wtToggleDetailBtn",
    LotteryDescCanvas       = "_wtLotteryOnlyDescPanel",
    LotteryDescTextBox      = "_wtLotteryOnlyDescText",
    LotteryPrizePoolSlot    = "_wtPrizePoolSlot",

    TimeAndModeCanvas       = "_wtTimeAndModePanel",
    ActivityTimeTextBox     = "_wtActivityTime",
    ActivityModeTag         = "_wtModelTag",

    ExtraButtonCanvas       = "_wtExtraBtnPanel",
    ExtraButtonButton       = "_wtExtraButton",
    ExtraButtonRedDot       = "_wtExtraButtonRedDot",

    ShowRuleButton          = "_wtShowRuleBtn",

    BackgroundCDNImg        = "_wtbgImg",
}

function ActivityMainPanel:InitCommonConrtolList()
    self._commonControlNameSet = {}
    for _, controlName in pairs(EActivityCommCtrl) do
        self._commonControlNameSet[controlName] = true
    end
end

function ActivityMainPanel:MakeGetControlFunc()
    -- 将自身传给获取公用控件的函数upvalue必须为弱引用
    local upvalWeakSelf = makeweak(self)

    ---@param controlName EActivityCommonControls
    ---@vararg EActivityCommonControls
    local function fGetCommonCtrl(controlName, ...)
        local activityMainPanel = getfromweak(upvalWeakSelf)
        if not activityMainPanel then return end

        local controlNames = {controlName, ...}
        local results = {}

        for idx, name in ipairs(controlNames) do
            -- 只允许获取EActivityCommonControls中定义的活动公用控件
            if activityMainPanel._commonControlNameSet[name] then
                if name == "" then 
                    results[idx] = activityMainPanel 
                else
                    results[idx] = activityMainPanel[name]
                end
            else
                results[idx] = nil
            end
        end

        return table.unpack(results)
    end

    return fGetCommonCtrl
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 初始化（事件/控件绑定/各类活动展示配置）

function ActivityMainPanel:BindWidgets()
    --左上活动主要信息
    self._wtActivityMainInfoRoot                = self:Wnd("PlatformPaddingBox_1", UIWidgetBase)    --主要信息根层级
    self._wtActivityMainInfoVBox                = self:Wnd("DFVerticalBox_0", UIWidgetBase)             --主要信息VerticalBox
    self._wtActivityName                        = self:Wnd("DFTextBlock_Title", UITextBlock)                --大标题
    self._wtActivityDesc                        = self:Wnd("DFMTextBlock_129", UITextBlock)                 --详情信息
    self._wtLotteryOnlyDescPanel                = self:Wnd("DFCanvasPanel_2", UIWidgetBase)                 --抽奖专用活动小标题容器
    self._wtLotteryOnlyDescText                 = self:Wnd("DFTextBlock_75", UITextBlock)                       --抽奖活动专用小标题
    self._wtTimeAndModePanel                    = self:Wnd("DFCanvasPanel_10", UIWidgetBase)                --(层级)
    self._wtActivityTime                        = self:Wnd("DFTextBlock_213", UITextBlock)                      --时间
    self._wtModelTag                            = self:Wnd("WBP_Activity_ModeTag", UIWidgetBase)                --模式标签
    self._wtLinkageImg                          = self:Wnd("themecrossoverimage", UIWidgetBase)                 -- 联动logo

    --左中额外按钮(追踪/兑换/...) (注意: 这些控件都在_wtActivityMainInfoVBox的层级下)
    self._wtExtraBtnPanel                       = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._wtExtraButton                         = self:Wnd("WBP_DFCommonButtonV3S1", DFCommonButtonOnly)
    self._wtExtraButtonRedDot                   = Module.ReddotTrie:CreateReddotIns(self._wtExtraButton)   -- 追踪按钮红点

    --右下角活动时间和模式信息（现在好像没在用？）
    self._wtTaskTime                            = self:Wnd("DFTextBlock_372", UITextBlock)
    self._wtactivityInclination                 = self:Wnd("DFTextBlock_127", UITextBlock)

    --活动背景图
    self._wtbgImg                               = self:Wnd("wtbgimg", DFCDNImage)

    --子UI画布
    self._canvasPages                           = self:Wnd("DFCanvasPanel11", UIWidgetBase)

    --物流抽奖活动相关控件
    self._wtToggleDetailBtn                     = self:Wnd("WBP_CommonIconCheckBox", DFCommonCheckButtonOnly)       --详情按钮（展示奖池）
    self._wtPrizePoolSlot                       = self:Wnd("DFNamedSlot_89", UIWidgetBase)                          --奖池容器


    --左下追踪奖励信息
    self._wtTrackRewardPanel                    = self:Wnd("DFCanvasPanel_42", UIWidgetBase)
    self._wtTrackRewardScrollBox                = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self._OnGetTrackRewardRromptCount, self._OnProcessTrackRewardRromptWidget)

    --活动奖励预览层级
    self._wtPlatformRewardBox                   = self:Wnd("PlatformPaddingBox_2", UIWidgetBase)
    self._wtDFRewardTxt                         = self:Wnd("DFTextBlock", UITextBlock)

    -- 显示活动详细规则按钮
    self._wtRuleBtnPanel                        = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)
    self._wtShowRuleBtn                         = self._wtRuleBtnPanel:Wnd("DFCheckBox_Icon", UICheckBox)

    --上下滚动
    self._wtDFSizeBox                           = self:Wnd("DFSizeBox_1", UIWidgetBase)

    --上下箭头
    self._wtPrevArrow                           = self:Wnd("WBP_Activity_UnderAarrow", UIWidgetBase)
    self._wtNextArrow                           = self:Wnd("WBP_Activity_UnderAarrow_1", UIWidgetBase)

    -- Mobile 活动分组页签滑动框
    self._wtTabScrollBox =  UIUtil.WndTabGroupBox(self, "WBP_DFTabV1GroupTopBox", self._Mobile_OnGetTabCount, self._Mobile_OnProcessTabWidget, self._Mobile_OnTabChanged)

    -- HD 侧边活动切换滚动菜单
    self._wtScrollBox =
    UIUtil.WndScrollBox(
        self,
        "DFScrollBox_181",
        self._OnSubSwitcherGetItemCount,
        self._OnSubSwitcherProcessWidget
    )
    -- HD 侧边活动切换滚动菜单触发（悬停此按钮唤出折叠菜单）
    self._wtScrollBoxbtn = self:Wnd("DFButton_0", UIButton)
    if IsHD() then
        self._wtScrollBox.OnScrollEnd:Add(self._OnScrollEnd, self)
        self:_ForceUpdateScrollBarVisibilityWorkaround()
        self._wtScrollBoxbtn:Event("OnUnHovered", self.OnUnhoverScrollBoxBtn, self)
    else
        self._wtScrollBox:Collapsed()
        self._wtScrollBoxbtn:Collapsed()
    end

    --绑定UI交互事件
    self._wtShowRuleBtn:SetCallback(self._OnShowRuleButtonClicked, self)
    self._wtExtraButton:Event("OnClicked", self._OnExtraButtonClicked, self)
    
    -- 由活动自己处理
    -- self._wtToggleDetailBtn:Event("OnCheckedClicked", self._OnToggleDetailButton, self, false)
    -- self._wtToggleDetailBtn:Event("OnUncheckedClicked", self._OnToggleDetailButton, self, true)
end

--- 加载活动导航栏
function ActivityMainPanel:_ShowActNavigationBar()
    local list = {}
    for index, value in ipairs(Server.ActivityServer:GetCurrencyColumn() or {}) do
        local clientId = ActivityLogic.GetIndexByCurrencyId(value.id)
        if clientId ~= 0 then
            table.insert(list, clientId)
        end
    end

    if self:_IsExistAct(1070510001) then
        table.insert(list, ECurrencyClientId.LogisticsVoucher)
    end
    if self:_IsExistAct(1080530001) then
        table.insert(list, ECurrencyClientId.CooperateVoucher)
    end
    
    table.insert(list, ECurrencyClientId.Tina)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, list)
end

function ActivityMainPanel:_IsExistAct(act_id)
    local act = Server.ActivityServer:GetActivityInfoByActivityID(act_id)
    if act then
        if GeneralHelperTool.IsTimeExpired(act.start_date) then
            return true
        else
            return false
        end
    else
        return false
    end
end

--- 包含所有类型的活动如何展示的配置
--- 加新活动在这里配置
function ActivityMainPanel:InitActivityTypeConfig()
    --[[
        活动配置规则:
        [ActivityType.YourActivityType] = {
            
            entryPoint = ...
                打开活动时如何展示活动内容
                填 SubUI  : 作为子UI嵌入ActivityMainPanel打开活动
                填 StackUI: 作为单独的面板打开
                填 函数   : 将调用该函数

            customParamList = {...} 
                打开活动时传给entryPoint的参数，可以使用
                例: {_ACTV_ID_, "param1", "param2"}
                则UI的OnInitExtraData (或活动入口函数）的参数为 [活动ID], "param1", "param2"
                特殊参数：
                    _ACTV_ID_       : 传参时将被替换为活动ID
                    _ACTV_INFO_     : 传参时将被替换为当时的ActivityInfo
                    _GET_CTRL_FUNC_ : 传参时将被替换为一个可以用于访问ActivityMainPanel中的活动通用控件的回调函数，见 MakeGetControlFunc
                该配置项默认为 {_ACTV_ID_}

            extraButtonConfig = {
                （可选）配置活动额外按钮的展示和行为(例如"兑换"，"赠卡记录"等)
                该配置项不存在时额外按钮不显示。
                注: 如果按钮逻辑复杂，可使用 _GET_CTRL_FUNC_ 然后获取 EActivityCommCtrl.ExtraButtonButton 进行操作，但务必在退出活动前将按钮状态恢复。

                icon = "按钮图标Asset路径"
                text = "按钮文本"
                fAction = 回调函数，点击按钮时的动作（该回调函数将获得 activityID 作为唯一的参数）
                fRedDot = 红点函数（可选）。该函数将获得 activityID 作为参数，且应当返回一个布尔值，用于确定按钮上是否显示红点
            }
        }
    ]]
    self.activityTypeConfig = {
        [ActivityType.ActivityTypeExchangeTask          ] = {entryPoint = UIName2ID.ActivityItemPanel            ,}, -- 兑换任务
        -- [ActivityType.ActivityTypeMilestoneTask      ] = {entryPoint = UIName2ID.ActivityMilestonesPanel      ,}, -- 里程碑
        [ActivityType.ActivityTypeDaily                 ] = {entryPoint = UIName2ID.ActivityDailiesPanel         ,}, -- 每日任务
        [ActivityType.ActivityTypeAttend                ] = {entryPoint = UIName2ID.ActivitySignInPanel          ,}, -- 签到
        [ActivityType.ActivityTypeMainpage              ] = {entryPoint = UIName2ID.ActivityFullScreenButton     ,}, -- 全屏
        [ActivityType.ActivityTypeRecruitNewMilestone   ] = {entryPoint = UIName2ID.ActivityGrowthMilestones     ,}, -- 新兵里程碑
        [ActivityType.ActivityTypeRecruitTraining       ] = {entryPoint = UIName2ID.ActivityNoviceChallenge      ,}, -- 新兵挑战
        [ActivityType.ActivityTypeRecruitAttend         ] = {entryPoint = UIName2ID.ActivityGrowthSignInPanel    ,}, -- 新兵签到
        [ActivityType.ActivityTypeMilestoneTaskV2       ] = {entryPoint = UIName2ID.ActivityHafkIntelligence     ,}, -- 哈夫克情报
        [ActivityType.ActivityTypeExchangeTaskV2        ] = {entryPoint = UIName2ID.ActivityAsaraBazaar          ,}, -- 阿萨拉集市
        [ActivityType.ActivityTypeExchangeOnly          ] = {entryPoint = UIName2ID.ActivityExchangeOnly         ,}, -- 纯兑换类
        [ActivityType.ActivityTypeGenericTemplate       ] = {entryPoint = UIName2ID.ActivityCommonTask           ,}, -- 通用简易任务
        [ActivityType.ActivityTypeSingleTaskTemplate    ] = {entryPoint = UIName2ID.ActivitySingleTask           ,}, -- 单一任务
        [ActivityType.ActivityTypeUnlockPasswordBox     ] = {entryPoint = UIName2ID.ActivityTopicPanel           ,}, -- 密码主题
        [ActivityType.ActivityTypeTestingWeaponTemplate ] = {entryPoint = UIName2ID.ActivityTastingWeaponsPanel  ,}, -- 品鉴武器
        [ActivityType.ActivityTypeOrderedTemplate       ] = {entryPoint = UIName2ID.ActivityOrderedPanel         ,}, -- 串行顺序
        [ActivityType.ActivityTypeCollectionTemplate    ] = {entryPoint = UIName2ID.ActivityCollectionPanel      ,}, -- 收藏模版活动
        [ActivityType.ActivityTypeKillProducer          ] = {entryPoint = UIName2ID.ActivitySniperPanel          ,}, -- 狙击制作
        [ActivityType.ActivityTypeH5Template            ] = {entryPoint = UIName2ID.ActivityAssemble             ,}, -- H5模板
        [ActivityType.ActivityTypeConstructMoss         ] = {entryPoint = UIName2ID.ActivityMossMainPanel        ,}, -- 建设moss主题
        [ActivityType.ActivityTypeStarFire              ] = {entryPoint = UIName2ID.ActivityStarFireMainPanel    ,}, -- 星火主题
        [ActivityType.ActivityTypeArchive               ] = {entryPoint = UIName2ID.ActivityFilingTaskMainPanel  ,}, -- 归档活动
        [ActivityType.ActivityTypeMoneyPaperExchange    ] = {entryPoint = UIName2ID.ActivityExchangeHaffCoins    ,}, -- 兑换活动
        [ActivityType.ActivityTypeRelink                ] = {entryPoint = UIName2ID.ActivityDecryptMainPanel     ,}, -- 脑机解密
        [ActivityType.ActivityTypeThemeActStar          ] = {entryPoint = UIName2ID.ActivityStars                ,}, -- 繁星
        [ActivityType.ActivityTypeSOLEquipmentDelivery  ] = {entryPoint = UIName2ID.ActivityEquipment            ,}, -- SOL装备投放
        [ActivityType.ActivityTypeSOLComponentDelivery  ] = {entryPoint = UIName2ID.ActivityAccessories          ,}, -- SOL配件投放
        [ActivityType.ActivityTypeMPComponentDelivery   ] = {entryPoint = UIName2ID.ActivityAccessories          ,}, -- MP配件投放
        [ActivityType.ActivityTypeWeaponDelivery        ] = {entryPoint = UIName2ID.ActivityWeapon               ,}, -- 枪械投放
        [ActivityType.ActivityTypeFlexibleAttend        ] = {entryPoint = UIName2ID.ActivitySignInTemplatePanel  ,}, -- 灵活签到模板
        [ActivityType.ActivityTypeAttend7Day            ] = {entryPoint = UIName2ID.ActivityGrowthSignInPanel    ,}, -- 七天签到模板（前端逻辑和新兵签到完全一致)
        [ActivityType.ActivityTypeNormal                ] = {entryPoint = UIName2ID.ActivityCommonDailyTask      ,}, -- 简易七日模板
        [ActivityType.ActivityTypeMandelBrick           ] = {entryPoint = UIName2ID.StoreActivityMandelDrawOnly  ,}, -- 曼德尔砖活动
        [ActivityType.ActivityTypeMPVehicleDelivery     ] = {entryPoint = UIName2ID.MPVehicleUnlockMainPanel     ,}, -- MP载具投放模板
        [ActivityType.ActivityTypeMPCommander           ] = {entryPoint = UIName2ID.ActivityPeakPanel            ,}, -- 大战场巅峰赛
        [ActivityType.ActivityTypeAhsarahTravel         ] = {entryPoint = UIName2ID.ActivityTaraMain             ,}, -- 阿萨拉巡旅
        [ActivityType.ActivityTypeAnythingExchangeCurrency ] = {entryPoint = UIName2ID.ActivityUniversalExchangeTemplatePanel,}, -- 万能兑换模板
        [ActivityType.ActivityTypeAnythingExchange         ] = {entryPoint = UIName2ID.ActivityUniversalExchangeTemplatePanel,}, -- 万能兑换模板
        [ActivityType.ActivityTypeSimpleMilestone          ] = {entryPoint = UIName2ID.ActivitySimpleMilestonesPanel         ,}, -- 简易里程碑
        [ActivityType.ActivityTypeSubscription             ] = {entryPoint = UIName2ID.ActivitySubscribePanel                ,}, -- QQ/微信订阅
        [ActivityType.ActivityTypeArknightsExchange        ] = not BUILD_REGION_CN_EXPER and {entryPoint = UIName2ID.ActivityStoreSubPanel,} or nil,-- 方舟兑换商城活动
        [ActivityType.ActivityTypePageExchange          ] = {entryPoint = UIName2ID.CommonStoreHome,},-- 通用商城活动
        [ActivityType.ActivityTypeArknightsGame         ] = {entryPoint = UIName2ID.MorgenMain,},-- 方舟博士的游戏局外

        -- 活动抽奖
        [ActivityType.ActivityTypeLotteryTemplate] = {
            entryPoint = UIName2ID.ActivityLotteryDrawPanel,
            customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
        },

        -- Raid合作物流
        [ActivityType.ActivityTypeRaidLotteryTemplate] = {
            entryPoint = UIName2ID.ActivityLotteryDrawPanel,
            customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
        },

        -- 调酒师
        [ActivityType.ActivityTypeMakeDrink] = {
            entryPoint = UIName2ID.ActivityBartender,
            customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
            extraButtonConfig = {
                icon    = Module.Activity.Config.DrinkIconList[4],
                text    = Module.Activity.Config.Loc.IllustratedHandbook,
                fAction = SafeCallBack(
                            function(activityID)
                                Module.Activity.Config.evtActivitySubBetweenInteractive:Invoke(activityID, EActSubInteractive.Next7)
                            end)
            }
        },

        -- -- 新年集换卡(She1临时不上活动)
        -- [ActivityType.ActivityTypeSheOneCard] = {
        --     entryPoint = UIName2ID.ActivityTackCards,
        --     customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
        --     extraButtonConfig = {
        --         icon = Module.Activity.Config.DrinkIconList[6],
        --         text = Module.Activity.Config.Loc.Message,
        --         fRedDot = function(activityID)
        --             local recv_cards = Server.ActivityServer:GetNewYearCard(activityID, EActSubInteractive.Next3)
        --             return recv_cards == nil
        --         end,
        --         fAction = SafeCallBack(
        --             function(activityID)
        --                 Server.ActivityServer:SendActivitySOCSendRecvFlowsReq(activityID)
        --             end)
        --     }
        -- },

        -- 角色调查活动
        [ActivityType.ActivityTypeSurveyBoss] = {
            entryPoint = UIName2ID.ActivityRoleSurvey,
            extraButtonConfig = {
                icon = Module.Activity.Config.DrinkIconList[5],
                text = Module.Activity.Config.Loc.RoleSurveyInfoExchangeBtn,
                fAction = SafeCallBack(
                    function(activityID)
                        Facade.UIManager:AsyncShowUI(UIName2ID.ActivityRoleSurveyExchange, nil, nil, activityID)
                    end)
            }
        },

        -- 曼德尔砖活动
        [ActivityType.ActivityTypeMandelBrick] = {
            entryPoint = UIName2ID.StoreActivityMandelDrawOnly,
            customParamList = {false, 1, 1},
        },

        -- SBC电台
        [ActivityType.ActivityTypeSBC] = {
            entryPoint = UIName2ID.ActivityRadioMainPanel,
            customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
        },
        
        -- 潘多拉活动
        [ActivityType.ActivityTypePandora] = {
            entryPoint = UIName2ID.PandoraSubActivity,
            customParamList = {_PANDORA_ARGS_},
        },

        -- 方舟招募活动
        [ActivityType.ActivityTypeArknightsRecruit] = {
            entryPoint = UIName2ID.ArkRecruitMain,
            customParamList = {_ACTV_ID_, _GET_CTRL_FUNC_},
        },
    }
end

function ActivityMainPanel:Ctor()

    self._activityID        = 0
    self._activityType      = 0
    self._selectedTabIndex  = 0
    self._name              = ""
    self._subItemFlag       = 0
    self._activityBtntable  = {}
    self._reddotList        = {}
    self._trackRewardNum    = 0
    self._desiredArrowType  = _ARROW_NONE_

    self._inputSummaries = {}
    self._inputSummaries_revidx = {}
    
    self._activityTabConfig = ActivityLogic.GetEventTabConfigTable()

    self:InitCommonConrtolList()
    self:InitActivityTypeConfig()
    self:UpdateGroupPresentationInfo()

    self:BindWidgets()
    self:InitEvent()

    Module.CommonBar:RegStackUITopBarTitle(self, ActivityConfig.Loc.TopBarTitle)

    if IsHD() then
        self._wtTabScrollBox:Collapsed()
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Custom)
        logwarning("SystemSettingMainView Customer Services")
        if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.ActivityPage) then
            local UIID = IsBuildRegionCN and UIName2ID.CustomerServicesBtn or UIName2ID.CustomerServicesOverSeaBtn
            logwarning("SystemSettingMainView Customer Services UIID " .. UIID)
            Module.CommonBar:RegStackUITopBarCustomInfo(self, {
                InfoUINavID = UIID,
                InfoUICacheInfo = UIUtil.CreateCacheInfo(UIID, CustomerServicesEntranceType.ActivityPage)
            })
        end
        self._wtTabScrollBox:Visible()
        UIThemeUtil.CheckIfAutoApplyTheme(self._wtTabScrollBox)
        self._wtTabScrollBox:SetIsAutoApplyThemeID(true)
        self:ActivityRefreshBtn()
    end

    self:_BindBackHandler()
    self:_SetBackgroundImage(nil)
    self:InitSwitchSubUI()

    self._prevOrNextCooldown = CallFilter.Interval(1)
    self._inputMgr = InputBindingAgent.New(self)
    self._inputMgr:AddBinding(
        "PrevActivity",
        {
            actionName = "Common_RightStickUp_Gamepad",
            caller = self,
            callback = CreateCallBack(self.OnPrevOrNextActivity, self, -1)
        },
        true
    )
    self._inputMgr:AddBinding(
        "NextActivity",
        {
            actionName = "Common_RightStickDown_Gamepad",
            caller = self,
            callback = CreateCallBack(self.OnPrevOrNextActivity, self, 1)
        },
        true
    )
    self:InjectLua()
end

function ActivityMainPanel:Imp_OnMouseWheel(inGeometry, inGestureEvent)
    local delta = UKismetInputLibrary.PointerEvent_GetWheelDelta(inGestureEvent)
    self:OnPrevOrNextActivity(delta)
end

function ActivityMainPanel:OnPrevOrNextActivity(delta)
    if not IsHD() then return end
    if not delta then return end
    if not self._activeSubUI then return end

    local shouldSwitch = false
	local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform()

    if self._activeSubUI.HandlePrevOrNext then
        shouldSwitch = self._activeSubUI:HandlePrevOrNext(mousePos, delta)
        loginfo("ActivityMainPanel:OnPrevOrNextActivity  SubUI-HandlePrevOrNext = ", shouldSwitch)
    elseif self._activeSubUI._wtHotzone then
        shouldSwitch = not ActivityLogic.IsPointInWidgets(mousePos, self._activeSubUI._wtHotzone)
        loginfo("ActivityMainPanel:OnPrevOrNextActivity  SubUI-CheckHotzone = ", shouldSwitch)
    end

    if shouldSwitch then
        if not self._prevOrNextCooldown:Check(true) then return end

        if delta > 0 then
            ActivityLogic.GoForward(self._activeSubUI)
        else
            ActivityLogic.GoBackward(self._activeSubUI)
        end
    end
end

--注册事件
function ActivityMainPanel:InitEvent()
    self:AddLuaEvent(ActivityConfig.evtSubItemClickChanged, self.EnterActivity, self)
    self:AddLuaEvent(ActivityConfig.evtFrontPageClickChanged, self.EnterActivity, self)
    self:AddLuaEvent(ActivityConfig.evtStartBtnHover, self.OnHoverScrollBoxBtn, self) --悬浮边栏按钮

    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskReward     , self.ShowDataChangeAwards, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityMilestoneAward , self.ShowDataChangeAwards, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityExchangeReward , self.ShowDataChangeAwards, self)

    self:AddLuaEvent(ActivityConfig.evtNewGoBackward, self._OnGoBackward, self)
    self:AddLuaEvent(ActivityConfig.evtNewGoForward, self._OnGoForward, self)

    self:AddLuaEvent(ActivityConfig.evtActivityBack, self._OnBack, self)
    self:AddLuaEvent(ActivityConfig.evtBackBtnChanged, self._BindBackHandler, self)

    -- self:AddLuaEvent(Server.ActivityServer.Events.evtNewActivityFetched, self._OnRefreshGroupTab, self)
    -- self:AddLuaEvent(Server.ActivityServer.Events.evtBannerInfoUpdate, self._OnRefreshGroupTab, self)
    -- self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTrackTaskChange, self.OnRefreshTracktxt, self)

    self:AddLuaEvent(ActivityConfig.evtExchangeChangeBoxStateChanged, self.OnRefreshReddot, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityUpdateSign, self.OnRefreshReddot, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskChange, self.OnRefreshReddot, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityExchangeChange, self.OnRefreshReddot, self)
    -- Module.Gamelet.Config.evtPandoraShowRedpoint:AddListener(self.OnRefreshReddot)

    -- 下面不太清楚是什么活动具体用来做什么用的，保留
    self:AddLuaEvent(ActivityConfig.evtFilingOnArchivePanelSwitch, self._OnOnArchivePanelSwitch, self) 
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityRoleSurveyExchangeClickEnd, self._OnEventActivityRoleSurveyExchangeClick, self)
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 事件

--活动数据变化调用红点刷新事件
function ActivityMainPanel:OnRefreshReddot()
    Server.ActivityServer.Events.evtActivityUpdateRedDot:Invoke()
    self:RefreshTrackBtnRedDot(self._activityID)
end

function ActivityMainPanel:_OnOnArchivePanelSwitch(bIsOpen)
    if bIsOpen then
        -- self:PlayAnimation(self.WBP_Activity_MainPanel_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self._wtActivityMainInfoVBox:Collapsed()
    else
        -- self:PlayAnimation(self.WBP_Activity_MainPanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self._wtActivityMainInfoVBox:SelfHitTestInvisible()
        Module.CommonBar:ChangeBackBtnText(ActivityConfig.Loc.OrdinaryTitle)
        Module.CommonBar:RegStackUITopBarTitle(self, ActivityConfig.Loc.OrdinaryTitle)
    end
end

function ActivityMainPanel:_OnEventActivityRoleSurveyExchangeClick(iActivityID, exchangeInfo, index)
    self:RefreshTrackBtnRedDot(iActivityID)
end

--- 通用展示奖励弹窗
function ActivityMainPanel:ShowDataChangeAwards(dataChange, expandInfo)
    Module.Activity:ShowErrorTipIfNeeded(expandInfo)
    Module.Activity:ShowDataChangeAwards(dataChange, self._activityID)
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 活动分组(页签)逻辑

-- 获取当前所有活动，按分组和活动状态排序
-- 保存并更新相应的查找索引表
function ActivityMainPanel:UpdateGroupPresentationInfo()
    local modeFilter = Module.Activity.Field:IsFilterByModeOn() and Server.ArmedForceServer:GetCurArmedForceMode() or nil
    local groupPresentationInfo = ActivityLogic.GetActivityGroupPresentationInfo(modeFilter)
    local groupIDtoTabIdx = {}
    for groupIdx, groupInfo in pairs(groupPresentationInfo) do
        groupIDtoTabIdx[groupInfo.groupID] = groupIdx
    end

    self._groupPresentationInfo = groupPresentationInfo
    self._groupIDtoTabIdx = groupIDtoTabIdx
end

---根据配置和当前活动的状况，生成顶栏页签注册信息
function ActivityMainPanel:GetCommonBarRegInfoAndGroupInfo()
    local tabsConfig = ActivityLogic.GetEventTabConfigTable()
    local groupPresentationInfo = self._groupPresentationInfo

    -- 生成页签注册配置
    local tabTxtList = {}
    local reddotTrieList = {}
    local themeIdList = {}

    for idx, info in ipairs(groupPresentationInfo) do
        local tabConfig = tabsConfig[info.groupID]

        table.insert(tabTxtList, tabConfig.tabName)

        local redDotKey
        if Module.Activity.Field:IsFilterByModeOn() then
            redDotKey = ActivityRedDotLogic.KeyForModeAndTab(Server.ArmedForceServer:GetCurArmedForceMode(), tabConfig.tabID)
        else
            redDotKey = ActivityRedDotLogic.KeyForTab(tabConfig.tabID)
        end

        table.insert(reddotTrieList, {
            uiNavId = UIName2ID.TopBar,
            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Activity, key = redDotKey}}
        })

        table.insert(themeIdList, tabConfig.themeID)
    end
    
    local result = {} ---@type topTabGroupRegInfo
    result.tabTxtList            = tabTxtList
    result.fCallbackIns          = SafeCallBack(self._OnGroupTabClick, self)
    result.defalutIdx            = 1
    result.tabGroupSize          = FVector2D(1904, 96)
    result.tabSpaceMargin        = FMargin(0, 0, 16, 0)
    result.bTriggerCallback      = true
    result.bNewReddotTrie        = true
    result.reddotTrieRegItemList = reddotTrieList
    result.themeIDList           = themeIdList
    return result, groupPresentationInfo
end

---HD端 刷新活动分组页签，并更新注册信息
function ActivityMainPanel:SetTopBarState(bVisible, optOverrideIndex)
    if IsHD() then
        local topTabGroupRegInfo = self:GetCommonBarRegInfoAndGroupInfo()
        if not bVisible then topTabGroupRegInfo = {} end
        if optOverrideIndex then topTabGroupRegInfo.defalutIdx = optOverrideIndex end
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTabGroupRegInfo)

        Module.CommonBar:SetTopTabGroup(topTabGroupRegInfo, 2)
        return topTabGroupRegInfo
    end
end


function ActivityMainPanel:_Mobile_OnGetTabCount()
    return self._tabData and #self._tabData or 0
end

function ActivityMainPanel:_Mobile_OnProcessTabWidget(pos, widget)
    pos = pos + 1 -- C index to Lua index
    if not self._tabData or not self._tabData[pos] then return end

    local tabData = self._tabData[pos]
    tabData:UpdateTabByLuaTabUIContent(widget)

    if not widget.__tabRedDotProxy then
        local redDotKey
        if Module.Activity.Field:IsFilterByModeOn() then
            redDotKey = ActivityRedDotLogic.KeyForModeAndTab(Server.ArmedForceServer:GetCurArmedForceMode(), tabData.__groupID)
        else
            redDotKey = ActivityRedDotLogic.KeyForTab(tabData.__groupID)
        end

        local redDotProxy = Module.ReddotTrie:RegisterStaticReddotDotWithConfig(widget, {{
            obType = EReddotTrieObserverType.Activity, 
            key = redDotKey
        }})
        widget.__tabRedDotProxy = redDotProxy
        table.insert(self._reddotList, redDotProxy)
    end
end

function ActivityMainPanel:_Mobile_OnTabChanged(pos)
    pos = pos + 1 -- C index to Lua index

    if not self._groupPresentationInfo then return end

    local groupInfo = self._groupPresentationInfo[pos]
    if not groupInfo then return end 

    -- 根据选中页签配置切换主题（仅Mobile，HD端切换主题已经由TopBar注册完成，Mobile需要切换页签时在这里处理）
    Facade.UIManager:SetAutoApplyThemeID(groupInfo.themeID, EApplyThemeIDChangeReason.TabSelection)

    self:_HandleGroupTabClick(groupInfo.groupID)
end

---Mobile 刷新活动分组页签按钮
function ActivityMainPanel:ActivityRefreshBtn()
    -- Reset
    -- for _, tabData in pairs(self._tabData or {}) do
    --     tabData:ResetReddot()
    -- end
    self._tabData = {}

    -- Update
    local eventTabConfigTable = ActivityLogic.GetEventTabConfigTable()
    for idx, groupInfo in ipairs(self._groupPresentationInfo) do
        local tabConfig = eventTabConfigTable[groupInfo.groupID]

        local redDotKey
        if Module.Activity.Field:IsFilterByModeOn() then
            redDotKey = ActivityRedDotLogic.KeyForModeAndTab(Server.ArmedForceServer:GetCurArmedForceMode(), tabConfig.tabID)
        else
            redDotKey = ActivityRedDotLogic.KeyForTab(tabConfig.tabID)
        end

        local tabData = TabUIContentDataV2:NewIns(
            idx,                                                                -- tabDataKey
            tabConfig.tabName,                                                  -- textContent
            true,                                                               -- bShowMainTitleText
            tabConfig.tabIcon,                                                  -- imgContentPath
            true,                                                               -- bShowIcon
            nil                                                                 -- namedSlotVisibility
            -- {                                                                   -- reddotRegItem
            --     reddotDataConfigWithStyleList = {{
            --         obType = EReddotTrieObserverType.Activity, key = redDotKey
            --     }}
            -- }
        )                                            
        tabData.__groupID = tabConfig.tabID

        self._tabData[idx] = tabData
    end

    self._wtTabScrollBox:RefreshTab()
end

---HD/Mobile分发 刷新活动分组页签
function ActivityMainPanel:_OnRefreshGroupTab()
    if not (Module.Activity.Field:IsShowingActivityUI() and self._bTabInView) then return end
    if IsHD() then
        self:SetTopBarState(true, self._selectedTabIndex or 1)
    else
        self:ActivityRefreshBtn()
    end
end

---Mobile: 设置当前选中页签，不会产生回调
function ActivityMainPanel:Mobile_SetSelectedGroupTabButton(groupID)
    if not self._tabData then return end
    for idx, tabData in ipairs(self._tabData) do
        if tabData.__groupID == groupID then
            self._wtTabScrollBox:SetTabIndex(idx - 1, false) -- Lua index to C index
            return
        end
    end
end

local _ValidateGroupPresentationInfoMsgFilter = 0
function ActivityMainPanel:_ValidateGroupPresentationInfo(bShowMsgIfFail)
    if not self._groupPresentationInfo then
        self:UpdateGroupPresentationInfo()
    end
    
    if not (self._groupPresentationInfo and self._groupPresentationInfo[1]) then 
        if bShowMsgIfFail then
            if os.clock() - _ValidateGroupPresentationInfoMsgFilter >= 1 then
                _ValidateGroupPresentationInfoMsgFilter = os.clock()
                Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.NetworkFailure)
            end
        end
        return false
    end

    return true
end

--- 活动组切换回调
function ActivityMainPanel:_OnGroupTabClick(tabIndex)
    ---见OnShowBegin...
    if self._ignoreNextTabGroupCallback then
        self._ignoreNextTabGroupCallback = false
        return
    end

    if not self:_ValidateGroupPresentationInfo(true) then
        return
    end

    if not self._groupPresentationInfo[tabIndex] then
        return
    end

    local activityGroupID = self._groupPresentationInfo[tabIndex].groupID
    self:_HandleGroupTabClick(activityGroupID)
    self:_BindBackHandler()
end

--- 活动组切换回调中间接口
function ActivityMainPanel:_HandleGroupTabClick(activityGroupID)
    LogAnalysisTool.DoSendActivityClientReportLog(2, tostring(activityGroupID), tostring(self._activityID), 0)

    self:ShowFrontPage(activityGroupID)
end

--#endregion 活动分组(页签)逻辑
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 导航返回逻辑

function ActivityMainPanel:_BindBackHandler()
    Module.CommonBar:BindPersistentBackHandler(self._OnBack, self)
end

function ActivityMainPanel:_OnBack(bBackFromStackUIActivity)
    -- 有些活动会打开新的栈UI, 只判断_bShowingActivityContent会导致误关闭活动界面
    -- 这样的活动在退出时要通过 evtActivityBack 并传 bBackFromStackUIActivity = true 告知主面板

    if bBackFromStackUIActivity and self._bShowingActivityContent then
        self:ShowFrontPage(self._selectedActivityGroup)
        self._bShowingActivityContent = false
        return
    end

    if self._bShowingActivityContent == true then
        self:ShowFrontPage(self._selectedActivityGroup)
        Module.CommonBar:CheckTopTabGroup(self._groupIDtoTabIdx[self._selectedActivityGroup], false, 2)
        return
    end
    
    self._wtTabScrollBox:SetTabIndex(0, false)
    --                               ^ C index

    Facade.UIManager:SetAutoApplyThemeID(NONE_THEME_ID)
    Module.Activity:_ToggleArknightsMusic(false)
    Facade.UIManager:CloseUI(self)
end

--#endregion 导航返回逻辑
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 活动页面内显示左侧活动切换页签逻辑

--全刷二级页签
function ActivityMainPanel:OnRefreshSub()
    if IsHD() then
        self._wtScrollBox:RefreshAllItems()
    end
end

function ActivityMainPanel:_OnSubSwitcherGetItemCount()
    if not self:_ValidateGroupPresentationInfo() then return 0 end
    if not self._selectedTabIndex then return 0 end
    if not self._groupPresentationInfo[self._selectedTabIndex] then return 0 end

    return #self._groupPresentationInfo[self._selectedTabIndex].activities
end

--子UI刷新
function ActivityMainPanel:_OnSubSwitcherProcessWidget(position, itemWidget)
    position = position + 1 -- C index to Lua index
    loginfo("ActivityMainPanel:_OnProcessActivityRromptWidget", position, itemWidget)
    if position then
        local activityID = self._groupPresentationInfo[self._selectedTabIndex].activities[position]
        itemWidget:RefreshItemWidget(activityID, self._selectedActivityGroup, self._activityID == activityID)
    end
end

--滚动结束停用红点In动画
function ActivityMainPanel:_OnScrollEnd()
    if not self._bAllowSubItemScroll then return end

    self._bAllowSubItemScroll = false

    local index
    for idx, activityID in ipairs(self._groupPresentationInfo[self._selectedTabIndex].activities or {}) do
        if self._activityID == activityID then
            index = idx
            break
        end
    end

    if not index then return end

    self:OnSubSrollToIndex(index)
    Module.Activity.Field:SetReddotAnimIn(false)
end

--未悬浮面板通知左侧页签收起
function ActivityMainPanel:OnUnhoverScrollBoxBtn()
    logwarning("[activitysub] OnUnhoverScrollBoxBtn")
    ActivityConfig.evtSubItemUnHover:Invoke()
    self._wtScrollBoxbtn:SelfHitTestInvisible()
    self._wtScrollBox:SetScrollBarVisibility(ESlateVisibility.Collapsed)
    self:_ForceUpdateScrollBarVisibilityWorkaround()
end

--展开时需要复原按钮可视性
function ActivityMainPanel:OnHoverScrollBoxBtn()
    logwarning("[activitysub] OnHoverScrollBoxBtn")
    Module.Activity.Field:SetReddotAnimIn(true)
    self._wtScrollBoxbtn:Visible()
    self._wtScrollBox:SetScrollBarVisibility(ESlateVisibility.Visible)
    self:_ForceUpdateScrollBarVisibilityWorkaround()
end

--设置微小偏移以刷新滚动条
function ActivityMainPanel:_ForceUpdateScrollBarVisibilityWorkaround()
    local CurrentOffset = self._wtScrollBox:GetScrollOffset()
    local MaxOffset = self._wtScrollBox:GetScrollOffsetOfEnd()
    local SmallOffset = CurrentOffset + 0.01
    if CurrentOffset >= MaxOffset then
        SmallOffset = CurrentOffset - 0.01
    end
    if CurrentOffset <= 0 then
        SmallOffset = CurrentOffset + 0.01
    end
    self._wtScrollBox:SetScrollOffset(SmallOffset)
end

function ActivityMainPanel:SetActivitySwitchMenuEnable(bVisible, bShowScrollBar)
    if bVisible then
        self._wtScrollBoxbtn:SelfHitTestInvisible()
    else
        self._wtScrollBoxbtn:Collapsed()
    end
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region SubUI页面切换

function ActivityMainPanel:SwitchAndFillSubUI(uiNavID, ...)
    self:PlayAnimation(self.WBP_Activity_MainPanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    Facade.UIManager:RemoveSubUIByParent(self, self._canvasPages)
    local weakIns = Facade.UIManager:SwitchSubUIByIndex(self, uiNavID, self._canvasPages, ...)
    local uiIns = getfromweak(weakIns)
    if uiIns then
        local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
        local commonAnchor = FAnchors()
        commonAnchor.Minimum = FVector2D(0, 0)
        commonAnchor.Maximum = FVector2D(1, 1)
        canvasSlot:SetAnchors(commonAnchor)
        canvasSlot:SetOffsets(FMargin(0, 0, 0, 0))
    else
        Facade.UIManager:RemoveSubUIByParent(self, self._canvasPages)
    end
    self._activeSubUI = uiIns
    return uiIns
end

function ActivityMainPanel:InitSwitchSubUI()
    local subUITable = {}
    for _, activityTypeConfig in pairs(self.activityTypeConfig) do
        local entryPoint = activityTypeConfig.entryPoint
        if type(entryPoint) == "number" and UITable[entryPoint].UILayer == EUILayer.Sub then
            subUITable[entryPoint] = entryPoint
        end
    end
    subUITable[UIName2ID.ActivityFrontPage] = UIName2ID.ActivityFrontPage
    Facade.UIManager:RegSwitchSubUI(self, subUITable)
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 子UI活动展示逻辑

function ActivityMainPanel:_SetBackgroundImage(optActivityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(optActivityID)

    local backgroundImg
    if activityInfo then
        backgroundImg = activityInfo.info1
    else
        backgroundImg = self:_GetLatestBannerBgImg()
    end

    if backgroundImg and backgroundImg ~= "" then
        local backImg = "Resource/Texture/Activity/" .. tostring(backgroundImg)
        self._wtbgImg:SetCDNImage(backImg, false, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
end

function ActivityMainPanel:SetWidgetVisibility_DefaultSubUIActivity()
    -- 将各控件可见性调整到子UI活动的默认状态
    self._wtActivityMainInfoRoot    :SelfHitTestInvisible()
    self._wtActivityMainInfoVBox    :SelfHitTestInvisible()
    self._wtActivityName            :SelfHitTestInvisible()
    self._wtActivityDesc            :SelfHitTestInvisible()
    self._wtTimeAndModePanel        :SelfHitTestInvisible()
    self._wtLotteryOnlyDescPanel    :Collapsed()
    self._wtExtraBtnPanel           :Collapsed()
    self._wtToggleDetailBtn         :Collapsed()
    self._wtPrizePoolSlot           :Collapsed()
    self._wtTrackRewardPanel        :Collapsed()

    self:SetActivityGroupTabsVisible(false)
    self:SetActivitySwitchMenuEnable(true)
end

---仅供EnterActivity调用
function ActivityMainPanel:EnterSubUIActivity(activityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    local tabConfig = ActivityLogic.GetEventTabConfigTable()[self._selectedActivityGroup]

    self:SetWidgetVisibility_DefaultSubUIActivity()                                                     -- 将各控件可见性调整到子UI活动的默认状态
    self:_ProcessSpecialLayout()                                                                        --物流
    self:_BindBackHandler()                                                                             -- 保底绑定返回事件
    self:SwitchAndFillSubUI(activityTypeConfig.entryPoint, self:GetActivityParams(activityID))          -- 切换活动面板
    self:_SetBackgroundImage(activityID)                                                                -- 切换背景图
    self:UpdateSubUIActivityMainInfo(activityID)                                                        -- 刷新左上活动主信息面板
    self:OnRefreshSub()                                                                                 -- 刷新活动切换折叠菜单
    self:RefreshExtraButton(activityID)                                                                 -- 刷新活动额外按钮状态

    -- 根据活动页签设置全局主题
    Facade.UIManager:SetAutoApplyThemeID(tostring(tabConfig.themeID), EApplyThemeIDChangeReason.TabSelection)

    -- 根据该活动在同分组内是否有可进入的前一个/后一个活动决定是否显示上下切换提示箭头
    local arrowType = _ARROW_NONE_
    arrowType = arrowType | (self:FindPrevOrNextActivity(activityID, -1) and _ARROW_PREV_ or _ARROW_NONE_)
    arrowType = arrowType | (self:FindPrevOrNextActivity(activityID,  1) and _ARROW_NEXT_ or _ARROW_NONE_)
    self:SetArrowType(arrowType)

    -- 进入活动不一定经过了页签，所以这里也要判断一下要不要播放联动音乐
    Module.Activity:_ToggleArknightsMusic(tabConfig.themeID == 1)

    -- 顶栏返回按钮文字设置为活动分组名
    local activityGroupName = tabConfig.tabName
    Module.CommonBar:ChangeBackBtnText(tostring(activityGroupName))
    Module.CommonBar:RegStackUITopBarTitle(self, tostring(activityGroupName))
end

function ActivityMainPanel:SetArrowType(arrowType)
    self._desiredArrowType = arrowType

    local function SetArrowActive(arrowWidget, bActive)
        if bActive then
            arrowWidget:SelfHitTestInvisible()
            arrowWidget:PlayAnimation(arrowWidget.WBP_Activity_UnderAarrow_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        else
            arrowWidget:Collapsed()
            arrowWidget:StopAnimation(arrowWidget.WBP_Activity_UnderAarrow_loop)
        end
    end

    -- 只有PC且鼠标操作才显示
    local bMouseInput = IsHD() and (not WidgetUtil.IsGamepad())

    SetArrowActive(self._wtPrevArrow, (arrowType & _ARROW_PREV_) > 0 and bMouseInput)
    SetArrowActive(self._wtNextArrow, (arrowType & _ARROW_NEXT_) > 0 and bMouseInput)
end

----------------------------------------------------------------------------------------------------------
--#region 通用描述弹窗

function ActivityMainPanel:_OnShowRuleButtonClicked(bIsChecked)
    self._wtShowRuleBtn:SetIsChecked(false, false)

    local specialHandlers = {
        [ActivityType.ActivityTypeMakeDrink] = {
            handler = function()
                ActivityConfig.evtActivitySubBetweenInteractive:Invoke(
                    self._activityID, EActSubInteractive.Next6
                )
            end,
            uiName = nil
        },
        [ActivityType.ActivityTypeArknightsRecruit] = {
            uiName = UIName2ID.ArkRecruitRulePop,
        },
        [ActivityType.ActivityTypeArknightsGame] = {
            uiName = UIName2ID.MorgenRulePop,
        },
        [ActivityType.ActivityTypeSBC] = {
            handler = function()
                self:_handleRadioRuleInfos(self._activityID)
            end,
            uiName = UIName2ID.ActivityTouringRuleDescription,
        },
        [ActivityType.ActivityTypeAhsarahTravel] = {
            handler = function()
                self:_handleTaraQuestionMarkTips(self._activityID)
            end,
            uiName = UIName2ID.ActivityTouringRuleDescription,
        },
    }

    local handlerConfig = specialHandlers[self._activityType]
    if handlerConfig and handlerConfig.handler then
        handlerConfig.handler()
        return
    end

    local uiNavID = handlerConfig and handlerConfig.uiName or UIName2ID.ActivityCommonDesction

    local function fFinishCallback(uiIns)
        self.currentChildPage = uiIns
    end
    Facade.UIManager:AsyncShowUI(uiNavID, fFinishCallback, nil, self._activityID)
end

function ActivityMainPanel:_handleTaraQuestionMarkTips(activityID)
    local title = ActivityConfig.Loc.ArkRuleText[1]
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    --描述
    local descTxt = activityInfo and activityInfo.details
    local tabTitleList = {
        ActivityConfig.Loc.GameplayIntroduction,
        ActivityConfig.Loc.OutsideTheChain
    }
    local sectionList = {
        title = {
            ActivityConfig.Loc.ActUITextBlock[1],
            ActivityConfig.Loc.ActUITextBlock[2],
            ActivityConfig.Loc.ActUITextBlock[3],
        },
        icon = {
            ActivityConfig.IconPath[10],
            ActivityConfig.IconPath[11],
            ActivityConfig.IconPath[12],
        },
        desc = {
            ActivityConfig.Loc.ActUITextBlock[4],
            ActivityConfig.Loc.ActUITextBlock[5],
            ActivityConfig.Loc.ActUITextBlock[6],
        },
    }
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityTouringRuleDescription, nil, self, title, descTxt, tabTitleList, sectionList)
end

function ActivityMainPanel:_handleRadioRuleInfos(activityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)

    local ruleDescriptionConfig = {
        title = ActivityConfig.Loc.PlayDescription,
        desc = "策划你要说啥", -- TODO：待改成读取后台数据
        tabs = {
            ActivityConfig.Loc.GameplayIntroduction,
            ActivityConfig.Loc.SupplementExplanation
        },
        sections = {
            title = {
                ActivityConfig.Loc.ObtainTuning,
                ActivityConfig.Loc.ListenInformation,
                ActivityConfig.Loc.PreselectReward,
            },
            icon = {
                ActivityConfig.ERadioImgPath[1],
                ActivityConfig.ERadioImgPath[2],
                ActivityConfig.ERadioImgPath[3],
            },
            desc = {
                ActivityConfig.Loc.RadioDescTxt[1],
                ActivityConfig.Loc.RadioDescTxt[2],
                ActivityConfig.Loc.RadioDescTxt[3],
            }
        }
    }

    local function fFinishCallback(uiIns)
        self.currentChildPage = uiIns
    end
    
    Facade.UIManager:AsyncShowUI(
        UIName2ID.ActivityTouringRuleDescription,
        nil,
        fFinishCallback,
        ruleDescriptionConfig.title,
        ruleDescriptionConfig.desc,
        ruleDescriptionConfig.tabs,
        ruleDescriptionConfig.sections
    )
end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 额外按钮逻辑

function ActivityMainPanel:RefreshExtraButton(activityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    local extraButtonConfig = activityTypeConfig.extraButtonConfig
    if extraButtonConfig == nil then
        self._wtExtraBtnPanel:Collapsed()
    else
        self._wtExtraBtnPanel:SelfHitTestInvisible()
        ActivityLogic.SetTextBlock(self._wtExtraButton, extraButtonConfig.text)
        ActivityLogic.SetImgByPath(self._wtExtraButton, extraButtonConfig.icon)
        if extraButtonConfig.fRedDot then
            self._wtExtraButtonRedDot:SetReddotVisible(extraButtonConfig.fRedDot(activityID), EReddotType.Normal)
        else
            self._wtExtraButtonRedDot:SetReddotVisible(false)
        end
    end
end

-- 额外按钮点击事件
function ActivityMainPanel:_OnExtraButtonClicked()
    if self._activityID == nil then return end

    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    local extraButtonConfig = activityTypeConfig.extraButtonConfig
    if extraButtonConfig and extraButtonConfig.fAction then
        extraButtonConfig.fAction(self._activityID)
    else
        -- self:_OnTrackButtonClicked() --追踪功能暂时不上，注释掉防止误发协议
    end
end

-- 刷新额外按钮红点
function ActivityMainPanel:RefreshTrackBtnRedDot(activityID)
    if not activityID then
        self._wtExtraButtonRedDot:SetReddotVisible(false)
        return
    end

    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    local extraButtonConfig = activityTypeConfig.extraButtonConfig
    if extraButtonConfig and extraButtonConfig.fRedDot then
        self._wtExtraButtonRedDot:SetReddotVisible(extraButtonConfig.fRedDot(activityID))
    else
        self._wtExtraButtonRedDot:SetReddotVisible(false)
    end
end

--#endregion
----------------------------------------------------------------------------------------------------------

function ActivityMainPanel:UpdateSubUIActivityMainInfo(activityID)
    if not activityID then return end
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    if not activityInfo then return end

    -- 刷新子UI活动通用信息
    if activityInfo.name then
        self._name = activityInfo.name
        self._wtActivityName:SetText(activityInfo.name)
    end

    if activityInfo.desc then
        self._wtActivityDesc:SetText(activityInfo.desc)
    end

    if activityInfo.details_show_way == 1 then
        self._wtRuleBtnPanel:SelfHitTestInvisible()
    else
        self._wtRuleBtnPanel:Collapsed()
    end

    if activityInfo.cooperation_label and activityInfo.cooperation_label ~= "" then
        self._wtLinkageImg:SelfHitTestInvisible()
    else
        self._wtLinkageImg:Collapsed()
    end
    
    self._wtModelTag:SetStyle(activityInfo.mode_tag)

    --物流she3特殊处理
    if activityInfo.actv_type == ActivityType.ActivityTypeLotteryTemplate then
        self._wtActivityTime:SetText(ActivityConfig.Loc.SupplementAwards)
        return
    end

    if activityInfo.time_show_way == 1 then
        self._wtActivityTime:Collapsed()
    else
        local startDate = setdefault(activityInfo.start_date, -1)
        local endDate = setdefault(activityInfo.end_date, -1)
        if startDate > 0 and endDate > 0 then
            local starTime = TimeUtil.TransTimestamp2AYYMMDDStr(startDate, "YY/MM/DD")
            local endTime = TimeUtil.TransTimestamp2AYYMMDDStr(endDate, "YY/MM/DD")
            self._wtActivityTime:SetText(string.format(ActivityConfig.Loc.TimeToTime, starTime, endTime))
        elseif endDate <= 0 then
            self._wtActivityTime:SetText(self:_GetTimeLimitedText())
        end
        self._wtActivityTime:SelfHitTestInvisible()
    end
end

function ActivityMainPanel:_GetTimeLimitedText()
    local actInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if actInfo then
        if actInfo.actv_type == ActivityType.ActivityTypeLotteryTemplate then
            return ActivityConfig.Loc.SupplementAwards
        end
        return ActivityConfig.Loc.ActivityFinishedByReward
    end
    return ""
end

--#endregion
----------------------------------------------------------------------------------------------------------

---物流相关
----------------------------------------------------------------------------------------------------------
function ActivityMainPanel:_ProcessSpecialLayout()
    -- if self._activityID == nil then
    --     return
    -- end
    -- local isBool = false
    -- local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    -- if activityInfo then
    --     if activityInfo.actv_type == ActivityType.ActivityTypeLotteryTemplate then
    --         isBool = true
    --     elseif activityInfo.actv_type == ActivityType.ActivityTypeRaidLotteryTemplate then
    --         isBool = true
    --     elseif activityInfo.actv_type == ActivityType.ActivityTypeMakeDrink then
    --         self:OnPlayMainAnim()
    --     end
    -- end
    -- if isBool then
    --     self._wtTimeAndModePanel:Collapsed()
    --     self._wtActivityDesc:Collapsed()
    --     self._wtLotteryOnlyDescPanel:SelfHitTestInvisible()
    --     self._wtLotteryOnlyDescText:Visible()
    --     self._wtToggleDetailBtn:Visible()
    --     self._wtPrizePoolSlot:SelfHitTestInvisible()
    -- end
end
----------------------------------------------------------------------------------------------------------


----------------------------------------------------------------------------------------------------------
--#region 进入FrontPage/进入活动/进入栈UI或自定义活动

---仅供EnterActivity调用
function ActivityMainPanel:EnterStackUIActivity(activityID)
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    Facade.UIManager:AsyncShowUI(activityTypeConfig.entryPoint, nil, nil, self:GetActivityParams(activityID))
end

---将各控件可见性调整到活动分组列表的默认状态
function ActivityMainPanel:SetWidgetVisibility_FrontPage()
    self._wtActivityMainInfoRoot    :Collapsed()
    self._wtActivityMainInfoVBox    :SelfHitTestInvisible()
    self._wtActivityName            :SelfHitTestInvisible()
    self._wtActivityDesc            :SelfHitTestInvisible()
    self._wtTimeAndModePanel        :SelfHitTestInvisible()
    self._wtLotteryOnlyDescPanel    :Collapsed()
    self._wtExtraBtnPanel           :Collapsed()
    self._wtToggleDetailBtn         :Collapsed()
    self._wtPrizePoolSlot           :Collapsed()
    self._wtTrackRewardPanel        :Collapsed()

    self:SetActivityGroupTabsVisible(true)
    self:SetActivitySwitchMenuEnable(false)
end

---打开活动分组列表界面
function ActivityMainPanel:ShowFrontPage(activityGroupID)
    if self._bShowingActivityContent == false and self._selectedActivityGroup == activityGroupID then return end
    if not self._groupIDtoTabIdx[activityGroupID] then return end
    if not self._groupPresentationInfo[self._groupIDtoTabIdx[activityGroupID]] then return end

    self._activityID              = nil
    self._bShowingActivityContent = false
    self._selectedActivityGroup   = activityGroupID
    self._selectedTabIndex        = self._groupIDtoTabIdx[activityGroupID]

    self:Mobile_SetSelectedGroupTabButton(activityGroupID)
    self:SetWidgetVisibility_FrontPage()                                                                -- 将各控件可见性调整到FrontPage默认状态                                        
    self:_ProcessSpecialLayout()                                                                        -- 物流
    self:_SetBackgroundImage(nil)                                                                       -- 切换背景图
    self:SetArrowType(_ARROW_NONE_)

    -- 顶栏返回按钮文字设置为活动分组名
    Module.ItemDetail:CloseAllPopUI()
    Module.CommonBar:ChangeBackBtnText(ActivityConfig.Loc.TopBarTitle)
    Module.CommonBar:RegStackUITopBarTitle(self, ActivityConfig.Loc.TopBarTitle)

    --显示对应页签的活动首页
    local uiIns = self:SwitchAndFillSubUI(UIName2ID.ActivityFrontPage, self._groupPresentationInfo[self._groupIDtoTabIdx[activityGroupID]])

    --把活动列表自定义排序后的顺序同步到主面板的存储
    if uiIns then
        local sortedActivityInfos = uiIns:GetSortedActivities() or {}
        local sortedActivityID = {}
        for idx, info in ipairs(sortedActivityInfos) do
            sortedActivityID[idx] = info.actv_id
        end
        self._groupPresentationInfo[self._groupIDtoTabIdx[activityGroupID]].activities = sortedActivityID
    end

    Facade.UIManager:SetAutoApplyThemeID(self._groupPresentationInfo[self._groupIDtoTabIdx[activityGroupID]].themeID, EApplyThemeIDChangeReason.TabSelection)

    -- 显示首页用背景图
    self._wtbgImg:SetCDNImage("Resource/Texture/Activity/C=AB3EA0A85B642B226A2C61D618BFE1AAAB8A425FF0C81332B84F86D8FB9EA13B.png", false, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    self:RemoveInputSummaries()

    if self._activityTabConfig[activityGroupID] then
        local themeID = self._activityTabConfig[activityGroupID].themeID
        -- 播放音乐(明日方舟需求)
        if themeID == 1 then
            Module.Activity:_ToggleArknightsMusic(true)
        else
            Module.Activity:_ToggleArknightsMusic(false)
        end
    end

    Server.ActivityServer.Events.evtActivityUpdateRedDot:Invoke()
end

---显示/隐藏活动分组切换(TopBar)
function ActivityMainPanel:SetActivityGroupTabsVisible(bVisible)
    if self._bHDTopBarVisible == bVisible then return end
    self._bHDTopBarVisible = bVisible

    if IsHD() then
        if bVisible then
            self:SetTopBarState(true, self._selectedTabIndex or 1)
        else
            self:SetTopBarState(false)
        end
    else
        if bVisible then
            self._wtTabScrollBox:Visible()
        else
            self._wtTabScrollBox:Collapsed()
        end
    end
end

---根据活动配置获取传给活动UI或函数的参数
function ActivityMainPanel:GetActivityParams(activityID)
    local paramHandlers = {
        [_ACTV_ID_] = function(ctx) return ctx.activityID end,
        [_ACTV_INFO_] = function(ctx) return ctx.activityInfo end,
        [_GET_CTRL_FUNC_] = function(ctx) return ctx.panel:MakeGetControlFunc() end,
        [_APP_ID_] = function(ctx) return ctx.pandoraInfo.app_id end,
        [_OPEN_ARGS_] = function(ctx) return ctx.pandoraInfo.open_args end,
        [_PANDORA_ARGS_] = function(ctx) return {iAppID = ctx.pandoraInfo.app_id, tOpenArgs = ctx.pandoraInfo.open_args} end
    }

    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    if not activityInfo then return activityID end

    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]
    if not activityTypeConfig or not activityTypeConfig.customParamList then return activityID end

    local result = {}
    local context = {
        panel = self,
        activityID = activityID,
        activityInfo = activityInfo,
        pandoraInfo = activityInfo.pandora_info or {}
    }

    for index, param in ipairs(activityTypeConfig.customParamList) do
        local handler = paramHandlers[param]
        result[index] = handler and handler(context) or param
    end

    return table.unpack(result)
end

---进入活动
function ActivityMainPanel:EnterActivity(activityID, bNoRetry)
    if not activityID then return end

    if Server.ActivityServer:GetActivityInfoByActivityID(activityID) == nil then return end

    -- 尝试进入的活动是预告态，且玩家在界面等待到解锁后立即进入的话，由于未拉取活动协议，此时该活动在Server是没有全量数据的
    -- 这种情况下需要拉取活动数据再重试该刷新请求，如果活动数据正常更新了，那么 isPreview flag 将被删除，进入正常逻辑
    -- 为了避免拉取失败等原因产生循环，该逻辑产生的重试刷新要传入 bNoRetry，以便拉取仍然失败时结束处理
    if activityID ~= 0 and Server.ActivityServer:HasForecastDataOnly(activityID) == true then
        if bNoRetry then
            Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.NetworkFailure)
        else
            Server.ActivityServer:InitActivityInfo(nil, nil, CreateCallBack(self.EnterActivity, self, activityID, true))
        end
        return
    end

    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
    local activityTypeConfig = self.activityTypeConfig[activityInfo.actv_type]

    if activityTypeConfig == nil then if _WITH_EDITOR == 1 then Module.CommonTips:ShowSimpleTip(ActivityConfig.testLoc.activityNotCompleted) end return end

    Module.Activity.Field:SetLastEnteredActivityID(activityID)
    ActivityLogic.SetActivityBrowsed(activityID)
    Module.ItemDetail:CloseAllPopUI()

    self._activityID              = activityID
    self._activityType            = activityInfo.actv_type
    self._bShowingActivityContent = true
    self._selectedActivityGroup   = activityInfo.tab_belonging
    self._selectedTabIndex        = self._groupIDtoTabIdx[activityInfo.tab_belonging]

    self._bAllowSubItemScroll = true

    local entryPoint = activityTypeConfig.entryPoint
    if type(entryPoint) == "function" then
        local fEnterActivity = entryPoint
        fEnterActivity(self:GetActivityParams(activityID))
    elseif type(entryPoint) == "number" then
        local uiNavID = entryPoint
        if UITable[uiNavID].UILayer == EUILayer.Stack then
            self:EnterStackUIActivity(activityID)
        elseif UITable[uiNavID].UILayer == EUILayer.Sub then
            self:EnterSubUIActivity(activityID)
        end

        --- BEGIN MODIFICATION @ VIRTUOS
        if IsHD() then
            self:UpdateCommonPanelSummaries()
        end
        --- END MODIFICATION

    end
end

--#endregion
----------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
--#region UI生命周期/跳转/打开指定界面逻辑

function ActivityMainPanel:OnShowBegin()
    self._inputMgr:Activate("PrevActivity")
    self._inputMgr:Activate("NextActivity")

    Module.Activity.Field:SetIsShowingActivityUI(true)
    Module.Activity.Field:SetMainPanel(self)

    self:_BindBackHandler()
    self:_SetBackgroundImage(self._activityID)

    --- BEGIN WORKAROUND
    --- 在OnShow之后会有来自 TopBarHD:OnStackViewChange -> ... -> ActivityMainPanel:_OnGroupTabClick
    --- 我们已经自己处理了界面状态，不应该再响应该模拟出来的点击页签事件
    --- 
    --- 正在显示SubUI子活动的时候，如果产生了StackUI切换再返回，由于进入子活动时已经清空了TopBar所以这一次返回
    --- 产生的 OnStackViewChange 不会产生模拟的点击页签事件. 这种情况下不能设置 _bShowingActivityContent
    --- 否则继续从子活动返回活动主面板时 _bShowingActivityContent 将在下一次玩家点击页签切换时阻止刷新内容
    if not self._bShowingActivityContent then
        self._ignoreNextTabGroupCallback = true
    end
    --- END WORKAROUND

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:ObserveInputTypeChange()
        self:UpdateCommonPanelSummaries()
    end
    --- END MODIFICATION
    
    
    --- BEGIN WORKAROUND
    --- 返回到活动内部或活动列表时，需要根据页签设置活动的主题
    --- 在这里的代码执行之后会有来自TopBar模块的 OnStackViewChange -> ... -> Facade.UIManager:SetAutoApplyThemeID
    --- 我们已经自己处理了界面状态，而且因为手游不注册 TopBar，会被强制切换成无主题，所以设置主题需要分两次，强制换回我们需要的主题
    local SetTheme = SafeCallBack(
        function ()
            -- 防止推迟调用时自身已不具有UI控制权
            if not Facade.UIManager:GetCurrentStackUI() == self then return end

            if self._bShowingActivityContent and self._activityID then
                -- 在活动内部，设置为当前活动归属页签主题
                local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
                if activityInfo then
                    if self._activityTabConfig[activityInfo.tab_belonging].themeID == 1 then
                        Module.Activity:_ToggleArknightsMusic(true)
                    end
                    Facade.UIManager:SetAutoApplyThemeID(self._activityTabConfig[activityInfo.tab_belonging].themeID)
                end
            else
                -- 在活动列表，设置为页签主题
                local themeID = self._activityTabConfig[self._selectedActivityGroup].themeID
                Facade.UIManager:SetAutoApplyThemeID(themeID or 0)
            end
        end
        , self
    )

    SetTheme()                          -- 第一次，立即设置
    Timer.DelayCall(0, SetTheme, self)  -- 第二次，下一帧设置，应该足以覆盖 TopBar 产生的主题切换

    Module.Gamelet:SendAllActivePandoraGameEvent("ActivityShow")
end

---@param _groupID      number  一级页签
---@param activityID    number  二级页签
---@param bSendLog      boolean 是否上传经分
function ActivityMainPanel:OnInitExtraData(desiredGroup, desiredActivity, bSendLog)
    self._activityID = nil
    self._selectedTabIndex = nil
    self._selectedActivityGroup = nil

    Module.Activity.Field:SetIsShowingActivityUI(true)
    
    local jumpGroup, jumpActivity, bParamInvalid = self:PreprocessJumpDest(desiredGroup, desiredActivity)

    if jumpGroup == nil then
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.JumpParamErrpr)
        return
    end

    if bSendLog then
        LogAnalysisTool.DoSendActivityClientReportLog(1, tostring(jumpGroup), tostring(jumpActivity), 0)
    end

    if jumpActivity then
        self._selectedActivityGroup = jumpGroup
        self:EnterActivity(jumpActivity)
    else
        self._wtTabScrollBox:SetTabIndex(self._groupIDtoTabIdx[jumpGroup] - 1, false) -- C index
        self:ShowFrontPage(jumpGroup)
    end
    --加载货币
    self:_ShowActNavigationBar()
end

---@return integer  jumpGroup
---@return integer  jumpActivity
---@return boolean  bParamInvalid
function ActivityMainPanel:PreprocessJumpDest(desiredGroup, desiredActivity)
    local jumpGroup = desiredGroup
    local jumpActivity = desiredActivity

    self:UpdateGroupPresentationInfo()
    if not self:_ValidateGroupPresentationInfo(true) then
        return 0, 0, true
    end

    local defaultGroup = self._groupPresentationInfo[1] and self._groupPresentationInfo[1].groupID

    -- 默认值：无参数，打开第一个页签
    if jumpGroup == nil and jumpActivity == nil then
        return defaultGroup, nil, false
    end

    -- 只指定活动ID：查询活动组ID作为jumpGroup，继续检查
    if jumpGroup == nil and jumpActivity ~= nil then
        local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(desiredActivity)
        if not activityInfo then
            Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.JumpParamErrpr)
            return jumpGroup, jumpActivity, true
        end
        jumpGroup = activityInfo.tab_belonging
    end

    -- (从这里开始已确定 jumpGroup 存在) ------------------------------

    -- 检查分组ID
    if not self._groupIDtoTabIdx[jumpGroup] then
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.JumpGroupFailed)
        return defaultGroup, nil, true
    end

    -- (从这里开始已确定 jumpGroup 有效) ------------------------------

    -- 没有指定活动ID：跳转到分组
    if jumpActivity == nil then
        return jumpGroup, nil, false
    end

    -- 指定了活动ID：检查活动ID是否在指定组内存在
    local groupInfo = self._groupPresentationInfo[self._groupIDtoTabIdx[jumpGroup]]
    for _, activityInGroup in pairs(groupInfo.activities) do
        if activityInGroup == jumpActivity then

            if not Server.ActivityServer:IsActivityStarted(jumpActivity) then
                Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.Staytuned)
                return jumpGroup, nil, true
            end

            return jumpGroup, jumpActivity, false
        end
    end

    -- 指定的活动ID和分组ID不匹配
    Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.JumpTargetMissing)
    return defaultGroup, nil, true
end


---@param _groupID number
---@param activityID number
function ActivityMainPanel:Jump(_groupID, activityID, bJumpWithinActivityModule)
    local jumpGroup, jumpActivity, bParamInvalid = self:PreprocessJumpDest(_groupID, activityID)
    if bJumpWithinActivityModule and bParamInvalid then return end

    if jumpActivity then
        self._selectedActivityGroup = jumpGroup
        self:EnterActivity(jumpActivity)
    else
        self:ShowFrontPage(jumpGroup)
    end
end

function ActivityMainPanel:OnHideBegin()

    self._inputMgr:DeactivateAll()

    self._bHDTopBarVisible = nil
    Module.Activity.Field:SetIsShowingActivityUI(false)
    Module.Activity.Field:SetMainPanel()

    if IsHD() then
        self:ObserveInputTypeChange(false)
        self:RemoveInputSummaries()
        Module.CommonBar:BindPersistentBackHandler()
    else
        Module.CommonBar:BindPersistentBackHandler()
    end

    --暂停调酒音效(保险起见双重处理)
    Facade.SoundManager:StopUIAudioEvent("UI_Project_Cocktail_Amb_Start")
    Module.Activity:_ToggleArknightsMusic(false)
end

function ActivityMainPanel:OnClose()
    -- Module.Gamelet.Config.evtPandoraShowRedpoint:RemoveListener(self.OnRefreshReddot)
    self:RemoveAllLuaEvent()

    Facade.UIManager:UnRegSwitchSubUI(self)

    for index, value in ipairs(self._reddotList or {}) do
        Module.ReddotTrie:UnRegisterStaticReddotDot(value)
    end
    self._reddotList = nil
    
    Facade.UIManager:ClearSubUIByParent(self, self._canvasPages)
    LogAnalysisTool.DoSendActivityClientReportLog(4, nil, nil, 1)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:UpdateCommonPanelSummaries()
    end
    --- END MODIFICATION

    Module.Gamelet:SendAllActivePandoraGameEvent("ActivityClose")
end

--#endregion
---------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
--#region 切换上一个或下一个活动

local function FindPair(t, pred)
    for k, v in pairs(t) do
        if pred(v) == true then return k, v end
    end
end

local function IndexValid(idx, list)
    return idx >= 1 and idx <= #list
end

---根据当前的展示状态决定某个活动的上一个或下一个允许进入的活动ID
function ActivityMainPanel:FindPrevOrNextActivity(currActivity, delta)
    if not currActivity then return end
    if not self._groupPresentationInfo then return end

    local now = Facade.ClockManager:GetLocalTimestamp()
    local activityInfo

    -- 根据页签和活动顺序找到应该切换到哪个活动
    while true do
        activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(currActivity)
        if activityInfo == nil then return end
        local groupID = activityInfo.tab_belonging

        -- 找到所在组的上一个/下一个活动
        if not self._groupIDtoTabIdx[groupID] then return end
        local groupInfo = self._groupPresentationInfo[self._groupIDtoTabIdx[groupID]]
        local pos = FindPair(groupInfo.activities, function(id) return id == currActivity end)

        -- 越界代表已经是第一个/最后一个活动，不再处理
        if not IndexValid(pos + delta, groupInfo.activities) then return end

        -- 找到了一个活动
        currActivity = groupInfo.activities[pos + delta]

        -- 检查活动是否开放，不开放的话尝试在当前方向继续寻找
        activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(currActivity)
        if not activityInfo then return end

        local unlockInfo        = ActivityLogic.GetDailyUnLockInfo(activityInfo.mode_leaning, activityInfo.actv_type)
        local bDailyTaskLocked  = unlockInfo and not unlockInfo.bIsUnlocked or false
        local bIsPreviewLocked  = activityInfo.start_date ~= nil and activityInfo.start_date > now

        if bDailyTaskLocked or bIsPreviewLocked then
            -- 活动不开放，currActivity已更新过，继续向同方向找
        else
            return currActivity
        end
    end
end

---先播放切换上一个或下一个活动的动画，然后实际进行切换
function ActivityMainPanel:_GoForwardOrBackward(activityUI, animBeforeSwitch, currActivity, delta)
    local targetActivityID = self:FindPrevOrNextActivity(currActivity, delta)
    if targetActivityID == nil then return end

    local targetActivityInfo = Server.ActivityServer:GetActivityInfoByActivityID(targetActivityID)
    if targetActivityInfo == nil then return end

    local targetGroupID = targetActivityInfo.tab_belonging
    LogAnalysisTool.DoSendActivityClientReportLog(3, tostring(targetGroupID), tostring(targetActivityID), 2)

    local actualSwitchAfterAnimation = CreateCallBack(self.EnterActivity, self, targetActivityID)

    if activityUI then
        if animBeforeSwitch then
            -- 1. 子UI提供动画，则子UI播放自己的切换动画
            local delayTime = animBeforeSwitch:GetEndTime()
            activityUI:PlayAnimation(animBeforeSwitch, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            Timer.DelayCall(delayTime, actualSwitchAfterAnimation)
        else
            -- 2. 子UI不提供动画，MainPanel播放通用动画
            if delta == 1 then
                self:PlayAnimation(self.WBP_Activity_DailyActivity_TurnPage_in_1, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            elseif delta == -1 then
                self:PlayAnimation(self.WBP_Activity_DailyActivity_TurnPage_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            end
            actualSwitchAfterAnimation()
        end
    else
        -- 3. 子UI不存在，直接切换，不播放动画
        actualSwitchAfterAnimation()
    end
end

function ActivityMainPanel:_OnGoBackward(activityUI, animBeforeSwitch)
    self:_GoForwardOrBackward(activityUI, animBeforeSwitch, self._activityID, -1)
end

function ActivityMainPanel:_OnGoForward(activityUI, animBeforeSwitch)
    self:_GoForwardOrBackward(activityUI, animBeforeSwitch, self._activityID, 1) -- 对这里确实是 -1 而不是 1，代码重构的时候不知道是哪里的逻辑反了
end

---未悬浮面板通知左侧页签收起
function ActivityMainPanel:OnUnHoverScrollBoxbtn()
    ActivityConfig.evtSubItemUnHover:Invoke()
    self._wtScrollBoxbtn:SelfHitTestInvisible()
    self._wtScrollBox:SetScrollBarVisibility(ESlateVisibility.Collapsed)
    self:_OnSetScrollBoxSmallOffset()
end

--#endregion
---------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
--#region TODO: 追踪相关，暂时没有用到，活动修改后大概率已经无法正常使用，需要时再重写

function ActivityMainPanel:_OnGetTrackRewardRromptCount()
    -- return self._trackRewardNum
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    self._rewardArr = {}
    if activityInfo then
        if activityInfo.reward_showed1 ~= 0 then
            table.insert(self._rewardArr, activityInfo.reward_showed1)
        end
        if activityInfo.reward_showed2 ~= 0 then
            table.insert(self._rewardArr, activityInfo.reward_showed2)
        end
        if activityInfo.reward_showed3 ~= 0 then
            table.insert(self._rewardArr, activityInfo.reward_showed3)
        end
    end
    return #self._rewardArr
end

function ActivityMainPanel:_OnProcessTrackRewardRromptWidget(position, itemWidget)
    -- local task = Server.ActivityServer:GetTaskInfosByActivityID(self._activityID)
    -- if task and task[position] and task[position].awards and task[position].awards[1] then
    --     local itemReward = ItemBase:New(task[position].awards[1].prop.id)
    --     itemWidget:InitItem(itemReward)
    -- end
    if self._rewardArr and self._rewardArr[position] then
        local itemReward = ItemBase:New(self._rewardArr[position])
        --设置itemview边距
        if DFHD_LUA == 1 then
            itemWidget:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.CommonViewItemViewB)
            self._wtRewardIcon:SetRootSize(256, 256)
        else
            itemWidget:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.CommonViewItemViewC)
            self._wtRewardIcon:SetRootSize(192, 192)
        end
        itemWidget:InitItem(itemReward)
    end
end

function ActivityMainPanel:_GetTrackTab1(groupType)
    if groupType ~= EActivitySelect.TrackActivity then
        return 1
    end
    local activityIdList = Server.ActivityServer:GetActivityIDsByGroupID(EActivitySelect.TrackActivity)
    local trackingid = 1
    for key, activityId in pairs(activityIdList) do
        local trackingState = Server.ActivityServer:GetActivityTrackingState(activityId)
        if trackingState then
            trackingid = key
            break
        end
    end
    return trackingid
end

function ActivityMainPanel:_OnTrackButtonClicked()
    self:StopAnimation(self.WBP_Activity_MainPanel_track)
    local num = Module.Activity.Field:GetActivityTracknum()
    local MaxTrackingNum = Server.ActivityServer:GetActivityMaxTracking()
    if MaxTrackingNum == 1 and num == 1 then
        local trackActivityID = 0
        local function fOnConfirmCallback()
            Server.ActivityServer:UnTrackInfo(trackActivityID, self._activityID, true)
        end
        local function fOnCancelCallback()
            self:PlayAnimation(self.WBP_Activity_MainPanel_track, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        end
        local trackList = Server.ActivityServer:GetActivityInfosByGroupID(EActivitySelect.TrackActivity)
        for index, value in ipairs(trackList) do
            if value.is_tracking then
                trackActivityID = value.actv_id
                if trackActivityID == self._activityID then
                    Server.ActivityServer:UnTrackInfo(self._activityID)
                    return
                end
            end
        end
        local activityName = Server.ActivityServer:GetActivityName(trackActivityID)
        local activityNowName = Server.ActivityServer:GetActivityName(self._activityID)
        Module.CommonTips:ShowConfirmWindow(string.format(ActivityConfig.Loc.CancelTrackedTaskTrackCurrenttask,activityName,activityName,activityNowName,activityName), fOnConfirmCallback, fOnCancelCallback, ActivityConfig.Loc.Cancel,ActivityConfig.Loc.Confirm)
    elseif MaxTrackingNum == 3 and num == 3 then
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.TipsMaxTip)
        self:PlayAnimation(self.WBP_Activity_MainPanel_track, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    else
        Server.ActivityServer:TrackInfo(self._activityID)
    end
end

function ActivityMainPanel:SetActivityTrackCount()
    --设置当前已追踪的活动数量
    local trackList =  Server.ActivityServer:GetActivityInfosByGroupID(EActivitySelect.TrackActivity)
    local activityVar = 0
    if trackList then
        for index, value in ipairs(trackList) do
            if value.is_tracking then
                activityVar = activityVar + 1
            end
        end
        Module.Activity.Field:SetActivityTracknum(activityVar)
    end
end


--#endregion
---------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
--#region 其他逻辑

--TODO:主面板滚动侧边栏到指定索引[invalid]
function ActivityMainPanel:OnSubSrollToIndex(index)
    if DFHD_LUA == 1 then
        local item = self._wtScrollBox:GetItemByIndex(index - 1)
        self._wtScrollBox:ScrollWidgetIntoView(item, true, EDescendantScrollDestination.IntoView)
    end 
end

function ActivityMainPanel:_GetLatestBannerBgImg()
    local bannerInfos = Server.ActivityServer:GetBannerInfosByID(0)
    local bannerFilterInfos = Server.ActivityServer:GetFilterBannerInfosByID(0)
    local backgroundImg = ""
    local curBanner = nil
    local maxTime = nil
    if bannerInfos then
        if bannerFilterInfos then
            for idx, banner in ipairs(bannerFilterInfos) do
                if maxTime == nil or banner.start_time > maxTime then
                    maxTime = banner.start_time
                    curBanner = banner
                end
            end
        else
            for idx, banner in ipairs(bannerInfos) do
                if maxTime == nil or banner.end_time > maxTime then
                    maxTime = banner.end_time
                    curBanner = banner
                end
            end
        end
        backgroundImg = curBanner.pic_resource
    end
    return backgroundImg
end

------------------------------------------------------测试接入pandora活动------------------------------------------------------
-- 弹窗Pop
-- slua.Do Module.Gamelet:ShowPandoraPopUI(5246,0)
-- function ActivityMainPanel:OpenPandoraPopUI()
--     local appId = Module.Gamelet.Config.PandoraPopAppTestId
--     -- local openArgs = {}
--     -- Module.Gamelet:OpenApp(appId, openArgs)
--     Module.Gamelet:ShowPandoraPopUI(appId, openArgs)
-- end

-- function ActivityMainPanel:OpenPandoraSubUI()
--     local function fFinishCallback(uiIns)
--         --更换控件
--         self._canvasPages:ClearChildren()
--         self._canvasPages:AddChild(uiIns)
--         local commonAnchor = FAnchors()
--         commonAnchor.Minimum = FVector2D(0, 0)
--         commonAnchor.Maximum = FVector2D(1, 1)
--         uiIns.Slot:SetAnchors(commonAnchor)
--         uiIns.Slot:SetOffsets(FMargin(0, 0, 0, 0))

--         self._currentChildPage = uiIns
--         --存储appId
--         uiIns.appId = appId
--         -- Module.Gamelet:OpenApp(appId, openArgs)
--         Module.Gamelet:ShowPandoraPopUI(appId, openArgs)
--     end
--     local appId = Module.Gamelet.Config.PandoraPopAppTestId
--     local openArgs = {}
--     Facade.UIManager:AsyncShowUI(UIName2ID.PandoraActivitySubUI, fFinishCallback, nil, appId)
-- end

--#endregion
----------------------------------------------------------------------------------------------------------

----------------------------------------------------------------------------------------------------------
--#region 输入模式切换逻辑 
--- BEGIN MODIFICATION @ VIRTUOS

function ActivityMainPanel:AddInputSummary(summary)
    -- 项目不能为空或重复
    if not summary or self._inputSummaries_revidx[summary] then
        return
    end

    table.insert(self._inputSummaries, summary)
    self._inputSummaries_revidx[summary] = #self._inputSummaries

    Module.CommonBar:SetBottomBarInputSummaryList(self._inputSummaries, false)
end

function ActivityMainPanel:RemoveInputSummaries()
    self._inputSummaries = {}
    self._inputSummaries_revidx = {}
    Module.CommonBar:ClearBottomBarStackSummaryList()
end

function ActivityMainPanel:UpdateCommonPanelSummaries()
    self:RemoveInputSummaries()
    -- Update activity details panel widgets gamepad input summaries.
    if self._wtShowRuleBtn:IsVisible() then
        if self._wtRuleBtnPanel:IsVisible() then
            self:AddInputSummary({actionName = "Common_ToggleTip", func = self._wtShowRuleBtn.SelfClick , caller = self._wtShowRuleBtn})
        end
        
        if self._wtExtraButton:IsVisible() and self._wtExtraBtnPanel:IsVisible() then
            self._wtExtraButton:SetDisplayInputAction("Common_ButtonTop", false, nil, true)
            self:AddInputSummary({actionName = "Common_ButtonTop", func = self._wtExtraButton.ButtonClick , caller = self._wtExtraButton, bOnlyUI = false, bHide = true})
        end
    end 
    --物流手柄适配
    if self._activityType == ActivityType.ActivityTypeLotteryTemplate or self._activityType == ActivityType.ActivityTypeRaidLotteryTemplate then
        ActivityConfig.evtOpenLotteryDrawPanel:Invoke(self._activityID)
    end
end

function ActivityMainPanel:ObserveInputTypeChange(valid)
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    if valid == false then
        return
    end

    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    end
end

function ActivityMainPanel:_OnInputTypeChanged(InputType)
    local isGamepad = InputType == EGPInputType.Gamepad

    -- Hide side scroll box if gamepad active.
    if isGamepad then
        self._wtDFSizeBox:Collapsed()
    else
        self._wtDFSizeBox:SelfHitTestInvisible()
    end

    -- Update arrow type.
    self:SetArrowType(self._desiredArrowType)
end

--- END MODIFICATION
--#endregion
----------------------------------------------------------------------------------------------------------

--框架按钮控制
function ActivityMainPanel:SetActivityMainCommonBar(isBool)
    if IsHD() then
        ActivityLogic.SetUIVisible(self._wtDFSizeBox, isBool)
        ActivityLogic.SetUIVisible(self._wtPrevArrow, isBool)
        ActivityLogic.SetUIVisible(self._wtNextArrow, isBool)
    end
    Module.CommonBar:SetTopBarVisible(isBool)
end

function ActivityMainPanel:OnPlayMainAnim(animType)
    if animType == nil then
        ActivityLogic.SetUIVisible(self._wtActivityMainInfoRoot, true)
        local animName = self.WBP_Activity_MainPanel_in01
        if animName then
            self:PlayWidgetAnimAt(animName, animName:GetEndTime())
        end
    elseif animType == 0 then
        ActivityLogic.SetUIVisible(self._wtActivityMainInfoRoot, true, 0)
        local animName = self.WBP_Activity_MainPanel_out01
        if animName then
            ActivityLogic.OnPlayAnim(self, animName, false)
        end
    elseif animType == 1 then
        ActivityLogic.SetUIVisible(self._wtActivityMainInfoRoot, true)
        local animName = self.WBP_Activity_MainPanel_in01
        if animName then
            ActivityLogic.OnPlayAnim(self, animName, false)
        end
    end
end

return ActivityMainPanel
