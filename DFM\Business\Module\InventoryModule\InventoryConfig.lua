----- <PERSON>O<PERSON> FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 道具提交
UITable[UIName2ID.ItemsSubmitPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsSubmitPanel"),
    BPKey = "WBP_ItemsSubmitPanel",
    SubUIs = {
        UIName2ID.ItemsSubmitItem,
        UIName2ID.PropSubmitItem,
        UIName2ID.WeaponSubmitItem,
        UIName2ID.PropAvailableItem,
        UIName2ID.WeaponAvailableItem,
        UIName2ID.WeaponRestrictionsComponent,
    },
    IsModal = true,
}

UITable[UIName2ID.ItemsSubmitItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsSubmitItem"),
    BPKey = "WBP_ItemsSubmitItem"
}

UITable[UIName2ID.PropSubmitItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsSubmitItem"),
    BPKey = "WBP_PropSubmitItem"
}

UITable[UIName2ID.WeaponSubmitItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsSubmitItem"),
    BPKey = "WBP_WeaponSubmitItem"
}

UITable[UIName2ID.PropAvailableItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsAvailableItem"),
    BPKey = "WBP_PropSubmission"
}

UITable[UIName2ID.WeaponAvailableItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.ItemsAvailableItem"),
    BPKey = "WBP_WeaponSubmission"
}

UITable[UIName2ID.WeaponRestrictionsComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemsSubmit.WeaponRestrictionsComponent"),
    BPKey = "WBP_WeaponRestrictionsComponent",
    SubUIs = {
		UIName2ID.QuestObjectiveCompoDesc
	},
}

UITable[UIName2ID.ItemViewParts] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.ItemView.ItemViewParts"),
    BPKey = "WBP_ItemViewParts"
}


-- UITable[UIName2ID.WarehouseTopHint] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.MainV2.WarehouseTopHint"),
--     BPKey = "WBP_DepositSelectAllPanel"
-- }

UITable[UIName2ID.WarehouseSelectAllHint_PC] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UIHD.WarehouseTopHint_HD"),
    BPKey = "WBP_Warehouse_SelectSub_Pc"
}

UITable[UIName2ID.WarehouseSelectAllHint] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.MainV2.WarehouseTopHint"),
    BPKey = "WBP_Warehouse_SelectSub"
}


UITable[UIName2ID.WarehouseBatchSellWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.MainV2.WarehouseBatchSellWindow"),
    BPKey = "WBP_WarehouseForSalePop",
    IsModal = true,
    IsAnimBlock = false,
}

UITable[UIName2ID.WarehouseBatchSellWindowItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.MainV2.WarehouseBatchSellWindowItem"),
    BPKey = "WBP_WarehouseForSalePopItem"
}

-----------------------------------------------------------------------
--region Common
--BEGIN MODIFICATION @ VIRTUOS : 对InvSlotView的SubUI根据HD做出区分
if IsHD() then
    UITable[UIName2ID.InvSlotView] = {
        UILayer = EUILayer.Sub,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.InvSlotView"),
        BPKey = "ItemSlotView",
        SubUIs = {
            UIName2ID.IVWarehouseTemplate,
            UIName2ID.IVGreyMask,
            UIName2ID.CommonItemHighlight,
            --BEGIN MODIFICATION @ VIRTUOS :
            UIName2ID.IVCommonBlankItemTemplate,
            --END MODIFICATION
        }
    }
else
    UITable[UIName2ID.InvSlotView] = {
        UILayer = EUILayer.Sub,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.InvSlotView"),
        BPKey = "ItemSlotView",
        SubUIs = {
            UIName2ID.IVWarehouseTemplate,
            UIName2ID.IVGreyMask,
            UIName2ID.CommonItemHighlight,
        }
    }
end
--END MODIFICATION


-- UITable[UIName2ID.ItemSlotCell] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "",
--     BPKey = "ItemSlotCell"
-- }

UITable[UIName2ID.ArchivePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.ArchivePanel"),
    BPKey = "WBP_ArchivePanel"
}

UITable[UIName2ID.ArchiveSubView] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.ArchiveSubView"),
    BPKey = "WBP_ArchiveSubView"
}

UITable[UIName2ID.ExtensionSlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.ExtensionSlotView"),
    BPKey = "WBP_ExtensionSlotView"
}

UITable[UIName2ID.AucSellConfirmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.AucSellConfirmWindow"),
    BPKey = "WBP_WarehouseSellWindow",
    IsModal = true,
}

UITable[UIName2ID.WarehouseExtSubPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Extension.WarehouseExtSubPanel",
    BPKey = "WBP_WarehouseExpansionManager",
    SubUIs = {
        UIName2ID.WarehouseExtSubCell
    }
}

UITable[UIName2ID.WarehouseExtSubCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Extension.WarehouseExtSubCell",
    BPKey = "WBP_WarehouseExpansionManagerItem",
    SubUIs = {
        UIName2ID.IVItemSelectedComponent
    }
}

UITable[UIName2ID.WarehouseRepairWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Repair.WarehouseRepairWindow",
    BPKey = "WBP_WarehouseRepairWindow",
    IsModal = true,
    -- Anim = {
    --     FlowInAni = "WBP_WarehouseRepairWindow_in",
    --     FlowOutAni = "WBP_WarehouseRepairWindow_out"
    -- }
}

UITable[UIName2ID.WarehouseRepairWindowItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Repair.WarehouseRepairWindowItem",
    BPKey = "WBP_WarehouseRepairItem",
}

-- UITable[UIName2ID.ArrangePopWindow] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.ArrangePopWindow",
--     BPKey = "WBP_WarehouseFinishingSettings",
--     IsModal = true,
-- }

-- MS23新增
UITable[UIName2ID.WarehouseContainerPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel",
    BPKey = "WBP_WarehouseContainerPanel",
    SubUIs = {
        UIName2ID.InvSlotView,
        UIName2ID.WarehouseEquipSlotView,
    },
}

UITable[UIName2ID.WarehouseContainerPanel_HD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD",
    BPKey = "WBP_WarehouseContainerPanel_Pc",
    SubUIs = {
        UIName2ID.InvSlotView,
        UIName2ID.WarehouseEquipSlotView,
    },
}

UITable[UIName2ID.WarehouseKeyContainerPanel_HD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.WarehouseKeyContainerPanel_HD",
    BPKey = "WBP_KeyContainerView_Pc",
    SubUIs = {
        UIName2ID.KeyContainerViewItem,
        UIName2ID.WarehouseEquipSlotView,
    }
}


--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Item Selection

UITable[UIName2ID.InvItemSelectWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.ItemSelection.InvItemSelectWindow",
    BPKey = "WBP_InvItemSelectWindow",
    Anim = {
        FlowInAni = "In_Anim"
    }
}

UITable[UIName2ID.InvAdapterSelectWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.ItemSelection.InvAdapterSelectWindow",
    BPKey = "WBP_InvItemSelectWindow",
    Anim = {
        FlowInAni = "In_Anim"
    }
}

UITable[UIName2ID.InvPerkSelectWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.ItemSelection.InvPerkSelectWindow",
    BPKey = "WBP_InvItemSelectWindow",
    Anim = {
        FlowInAni = "In_Anim"
    },
    SubUIs = {
        UIName2ID.FastEquipContainter
    }
}

UITable[UIName2ID.InvSelectionCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.ItemSelection.InvSelectionCell",
    BPKey = "WBP_InvSelectionCell"
}

--endregion
-----------------------------------------------------------------------

UITable[UIName2ID.ItemRepairPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Repair.ItemRepairPanel"),
    BPKey = "WBP_ItemRepairPanel",
    SubUIs = {
        UIName2ID.ItemRepairConsumeItemView
    }
}

UITable[UIName2ID.ItemRepairConsumeItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Repair.ItemRepairConsumeItemView"),
    BPKey = "WBP_ItemRepairConsumeItemView"
}

-- UITable[UIName2ID.ItemStackPanel] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UI.Common.ItemStackPanel"),
--     BPKey = "H_Bag_PopWindows"
-- }

UITable[UIName2ID.KeyUnlockWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.KeyUnlockWindow",
    BPKey = "WBP_WarehouseKeySlotWindow_PopWindow"
}

UITable[UIName2ID.WarehouseWithTabBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Common.WarehouseWithTabBtn",
    BPKey = "WBP_WarehouseWithTabButton"
}

-----------------------------------------------------------------------
--region New warehouse

if IsHD() then
    -- 仓库主界面
	UITable[UIName2ID.WarehouseMain] = {
		UILayer = EUILayer.Stack,
		LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UIHD.WarehouseMain_HD"),
		BPKey = "WBP_WarehouseMain_Pc",
		SubUIs = {
			UIName2ID.WarehouseEquipPanel_HD,
            UIName2ID.WarehouseWithTab_HD,
            UIName2ID.CharacterCaptureUtil,
            UIName2ID.CommonKeyIconTips,
		},
        ReConfig = {
            IsPoolEnable = true,
        },
        Anim = {
            -- bManuelAnim = true,
            FlowInAni = "WBP_WarehouseMain_in",
            FlowOutAni = "WBP_WarehouseMain_out"
        },

        bEnableWorldRendering = true
	}

    -- 配装页面
    UITable[UIName2ID.WarehouseEquipPanel_HD] = {
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UIHD.WarehouseEquipPanel_HD"),
        UILayer = EUILayer.Sub,
        BPKey = "WBP_WarehouseEquipPanel_Pc",
        SubUIs = {
            UIName2ID.InvSlotView,
            UIName2ID.WarehouseEquipSlotView,
            UIName2ID.WarehouseKeyContainerPanel_HD,
            UIName2ID.WareHouseContainerBox_HD
        }
    }

    -- 仓库页面
    UITable[UIName2ID.WarehouseWithTab_HD] = {
        UILayer = EUILayer.Sub,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "InventoryModule.UIHD.WarehouseWithTab_HD"),
        BPKey = "WBP_WarehouseWithTab_Pc",
        SubUIs = {
            UIName2ID.WarehouseDepositTab,
            UIName2ID.Warehouse,
            UIName2ID.WarehouseListCell,
            UIName2ID.WarehouseExtSubPanel,
            UIName2ID.WarehouseSellSub,
            UIName2ID.WarehouseExtSub,
            UIName2ID.WarehouseWithTabBtn,
        }
    }
else
    UITable[UIName2ID.WarehouseMain] = {
        UILayer = EUILayer.Stack,
        LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseMain",
        BPKey = "WBP_WarehouseMain",
        SubUIs = {
            UIName2ID.WarehouseWithTab,
            UIName2ID.WarehouseEquipPanel,
            UIName2ID.ExtReplaceSelectionWindow,
        },
        --- 默认Cache配置
        ReConfig = {
            IsPoolEnable = true,
        },
    }
end

UITable[UIName2ID.WarehouseEquipPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipPanel",
    BPKey = "WBP_WarehouseEquipPanel",
    SubUIs = {
        UIName2ID.EquipmentContainerPreview,
        UIName2ID.InvSlotView,
        UIName2ID.WarehouseEquipSlotView,
        UIName2ID.KeyContainerView,
        UIName2ID.WareHouseContainerBox
    }
}


UITable[UIName2ID.WarehouseEquipSlotView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView",
    BPKey = "WBP_WarehouseEquipSlotView",
    SubUIs = {
        UIName2ID.HDKeyIconBoxText,
        UIName2ID.WHEquipKeyIconBoxText,
        UIName2ID.HDWHEquipKeyIconBoxText,
        UIName2ID.ItemviewRepair,
        UIName2ID.IVWarehouseTemplate
    }
}

UITable[UIName2ID.WarehouseWithTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseWithTab",
    BPKey = "WBP_WarehouseWithTab",
    SubUIs = {
        UIName2ID.WarehouseDepositTab,
        UIName2ID.Warehouse,
        UIName2ID.WarehouseListCell,
        UIName2ID.WarehouseExtSubPanel,
        UIName2ID.WarehouseSellSub,
        UIName2ID.WarehouseExtSub
    }
}

if IsHD() then
    UITable[UIName2ID.WarehouseDepositTab] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UIHD.WarehouseDepositTab_HD",
        BPKey = "WBP_WarehouseExpansionButton_Pc",
    }
else
    UITable[UIName2ID.WarehouseDepositTab] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseDepositTab",
        BPKey = "WBP_WarehouseExpansionButton",
    }
        
end

UITable[UIName2ID.Warehouse] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.Warehouse",
    BPKey = "WBP_Warehouse",
    SubUIs = {
        UIName2ID.InvSlotView,
        UIName2ID.WarehouseDragDropMask
    }
}

UITable[UIName2ID.WarehouseListCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseListCell",
    BPKey = "WBP_WarehousePropCommon"
}

UITable[UIName2ID.WarehouseDragDropMask] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonDragDropMask", 
    BPKey = "WBP_WarehouseScroll"
}

UITable[UIName2ID.WarehouseDragDropMaskShadow] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.CommonWidgetModule.UI.CommonDragDropMask", 
    BPKey = "WBP_ScrollMask"
}

if IsHD() then
    UITable[UIName2ID.WareHouseContainerBox_HD] = {
        UILayer = EUILayer.Sub,
        LuaPath= "DFM.Business.Module.InventoryModule.UIHD.WareHouseContainerBox_HD",
        BPKey = "WBP_WarehouseSafeBoxItem_Pc",
        SubUIs = {
            UIName2ID.InvSlotView,
            UIName2ID.WarehouseEquipSlotView,
            UIName2ID.ContainerMask
        }
    }
else
    UITable[UIName2ID.WareHouseContainerBox] = {
        UILayer = EUILayer.Sub,
        LuaPath= "DFM.Business.Module.InventoryModule.UI.Component.WareHouseContainerBox",
        BPKey = "WBP_WarehouseSafeBoxItem",
        SubUIs = {
            UIName2ID.InvSlotView,
            UIName2ID.WarehouseEquipSlotView,
        }
    }
end

UITable[UIName2ID.ContainerMask] = {
    UILayer = EUILayer.Sub,
    LuaPath= "DFM.Business.Module.InventoryModule.UI.Component.ContainerMask",
    BPKey = "WBP_SlotCompWarehouseMask"
}

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region New selection window

UITable[UIName2ID.NewExtSelectionWindowCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Selection.NewExtSelectionWindowCell",
    BPKey = "WBP_WarehouseExpansionItem"
}

UITable[UIName2ID.NewExtSelectionWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Selection.NewExtSelectionWindow",
    BPKey = "WBP_WarehouseExpansionWindow",
    SubUIs = {
        UIName2ID.NewExtSelectionWindowCell,
        UIName2ID.ItemDetailView,
        UIName2ID.ItemDetailContentExchange,
        UIName2ID.ItemDetailContentSwitcher,
        UIName2ID.CommonEmptyContent,
    },
    IsModal = true,
}

UITable[UIName2ID.ExtReplaceSelectionWindow] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Extension.ExtReplaceSelectionWindow",
    BPKey = "WBP_WarehouseExpansionReplacePanel",
    SubUIs = {
        UIName2ID.NewExtSelectionWindowCell,
        UIName2ID.WarehouseDepositTab,
    },
    Anim = {
        FlowInAni = "WBP_WarehouseExpansionReplacePanel_in",
        FlowOutAni = "WBP_WarehouseExpansionReplacePanel_out"
    },
    IsModal = true,
}

UITable[UIName2ID.ExtReplaceConfirmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Extension.ExtReplaceConfirmWindow",
    BPKey = "WBP_WarehouseExpansionReplaceTip",
    SubUIs = {
        UIName2ID.ExtReplaceConfirmWindowItem
    },
    IsModal = true,
}

UITable[UIName2ID.ExtReplaceConfirmWindowItem] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Extension.ExtReplaceConfirmWindowItem",
    BPKey = "WBP_WarehouseExpansionTipItem"
}

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Key and SafeBox

-- if IsHD() then
--     UITable[UIName2ID.KeyContainerView_HD] = {
--         UILayer = EUILayer.Sub,
--         LuaPath = "DFM.Business.Module.InventoryModule.UIHD.KeyContainerView_HD",
--         BPKey = "WBP_KeyContainerView_Pc",
--         SubUIs = {
--             UIName2ID.KeyContainerViewItem
--         }
--     }
-- end

UITable[UIName2ID.KeyContainerView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.KeyContainerView",
    BPKey = "WBP_KeyContainerView",
    SubUIs = {
        UIName2ID.KeyContainerViewItem,
        UIName2ID.WarehouseEquipSlotView,
        UIName2ID.ContainerMask
    }
}

UITable[UIName2ID.KeyContainerViewItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.KeyContainerSubView",
    BPKey = "WBP_KeyContainerViewItem"
}

UITable[UIName2ID.SafeBoxAndKeyChainPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.SafeBoxAndKeyChainPopWindow",
    BPKey = "WBP_WarehouseSafetyBoxPop",
    SubUIs = {
        UIName2ID.SafeBoxAndKeyChainItem
    },
    IsModal = true,
}

UITable[UIName2ID.SafeBoxAndKeyChainItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.SafeBoxAndKeyChainItem",
    BPKey = "WBP_WarehouseSafetyBoxItem",
}

UITable[UIName2ID.SafeBoxAndKeyChainMain] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.SafeBoxAndKeyChainMain",
    BPKey = "WBP_WarehouseSafetyBox_Main",
    ReConfig = {
        IsPoolEnable = true,
    },
}

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
---region 旧的UI配置
-- Define config about inventory module here

UITable[UIName2ID.TreasureMapMain] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ItemDetailModule.UI.TreasureMapMain",
    BPKey = "WBP_TreasureMapMain"
}

--- ItemView Component

--- Common

---endregion
-----------------------------------------------------------------------


-----------------------------------------------------------------------
--region Ext Arrangement

UITable[UIName2ID.WarehouseArrangeWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseArrangeWindow",
    BPKey = "WBP_Warehouse_ArrangementWindow_PopWindow",
    SubUIs = {
        UIName2ID.WarehouseArrangeWindowCell
    }
}

UITable[UIName2ID.WarehouseArrangeWindowCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseArrangeWindowCell",
    BPKey = "WBP_Warehouse_ArrangementWindow_BoxS1_PopWindow"
}

--#region 愿望单
UITable[UIName2ID.WishPopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Wish.WishPopView",
    BPKey = "WBP_Warehouse_WishList",
    Anim = {
        FlowInAni = "WBP_Warehouse_WishList_in_01",
        FlowOutAni = "WBP_Warehouse_WishList_out_01",
    },
    IsModal = true,
}

-- azhengzheng:愿望单HD
UITable[UIName2ID.WishPopView_HD] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Wish.WishPopView_HD",
    BPKey = "WBP_Warehouse_WishList_Pc",
    IsModal = true,
}

UITable[UIName2ID.WishItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Wish.WishItem",
    BPKey = "WBP_Warehouse_WishListBox",
    Anim = {
        FlowInAni = "WBP_Warehouse_WishListBox_in",
    }
}
--#endregion

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Ext And Sell Panel

if IsHD() then
    UITable[UIName2ID.WarehouseSellSub] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UIHD.WarehouseSellSubPanel_HD",
        BPKey = "WBP_Warehouse_SellSub_Pc"
    }

    UITable[UIName2ID.WarehouseExtSub] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UIHD.WarehouseExtArrangeSubPanel_HD",
        BPKey = "WBP_Warehouse_ExtArrange_Pc"
    }
else
    UITable[UIName2ID.WarehouseSellSub] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseSellSubPanel",
        BPKey = "WBP_Warehouse_SellSub"
    }

    UITable[UIName2ID.WarehouseExtSub] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseExtArrangeSubPanel",
        BPKey = "WBP_Warehouse_ExtArrange"
    }
end

UITable[UIName2ID.SafeBoxSkinDetailPage] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.SafeBoxSkinDetailPage",
    BPKey = "WBP_Collections_BlueprintDetail",
    SubUIs = {
        UIName2ID.GunsmithSceneSocketMainUI,
        UIName2ID.ItemDetailViewEquip,
    },
    Anim = {
        FlowInAni = "WBP_Collections_BlueprintDetail_IN",
        FlowOutAni = "WBP_Collections_BlueprintDetail_out",
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMall,
}

UITable[UIName2ID.SafeBoxSkinVideoPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Key.SafeBoxSkinVideoPanel",
    BPKey = "WBP_CollectionsVideos",
    SubUIs = {
    },
    -- Anim = {
    --     FlowInAni = "WBP_Hero_SkillsVideos_in",
    --     FlowOutAni = "WBP_Hero_SkillsVideos_out",
    -- },
    bEnableWorldRendering = true,
}

--endregion
-----------------------------------------------------------------------

local InventoryConfig = {
    -- 道具ID默认长度
    ItemIdLength = 11,

    --[[SlotBgMapping = {
        [ESlotType.Helmet] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_008.H_Bag_Icon_008'",
        [ESlotType.BreastPlate] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_11.G_Common_EquipClass_Icon_W_11'",
        [ESlotType.ChestHanging] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_009.H_Bag_Icon_009'",
        [ESlotType.Bag] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_011.H_Bag_Icon_011'",
        [ESlotType.SafeBox] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_010.H_Bag_Icon_010'",
        [ESlotType.MainWeaponLeft] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_001.H_Bag_Icon_001'",
        [ESlotType.MainWeaponRight] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_001.H_Bag_Icon_001'",
        [ESlotType.MeleeWeapon] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_007.H_Bag_Icon_007'",
        [ESlotType.KeyChain] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_013.H_Bag_Icon_013'",

        [ESlotType.ArmedForceProp1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.ArmedForceProp2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceProp1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceProp2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceTDMProp] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",

        [ESlotType.MP_MainWeapon] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_001.H_Bag_Icon_001'",
        [ESlotType.MP_SecondaryWeapon] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_001.H_Bag_Icon_001'",
        [ESlotType.MP_MeleeWeapon] = "PaperSprite'/Game/UI/UIAtlas/Icon/White/H_Bag_Icon/BakedSprite/H_Bag_Icon_007.H_Bag_Icon_007'",
    },
    SlotBgMappingInGame = {
        [ESlotType.Helmet] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_07.G_Common_EquipClass_Icon_W_07'",
        [ESlotType.BreastPlate] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_11.G_Common_EquipClass_Icon_W_11'",
        [ESlotType.ChestHanging] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_13.G_Common_EquipClass_Icon_W_13'",
        [ESlotType.Bag] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_00.G_Common_EquipClass_Icon_W_00'",
        [ESlotType.SafeBox] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_03.G_Common_EquipClass_Icon_W_03'",
        [ESlotType.MainWeaponLeft] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_06.G_Common_EquipClass_Icon_W_06'",
        [ESlotType.MainWeaponRight] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_06.G_Common_EquipClass_Icon_W_06'",
        [ESlotType.MeleeWeapon] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_04_01.G_Common_EquipClass_Icon_W_04_01'",
        [ESlotType.KeyChain] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_05.G_Common_EquipClass_Icon_W_05'",

        [ESlotType.ArmedForceProp1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.ArmedForceProp2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceProp1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceProp2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",
        [ESlotType.MP_ArmedForceTDMProp] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0030.Common_ItemClass_Icon_0030'",

        [ESlotType.MP_MainWeapon] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_06.G_Common_EquipClass_Icon_W_06'",
        [ESlotType.MP_SecondaryWeapon] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_06.G_Common_EquipClass_Icon_W_06'",
        [ESlotType.MP_MeleeWeapon] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_04_01.G_Common_EquipClass_Icon_W_04_01'",
    },--]]
    SlotNameMapping = {
        [ESlotType.Helmet] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_Helmet", "头盔"),
        [ESlotType.BreastPlate] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_BreastPlate", "防弹衣"),
        [ESlotType.ChestHanging] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_ChestHanging", "胸挂"),
        [ESlotType.Bag] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_Bag", "背包"),
        [ESlotType.SafeBox] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_SafeBox", "安全箱"),
        [ESlotType.MainWeaponLeft] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MainWeaponLeft", "主武器"),
        [ESlotType.MainWeaponRight] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MainWeaponRight", "副武器"),
        [ESlotType.MeleeWeapon] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_SecondaryWeapon", "近战武器"),
        [ESlotType.Pistrol] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_Pistrol", "手枪"),
        [ESlotType.KeyChain] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_KeyChain", "钥匙串"),
        [ESlotType.Medicine] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_Medicine", "药品"),
        [ESlotType.BulletLeft] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_BulletLeft", "子弹1"),
        [ESlotType.BulletRight] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_BulletRight", "子弹2"),

        [ESlotType.ArmedForceProp1] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_ArmedForceProp1", "兵种基础道具1"),
        [ESlotType.ArmedForceProp2] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_ArmedForceProp2", "兵种基础道具2"),
        [ESlotType.MP_ArmedForceProp1] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_ArmedForceProp1", "兵种全面战场道具1"),
        [ESlotType.MP_ArmedForceProp2] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_ArmedForceProp2", "兵种全面战场道具2"),
        [ESlotType.MP_ArmedForceTDMProp] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_ArmedForceTDMProp", "兵种道具"),

        [ESlotType.MP_MainWeapon] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_MainWeapon", "主武器"),
        [ESlotType.MP_SecondaryWeapon] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_SecondaryWeapon", "副武器"),
        [ESlotType.MP_MeleeWeapon] = NSLOCTEXT("InventoryModule", "Lua_InventorySlotName_MP_MeleeWeapon", "近战武器"),
    },
    SlotContainerNameMapping = {
        [ESlotType.BagContainer] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_Bag", "背包"),
        [ESlotType.ChestHangingContainer] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_ChestHanging", "胸挂"),
        [ESlotType.SafeBoxContainer] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_SafeBox", "安全箱"),
        [ESlotType.MainContainer] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_MainContainer", "仓库")
    },
    MysticalSkinRarityTxtMapping = {
        [1] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_Excellent", "优品"),
        [2] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_Rare", "稀有"),
        [3] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_Best", "极品"),
        [4] = NSLOCTEXT("InventoryModule", "Lua_InventoryContainerName_Extraordinary", "极品")
    },

    Loc = {
        SubmitItemTitle = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemTitle", "提交物品"),
        SubmitItemFull = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemFull", "无需再提交%s"),
        SubmitItemSuccess = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemSuccess", "物品提交成功"),
        SubmitItemFail = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemFail", "绑定道具不符合任务要求"),
        SubmitItemBinded = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemBinded", "绑定"),
        SubmitItemUnBinded = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemUnBinded", "非绑定"),
        SubmitItemUnAvailable = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemUnAvailable", "当前没有此道具"),
        SubmitItemZero = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemZero", "当前拥有数为0"),
        WeaponComponentMeet = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_WeaponComponentMeet", "配件符合要求"),
        WeaponComponentNotMeet = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_WeaponComponentNotMeet", "配件不符合要求"),
        SubmitItemDemandCount = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_SubmitItemDemandCount", "{Num1}/<customstyle color=\"Color_Quality00\">{Num2}</>"),
        DepositaryTitle = NSLOCTEXT("InventoryModule", "Lua_InventoryDepository", "仓库"),
        Return2DepositoryTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_Return2DepositoryTitle", "返回仓库"),
        DisabledPartConflict = NSLOCTEXT("InventoryModule", "Lua_Inventory_DisabledPartConflict", "与[%s]冲突"),
        FetchInventoryDataFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_FetchInventoryDataFail", "拉取仓库数据失败"),
        SyncServerFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_SyncServerFail", "同步服务器失败，有Bug"),
        EquipFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_EquipFail", "装备[%s]失败"),
        CarryFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_CarryFail", "携带[%s]失败"),
        SplitFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_SplitFail", "拆分[%s]失败"),
        SplitSuccess=NSLOCTEXT("InventoryModule", "Lua_Inventory_SplitSuccess", "拆分%s成功"),
        RepairCost = NSLOCTEXT("InventoryModule", "Lua_Inventory_RepairCost", "%d/拥有%d"),
        LackRepairMaterial = NSLOCTEXT("InventoryModule", "Lua_Inventory_LackRepairMaterial", "三角币不足"),
        AdapaterNotAttached = NSLOCTEXT("InventoryModule", "Lua_Inventory_AdapterNotAttached", "未装配"),
        HealthIsFull = NSLOCTEXT("InventoryModule", "Lua_Inventory_HealthIsFull", "血量已满，无需使用"),
        SellConfirm = NSLOCTEXT("InventoryModule", "Lua_Inventory_SellConfirm", "出售确认"),
        SellIncome = NSLOCTEXT("InventoryModule", "Lua_Inventory_SellIncome", "出售收益"),
        DontEquipContainerToSelfSlot = NSLOCTEXT("InventoryModule", "Lua_Inventory_DontEquipContainerToSelfSlot", "不能把背包装入自己中"),
        WeaponDontHaveEnoughComponents = NSLOCTEXT("InventoryModule","Lua_Inventory_WeaponDontHaveEnoughComponents","枪匣没有足够的配件，无法装备"),
        ContainLevel = NSLOCTEXT("InventoryModule", "Lua_Inventory_ContainLevel", "容量"),
        DepositoryPropertyTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_DepositoryPropertyTitle", "角色"),
        ReductionHint = NSLOCTEXT("InventoryModule", "Lua_Inventory_ReductionHint", "（上限 -%s）"),
        
        DoubleClick2Equip = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2Equip", "双击装备"),
        DoubleClick2Carry = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2Carry", "双击携带"),
        DoubleClick2PickUp = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2PickUp", "双击拾取"),
        DoubleClick2Remove = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2Remove", "双击移除"),
        DoubleClick2UnEquip = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2UnEquip", "双击卸下"),
        DoubleClick2BackToDepos = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2BackToDepos", "双击放回"),
        DoubleClick2Shop = NSLOCTEXT("InventoryModule", "Lua_Inventory_DoubleClick2Shop", "双击购买"),
        Click2Equip = NSLOCTEXT("InventoryModule", "Lua_Inventory_Click2Equip", "点击装备"),
        NoSuitableContainer = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoSuitableContainer", "没有合适的容器"),
        ItemConflictHint = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemConflictHint", "<ItemHighlight>%s</>和%s冲突!"),
        UnEquipSafeBoxNotAllow = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnEquipSafeBoxNotAllow", "不允许卸下安全箱"),
        ShouldEquipSafeBoxFirst = NSLOCTEXT("InventoryModule", "Lua_Inventory_ShouldEquipSafeBoxFirst", "必须先装上安全箱"),
        UnEquipExtendItemFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnEquipExtendItemFail", "此操作会导致仓库容量不足，请整理后重试"),
        UnEquipKeyCaseNotAllow = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnEquipKeyCaseNotAllow", "不允许卸下钥匙包"),
        UnEquipDefaultBagNowAllow = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnEquipDefaultBagNowAllow", "不允许卸下拾荒小包"),
        UnEquipSomethingNotAllow = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnEquipSomethingNotAllow", "不允许卸下%s"),
        SellDefaultBagNowAllow = NSLOCTEXT("InventoryModule", "Lua_Inventory_SellDefaultBagNowAllow", "不允许出售拾荒小包"),
        NotNeedToRepair = NSLOCTEXT("InventoryModule", "Lua_Inventory_NotNeedToRepair", "无需修理"),
        AutoEquipAmmoFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_AutoEquipAmmoFail", "配装子弹失败"),
        AutoEquipAmmoSuccess = NSLOCTEXT("InventoryModule", "Lua_Inventory_AutoEquipAmmoSuccess", "配装子弹成功"),
        PurchaseSuccess = NSLOCTEXT("InventoryModule", "Lua_Inventory_PurchaseSuccess", "购买成功"),
        PurchaseFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_PurchaseFail", "购买失败"),
        PurchaseMoneyLacking = NSLOCTEXT("InventoryModule", "Lua_Inventory_PurchaseMoneyLacking", "可用资金不足"),
        PackageSpaceLacking = NSLOCTEXT("InventoryModule", "Lua_Inventory_PackageSpaceLacking", "携带空间不足，需要容量"),
        SpaceLackingCantUnequip = NSLOCTEXT("InventoryModule", "Lua_Inventory_SpaceLackingCantUnequip", "空间已满，无法卸下"),
        InventorySpaceLacking = NSLOCTEXT("InventoryModule", "Lua_Inventory_InventorySpaceLacking", "仓库空间不足，需要容量"),
        InventorySpaceLacking2 = NSLOCTEXT("InventoryModule", "Lua_Inventory_InventorySpaceLacking2", "空间已满，操作失败"),
        ConfirmTakeOffTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_ConfirmTakeOffTitle", "子弹确认"),
        ConfirmNeedTakeOffAmmo = NSLOCTEXT("InventoryModule", "Lua_Inventory_ConfirmNeedTakeOffAmmo", "是否需要放回已携带的%s子弹？"),
        NoAvailableEquipSlot = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoAvailableEquipSlot", "没有合适的装备位"),
        OtherReason = NSLOCTEXT("InventoryModule", "Lua_Inventory_OtherReason", "其他原因失败"),
        ExtendSmallThanCurrent = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtendSmallThanCurrent", "将安装的扩容比原有扩容的容量小，不可替换"),
        SelectAllText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectAllText", "需要选择仓库中所有的<Blue>%s</>吗？"),
        ItemDetailContainer = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_ItemDetailContainer", "容器"),
        ItemDetailExtension = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_ItemDetailExtension", "扩容"),
        NoFilterItems = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_NoFilterItems", "仓库中暂无此类物品"),
        NoExtItemEquipped = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoExtItemEquipped", "当前未安装任何扩容道具"),
        ItemsMoveToNewDepositOverflow = NSLOCTEXT("InventoryModule","Lua_Inventory_ItemsMoveToNewDepositOverflow","所选物品超出扩容容量"),
        WhetherToEquipExtItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_WhetherToEquipExtItem", "是否安装此扩容箱到仓库？"),
        EquipExtBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_EquipExtBtnText", "安装"),
        EquipExtTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_EquipExtTitle", "扩容安装"),
        ExtensionSlotLockHint = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtensionSlotLockHint", "扩容槽未解锁，请前往特勤处解锁新位置"),
        ExetnsionSlotLockPleaseWait = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExetnsionSlotLockPleaseWait", "敬请期待"),
        SwapMultiNotAllowed = NSLOCTEXT("InventoryModule", "Lua_Inventory_SwapMultiNotAllowed", "不允许一次交换多个道具"),
        SwapDiffSizeNotAllowed = NSLOCTEXT("InventoryModule", "Lua_Inventory_SwapDiffSizeNotAllowed", "不允许交换大小不同的道具"),
        PlacePositionNotValid = NSLOCTEXT("InventoryModule", "Lua_Inventory_PlacePositionNotValid", "放置位置不合法"),
        NoEnoughSpace = NSLOCTEXT("InventoryModule", "Lua_GameInventory_NoEnoughSpace", "储物空间不足，需要容量"),
        DepositCellTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_DepositTitle", "%d号仓库"),
        ClickToReplace = NSLOCTEXT("InventoryModule", "Lua_Inventory_ClickToReplace", "点击替换"),
        KeyIndexNotMatch = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyIndexNotMatch", "钥匙不可用放在此地图的钥匙包内"),
        RepairRequirementNotMatch = NSLOCTEXT("InventoryModule", "Lua_Inventory_RepairRequirementNotMatch", "所需材料不足"),
        DepositTotalPriceTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_DepositTotalPriceTitle", "仓库估值："),
        DepositCapacityTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_DepositCapacityTitle", "仓库容量："),
        QuickSellHint_QuestItemName = NSLOCTEXT("InventoryModule", "Lua_Inventory_QuickSellHint_QuestItemName", "任务道具<customstyle color=\"Color_Highlight01\">%s</><dfmrichtext type=\"img\" id=\"desNotice\"/>"),
        QuickSellHint = NSLOCTEXT("InventoryModule", "Lua_Inventory_QuickSellHint", "%s，确认以%s总价出售吗？"),
        QuickSellHintEtc = NSLOCTEXT("InventoryModule", "Lua_Inventory_QuickSellHintEtc", "%s等道具，确认以%s总价出售吗？"),
        BatchSellText = NSLOCTEXT("InventoryModule", "Lua_Inventory_BatchSellText", "%s <customstyle Color=\"C002\">x%s</>"),
        BatchSellTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_BatchSellTitle", "出售确认"),
        BatchSellPopMainText = NSLOCTEXT("InventoryModule", "Lua_Inventory_BatchSellPopMainText", "确认以<dfmrichtext type=\"img\" width=\"38\" height=\"38\" id=\"bankNote\" align=\"0\"/><customstyle color=\"Color_Highlight01\">%s</>出售以下道具吗？"),
        InvSellConfirmText = NSLOCTEXT("InventoryModule", "Lua_Inventory_InvSellConfirmText", "出售"),
        InvSellCancelText = NSLOCTEXT("InventoryModule", "Lua_Inventory_InvSellCancelText", "取消"),
        SelectionEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionEquipBtnText", "装备"),
        SelectionUnEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionUnEquipBtnText", "卸下"),
        SelectionBuyAndEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionBuyAndEquipBtnText", "购买并装备"),
        SelectionEquipPerkBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionEquipPerkBtnText", "安装"),
        SelectionUnEquipPerkBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionUnEquipPerkBtnText", "拆卸"),
        SelectionBuyAndEquipPerkBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionBuyAndEquipPerkBtnText", "购买并安装"),
        SelectionDefaultTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionDefaultTitle", "道具选择"),
        SelectionExtTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionExtTitle", "选择扩容"),
        SelectionAdapterTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionAdapterTitle", "选择配件"),
        SelectionPerkTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectionPerkTitle", "选择强化套件"),
        UnBindItemPileOnBindItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnBindItemPileOnBindItem", "{NumOfItem}个非绑定{ItemName}道具"),
        TimeItemCantSell = NSLOCTEXT("InventoryModule", "Lua_Inventory_TimeItemCantSell", "限时物品不可出售"),
        ItemCantSellDefaultTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemCantSellDefaultTip", "该道具不能被出售"),
        PerkEquipSuccess = NSLOCTEXT("InventoryModule", "Lua_Inventory_PerkEquipSuccess", "Perk安装成功"),
        PerkEquipFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_PerkEquipFail", "Perk安装失败"),
        PerkAlreadyOnThisWeapon = NSLOCTEXT("InventoryModule", "Lua_Inventory_PerkAlreadyOnThisWeapon", "配件已经在这把枪上了"),
        ItemSubmit = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemSubmit", "提交"),
        ComponentNotSatisfy = NSLOCTEXT("InventoryModule", "Lua_Inventory_ComponentNotSatisfy", "配件未达标"),
        GoToWeaponAssembly = NSLOCTEXT("InventoryModule", "Lua_Inventory_GoToWeaponAssembly", "改装"),
        ComponentNeeded = NSLOCTEXT("InventoryModule", "Lua_Inventory_ComponentNeeded", "配件要求"),
        NoComponentNeeded = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoComponentNeeded", "无配件要求"),
        AttributeNeeded = NSLOCTEXT("InventoryModule", "Lua_Inventory_AttributeNeeded", "属性要求"),
        NoAttributeNeeded = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoAttributeNeeded", "无属性要求"),
        AllRequirmentsMeet = NSLOCTEXT("InventoryModule", "Lua_InventoryConfig_AllRequirmentsMeet", "所有要求都满足"),
        JumpToShop = NSLOCTEXT("InventoryModule", "Lua_Inventory_JumpToShop", "前往军需处"),
        BPLockTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_BPLockTxt", "通行证未解锁"),
        SeasonQuestLockTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_SeasonQuestLockTxt", "赛季任务暂未解锁"),
        RankingSOLUnLockTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_RankingSOLUnLockTxt", "排位赛暂未解锁"),
        NotAllowedPutIn = NSLOCTEXT("InventoryModule", "Lua_Inventory_NotAllowedPutIn", "不允许放入此物品类型"),

        -----------------------------------------------------------------------
        --region 钥匙相关
        KeyMapCannotUnlock = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyMapCannotUnlock", "\"%s\"尚未解锁"),
        KeySpaceCellCannotUnlock = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeySpaceCellCannotUnlock", "槽位尚不可解锁"),
        KeySpaceCellStillNotUnlock = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeySpaceCellStillNotUnlock", "槽位尚未解锁，请点击槽位查看详情"),
        KeyMapText = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyMapText", "%s:"),
        --endregion
        -----------------------------------------------------------------------

        WarehouseBackBtnTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseBackBtnTitle", "仓库"),
        WarehouseFilterTitle_Prefix = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Prefix", "筛选：%s"),
        WarehouseFilterTitle_Weapon = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Weapon", "枪械"),
        WarehouseFilterTitle_Adapter = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Adapter", "配件"),
        WarehouseFilterTitle_Equipment = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Equipment", "装备"),
        WarehouseFilterTitle_Consumable = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Consumable", "消耗品"),
        WarehouseFilterTitle_Key = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Key", "钥匙"),
        WarehouseFilterTitle_Other = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFilterTitle_Other", "其他"),
        WarehouseListCellDurability = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseListCellDurability", "耐久"),
        WarehouseListCellCapacity = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseListCellCapacity", "容量"),
        WarehouseDepositoryTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseDepositoryTitle", "主仓库"),
        WarehouseMultiSellModeTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseMultiSellModeTitle", "批量出售"),
        WarehouseTransferAllSuccess = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTransferAllSuccess", "转移成功"),
        WarehouseTransferAllFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTransferAllFail", "转移失败，仓库空间不足"),
        WarehouseNothing2Transfer = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseNothing2Transfer", "当前没有可转移的物品"),
        WarehouseNothing2Manage = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseNothing2Manage", "当前没有道具"),
        WarehouseNothing2Sell = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseNothing2Sell", "当前没有道具"),

        WarehouseEmptyEquipSlotClickTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseEmptyEquipSlotClickTip", "请配置装备"),

        WarehouseExtSlotLock = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtSlotLock", "升级仓库以解锁扩容箱槽位"),

        WarehouseExtWindowTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtWindowTitle", "管理扩容箱"), 
        WarehouseExtItemLevelUpConfirmText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemLevelUpConfirmText","确定用以下物品将<customstyle color=\"Color_Highlight01\" size=\"Size_BoldTitle02\">%s</>升级至<customstyle color=\"Color_Highlight01\" size=\"Size_BoldTitle02\">%s</>吗？"),
        WarehouseExtItemLevelUpBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemLevelUpBtnText", "升级"),
        WarehouseExtItemEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemEquipBtnText", "安装"),
        WarehouseExtItemUnEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemUnEquipBtnText", "卸下"),
        WarehouseExtItemReplaceBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemReplaceBtnText", "替换"),
        WarehouseExtItemBuyAndEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemBuyAndEquipBtnText", "%s%s 购买并安装"),
        WarehouseExtItemExchangeAndEquipBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemExchangeAndEquipBtnText", "兑换并安装"),
        WarehouseExtItemBuyConfirmText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemBuyConfirmText", "确定花费%s购买%s吗"),
        WarehouseExtItemBuyConfirmBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemBuyConfirmBtnText", "购买"),
        WarehouseExtItemRepairBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemRepairBtnText", "确认修理"),
        WarehouseExtItemRepairConfirmText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtItemRepairConfirmText", "<customstyle color=\"Color_Highlight01\">%s</>仅轻微损耗，本次修理性价比低，是否确认修理？"),
        WarehouseEquipExtItemSuccessTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseEquipExtItemSuccessTip", "成功安装<Green>%s</>"),
        WarehouseUnEquipExtItemSuccessTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseUnEquipExtItemSuccessTip", "成功卸下<Green>%s</>"),
        WarehouseReplaceExtItemSuccessTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseReplaceExtItemSuccessTip", "成功替换<Green>%s</>"),
        -- WarehouseEquipExtItemFailTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseEquipExtItemFailTip", "<Green>%s</>"),
        WarehouseUnEquipExtItemFailTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseUnEquipExtItemFailTip", "卸下后仓库容量不足，不可卸下"),
        WarehouseReplaceExtItemFailTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseReplaceExtItemFailTip", "替换后仓库容量不足，不可卸下"),

        WarehouseExtArrangeSelectNum = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtArrangeSelectNum", "已选{Num}个箱子"),
        WarehouseExtArrangeNoSelectExtpansion = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtArrangeNoSelectExtpansion", "请选择要整理的箱子"),
        WarehouseFirstEquipExtBoxTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseFirstEquipExtBoxTip", "是否将主仓库部分%s放入扩容箱？"),

        WarehouseSellTip_NoSelection = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseSellTip_NoSelection", "请选择要出售的物品"),
        WarehouseSellTip_HasSelection = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseSellTip_HasSelection", "已选"),

        ItemHasBeenPutInExtBoxTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemHasBeenPutInExtBoxTip", "<customstyle color=\"Color_Highlight01\">%s</>已放入<customstyle color=\"Color_Highlight01\">%s</>"),

        AucSellConfirmWindowRecentPriceText = NSLOCTEXT("InventoryModule", "Lua_Inventory_AucSellConfirmWindowRecentPriceText", "过去%d小时成交均价"),
        AucSellConfirmWindowRecentNumText = NSLOCTEXT("InventoryModule", "Lua_Inventory_AucSellConfirmWindowRecentNumText", "过去%d小时交易数量：%s件"),

        SplitFailForNoWarehouseSpace = NSLOCTEXT("InventoryModule", "Lua_Inventory_SplitFailForNoWarehouseSpace", "仓库空间不足，无法拆分"),
        SplitFailForNoContainerSpace = NSLOCTEXT("InventoryModule", "Lua_Inventory_SplitFailForNoContainerSpace", "储物空间不足，无法拆分"),

        WarehouseExtExchangeMaterialNotEnough = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtExchangeMaterialNotEnough", "兑换所需物不足"),
        WarehouseExtExchangeSoldOut = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExtExchangeSoldOut", "已售罄"),

        OnlySameTypeExtItemCanReplace = NSLOCTEXT("InventoryModule", "Lua_Inventory_OnlySameTypeExtItemCanReplace", "只有类型相同的扩容箱才能替换"),
        WarehouseCapacityNotEnoughAfterReplaceExtItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseCapacityNotEnoughAfterReplaceExtItem", "替换后仓库容量不足，不可替换"),
        MainExtCantReplace = NSLOCTEXT("InventoryModule", "Lua_Inventory_MainExtCantReplace", "主仓库不支持替换"),

        WarehouseTopHint_SelectAll = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHint_SelectAll", "需要选择仓库中所有<customstyle color=\"Color_Highlight01\">%s</>吗？"),
        WarehouseTopHint_GoToSetting = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHint_GoToSetting", "是否前往设置页面进行更详细的整理设置？"),
        WarehouseTopHint_AutoSortAfterEquipExtItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHint_AutoSortAfterEquipExtItem", "是否对主仓库进行一次整理，将部分<customstyle color=\"Color_Highlight01\">%s</>放入扩容箱？"),
        WarehouseTopHintBtnText_SelectAll = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHintBtnText_SelectAll", "选择所有"),
        WarehouseTopHintBtnText_GoToSetting = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHintBtnText_GoToSetting", "前往设置"),
        WarehouseTopHintBtnText_AutoSortAfterEquipExtItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseTopHintBtnText_AutoSortAfterEquipExtItem", "确认整理"),

        ExtSelectionItemTitle_Warehouse = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtSelectionItemTitle_Warehouse", "仓库"),
        ExtSelectionItemTitle_Shop = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtSelectionItemTitle_Shop", "商店"),
        SellPriceText = NSLOCTEXT("InventoryModule", "Lua_Inventory_SellPriceText", "已选 <dfmrichtext type=\"img\" width=\"46\" height=\"46\" id=\"bankNote\" align=\"0\"/>"),
        PriceText = NSLOCTEXT("InventoryModule", "Lua_Inventory_PriceText", "<dfmrichtext type=\"img\" width=\"36\" height=\"36\" id=\"bankNote\" align=\"0\"/>%d"),
        PriceStrText = NSLOCTEXT("InventoryModule", "Lua_Inventory_PriceStrText", "<dfmrichtext type=\"img\" width=\"36\" height=\"36\" id=\"bankNote\" align=\"0\"/>%s"),
        ContainerPanelCapacityText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ContainerPanelCapacityText", "<customstyle Size=\"Size_BoldTitle01\">%d</><customstyle Color=\"C002\">/%d </>"),
        SelectedSellItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectedSellItem", "请选择要出售的道具"),
        -----------------------------------------------------------------------
        --region ExtManagement
        UnequipWarehouseNotAllowed = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipWarehouseNotAllowed", "主仓库不可卸下"),
        DragUpAndDownToSort = NSLOCTEXT("InventoryModule", "Lua_Inventory_DragUpAndDownToSort", "上下拖动进行排序"),
        SelectExtItemToManage = NSLOCTEXT("InventoryModule", "Lua_Inventory_SelectExtItemToManage", "选中扩容箱进行管理"),
        ClickRightEmptySlotToEquipExtItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_ClickRightEmptySlotToEquipExtItem", "点击右侧空槽位以安装扩容箱"),
        LevelupWarehouseToUnlockMoreExtSlots = NSLOCTEXT("InventoryModule", "Lua_Inventory_LevelupWarehouseToUnlockMoreExtSlots", "升级仓库以解锁更多扩容槽位，是否前往特勤处升级？"),
        LevelupWarehouseToUnlockMoreExtSlots_ConfirmBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_LevelupWarehouseToUnlockMoreExtSlots_ConfirmBtnText", "前往"),

        ExtItemDontSupportThisItem = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtItemDontSupportThisItem", "<customstyle color=\"Color_Highlight01\">%s</>不支持此类道具"),
        ExtItemSpaceNotEnough = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtItemSpaceNotEnough", "<customstyle color=\"Color_Highlight01\">%s</>空间不足"),

        UnequipExtItemSuccessHintPrefix = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipExtItemSuccessHintPrefix", "成功卸下扩容箱"),
        UnequipExtItemSuccessHintSuffix_AllWarehouse = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipExtItemSuccessHintSuffix_AllWarehouse", "成功卸下扩容箱，物品已移动至<customstyle color=\"Color_Highlight01\">主仓库</>和<customstyle color=\"Color_Highlight01\">其他扩容箱</>"),
        UnequipExtItemSuccessHintSuffix_MainWarehouse = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipExtItemSuccessHintSuffix_MainWarehouse", "成功卸下扩容箱，物品已移动至<customstyle color=\"Color_Highlight01\">主仓库</>"),
        UnequipExtItemSuccessHintSuffix_ExtWarehouse = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipExtItemSuccessHintSuffix_ExtWarehouse", "成功卸下扩容箱，物品已移动至<customstyle color=\"Color_Highlight01\">其他扩容箱</>"),
        UnequipExtItemFailHint = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnequipExtItemFailHint", "卸下后仓库容量不足，请整理出空间后重试"),
        ItemNotFitExt = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemNotFitExt", "%s不能放入此道具类型"),
        NoExtItemInWarehouse = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoExtItemInWarehouse", "仓库中没有可用扩容箱，请前往军需处购买"),
        --endregion
        -----------------------------------------------------------------------
        
        WarehouseCollapsedBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseCollapsedBtnText", "收起"),
        WarehouseExpandBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WarehouseExpandBtnText", "展开"),

        BindingEquipmentContainerName = NSLOCTEXT("InventoryModule", "Lua_Inventory_BindingEquipmentContainerName", "<SecDark_Unreach>%s</> <dfmrichtext type=\"img\" id=\"bind\"/>"),
        EquipmentContainerName = NSLOCTEXT("InventoryModule", "Lua_Inventory_EquipmentContainerName", "<SecDark_Unreach>%s</>"),
        CanNotFindKeyInfo = NSLOCTEXT("InventoryModule", "Lua_Inventory_CanNotFindKeyInfo", "无法在KeyInfo表中找到该ID的钥匙信息："),


        -----------------------------------------------------------------------
        --region WishList
        NeedAddToWishList = NSLOCTEXT("InventoryModule", "Lua_Inventory_NeedAddToWishList", "添加到收藏列表"),
        WishListAlreadyAdd = NSLOCTEXT("InventoryModule", "Lua_Inventory_WishListAlreadyAdd", "已添加到收藏"),
        WishListUnlimited = NSLOCTEXT("InventoryModule", "Lua_Inventory_WishListUnlimited", "不限"),
        WishTimeSortFilterBtnTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WishTimeSortFilterBtnTitle", "时间"),
        WishNumSortFilterBtnTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_WishNumSortFilterBtnTitle", "所需数量"),
        NoWishItemAtTheMoment = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoWishItemAtTheMoment", "暂无收藏道具"),
        TheCurrentPageCannotOpenTheFavoritesList = NSLOCTEXT("InventoryModule", "Lua_Inventory_TheCurrentPageCannotOpenTheFavoritesList", "当前页面无法打开收藏列表"),
        --endregion
        -----------------------------------------------------------------------
        EquipmentRepairCost = NSLOCTEXT("InventoryModule", "Lua_Inventory_EquipmentRepairCost", "<dfmrichtext type=\"img\" width=\"40\" height=\"40\" id=\"bankNote\"/><dfmrichtext type=\"css\" color=\"%s\">%s</>"),
        ExpansionManagement = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExpansionManagement", "扩容管理"),
        ExtNoEnoughSpace = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtNoEnoughSpace", "仓库空间不足，需要容量"),
        BagHasNotAllowedProps = NSLOCTEXT("InventoryModule", "Lua_Inventory_BagHasNotAllowedProps", "背包中有不可入局道具，请前往仓库转移此道具"),

        ConfirmText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ConfirmText", "确认"),
        CancelText = NSLOCTEXT("InventoryModule", "Lua_Inventory_CancelText", "取消"),
        RepairText = NSLOCTEXT("InventoryModule", "Lua_Inventory_RepairText", "暂未解锁"),
        UseContainerText = NSLOCTEXT("InventoryModule", "Lua_Inventory_UseContainerText", "使用"),

        InventoryNoExpansion = NSLOCTEXT("InventoryModule", "Lua_Inventory_InventoryNoExpansion", "仓库中没有扩容箱"),
        ShopNoExpansion = NSLOCTEXT("InventoryModule", "Lua_Inventory_ShopNoExpansion", "暂无在售扩容箱"),

        DurabilityRepair = NSLOCTEXT("InventoryModule", "Lua_Inventory_DurabilityRepair", "当前装备耐久度已满，无需修理"),
        TypeDifferent = NSLOCTEXT("InventoryModule", "Lua_Inventory_TypeDifferent", "维修套件与当前装备不匹配"),
        AutoExtText = NSLOCTEXT("InventoryModule", "Lua_Inventory_AutoExtText", "自动整理规则"),
        AutoExtText1 = NSLOCTEXT("InventoryModule", "Lua_Inventory_AutoExtText1", "勾选扩容箱后主仓库的道具会优先装入扩容箱。"),
        AutoExtText2 = NSLOCTEXT("InventoryModule", "Lua_Inventory_AutoExtText2", "可通过下方设置按钮，选择更加紧凑的整理方式。"),

        ArrangeFaildTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ArrangeFaildTips", "仓库空间无法完成整理，请安装扩容箱后重试"),
        RangeUnlockedTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_RangeUnlockedTxt", "靶场Lv.2解锁"),
        GoToBlackSite = NSLOCTEXT("InventoryModule", "Lua_Inventory_GoToBlackSite", "<customstyle color=\"Color_Highlight01\">靶场到达Lv.2</>解锁中级修理，是否前往查看？"),


        -- Hover
        HoverPortableSpaceName = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverPortableSpaceName", "快捷使用空间"),
        HoverPortableSpaceDes = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverPortableSpaceDes", "此类空间内的药品、子弹能够从轮盘中取出使用，也可以通过快捷键快速使用"),
        HoverNormSpaceName = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverNormSpaceName", "普通存储空间"),
        HoverNormSpaceDes = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverNormSpaceDes", "此类空间中的药品、子弹无法被放入轮盘，也无法被快速使用"),
        HoverSafeSpaceName = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverSafeSpaceName", "安全存储空间"),
        HoverSafeSpaceDes = NSLOCTEXT("InventoryModule", "Lua_Inventory_HoverSafeSpaceDes", "此类空间中的物品，不会因为撤离失败掉落"),
        ArchiveSpaceDes = NSLOCTEXT("InventoryModule", "Lua_Inventory_ArchiveSpaceDes", "玩家身份牌栏位，无法放入其他物品以及其他玩家的身份牌"),
        FixSafeBoxTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_FixSafeBoxTips", "固定安全箱"),
        UnFixSafeBoxTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnFixSafeBoxTips", "解除固定安全箱"),

        ExtManagerTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtManagerTitle", "管理扩容箱"),
        ExtManagerText1 = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtManagerText1", "卸下：点击删除按钮"),
        ExtManagerText2 = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtManagerText2", "排序：长按拖拽可对扩容箱进行排序"),
        ExtReplaceTitle = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtReplaceTitle", "替换扩容箱"),

        -- 商业化相关
        ExpiredMaskTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExpiredMaskTips", "%s已过期"),
        NoActivationTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_NoActivationTips", "%s未激活"),
        ItemNoActivationTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemNoActivationTips", "<customstyle color=\"C003\">未激活</>"),
        ItemExpiredTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemExpiredTips", "<customstyle color=\"Color_DarkNegative\">已过期</>"),
        ExpiredTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExpiredTips", "该权益已过期"),
        ExpiredNonsupportTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExpiredNonsupportTips", "该模式不兼容安全箱"),
        SafeBoxCapacity = NSLOCTEXT("InventoryModule", "Lua_Inventory_SafeBoxCapacity", "%d(%dx%d)"),
        UseTimeText = NSLOCTEXT("InventoryModule", "Lua_Inventory_UseTimeText", "剩余权限：%s"),
        PermanentText = NSLOCTEXT("InventoryModule", "Lua_Inventory_PermanentText", "永久"),
        NetBarSafeBoxText = NSLOCTEXT("InventoryModule", "Lua_Inventory_NetBarSafeBoxText", "在网吧登录期间生效"),
        KeyChainCapacityText = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyChainCapacityText", "每张地图%s格"),
        SafeBoxPermissions = NSLOCTEXT("InventoryModule", "Lua_Inventory_SafeBoxPermissions", "安全箱权限"),
        KeyChainPermissions = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyChainPermissions", "门禁卡包权限"),
        InactivatedText = NSLOCTEXT("InventoryModule", "Lua_Inventory_InactivatedText", "无权限"),
        ItemUnLockText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemUnLockText", "%s未解锁"),
        ItemUsedText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ItemUsedText", "使用中"),
        GotoGetText = NSLOCTEXT("InventoryModule", "Lua_Inventory_GotoGetText", "前往获取"),
        UseActivateCardTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_UseActivateCardTxt", "使用体验卡"),
        ActivatePermissions = NSLOCTEXT("InventoryModule", "Lua_Inventory_ActivatePermissions", "激活权限"),
        ActivateCardNum = NSLOCTEXT("InventoryModule", "Lua_Inventory_ActivateCardNum", "拥有权限卡：%s"),
        ExpiredText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExpiredText", "已过期"),
        DayAndHourText = NSLOCTEXT("InventoryModule", "Lua_Inventory_DayAndHourText", "{day}天{hour}时"),
        HourAndMinText = NSLOCTEXT("InventoryModule", "Lua_Inventory_HourAndMinText", "{hour}时{min}分"),
        MinAndSecondText = NSLOCTEXT("InventoryModule", "Lua_Inventory_MinAndSecondText", "{min}分{second}秒"),
        RenewalTipsText = NSLOCTEXT("InventoryModule", "Lua_Inventory_RenewalTipsText", "是否花费 <dfmrichtext type=\"img\" width=\"46\" height=\"46\" id=\"bankNote\" align=\"0\"/>%s购买7天权限"),
        KeyChainHoverTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyChainHoverTips", "替换门禁卡包"),
        KeyChainHoverTipsDesc = NSLOCTEXT("InventoryModule", "Lua_Inventory_KeyChainHoverTipsDesc", "*门禁卡包不会因撤离失败掉落"),
        SafeBoxHoverTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_SafeBoxHoverTips", "替换安全箱"),
        SafeBoxHoverTipsDesc = NSLOCTEXT("InventoryModule", "Lua_Inventory_SafeBoxHoverTipsDesc", "*安全箱不会因撤离失败掉落"),
        FultonRecycleExpiredTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_FultonRecycleExpiredTxt", "安全箱已过期"),
        RenalTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_RenalTxt", "续费"),
        TransferAll = NSLOCTEXT("InventoryModule", "Lua_Inventory_TransferAll", "全部移出"),
        VipExtCantReplaceTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_VipExtCantReplaceTips", "权益扩容箱无法替换"),
        VIPExtNormalTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_VIPExtNormalTxt", "此扩容箱通过高级权限卡解锁，可前往调查档案获得对应权限卡；权限失效后，此扩容箱内道具仅可取出，不可进行放入、移动、整理等操作。"),
        GetSafeBoxWayTxt_1 = NSLOCTEXT("InventoryModule", "Lua_Inventory_GetSafeBoxWayTxt_1", "S%s赛季任务获得"),
        GetSafeBoxWayTxt_2 = NSLOCTEXT("InventoryModule", "Lua_Inventory_GetSafeBoxWayTxt_2", "S%s赛季通行证获得"),
        CustomizeText = NSLOCTEXT("InventoryModule", "Lua_Inventory_CustomizeText", "自定义"),
        DetailCheckTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_DetailCheckTxt", "查看详情"),
        ViewActionTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_ViewActionTxt", "检视动作"),
        EffectPreviewTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_EffectPreviewTxt", "效果预览"),

        -- 按钮tips
        ExtManagerBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtManagerBtnText", "管理扩容箱"),
        AddExtBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_AddExtBtnText", "添加扩容箱"),
        UnLockExtBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_UnLockExtBtnText", "解锁扩容箱槽位"),
        CollectionInBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_CollectionIn", "藏品仓库"),
        WishListBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_WishList", "收藏列表"),
        ExtArrangeBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtArrange", "整理仓库"),
        ExtArrangeTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtArrangeTxt", "整理"),
        ExtBtnClickedText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtBtnClickedText", "单击进入整理模式"),
        ExtBtnDoubleText = NSLOCTEXT("InventoryModule", "Lua_Inventory_ExtBtnDoubleText", "双击整理当前分页"),

        SellBtnText = NSLOCTEXT("InventoryModule", "Lua_Inventory_Sell", "批量出售"),
        CollectionRoomBtnTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_CollectionRoomBtnTxt", "前往收藏室"),
        AllValueText = NSLOCTEXT("InventoryModule", "Lua_Inventory_AllValue", "所有道具出售拍卖行的参考价值总和"),
        GoToCollectionRoomTips = NSLOCTEXT("InventoryModule", "Lua_Inventory_GoToCollectionRoomTips", "是否前往特勤处收藏室管理界面"),

        -- 玄学皮肤
        MysticalSkinTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalSkinTxt", "典藏"),
        MysticalSkinWearAndRarity = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalSkinWearAndRarity", "%s | 成色%s"),
        MysticalSkinWearTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalSkinWearTxt", "成色%s"),

        -- 玄学挂饰
        MysticalPendantTxt = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalPendantTxt", "%s | %s | %s"),
        MysticalPendantTxt1 = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalPendantTxt1", "%s | %s"),
        MysticalPendantTxt2 = NSLOCTEXT("InventoryModule", "Lua_Inventory_MysticalPendantTxt2", "%s"),

        -- 单击转移
        SingleClickMove_FindEmptyLocationFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_SingleClickMove_FindEmptyLocationFail", "目标位置空间不足，移动失败"),
        SingleClickMove_EquipSucceed = NSLOCTEXT("InventoryModule", "Lua_Inventory_SingleClickMove_EquipSucceed", "安装成功"),
        SingleClickMove_EquipFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_SingleClickMove_EquipFail", "安装失败"),
        SingleClickMove_ReplaceSucceed = NSLOCTEXT("InventoryModule", "Lua_Inventory_SingleClickMove_ReplaceSucceed", "替换成功"),
        SingleClickMove_ReplaceFail = NSLOCTEXT("InventoryModule", "Lua_Inventory_SingleClickMove_ReplaceFail", "替换失败"),

        -- 补偿
        NotifyReissueText = NSLOCTEXT("InventoryModule", "Lua_Inventory_NotifyReissueText", "检测到您近期的对局异常，指挥部正在核实是否有物资意外丢失，在此之前可以正常入局。"),

        --出售
        CurrencyOverflowTip = NSLOCTEXT("InventoryModule", "Lua_Inventory_CurrencyOverflowTip", "此次出售包含价值<dfmrichtext type=\"img\" width=\"38\" height=\"38\" id=\"bankNote\" align=\"0\"/>{currencyNum}，哈夫币超出上限的部分无法通过此次出售获得，是否出售？"),
        NumFormat = NSLOCTEXT("InventoryModule", "Lua_Inventory_NumFormat", "{curNum}/{maxNum}"),

        -- 匹配中
        MatchingText = NSLOCTEXT("InventoryModule", "Lua_Inventory_MatchingText", "匹配中无法进行此操作"),
    },
    DefaultBatchSlotPadding = FMargin(2, 2, 2, 2),
    MaxContainerLength = 5,
    MaxContainerHeight = 7,
    MaxSceneBoxLength = 6,

    -- New warehouse config
    SCROLL_SLEEP_TIME = 0.3,
    SCROLL_MOVE_TIME = 0.3,
    AUTO_SCROLL_SPEED = 2,
    SCROLL_MOVE_DISTANCE = 2 * ItemConfig.DefaultItemViewSize,

    EContainerMaskType = {
        NormalState = 0,
        ExpiredStatus = 1,
        JumpState = 2,
    },
}

-----------------------------------------------------------------------
--region Inventory Event
InventoryConfig.Events = {
    evtUpdateItemUnitSize = LuaEvent:NewIns("InventoryConfig.Events.evtUpdateItemUnitSize"),
    evtFirstItemChosen = LuaEvent:NewIns("InventoryConfig.Events.evtFirstItemChosen"),
    evtListItemClicked = LuaEvent:NewIns("InventoryConfig.Events.evtListItemClicked"),
    evtListItemDoubleClicked = LuaEvent:NewIns("InventoryConfig.Events.evtListItemDoubleClicked"),
    evtEquipSlotViewClicked = LuaEvent:NewIns("InventoryConfig.Events.evtEquipSlotViewClicked"),
    evtEquipSlotViewDoubleClicked = LuaEvent:NewIns("InventoryConfig.Events.evtEquipSlotViewDoubleClicked"),

    evtForceResetPartDisplayMaterial = LuaEvent:NewIns("InventoryConfig.Events.evtForceResetPartDisplayMaterial"),
    evtOnExtSplitBtnClick = LuaEvent:NewIns("InventoryConfig.Events.evtOnExtSplitBtnClick"),
    evtOnToggleLabelPanel = LuaEvent:NewIns("InventoryConfig.Events.evtOnToggleLabelPanel"),
    evtOnCloseLabelPanel = LuaEvent:NewIns("InventoryConfig.Events.evtOnCloseLabelPanel"),
    evtSwitchToThisTab = LuaEvent:NewIns("InventoryConfig.Events.evtSwitchToThisTab"),
    evtNotifyDepositDetailReset = LuaEvent:NewIns("InventoryConfig.Events.evtNotifyDepositDetailReset"),

    evtEnterExtManageMode = LuaEvent:NewIns("InventoryConfig.Events.evtEnterExtManageMode"),
    evtEnterExtArrangeMode = LuaEvent:NewIns("InventoryConfig.Events.evtEnterExtArrangeMode"),
    evtEnterSellMode = LuaEvent:NewIns("InventoryConfig.Events.evtEnterSellMode"),
    evtEnterReplaceExtPanel = LuaEvent:NewIns("InventoryConfig.Events.evtEnterReplaceExtPanel"),

    evtExtManageDragSortStart = LuaEvent:NewIns("InventoryConfig.Events.evtExtManageDragSortStart"),
    evtWareHouseMainOpenFinish = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseMainOpenFinish"),
    evtWareHouseMainOnHideBegin = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseMainOnHideBegin"),

    evtWarehouseExpansionOpenFinish = LuaEvent:NewIns("InventoryConfig.Events.evtWarehouseExpansionOpenFinish"),
    evtWareHouseTransferEnd = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseTransferEnd"),

    evtWarehouseChangeTitle = LuaEvent:NewIns("InventoryConfig.Events.evtWarehouseChangeTitle"),
    evtRefreshKeysContainerCapacity = LuaEvent:NewIns("InventoryConfig.Events.evtRefreshKeysContainerCapacity"),
    evtIsShowSelecNotingPanel = LuaEvent:NewIns("InventoryConfig.Events.evtIsShowSelecNotingPanel"),
    evtChangeTopBarStyle = LuaEvent:NewIns("InventoryConfig.Events.evtChangeTopBarStyle"),
    evtJumpToExtSlot = LuaEvent:NewIns("InventoryConfig.Events.evtJumpToExtSlot"),
    evtJumpToEquipPanel = LuaEvent:NewIns("InventoryConfig.Events.evtJumpToEquipPanel"),
    evtEquipPanelNavChange = LuaEvent:NewIns("InventoryConfig.Events.evtEquipPanelNavChange"),
    evtSaveExpansionNum = LuaEvent:NewIns("InventoryConfig.Events.evtSaveExpansionNum"),
    evtCompareExpansionNum = LuaEvent:NewIns("InventoryConfig.Events.evtCompareExpansionNum"),
    evtJumpToShopMain = LuaEvent:NewIns("InventoryConfig.Events.evtJumpToShopMain"),
    evtAutoScrollVisible = LuaEvent:NewIns("InventoryConfig.Events.evtAutoScrollVisible"),
    evtShowEquipCompareIcon = LuaEvent:NewIns("InventoryConfig.Events.evtShowEquipCompareIcon"),
    evtPopWindowStageChange=LuaEvent:NewIns("InventoryConfig.Events.evtPopWindowStageChange"),
    evtSaleCompleted = LuaEvent:NewIns("InventoryConfig.Events.evtSaleCompleted"),
    evtWidgetIntoView = LuaEvent:NewIns("InventoryConfig.Events.evtWidgetIntoView"),
    
    evtBottomBarSetting = LuaEvent:NewIns("InventoryConfig.Events.evtBottomBarSetting"),
    evtBottomBarSettingArrMode = LuaEvent:NewIns("InventoryConfig.Events.evtBottomBarSettingArrMode"),
    evtBatchSellBottomBarSetting = LuaEvent:NewIns("InventoryConfig.Events.evtBatchSellBottomBarSetting"),
    evtFlipFlopSafeBoxPinState = LuaEvent:NewIns("InventoryConfig.Events.evtFlipFlopSafeBoxPinState"),
    evtSellModeFocusFirstItemFailed = LuaEvent:NewIns("InventoryConfig.Events.evtSellModeFocusFirstItemFailed"),
    evtCreateWarehouseNavgroupOnly = LuaEvent:NewIns("InventoryConfig.Events.evtCreateWarehouseNavgroupOnly"),
    evtChangeSafeBoxState = LuaEvent:NewIns("InventoryConfig.Events.evtChangeSafeBoxState"),
    evtHideSAKSelected = LuaEvent:NewIns("InventoryConfig.Events.evtHideSAKSelected"),
    evtChangeSeletItem = LuaEvent:NewIns("InventoryConfig.Events.evtChangeSeletItem"),

    evtOnBatchSellWindowShow = LuaEvent:NewIns("InventoryConfig.Events.evtOnBatchSellWindowShow"),
    evtTrySale = LuaEvent:NewIns("InventoryConfig.Events.evtTrySale"),

    evtNavTabHightLight = LuaEvent:NewIns("InventoryConfig.Events.evtNavTabHightLight"),
    evtRepairWindowRepairBtnClick = LuaEvent:NewIns("InventoryConfig.Events.evtRepairWindowRepairBtnClick"),
    evtRepairWindowRepairFailedByNoEnoughCurrency = LuaEvent:NewIns("InventoryConfig.Events.evtRepairWindowRepairFailedByNoEnoughCurrency"),
    evtRepairWindowShowBegin = LuaEvent:NewIns("InventoryConfig.Events.evtRepairWindowShowBegin"),
    evtRepairWindowHideBegin = LuaEvent:NewIns("InventoryConfig.Events.evtRepairWindowHideBegin"),

    evtWarehouseEquipSlotViewOnDrop = LuaEvent:NewIns("InventoryConfig.Events.evtWarehouseEquipSlotViewOnDrop"),
    evtRefreshCharacter = LuaEvent:NewIns("InventoryConfig.Events.evtRefreshCharacter"),

    evtWareHouseWithTabExtArrangeBtnPressed = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseWithTabExtArrangeBtnPressed"),

    -- BEGIN MODIFICATION @ VIRTUOS : 
    evtWareHouseDepositTabOnFocusReceived = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseDepositTabOnFocusReceived"),
    evtWareHouseDepositTabOnFocusLost = LuaEvent:NewIns("InventoryConfig.Events.evtWareHouseDepositTabOnFocusLost"),

    evtItemSubmitPanelNavigationUpdate = LuaEvent:NewIns("InventoryConfig.Events.evtItemSubmitPanelNavigationUpdate"),

    evtBottomBarSummaryListChangedByItemView = LuaEvent:NewIns("InventoryConfig.Events.evtBottomBarSummaryListChangedByItemView"),
    evtUpdateAllBlankItemVisibility = LuaEvent:NewIns("InventoryConfig.Events.evtUpdateAllBlankItemVisibility"),
    evtUpdateFirstRowBlankItemVisibility = LuaEvent:NewIns("InventoryConfig.Events.evtUpdateFirstRowBlankItemVisibility"),
    evtRemoveYModeBlankItemVisibility = LuaEvent:NewIns("InventoryConfig.Events.evtRemoveYModeBlankItemVisibility"),
    evtProcessItemMoveResultFrontEnd = LuaEvent:NewIns("InventoryConfig.Events.evtProcessItemMoveResultFrontEnd")
    -- END MODIFICATION

}

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region SlotView不同类型
InventoryConfig.ESlotViewType = {
    None = 0,
    NormalSlot = 1,
    OverviewSlot = 2,
    SelectionList = 3,
    EquipmentDepositSlot = 4,
    SpecialWeaponAdapter = 5
}
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--#region 愿望单排序类型
InventoryConfig.EWishSortType = {
    None = 0,
    TimeAsc = 1,        -- 时间升序
    TimeDesc = 2,       -- 时间降序
    NumAsc = 3,         -- 需求数升序
    NumDesc = 4         -- 需求数降序
}
--#endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 道具过滤

InventoryConfig.EDepositLabel = {
    None = 0,
    Weapon = 1,
    Equipment = 2,
    Consumable = 3,
    Other = 4
}

InventoryConfig.EHeroId = {
    Support = "88000000027",
    Reconnaissance = "88000000028",
    Guard = "88000000029",
    Assault = "88000000030",
}

-- InventoryConfig.MapLabelId2Image = {
--     [InventoryConfig.EDepositLabel.None] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_Public_Icon_01.G_Common_Public_Icon_01'",
--     [InventoryConfig.EDepositLabel.Weapon] = "PaperSprite'/Game/UI/UIAtlas/Global/G_Common/G_Common/BakedSprite/G_Common_EquipClass_Icon_W_06.G_Common_EquipClass_Icon_W_06'",
--     [InventoryConfig.EDepositLabel.Equipment] = "PaperSprite'/Game/UI/UIAtlas/FrontEnd/F_Common/F_Common/BakedSprite/F_Common_ItemClass_Icon_Seleted_21.F_Common_ItemClass_Icon_Seleted_21'",
--     [InventoryConfig.EDepositLabel.Consumable] = "PaperSprite'/Game/UI/UIAtlas/FrontEnd/F_Common/F_Common/BakedSprite/F_Common_ItemClass_Icon_Seleted_14.F_Common_ItemClass_Icon_Seleted_14'",
--     [InventoryConfig.EDepositLabel.Other] = "PaperSprite'/Game/UI/UIAtlas/FrontEnd/F_Common/F_Common/BakedSprite/F_Common_ItemClass_Icon_Seleted_13.F_Common_ItemClass_Icon_Seleted_13'"
-- }

InventoryConfig.HeroId2Image = {
    [InventoryConfig.EHeroId.Support] = "Texture2D'/Game/UI_HD/UIAtlas/System/Warehouse/Sp/WarehousePc_Sp_02.WarehousePc_Sp_02'",
    [InventoryConfig.EHeroId.Reconnaissance] = "Texture2D'/Game/UI_HD/UIAtlas/System/Warehouse/Sp/WarehousePc_Sp_03.WarehousePc_Sp_03'",
    [InventoryConfig.EHeroId.Guard] = "Texture2D'/Game/UI_HD/UIAtlas/System/Warehouse/Sp/WarehousePc_Sp_05.WarehousePc_Sp_05'",
    [InventoryConfig.EHeroId.Assault] = "Texture2D'/Game/UI_HD/UIAtlas/System/Warehouse/Sp/WarehousePc_Sp_04.WarehousePc_Sp_04'",
}

InventoryConfig.ContainersInOrder = {
    ESlotType.BagContainer,
    ESlotType.ChestHangingContainer,
    ESlotType.SafeBoxContainer
}

--InventoryConfig.BGMediaSourcePath = "FileMediaSource'/Game/UI/UIVideo/MS_Inventory.MS_Inventory'"
InventoryConfig.BGMediaRowName = "InventoryMedia"

InventoryConfig.MapLevel2TitleBarColor = {
    [1] = ColorUtil.GetLinearColorByHex("0000007F"),
    [2] = ColorUtil.GetLinearColorByHex("174523E6"),
    [3] = ColorUtil.GetLinearColorByHex("234D78E6"),
    [4] = ColorUtil.GetLinearColorByHex("4A437FE6"),
    [5] = ColorUtil.GetLinearColorByHex("8B602AE6"),
    [6] = ColorUtil.GetLinearColorByHex("6E2626E6"),
}

InventoryConfig.LOCATE_SCROLL_INTERNAL = 0.01
InventoryConfig.LOCATE_SCROLL_SPEED = 0.1
InventoryConfig.LOCATE_SCROLL_OFFSET = 140

InventoryConfig.EExtItemType = {
    WeaponBox = 1,
    EquipmentBox = 2,
    CollectableBox = 3,
    AllBox = 9
}

InventoryConfig.EFastEquipCheckResult = {
    NoFastEquip = 0,
    Fail = 1,
    Success = 2,
    InMatching = 3
}

InventoryConfig.ELoadAmmoCheckResult = {
    NoLoadAmmo = 0,
    Fail = 1,
    Success = 2
}

InventoryConfig.ERepairLevel = {
    PrimaryRepair = 1,
    MiddleRepair = 2,
    AdvancedRepair = 3,
}

InventoryConfig.EInvSlotViewType = {
    Default = 1,                                    -- 仓库
    Auciton = 2,                                    -- 交易行
    Assembly_QuickOperation = 3,                    -- 配装配置界面
    Assembly_Preset = 4,                            -- 配装方案界面
    Settlement = 5,                                 -- 结算界面
}

InventoryConfig.MapExtType2FirstTimeEquipTip = {
    [1] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipWeaponExtBox_SubText", "武器"),
    [2] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipEquipmentExtBox_SubText", "装备"),
    [3] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipCollectableExtBox_SubText", "收集品"),
    [9] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipCommonExtBox_SubText", "道具"),
    [10] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipAccessories_SubText", "配件"),
    [12] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipAmmunitionExtBox_SubText", "弹药"),
    [13] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipMedicineExtBox_SubText", "医药"),
    [14] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipKeysExtBox_SubText", "钥匙"),
    [15] = NSLOCTEXT("InventoryModule", "Lua_Inventory_FirstTimeEquipFilesExtBox_SubText", "档案"),
}


UITable[UIName2ID.NewExtSelectionWindowCellWrap] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.InventoryModule.UI.Selection.NewExtSelectionWindowCellWrap",
    BPKey = "WBP_WarehouseExpansionItemWrap"
}

InventoryConfig.EContainerType = {
    SafeBox = 0,
    KeyChain = 1
}

InventoryConfig.SlotsNotAllowOperate = {
    [ESlotType.MeleeWeapon] = true,
    [ESlotType.SafeBox] = true,
    [ESlotType.KeyChain] = true,
}

InventoryConfig.SlotsAllowOperateInCollectionRoom = {
    [ESlotType.Pocket] = true,
    [ESlotType.ChestHangingContainer] = true,
    [ESlotType.BagContainer] = true,
    [ESlotType.NearbyPickups] = true,
}

InventoryConfig.SlotsNotAllowOperateForMobile = {
    [ESlotType.MeleeWeapon] = true,
}

InventoryConfig.SlotsNotAllowDragForMobile = {
    [ESlotType.SafeBox] = true,
    [ESlotType.KeyChain] = true,
}

InventoryConfig.WarehouseBtnType = {
    ExtArrangeBtn = 1,
    SellBtn = 2,
    WistBtn = 3,
    ExtManagerBtn = 4,
    CollectionRoom = 5,
}

InventoryConfig.ReplaceContainerShellCheckSlots = {
    ESlotType.BagContainer,
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.SafeBoxContainer,
}

InventoryConfig.bEnablePreCreate = false                     -- 预创建（todo:框架不支持）
InventoryConfig.bEnableScrollDispatch = true                 -- 是否开启滚动瀑布流加载ItemView（只保留视口内的ItemView）
InventoryConfig.bEnableEquipSlotScrollDispatch = false       -- 装备槽是否开启滚动瀑布流加载ItemView（只保留视口内的ItemView）
InventoryConfig.DefaultRemainTryGetScrollBoxHeightTimes = 3
InventoryConfig.bUseMaxFrameOrItem = false                   -- 滚动分帧限定在多少帧内完成，还是限定每帧处理多少ItemView
-- 如果选择限定在多少帧内完成，具体的帧数配置：
InventoryConfig.ScrollDispatchMaxFrameNumInLooting = 3          -- 滚动分帧限定在多少帧内处理完所有的ItemView，0表示不分帧（局内）
InventoryConfig.ScrollDispatchMaxFrameNumInWH = 2               -- 滚动分帧限定在多少帧内处理完所有的ItemView，0表示不分帧（仓库左侧网格）
InventoryConfig.ScrollDispatchMaxFrameNumForDepository = 0      -- 滚动分帧限定在多少帧内处理完所有的ItemView，0表示不分帧（仓库和扩容箱网格）
-- 如果选择限定在每帧处理多少ItemView，具体的ItemView数配置：
InventoryConfig.ScrollDispatchMaxItemNumInLooting = 1          -- 滚动分帧限定每帧至少处理多少ItemView（局内）
InventoryConfig.ScrollDispatchMaxItemNumInWH = 1               -- 滚动分帧限定每帧至少处理多少ItemView（仓库左侧网格）
InventoryConfig.ScrollDispatchMaxItemNumForDepository = 1      -- 滚动分帧限定每帧至少处理多少ItemView（仓库和扩容箱网格）
InventoryConfig.bEnableSingleClickTransferMode = true           -- 是否开启单击转移模式

InventoryConfig.TabIconPath = {
    [1] = "PaperSprite'/Game/UI/UIAtlas/System/Warehouse/BakedSprite/Warehouse_Icon_0403.Warehouse_Icon_0403'",
    [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0102.CommonHall_Merchant_Icon_0102'",
    [3] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0212.CommonHall_Merchant_Icon_0212'",
    [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_EquipClass_Icon_0001.Common_EquipClass_Icon_0001'",
    [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_EquipClass_Icon_0005.Common_EquipClass_Icon_0005'",
    [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_EquipClass_Icon_0004.Common_EquipClass_Icon_0004'"
}

InventoryConfig.SlotsNotResponseR = {
    [ESlotType.MeleeWeapon] = true,
    [ESlotType.SafeBox] = true,
    [ESlotType.KeyChain] = true,
}

InventoryConfig.MapSlotType2HoverDescription = {
    [ESlotType.ChestHangingContainer] = InventoryConfig.Loc.HoverPortableSpaceDes,
    [ESlotType.Pocket] = InventoryConfig.Loc.HoverPortableSpaceDes,

    [ESlotType.BagContainer] = InventoryConfig.Loc.HoverNormSpaceDes,

    [ESlotType.KeyChainContainer] = InventoryConfig.Loc.HoverSafeSpaceDes,
    [ESlotType.SafeBoxContainer] = InventoryConfig.Loc.HoverSafeSpaceDes,
    [ESlotType.ArchiveContainer] = InventoryConfig.Loc.ArchiveSpaceDes,
}

InventoryConfig.ESafeBoxType = {
    Normal = 0,
    SafeBoxSkin = 1,
}

return InventoryConfig