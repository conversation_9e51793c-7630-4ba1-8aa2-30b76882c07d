----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingVehicleControl
local ClientVehicleSetting = import "ClientVehicleSetting"
local SystemSettingVehicleControl = ui("SystemSettingVehicleControl")
local UDFMMobileCustomLayoutBPLibrary = import "DFMMobileCustomLayoutBPLibrary"
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local VehicleSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.VehicleSettingLogic"
local EDriveAngle = import "EDriveAngle"
local ERollCameraMode = import "ERollCameraMode"
local EVehicleCannonLockMode = import "EVehicleCannonLockMode"
local ESecondWeaponFireBtn = import "ESecondWeaponFireBtn"
local EVehicleMode = import "EVehicleMode"
local EWeaponVehicleMode = import"EWeaponVehicleMode"
local EVehicleAimAssistMode =import "EVehicleAimAssistMode"
local EBattleVehicle = Module.SystemSetting.Config.EBattleVehicle
local EVehicleAimAssistType = import"EVehicleAimAssistType"

local EVehicleMode2Name = {
    [EVehicleMode.Button] = "ButtonStyle",
	[EVehicleMode.Glider] = "GliderStyle",
	[EVehicleMode.JoyStick] = "JoyStickStyle",
}

function SystemSettingVehicleControl:Ctor()
    self._wtBtnVehicleButton = self:Wnd("VehicleModeButton",UIWidgetBase)
    self._wtBtnVehicleGlider = self:Wnd("VehicleModeGlider", UIWidgetBase)
    self._wtBtnJetGlider = self:Wnd("VehicleModeGlider_1", UIWidgetBase)
    self._wtBtnJetJoyStick = self:Wnd("VehicleModeJoyStick_1", UIWidgetBase)
    self._wtBtnVehicleJoyStickStyle = self:Wnd("VehicleModeJoyStick", UIWidgetBase)
    --战斗载具
    --self._wtTank = self:Wnd("VehicleModeTank", UIWidgetBase)
    --self._wtBtnWeaponVehicleButton = self:Wnd("WeaponVehicleModeButton", UIWidgetBase)
    self._wtHelicopter = self:Wnd("VehicleModeHelicopter", UIWidgetBase)
    self._wtBtnDriveAngleOption = self:Wnd("DriveAngleOption", UIWidgetBase)
    self._wtBtnFastSwitchWeaponOption = self:Wnd("FastSwitchWeaponOption", UIWidgetBase)
    self._wtBtnRollCamera = self:Wnd("RollCamera", UIWidgetBase)
    self._wtBtnCannonCamFollowOption = self:Wnd("CannonCamFollowOption", UIWidgetBase)
    self._wtBtnVehicleTurretLock = self:Wnd("VehicleTurretLock", UIWidgetBase)
    self._wtBtnTankCamFollowOption = self:Wnd("TankCamFollowOption", UIWidgetBase)
    self._wtBtnTankDirectionOption = self:Wnd("TankDirectionOption", UIWidgetBase)
    self._wtAimingAssist = self:Wnd("AimingAssist", UIImage)
    self._wtBtnAimingAssist_GroundVehicles = self:Wnd("AimingAssist_GroundVehicles", UIWidgetBase)
    self._wtBtnAimingAssist_AirVehicles = self:Wnd("AimingAssist_AirVehicles", UIWidgetBase)
    self._wtBtnAimingAssist_Fighter = self:Wnd("AimingAssist_Fighter", UIWidgetBase)
    self._wtBtnAimingAssist_gunner = self:Wnd("AimingAssist_gunner", UIWidgetBase)
    self._wtBtnFixedWingOption = self:Wnd("FixedWingOption_1", UIWidgetBase)
    self:BindBtnEvent()
    self._clientVehicleSetting = ClientVehicleSetting.Get(GetWorld())
end

function SystemSettingVehicleControl:OnOpen()

end

function SystemSettingVehicleControl:OnShowBegin()
    self:Init()
    self:RefreshWidget()
end

function SystemSettingVehicleControl:OnShow()
end



function SystemSettingVehicleControl:OnClose()
end

function SystemSettingVehicleControl:BindBtnEvent()

    self._wtBtnVehicleButton:Wnd("Button_1", UIButton):Event("OnClicked", self._ChangeVehicleMode, self, EVehicleMode.Button)
    self._wtBtnVehicleGlider:Wnd("Button_1", UIButton):Event("OnClicked", self._ChangeVehicleMode, self, EVehicleMode.Glider)
    self._wtBtnVehicleJoyStickStyle:Wnd("Button_1", UIButton):Event("OnClicked", self._ChangeVehicleMode, self, EVehicleMode.JoyStick)

    self._wtBtnVehicleButton:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenVehicleLayout, self, EVehicleMode.Button)
    self._wtBtnVehicleGlider:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenVehicleLayout, self, EVehicleMode.Glider)
    self._wtBtnVehicleJoyStickStyle:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenVehicleLayout, self, EVehicleMode.JoyStick)

    self._wtHelicopter:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenHelicopterLayout, self)

    self._wtBtnJetGlider:Wnd("Button_1", UIButton):Event("OnClicked", self._ChangeJetMode, self, EVehicleMode.Glider)
    self._wtBtnJetJoyStick:Wnd("Button_1", UIButton):Event("OnClicked", self._ChangeJetMode, self, EVehicleMode.JoyStick)

    self._wtBtnJetGlider:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenJetLayout, self, EVehicleMode.Glider)
    self._wtBtnJetJoyStick:Wnd("Button_143", UIButton):Event("OnClicked", self._OpenJetLayout, self, EVehicleMode.JoyStick)

    self._wtBtnDriveAngleOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessDriveAngle(EDriveAngle.Follow) end)
    self._wtBtnDriveAngleOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessDriveAngle(EDriveAngle.Free) end)

    self._wtBtnTankCamFollowOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessTankCamFollow(true) end)
    self._wtBtnTankCamFollowOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessTankCamFollow(false) end)

    self._wtAimingAssist:SetBtnClickByIndex(1, self._VehicleAimAssist, self, EVehicleAimAssistMode.AllOpen)
    self._wtAimingAssist:SetBtnClickByIndex(2, self._VehicleAimAssist, self, EVehicleAimAssistMode.AllClose)
    self._wtAimingAssist:SetBtnClickByIndex(3, self._VehicleAimAssist, self, EVehicleAimAssistMode.Custom)

    self._wtBtnAimingAssist_GroundVehicles:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.GroundVehicle,true) end)
    self._wtBtnAimingAssist_GroundVehicles:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.GroundVehicle,false) end)

    self._wtBtnAimingAssist_AirVehicles:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Helicopter,true) end)
    self._wtBtnAimingAssist_AirVehicles:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Helicopter,false) end)

    self._wtBtnAimingAssist_Fighter:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Jet,true) end)
    self._wtBtnAimingAssist_Fighter:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Jet,false) end)

    

    self._wtBtnAimingAssist_gunner:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Gunner,true) end)
    self._wtBtnAimingAssist_gunner:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessAimAssistMap(EVehicleAimAssistType.Gunner,false) end)

    self._wtBtnFixedWingOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessFixedWingOption(true) end)
    self._wtBtnFixedWingOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessFixedWingOption(false) end)

    self._wtBtnRollCamera:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessRollCameraMode(ERollCameraMode.Click) end)
    --self._wtBtnRollCamera:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessRollCameraMode(ERollCameraMode.Press) end)
    self._wtBtnRollCamera:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessRollCameraMode(ERollCameraMode.Mixed) end)

    self._wtBtnCannonCamFollowOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessCannonCamFollow(true) end)
    self._wtBtnCannonCamFollowOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessCannonCamFollow(false) end)
    
    self._wtBtnVehicleTurretLock:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessVehicleCannonLockMode(EVehicleCannonLockMode.Close) end)
    self._wtBtnVehicleTurretLock:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessVehicleCannuonLockMode(EVehicleCannonLockMode.Click) end)
    self._wtBtnVehicleTurretLock:SetBtnClickByIndex(3, function () VehicleSettingLogic.ProcessVehicleCannonLockMode(EVehicleCannonLockMode.Press) end)

    self._wtBtnTankDirectionOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessTankDirection(true) end)
    self._wtBtnTankDirectionOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessTankDirection(false) end)

    self._wtBtnFastSwitchWeaponOption:SetBtnClickByIndex(1, function () VehicleSettingLogic.ProcessEnableFastSwitchVehicleWeapon(ESecondWeaponFireBtn.Close) end)
    self._wtBtnFastSwitchWeaponOption:SetBtnClickByIndex(2, function () VehicleSettingLogic.ProcessEnableFastSwitchVehicleWeapon(ESecondWeaponFireBtn.ChangeWeapon) end)
    self._wtBtnFastSwitchWeaponOption:SetBtnClickByIndex(3, function () VehicleSettingLogic.ProcessEnableFastSwitchVehicleWeapon(ESecondWeaponFireBtn.Fire) end)

end

--获取config中的配置，初始化所有设置项
function SystemSettingVehicleControl:Init()
    self._wtAimingAssist:InitSettingBtnByEume(self._clientVehicleSetting.VehicleAimAssistMode)
    self._wtBtnDriveAngleOption:InitSettingBtnByEume(self._clientVehicleSetting.DriveAngle)
    self._wtBtnRollCamera:InitSettingBtnByEume(self._clientVehicleSetting.RollCameraMode)
    self._wtBtnCannonCamFollowOption:InitSettingBtnByBool(self._clientVehicleSetting.CannonCamFollow)
    self._wtBtnVehicleTurretLock:InitSettingBtnByEume(self._clientVehicleSetting.VehicleCannonLockMode)
    self._wtBtnTankCamFollowOption:InitSettingBtnByBool(self._clientVehicleSetting.TankCamFollow)
    self._wtBtnTankDirectionOption:InitSettingBtnByBool(self._clientVehicleSetting.TankDirection)
    self._wtBtnFastSwitchWeaponOption:InitSettingBtnByEume(self._clientVehicleSetting.SecondWeaponFireBtn)
    self._wtBtnAimingAssist_GroundVehicles:InitSettingBtnByBool(self._clientVehicleSetting.VehicleAimAssistModeMap[EVehicleAimAssistType.GroundVehicle])
    self._wtBtnAimingAssist_AirVehicles:InitSettingBtnByBool(self._clientVehicleSetting.VehicleAimAssistModeMap[EVehicleAimAssistType.Helicopter])
    self._wtBtnAimingAssist_Fighter:InitSettingBtnByBool(self._clientVehicleSetting.VehicleAimAssistModeMap[EVehicleAimAssistType.Jet])
    self._wtBtnAimingAssist_gunner:InitSettingBtnByBool(self._clientVehicleSetting.VehicleAimAssistModeMap[EVehicleAimAssistType.Gunner])
    self._wtBtnFixedWingOption:InitSettingBtnByBool(self._clientVehicleSetting.bFixedWingOption)
end

function SystemSettingVehicleControl:RefreshWidget()
    local weaponVehicleMode = self._clientVehicleSetting.WeaponVehicleMode
    local vehicleMode = self._clientVehicleSetting.VehicleMode
    local jetMode = self._clientVehicleSetting.JetMode
    self._wtBtnVehicleButton:BP_Set_Select(false)
    self._wtBtnVehicleGlider:BP_Set_Select(false)
    self._wtBtnVehicleJoyStickStyle:BP_Set_Select(false)
    self._wtBtnJetGlider:BP_Set_Select(false)
    self._wtBtnJetJoyStick:BP_Set_Select(false)
    --self._wtTank:BP_Set_Select(false)
    --self._wtBtnWeaponVehicleButton:BP_Set_Select(false)
    if vehicleMode == EVehicleMode.Button then
        self.VehicleModeButton:BP_Set_Select(true)
    elseif vehicleMode == EVehicleMode.Glider then
        self.VehicleModeGlider:BP_Set_Select(true)
    elseif vehicleMode == EVehicleMode.JoyStick then
        self.VehicleModeJoyStick:BP_Set_Select(true)
    end
    if weaponVehicleMode == EWeaponVehicleMode.JoyStick then
        --self._wtTank:BP_Set_Select(true)
    elseif weaponVehicleMode == EWeaponVehicleMode.Button then
        --self._wtBtnWeaponVehicleButton:BP_Set_Select(true)
    end

    if jetMode == EVehicleMode.Glider then
        self._wtBtnJetGlider:BP_Set_Select(true)
    elseif jetMode == EVehicleMode.JoyStick then
        self._wtBtnJetJoyStick:BP_Set_Select(true)
    end

    self._wtHelicopter:BP_Set_Select(true)
end

--载具布局模式选择

function SystemSettingVehicleControl:_ChangeVehicleMode(vehicleMode)
    VehicleSettingLogic.ProcessVehicleMode(vehicleMode)
    self:RefreshWidget()

    local layoutName = EVehicleMode2Name[vehicleMode]
    --同步后台载具模式
    Server.SystemSettingServer:SendCurrentUsingLayout("VehicleLayout",layoutName)
    --刷新载具按键数据
	UDFMMobileCustomLayoutBPLibrary.FetchAllCustomLayoutAppearance(GetWorld())
end

function SystemSettingVehicleControl:_OpenVehicleLayout()
    Facade.UIManager:AsyncShowUI(UIName2ID.VehicleLayoutMain, nil, nil)
end

function SystemSettingVehicleControl:_OpenHelicopterLayout()
    Facade.UIManager:AsyncShowUI(UIName2ID.VehicleLayoutMain, nil, nil, EBattleVehicle.Helicopter)
end

function SystemSettingVehicleControl:_ChangeJetMode(JetMode)
    VehicleSettingLogic.ProcessJetMode(JetMode)
    self:RefreshWidget()
end

function SystemSettingVehicleControl:_OpenJetLayout(controlMode)
    Facade.UIManager:AsyncShowUI(UIName2ID.VehicleLayoutMain, nil, nil, EBattleVehicle.Jet,controlMode)
end

function SystemSettingVehicleControl:_VehicleAimAssist(vehicleAimAssistMode)
    VehicleSettingLogic.ProcessVehicleAimAssist(vehicleAimAssistMode)
    if vehicleAimAssistMode == EVehicleAimAssistMode.Custom then
        if not self._wtAimingAssist:GetIsDragOpen() then
            self.AimAssistCustomization:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self._wtAimingAssist:SetIsDragOpen(true)
        else
            self.AimAssistCustomization:SetVisibility(ESlateVisibility.Collapsed)
            self._wtAimingAssist:SetIsDragOpen(false)
        end
    else
        self.AimAssistCustomization:SetVisibility(ESlateVisibility.Collapsed)
        self._wtAimingAssist:SetIsDragOpen(false)
    end
end



return SystemSettingVehicleControl