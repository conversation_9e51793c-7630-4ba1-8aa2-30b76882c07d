----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local ADFMCharacter = import "DFMCharacter"
local ULAI = import "LAI"


---@class ACollectionRoomInteractorBase
local ACollectionRoomInteractorBase = class("ACollectionRoomInteractorBase", CppObject)

function ACollectionRoomInteractorBase:log(...)
    loginfo(self.__cppinst, self._cname, ...)
end

function ACollectionRoomInteractorBase:Ctor()
    self:log("Ctor")

    self.bTriggered = false
    self.interactorType = nil
    rawset(self, "_bIgnoreWorldForceGc", true)
end

function ACollectionRoomInteractorBase:Imp_ReceiveBeginPlay()
    self:log("Imp_ReceiveBeginPlay")

    local collistionCmp = self:GetComponentByClass(UE.BoxComponent)
    if collistionCmp then
        self.beginOverlapHandle = collistionCmp.OnComponentBeginOverlap:Add(self.OnComponentBeginOverlap, self)
        self.endOverlapHandle = collistionCmp.OnComponentEndOverlap:Add(self.OnComponentEndOverlap, self)
    end
end

function ACollectionRoomInteractorBase:Imp_ReceiveEndPlay()
    self:log("Imp_ReceiveEndPlay")

    local collistionCmp = self:GetComponentByClass(UE.BoxComponent)
    if collistionCmp then
        if self.beginOverlapHandle then
            collistionCmp.OnComponentBeginOverlap:Remove(self.beginOverlapHandle)
            self.beginOverlapHandle = nil
        end
        if self.endOverlapHandle then
            collistionCmp.OnComponentEndOverlap:Remove(self.endOverlapHandle)
            self.endOverlapHandle = nil
        end
    end

    -- if self.beginOverlapHandle then
    --     self.OnActorBeginOverlap:Remove(self.beginOverlapHandle)
    --     self.beginOverlapHandle = nil
    -- end
    -- if self.endOverlapHandle then
    --     self.OnActorEndOverlap:Remove(self.endOverlapHandle)
    --     self.endOverlapHandle = nil
    -- end
end

local function fIsLocalPlayerActor(actor)
    return isvalid(actor) and ULAI.IsSubClassOf(actor, ADFMCharacter) and UGameplayBlueprintHelper.IsLocalPlayer(actor)
end

function ACollectionRoomInteractorBase:OnComponentBeginOverlap(myActor, otherActor)
    self:log("OnComponentBeginOverlap", otherActor)

    if fIsLocalPlayerActor(otherActor) then
        self.bTriggered = true
        self:TriggerStart()
    end
end

function ACollectionRoomInteractorBase:OnComponentEndOverlap(myActor, otherActor)
    self:log("OnComponentEndOverlap", otherActor)

    if fIsLocalPlayerActor(otherActor) then
        self.bTriggered = false
        self:TriggerStop()
    end
end

function ACollectionRoomInteractorBase:TriggerStart()
    if self.interactorType ~= nil then
        Module.IrisSafeHouse.Field:AddOperator(self, self.interactorType, 100, 100)
    end
end

function ACollectionRoomInteractorBase:TriggerStop()
    Module.IrisSafeHouse.Field:RemoveOperator(self)
end

function ACollectionRoomInteractorBase:HasTriggered()
    return self.bTriggered
end

function ACollectionRoomInteractorBase:StartInteract()

end

return ACollectionRoomInteractorBase