----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBlackSite)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class BlackSiteModule: ModuleBase
local BlackSiteModule = class("BlackSiteModule", require "DFM.YxFramework.Managers.Module.ModuleBase")

local BlackSiteLogic = require "DFM.Business.Module.BlackSiteModule.BlackSiteLogic"

function BlackSiteModule:OnInitModule()
    self:OutputLog("OnInitModule")
end
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function BlackSiteModule:OnLoadingLogin2Frontend(gameFlowType)
    Module.Subscribe:CheckGameEventStatus("FE7EF0A020A3EB823072513A3D074DD174359DABDE430B0A69CFF87D05636DD7", function() end)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function BlackSiteModule:OnLoadingGame2Frontend(gameFlowType)
    Module.Subscribe:CheckGameEventStatus("FE7EF0A020A3EB823072513A3D074DD174359DABDE430B0A69CFF87D05636DD7", function() end)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function BlackSiteModule:OnLoadingFrontend2Game(gameFlowType)
    self.Field:ResetCache()
    Server.BlackSiteServer:ClearServerData()
end

function BlackSiteModule:OutputLog(log)
    BlackSiteLogic.OutputLog(log)
end

function BlackSiteModule:CheckIsRightReq(callBack)
    BlackSiteLogic.CheckIsRightReq(callBack)
end

function BlackSiteModule:OpenMainMenu()
    BlackSiteLogic.OpenMainMenu()
end

--[[
    id:设备id or 配方id
    construct:boolean
    skipCheck:boolean
    规则:
        1.不填id时,如果construct为true,进入特勤处建造主页,否则进入制造主页
        2.填设备id时,根据construct判断进入该设备的建造还是制造主页
        3.填配方id时,进入该配方所属设备的制造主页
        4.skipCheck为true时,代表跳过模块解锁检测
]]
function BlackSiteModule:Jump(id, construct, skipCheck, callBack)
    if id then
        self:CheckIsRightReq(
            function(res)
                if res then
                    BlackSiteLogic.Jump(id, construct, skipCheck, callBack)
                else
                    if callBack then
                        callBack()
                    end
                end
            end
        )
    else
        BlackSiteLogic.Jump(id, construct, skipCheck, callBack)
    end

    return {UIName2ID.BlackSiteMainPanel}
end

function BlackSiteModule:BlackSiteGetInfoReq()
    BlackSiteLogic.BlackSiteGetInfoReq()
end

function BlackSiteModule:GetJumpInfoByItemId(itemId)
    if Server.BlackSiteServer:GetIsRightInfo() then
        return BlackSiteLogic.GetJumpInfoByItemId(tonumber(itemId))
    end
    self:ReceiveAward()
    return {}, {}
end

function BlackSiteModule:GetDeviceUpgradeDataRow(deviceId, level)
    if Server.BlackSiteServer:GetIsRightInfo() then
        return Server.BlackSiteServer:GetDeviceUpgradeDataRow(deviceId, level)
    end
    self:ReceiveAward()
end

function BlackSiteModule:GetDeviceData(deviceId)
    if Server.BlackSiteServer:GetIsRightInfo() then
        return Server.BlackSiteServer:GetDeviceData(deviceId)
    end
    self:ReceiveAward()
end

function BlackSiteModule:GetAllDeviceData()
    if Server.BlackSiteServer:GetIsRightInfo() then
        return Server.BlackSiteServer:GetAllDeviceData()
    end
    self:ReceiveAward()
    return Server.BlackSiteServer:GetAllDeviceData()
end

function BlackSiteModule:ReceiveAward()
    BlackSiteLogic.ReceiveAward()
end

function BlackSiteModule:_GetSubUIBySelf(parent, UIID, instanceID)
    return BlackSiteLogic._GetSubUIBySelf(parent, UIID, instanceID)
end

function BlackSiteModule:_AddSubUIBySelf(parent, UIID, container)
    return BlackSiteLogic._AddSubUIBySelf(parent, UIID, container)
end

function BlackSiteModule:JumpToMainPanelByUIIdx(uiIdx, bForceJump)
    return BlackSiteLogic.JumpToMainPanelByUIIdx(uiIdx, bForceJump)
end

function BlackSiteModule:SetCurTabIdx(idx)
    BlackSiteLogic.SetCurTabIdx(idx)
end

function BlackSiteModule:GetCurTabIdx()
    return BlackSiteLogic.GetCurTabIdx()
end

function BlackSiteModule:CollectFormulaByID(formulaID, itemName)
    BlackSiteLogic.CollectFormulaByID(formulaID, itemName)
end

function BlackSiteModule:SetItemQualityImg(img, quality)
    BlackSiteLogic.SetItemQualityImg(img, quality)
end

-- azhengzheng:用过id检查设备是否开放
-- deviceID:EBlackSiteDeviceName2Id
function BlackSiteModule:CheckDeviceIsOpenByID(deviceID, bShowTip)
    return BlackSiteLogic.CheckDeviceIsOpenByID(deviceID, bShowTip)
end

function BlackSiteModule:CheckFormulaCollectBtnCDFinish()
    return BlackSiteLogic.CheckFormulaCollectBtnCDFinish()
end

function BlackSiteModule:CheckBtnCDFinishByType(type, tip)
    return BlackSiteLogic.CheckBtnCDFinishByType(type, tip)
end

return BlackSiteModule