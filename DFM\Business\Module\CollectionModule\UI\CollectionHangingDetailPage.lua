----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionHangingDetailPage : LuaUIBaseWindow
local CollectionHangingDetailPage = ui("CollectionHangingDetailPage", require("DFM.YxFramework.Managers.UI.LuaUIBaseWindow"))
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local UGunPresetTableManager = import "GunPresetTableManager"
local EModularPartNodeType = import "EModularPartNodeType"
local UGPGameViewportClient = import "GPGameViewportClient"
local EMouseCursor = import("EMouseCursor")
local UGPInputHelper = import("GPInputHelper")
-- BEGIN MODIFICATION @ VIRTUOS : UI Input Navigitor
local ItemDetailEquipWeaponMysticalSkin = require("DFM.Business.Module.ItemDetailModule.UI.ItemDetailEquipWeaponMysticalSkin")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
ECheckButtonState = import"ECheckButtonState"
local FAnchors                      = import "Anchors"
local RotateComponent                      = import "HallGeneralRotateComponent"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
-- END MODIFICATION
--- BEGIN MODIFICATION @ VIRTUOS
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
--- END MODIFICATION

function CollectionHangingDetailPage:Ctor()
    self._wtMask = self:Wnd("DFImage", UIImage)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtPartsSlot = self:Wnd("wtPartsSlot", UIWidgetBase)
    self._wtEffectsBtn = self:Wnd("wtEffectsBtn", DFCommonButtonOnly)
    self._wtEffectsBtn:Event("OnClicked", self._ShowEffectsPage, self)
    self._wtRenameBtn = self:Wnd("wtRenameBtn", DFCommonButtonOnly)
    self._wtRenameBtn:Event("OnClicked", self._ShowHangingRenamePage, self)
    self._wtShowPresetPartsCheckBox = self:Wnd("wtShowPresetPartsCheckBox", DFCheckBox)
    self._wtShowPresetPartsCheckBox:Event("OnCheckStateChanged", self._OnShowPresetPartsCheckBoxStateChanged, self)
    self._wtPurchaseBtn = self:Wnd("wtPurchaseBtn", DFCommonButtonOnly)
    self._wtPurchaseBtn:Event("OnClicked", self._OnPurchaseBtnClicked, self)
    self._wtShowPresetHint = self:Wnd("wtShowPresetHint", UITextBlock)
    self._wtShowPresetHint:SetText(CollectionConfig.Loc.PreviewInstallation)
    self._wtShowPresetPartsCheckBox:SetIsChecked(false, false)
    self._wtShowPresetPartsCheckBox:Collapsed()
    self._wtShowPresetHint:Collapsed()
    self:BP_SetType(0)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {ECurrencyClientId.Special, ECurrencyClientId.Diamond})
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Collection.Config.Loc.WeaponSkinDetail)
    self._wtEffectsBtn:Collapsed()
    self._wtWeaponPanel = self:Wnd("DFCanvasPanel_68", UIWidgetBase)
    self._wtItemClassifyTabs = self:Wnd("WBP_DFCommonSecondTab_2", UIWidgetBase)
    self._wtItemClassifyTabs:SetConsumeMouseWheel(EConsumeMouseWheel.Always)
    self._wtItemClassifyTabs:BindHoverSubItemCallback(self.HoverSubItem,self)
    self._wtItemClassifyTabs:AddSubItemWidgetShowListener(self._CommonSecondTabSubItemFirstShow,self)
    self._wtWeaponButton = self:Wnd("DFCommonCheckButton", UIWidgetBase)
    self._wtWeaponButton:Event("OnCheckedBoxStateChangedNative",self._OnWeaponOpenStateChanged,self)
    self._wtScrollGridBox = UIUtil.WndWaterfallScrollBox(self, "ScrollGridBox", self._OnGetWeaponListCount, self._OnProcessWeaponWidget)
    self._wtWeaponButtonText = self._wtWeaponButton:Wnd("TextBlockMain", UITextBlock)
    self._DFCanvasPosReContainer_0 =  self:Wnd("DFCanvasPosReContainer_0", UIWidgetBase)

    self._DFCanvasPosReContainer_2 =  self:Wnd("DFCanvasPosReContainer_2", UIWidgetBase)

    self._wtIconCheckBox  = self:Wnd("WBP_CommonKeyIconBox",HDKeyIconBox)
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 10
    end
    self._defaultRotate = nil
    -- END MODIFICATION
    -- self.gunDisplayType = {
    --     CollectionConfig.WeaponType2Name[ItemConfig.EWeaponItemType.Pistol] = {
    --         Type = "Near",
    --     }
    -- }
    self.CameraDisType = {
        Far = 1,
        Near = 2,
    }
end

function CollectionHangingDetailPage:HoverSubItem(widget,position)
    if WidgetUtil.IsGamepad() then 
        local listBox = self._wtItemClassifyTabs:GetSubListBox()
        self:_OnTabChanged(self._curMainIdx,position)
    end
end
-- BEGIN MODIFICATION @ VIRTUOS : UI Input Navigitor
function CollectionHangingDetailPage:_EnableInput(bEnable)
    if not IsHD() then
        return
    end

    if bEnable then 

        self._wtRenameBtn:SetDisplayInputAction("Collection_Rename_Gamepad", true, nil, true)
        if self._RenameBtn == nil then
            self._RenameBtn = self:AddInputActionBinding("Collection_Rename_Gamepad", EInputEvent.IE_Pressed, self._ShowHangingRenamePage, self, EDisplayInputActionPriority.UI_Stack)            
        end

        if self._wtItemDetailView:IsVisible() and self:_IsShowedTipBox() then
            self._ToggleTip  = self:AddInputActionBinding(
                "Common_ToggleTip", 
                EInputEvent.IE_Pressed, 
                self._ToggleTips,
                self, 
                EDisplayInputActionPriority.UI_Stack
            )
            table.insert(self._PCActionList,{actionName="Common_ToggleTip", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        end

        --武器旋转的输入
        if self._RotationX == nil then
            self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
        end

        if self._RotationY == nil then
            self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
        end

    else

        if self._RotationX then
            self:RemoveInputActionBinding(self._RotationX)
            self._RotationX = nil
        end

        if self._RotationY then
            self:RemoveInputActionBinding(self._RotationY)
            self._RotationY = nil
        end

        if self._RenameBtn then 
            self:RemoveInputActionBinding(self._RenameBtn)
            self._RenameBtn = nil
        end
        
        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end

        if self._ToggleTip then
            self:RemoveInputActionBinding(self._ToggleTip)
            self._ToggleTip= nil
        end

        if self._navGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
            self._navGroup = nil
        end
    end
end

function CollectionHangingDetailPage:_IsShowedTipBox()
    if self._wtItemDetailView._CollectionWeaponSkinPanel == nil then
        return false
    else
        local ItemDetailWeaponSkinPanel = self._wtItemDetailView._CollectionWeaponSkinPanel
        if ItemDetailWeaponSkinPanel._wtIDParent and ItemDetailWeaponSkinPanel._wtIDParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtRateParent and ItemDetailWeaponSkinPanel._wtRateParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtWearParent and ItemDetailWeaponSkinPanel._wtWearParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtPlaceholderOneParent and ItemDetailWeaponSkinPanel._wtPlaceholderOneParent:IsVisible() then
            return true
        end

        if ItemDetailWeaponSkinPanel._wtPlaceholderTwoParent and ItemDetailWeaponSkinPanel._wtPlaceholderTwoParent:IsVisible() then
            return true
        end
    end

    return false
end

function CollectionHangingDetailPage:_ToggleTips()
    if not IsHD() then
        return
    end
    if self._wtItemDetailView then
        --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
        self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
        if not self._ItemDetailEquipWeaponMysticalSkin or self._wtShowPresetPartsCheckBox:IsChecked() == true then
            return
        end
        if self._ItemDetailEquipWeaponMysticalSkin and self._ItemDetailEquipWeaponMysticalSkin:IsVisible() then
           self:_UpdatingShowCount()
           self:_UpdatingTipParameters()
            --显示tips
            if self._wtTipsCheckbox and self._wtParent and self._TipsType then
                self:_ShowTip(self._TipsType,self._wtTipsCheckbox,self._wtParent)
            end
        end
       
    end
end

function CollectionHangingDetailPage:_ShowTip(TipsType,wtTipsCheckbox1, wtIDParent)
    if not IsHD() then
        return
    end
    self:_HideTip()
    if not self._handle then
        self._handle = Module.ItemDetail:OpenItemCommonMiniTipsByTipsType(TipsType, wtTipsCheckbox1, wtIDParent, true, nil, nil, 40)
    end
end

function CollectionHangingDetailPage:_HideTip()
    if not IsHD() then
        return
    end
    if self._handle then
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
        self._handle = nil
    end
end


function CollectionHangingDetailPage:_UpdatingShowCount()
    if not IsHD() then
        return
    end

    if self._ShowCount ~= nil then
        --获取tips参数,并根据当前按下Y的次数打开不同的tips
        self._ShowCount = self._ShowCount + 1
        self._ShowCount = self._ShowCount % 6
        if not self._ItemDetailEquipWeaponMysticalSkin._wtIDParent or not self._ItemDetailEquipWeaponMysticalSkin._wtIDParent:IsVisible() then
            if self._ShowCount == 1 then
            self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtRateParent or not self._ItemDetailEquipWeaponMysticalSkin._wtRateParent:IsVisible() then
            if self._ShowCount == 2 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtWearParent or not self._ItemDetailEquipWeaponMysticalSkin._wtWearParent:IsVisible() then
            if self._ShowCount == 3 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtPlaceholderOneParent or not self._ItemDetailEquipWeaponMysticalSkin._wtPlaceholderOneParent:IsVisible() then
            if self._ShowCount == 4 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        if not self._ItemDetailEquipWeaponMysticalSkin._wtPlaceholderTwoParent or not self._ItemDetailEquipWeaponMysticalSkin._wtPlaceholderTwoParent:IsVisible() then
            if self._ShowCount == 5 then
                self._ShowCount = self._ShowCount + 1
            end
        end
        self._ShowCount = self._ShowCount % 6
    end
    
end
function CollectionHangingDetailPage:_UpdatingTipParameters()
    if not IsHD() then
        return
    end

    --根据_ShowCount显示不同的tip
    self._wtParent = nil
    self._wtTipsCheckbox = nil
    self._TipsType = nil
    if self._ShowCount == 1 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("SerialNumber", UIWidgetBase)
        self._wtIDPanel =  self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem", UIWidgetBase)
        if self._wtIDPanel then
            self._wtTipsCheckbox = self._wtIDPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinID
    elseif self._ShowCount == 2 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("Rarity", UIWidgetBase)
        self._wtRatePanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_1", UIWidgetBase)
        if self._wtRatePanel then
            self._wtTipsCheckbox = self._wtRatePanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinRare
    elseif self._ShowCount == 3 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("Wear", UIWidgetBase)
        self._wtWearPanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_2", UIWidgetBase)
        if self._wtWearPanel then
            self._wtTipsCheckbox =self._wtWearPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.SkinWear
    elseif self._ShowCount == 4 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("CountQuantity", UIWidgetBase)
        self._wtkillCntPanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_3", UIWidgetBase)
        if self._wtkillCntPanel then
            self._wtTipsCheckbox =self._wtkillCntPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.PendantPlaceholderOne
    elseif self._ShowCount == 5 then
        self._wtParent = self._ItemDetailEquipWeaponMysticalSkin:Wnd("CountQuantity_1", UIWidgetBase)
        self._wtkillCntPanel = self._ItemDetailEquipWeaponMysticalSkin:Wnd("WBP_ItemDetailContent_SkinItem_4", UIWidgetBase)
        if self._wtkillCntPanel then
            self._wtTipsCheckbox =self._wtkillCntPanel:Wnd("WBP_DFCommonCheckInstruction_2", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
        end
        self._TipsType = ETipsType.PendantPlaceholderTwo
    else
        --隐藏tip
        self:_HideTip()
        return
    end
end

function CollectionHangingDetailPage:_HideDetailViewTips()
    self._ItemDetailEquipWeaponMysticalSkin = self._wtItemDetailView._CollectionWeaponSkinPanel
    if self._ItemDetailEquipWeaponMysticalSkin and self._ItemDetailEquipWeaponMysticalSkin.OnBtnTipsUnhoverd then
        self._ItemDetailEquipWeaponMysticalSkin:OnBtnTipsUnhoverd()
    end
end

function CollectionHangingDetailPage:_WeaponRotationX(value)
    if not IsHD() then
        return
    end
    self._RotationVec.X = value * -1 * self._WeaponRotationZoom
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end

function CollectionHangingDetailPage:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function CollectionHangingDetailPage:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return
    end

    if InputType ~= EGPInputType.Gamepad then
        if self._wtItemDetailView:IsVisible() then
            --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
            self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
            if self._ItemDetailEquipWeaponMysticalSkin then
                self._ItemDetailEquipWeaponMysticalSkin:OnBtnTipsUnhoverd()
            end
        end
    end
end
-- END MODIFICATION


function CollectionHangingDetailPage:OnInitExtraData(item, hidePopUpCallback, bAutoShowPresetParts, skinItem)
    self._item = item
    self._hidePopUpCallback = hidePopUpCallback
    self._bAutoShowPresetParts = bAutoShowPresetParts or false
    self._skinItem = skinItem
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionHangingDetailPage:OnOpen()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallMall)
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Add(self._OnRotateModel, self)
        self._displayCtrlActor.OnTouchPressedSignature:Add(self._OnPressed, self)
    end
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(true,Module.Reward.Config.ECollectionDetaiType.Pendant)
    self:_AddListeners()
    UGPGameViewportClient.SetCursor(self,EMouseCursor.Hand)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
    self._wtItemClassifyTabs:SetIndexChangeEvent(self._OnTabChanged, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionHangingDetailPage:OnClose()
end

function CollectionHangingDetailPage:OnActivate()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallMall)
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Add(self._OnRotateModel, self)
        self._displayCtrlActor.OnTouchPressedSignature:Add(self._OnPressed, self)
    end
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(true,Module.Reward.Config.ECollectionDetaiType.Pendant)
    self:_AddListeners()
    UGPGameViewportClient.SetCursor(self,EMouseCursor.Hand)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
end


function CollectionHangingDetailPage:OnDeactivate()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor.OnTouchRepeatSignature:Remove(self._OnRotateModel, self)
        self._displayCtrlActor.OnTouchPressedSignature:Remove(self._OnPressed, self)
    end
    -- UGPGameViewportClient.SetCursor(self,EMouseCursor.Default) 不能是Default
    UGPGameViewportClient.SetCursor(self,EMouseCursor.None)
    self:RemoveAllLuaEvent()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
    -- local displayType = IsHD() and "Weapon" or "WeaponM"
    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", displayType)
    self._hidePopUpCallback = nil
    Module.Reward.Config.Events.evtOpenCollectionDetailPage:Invoke(false,Module.Reward.Config.ECollectionDetaiType.Pendant)
end


-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionHangingDetailPage:OnShow()
    self.seletedWeaponId = nil
    self._refreshWeaponList = nil
    self:_RefreshWeaponInfoList()
    self._wtWeaponButton:Collapsed()
    self._wtWeaponPanel:Collapsed()
    -- self._wtWeaponButton:OnDropDownCheckBtnStateChanged(false)
    self._DFCanvasPosReContainer_0:Collapsed()
    self._DFCanvasPosReContainer_2:Collapsed()
    self._wtItemClassifyTabs:SetNotDefaultSeleted()
    self.DFCommonCheckButton:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionHangingDetailPage:OnHide()
    local sceneCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallMall)
    if sceneCtrl and self._defaultRotate then 
        sceneCtrl.GeneralRotateComponent.YawRange = self._defaultRotate
    end
    self._DFCanvasPosReContainer_0:Collapsed()
    self._DFCanvasPosReContainer_2:Collapsed()
    self._wtWeaponButton:Collapsed()
    self._wtWeaponPanel:Collapsed()
    self._wtShowPresetPartsCheckBox:SetIsChecked(false, false)
    self._wtShowPresetPartsCheckBox:Collapsed()
    self._wtShowPresetHint:Collapsed()
    self:BP_SetType(0)
    self.DFCommonCheckButton:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
    UGPGameViewportClient.SetCursor(self,EMouseCursor.None)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移
end


function CollectionHangingDetailPage:OnShowBegin()
    self:_OnRefreshItemDetail()
    Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6, EOffsetType.ZOffset)
    if Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.HallMall then
        self:_OnRefreshModel(ESubStage.HallMall)
    end
    if self._hidePopUpCallback then
        self._hidePopUpCallback(false)
    end
    self._ShowCount = 0
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableInput(true)
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._PCActionList)

        self._wtLeftNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtItemClassifyTabs, self, "Hittest")
        if self._wtLeftNavGroup then
            self._wtLeftNavGroup:AddNavWidgetToArray(self._wtItemClassifyTabs.DFCommonSecondTab.MainTabList)
            self._wtLeftNavGroup:SetScrollRecipient(self._wtItemClassifyTabs.DFCommonSecondTab.MainTabList)
            local listBox = self._wtItemClassifyTabs:GetSubListBox()
            if listBox then 
                self._wtLeftNavGroup:AddNavWidgetToArray(listBox)
                self._wtLeftNavGroup:SetScrollRecipient(listBox)
    
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtLeftNavGroup)
        end


        self._wtRightNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollGridBox, self, "Hittest")
        if self._wtRightNavGroup then
            self._wtRightNavGroup:AddNavWidgetToArray(self._wtScrollGridBox)
            self._wtRightNavGroup:SetScrollRecipient(self._wtScrollGridBox)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtRightNavGroup)
        end

        if self._wtIconCheckBox then
            self._wtIconCheckBox:SetOnlyDisplayOnGamepad(true)
            self._wtIconCheckBox:InitByDisplayInputActionName("PreviewInstallationEffect",false, 0, true)
        end
        self._checkBoxInputAction = self:AddInputActionBinding("PreviewInstallationEffect", EInputEvent.IE_Pressed, self._OnShowPresetPartsCheckBoxStateChanged_GamePad, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtShowPresetPartsCheckBox:IsChecked() then 
            self._weaponButtonAction =  self:AddInputActionBinding("WeaponAppearance", EInputEvent.IE_Pressed, self._OnWeaponOpenStateChanged_Pad, self, EDisplayInputActionPriority.UI_Stack)
        end
        if self._wtWeaponButton.wtKeyIcon then
            self._wtWeaponButton.wtKeyIcon:SetOnlyDisplayOnGamepad(true)
            self._wtWeaponButton.wtKeyIcon:InitByDisplayInputActionName("WeaponAppearance",false, 0, true)
        end
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    -- END MODIFICATION
end


function CollectionHangingDetailPage:OnHideBegin()
    if self._hidePopUpCallback then
        self._hidePopUpCallback(true)
    end
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableInput(false)
    end 

    WidgetUtil.RemoveNavigationGroup(self)

    if self._checkBoxInputAction then
        self:RemoveInputActionBinding(self._checkBoxInputAction)
        self._checkBoxInputAction = nil
    end
    if self._weaponButtonAction then 
        self:RemoveInputActionBinding(self._weaponButtonAction)
        self._weaponButtonAction = nil
    end
    -- END MODIFICATION
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionHangingDetailPage:OnAnimFinished(anim)
end



function CollectionHangingDetailPage:_AddListeners()
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self._OnRefreshModel, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnRefreshItemDetail, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyMallGiftSuc, self._OnStoreBuyHotRecommendationSuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyRecordUpdate, self._OnStoreBuyRecordUpdate, self)
    self:AddLuaEvent(LuaGlobalEvents.evtCommonSecondTabSubItemFirstShow, self._CommonSecondTabSubItemFirstShow, self)
end

function  CollectionHangingDetailPage:_CommonSecondTabSubItemFirstShow()
    -- local listBox = self._wtItemClassifyTabs:GetSubListBox()
    -- if listBox  and not self.test  then 
    --     self.test = true
    --     self._wtLeftNavGroup:AddNavWidgetToArray(listBox)
    --     self._wtLeftNavGroup:SetScrollRecipient(listBox)
    -- end
    local item = self._wtItemClassifyTabs:GetFirstSubItem()
    if item then 
        WidgetUtil.SetUserFocusToWidget(item, true)
    end
end

function CollectionHangingDetailPage:_OnRefreshItemDetail()
    self._wtItemDetailView:Collapsed()
    self._wtPartsSlot:Collapsed()
    self._wtEffectsBtn:Collapsed()
    self._wtRenameBtn:Collapsed()
    self._wtPurchaseBtn:Collapsed()
    self._wtMask:Visible()
    self._PCActionList = {}
    if isvalid(self._item) then
        if self._item.itemMainType == EItemType.Adapter then
            self._item = Server.CollectionServer:GetCollectionItemById(self._item.id, self._item.gid) or self._item
            if self._item.itemSubType == ItemConfig.EAdapterItemType.Pendant then 
                self._pendantInfo = CollectionLogic.GetPendantDataRow(self._item.id)
                self._wtItemDetailView:UpdateItem(self._item)
            end 
            if self._pendantInfo ~= nil and self._pendantInfo.VideosPath and #self._pendantInfo.VideosPath > 0 then
                if IsHD() then
                    self._wtEffectsBtn:Collapsed()
                    table.insert(self._PCActionList, {actionName = "GunSkinDetail_ShowEffects",func = self._ShowEffectsPage, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    self._wtEffectsBtn:SelfHitTestInvisible()
                end
            end
        end
        self._wtItemDetailView:SetDetailBtnVisible(false)
        self._wtItemDetailView:SelfHitTestInvisible()
        if not Server.CollectionServer:GetHangingIfOwned(self._item.id, self._item.gid) then
            self.shopData = Server.StoreServer:GetMallGiftWeaponSkinDataByItemID(self._item.id)
            if self.shopData ~= nil then
                local buyLimted = self:GetMallGiftIsBuyLimitedByShopData(self.shopData)
                if buyLimted then
                    -- self._wtPurchaseBtn:BP_SetMainTitle(Module.Store.Config.Loc.RecommendBundleAllItemBuyButtonTip)
                    -- self._wtPurchaseBtn:SetIsEnabled(false)
                else
                    self._wtPurchaseBtn:Visible()
                    self._wtPurchaseBtn:SetIsEnabled(true)
    
                    local priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal,
                                Module.Currency:GetRichTxtImgByItemId(self.shopData.CurrencyType),
                                MathUtil.GetNumberFormatStr(self.shopData.Price))
    
                    self._wtPurchaseBtn:BP_SetMainTitle(priceStr)
                end
            end
        else
            if ItemHelperTool.IsMysticalPendant(self._item.id) and self._item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then 
                self._wtRenameBtn:SelfHitTestInvisible()
            end
        end
    end
    self._wtShowPresetPartsCheckBox:SelfHitTestInvisible()
    self._wtShowPresetHint:SelfHitTestInvisible()
    Module.CommonBar:SetBottomBarTempInputSummaryList(self._PCActionList)
end

function CollectionHangingDetailPage:GetMallGiftIsBuyLimitedByShopData(shopData)
    local ret = false

    if shopData == nil or shopData.GoodsId == nil then
        return ret
    end

    local buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(shopData.GoodsId)
    if buyRecord ~= nil then
        if shopData.LimitType ~= nil and shopData.LimitType > 0 and shopData.LimitAmount > 0 then
            local timeStamp =  Server.StoreServer:GetBuyLimitResetTimeByLimitType(shopData.LimitType)
            if timeStamp ~= 0 then
                if buyRecord.num >= shopData.LimitAmount and buyRecord.buy_time > timeStamp then
                    ret = true
                end
            end
        else
            ret = true
        end
    else
        --buyRecord == nil doesn't limited
        ret = false
    end

    return ret
end

function CollectionHangingDetailPage:_OnRefreshModel(curSubStageType,item)
    if not curSubStageType or curSubStageType == ESubStage.HallMall then
        Module.Hero:ShowOperatorWatch("None")--删除手表
        --设置背景
        Facade.HallSceneManager:SetDisplayBackground(1, false)
        local isWeapon = false
        if not item then 
            item = self._item
        else
            isWeapon = true
        end
        if isvalid(item) then
            local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
            local propInfo = item:GetRawPropInfo()
            local modularDesc = WeaponAssemblyTool.PropInfo_To_Desc(propInfo)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", itemMainType == EItemType.Adapter)
            if not isWeapon then 
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", false)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", modularDesc, item.id, false, true)
            else
                self.lastCameraDisType = self.curCameraDisType
                local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
                local displayType = "Pendant"
                local cameraDisType
                if itemSubType == ItemConfig.EWeaponItemType.Pistol then 
                    cameraDisType = self.CameraDisType.Near
                else
                    cameraDisType = self.CameraDisType.Far
                end
                self.curCameraDisType = cameraDisType
                if not self.lastCameraDisType  then 
                    if cameraDisType ==  self.CameraDisType.Far then 
                        displayType = "Pendant"
                    else
                        displayType = "Pendant_Near"
                    end
                else
                    if self.curCameraDisType ~= self.lastCameraDisType then
                        if self.curCameraDisType == self.CameraDisType.Far then 
                            displayType = "Pendant_NearToFar"
                        else
                            displayType = "Pendant_FarToNear"
                        end
                    else
                        if cameraDisType ==  self.CameraDisType.Far then 
                            displayType = "Pendant"
                        else
                            displayType = "Pendant_Near"
                        end
                    end
                end
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", false)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", true)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponPendant", modularDesc, item.id, false, true)
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", displayType)
            end
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetScaleMinAndMax",0,20)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        end 
    end
end

function CollectionHangingDetailPage:_OnPurchaseBtnClicked()
    -- self:DoBuyMallGifts()
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData, self.shopData.BundleItems, self.shopData.BundleItems, true)
end

function CollectionHangingDetailPage:GetSubstituteCurrencyItemID()
    return 17888808888
end


function CollectionHangingDetailPage:DoBuyMallGifts()
    if self.shopData.IsCash > 0 then
        return
    else
        local goods_id = self.shopData.GoodsId
        local price = self.shopData.Price

        local currency_type = self.shopData.CurrencyType
        local currecny = Module.Currency:GetNumByItemId(currency_type)
        local currency_type_sub = self:GetSubstituteCurrencyItemID()
        local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

        local fee = 0
        local fee_sub = 0
        local bMoneyEnough = false
        if currecny >= price then
            bMoneyEnough = true
            fee = price
        else
            --use currency_type_sub
            if currency_type ~= currency_type_sub then
                if currecny + currecny_sub >= price then
                    bMoneyEnough = true
                    fee = currecny
                    fee_sub = price - currecny
                end
            end
        end

        if bMoneyEnough then
            Server.StoreServer:SendShopBuyBuyMallGiftReq(goods_id, currency_type, fee, currency_type_sub, fee_sub, 1)
            self._wtPurchaseBtn:SetIsEnabled(false)
        else
            -- Module.CommonTips:ShowSimpleTip(Module.Store.Config.Loc.NotEnoughMoney)
            loginfo("[ProductPreview] DoBuyMallGifts NotEnoughMoney currecny:" .. currecny)
            local showStr = Module.Store.Config.Loc.RechargeTipsWindowGoToChargeView
            local confirmStr = Module.Store.Config.Loc.StoreMainTabRecharge

            local _rechargeCancelHandle = function()
            end

            local _rechargeConfirmHandle = function()
                Module.Store:ShowStoreRechargeMainPanle()
            end

            Module.CommonTips:ShowConfirmWindow(showStr, _rechargeConfirmHandle, _rechargeCancelHandle, nil, confirmStr)
            return
        end

    end
end

function CollectionHangingDetailPage:_ShowEffectsPage()
    local pendantDataRow = CollectionLogic.GetPendantDataRow(self._item.id)
    if pendantDataRow then
        Module.Collection:ShowCommonVideoListView(pendantDataRow.VideosName, pendantDataRow.VideosDes, pendantDataRow.VideosPath)
    else
        logerror("[v_dzhanshen] CollectionHangingDetailPage:_ShowEffectsPage pendantDataRow invalid")
    end
end

function CollectionHangingDetailPage:_OnSkinEffectVideoEnd()
end

function CollectionHangingDetailPage:_OnShowPresetPartsCheckBoxStateChanged_GamePad()
    local isChecked = self._wtShowPresetPartsCheckBox:IsChecked()
    self._wtShowPresetPartsCheckBox:SetIsChecked(not isChecked)
    if self._wtShowPresetPartsCheckBox:IsChecked() then 
        self._weaponButtonAction =  self:AddInputActionBinding("WeaponAppearance", EInputEvent.IE_Pressed, self._OnWeaponOpenStateChanged_Pad, self, EDisplayInputActionPriority.UI_Stack)
        self:RemoveInputActionBinding(self._ToggleTip)
        if IsHD() then
            self:_HideTip()
            self._ShowCount = 0
        else
            self:_HideDetailViewTips()
        end
    else
        if self._wtItemDetailView:IsVisible() and self:_IsShowedTipBox() then
            self._ToggleTip  = self:AddInputActionBinding(
                "Common_ToggleTip", 
                EInputEvent.IE_Pressed, 
                self._ToggleTips,
                self, 
                EDisplayInputActionPriority.UI_Stack
            )
            table.insert(self._PCActionList,{actionName="Common_ToggleTip", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        end
        self:RemoveInputActionBinding(self._weaponButtonAction)
    end
    self:_OnShowPresetPartsCheckBoxStateChanged(not isChecked)
end

function CollectionHangingDetailPage:_OnShowPresetPartsCheckBoxStateChanged(bChecked)
    if bChecked then
        self:BP_SetType(1)
        self._DFCanvasPosReContainer_0:SelfHitTestInvisible()
        self._DFCanvasPosReContainer_2:SelfHitTestInvisible()
        self._wtPartsSlot:SelfHitTestInvisible()
        LogAnalysisTool.AddClickSkinDefPreset(self._item.id)
        self._wtWeaponButton:SelfHitTestInvisible()
        if self._wtItemClassifyTabs and not self._refreshWeaponList then
            self._refreshWeaponList = true
            self._wtItemClassifyTabs:SelfHitTestInvisible()
            self._wtItemClassifyTabs:SetTabIndex(1, 1)
            self._wtItemClassifyTabs:FreshMainTabByDataList(self._mainTabList)
            self._wtItemClassifyTabs:RefreshTabItem()
        else
            local item = self._listWeapon[self.selectWeapon]
            self:_OnRefreshModel(ESubStage.HallMall,item)
        end
        self.DFCommonCheckButton:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
        self._wtWeaponPanel:Collapsed()
        local sceneCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallMall)
        if sceneCtrl then
            if self._defaultRotate == nil then 
                self._defaultRotate =  sceneCtrl.GeneralRotateComponent.YawRange
            end
            sceneCtrl.GeneralRotateComponent.YawRange = FVector2D(-50, 30)
        end
        self.seletedWeaponId = nil
        if self._wtLeftNavGroup then 
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtLeftNavGroup)
        end
    else
        self:BP_SetType(0)
        self._DFCanvasPosReContainer_0:Collapsed()
        self._DFCanvasPosReContainer_2:Collapsed()
        self._wtPartsSlot:Collapsed()
        self._wtWeaponButton:Collapsed()
        self:_OnRefreshModel(ESubStage.HallMall)
        local sceneCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallMall)
        if sceneCtrl then 
            sceneCtrl.GeneralRotateComponent.YawRange =  self._defaultRotate
        end
    end
end


function CollectionHangingDetailPage:_OnRotateModel(bKeyPressed)
    if bKeyPressed == true then
        self._wtItemDetailView:Collapsed()
        self._wtPartsSlot:Collapsed()
        self._wtShowPresetPartsCheckBox:Collapsed()
        self._wtShowPresetHint:Collapsed()
        self._wtRenameBtn:Collapsed()
        self._wtWeaponButton:Collapsed()
        self._wtWeaponPanel:Collapsed()
        self._wtItemClassifyTabs:Collapsed()
        if self._wtIconCheckBox then 
            self._wtIconCheckBox:Collapsed()
        end
        if not IsHD() then
            self._wtEffectsBtn:Collapsed()
            Module.CommonBar:SetTopBarVisible(false)
        end
         -- BEGIN MODIFICATION @ VIRTUOS : 关闭手柄打开的Tips
         if IsHD() then
            self:_HideTip()
            self._ShowCount = 0
        else
            self:_HideDetailViewTips()
        end
        -- END MODIFICATION
    else
        self._wtItemDetailView:SelfHitTestInvisible()
        if self._item.gid > 0 and Server.CollectionServer:GetHangingIfOwned(self._item.id, self._item.gid) and self._item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
            self._wtRenameBtn:SelfHitTestInvisible()
        end
        if not IsHD() then
            if self._pendantInfo ~= nil and self._pendantInfo.VideosPath and #self._pendantInfo.VideosPath > 0 then
                self._wtEffectsBtn:SelfHitTestInvisible()
            end
            Module.CommonBar:SetTopBarVisible(true)
        end
        if WidgetUtil.IsGamepad() then
            if self._wtIconCheckBox then 
                self._wtIconCheckBox:SelfHitTestInvisible()
            end
        end
        self._wtShowPresetPartsCheckBox:SelfHitTestInvisible()
        self._wtShowPresetHint:SelfHitTestInvisible()
        if self._wtShowPresetPartsCheckBox:IsChecked() == true then
            self._wtPartsSlot:SelfHitTestInvisible()
            self._DFCanvasPosReContainer_0:SelfHitTestInvisible()
            self._DFCanvasPosReContainer_2:SelfHitTestInvisible()
            self._wtWeaponButton:SelfHitTestInvisible()
            self._wtItemClassifyTabs:SelfHitTestInvisible()
            if self._wtWeaponButton:IsChecked() == true then 
                self._wtWeaponPanel:SelfHitTestInvisible()
            end
            self:BP_SetType(1)
        else
            self:BP_SetType(0)
            self._wtPartsSlot:Collapsed()
            self._wtItemClassifyTabs:Collapsed()
            self._DFCanvasPosReContainer_0:Collapsed()
            self._DFCanvasPosReContainer_2:Collapsed()
            self._wtWeaponButton:Collapsed()
        end
    end
end


function CollectionHangingDetailPage:_ShowHangingRenamePage()
    if self._wtRenameBtn:IsVisible() then
        if isvalid(self._item) then
            if self._item.itemMainType == EItemType.Adapter and ItemHelperTool.IsMysticalPendant(self._item.id) and self._item.quality >= 5 then
                Facade.UIManager:AsyncShowUI(UIName2ID.CollectionPendantChangeNameView, nil, nil, self._item)
            end
        end
    end
end

function CollectionHangingDetailPage:_OnStoreBuyHotRecommendationSuc(dataChange)
    --update buy record
    Server.StoreServer:SendShopGetBuyRecordReq()
    loginfo("[ProductPreview] _OnStoreBuyHotRecommendationSuc")

    local itemList = {}
    if dataChange then
        if dataChange.prop_changes then
            local prop_changes = dataChange.prop_changes
            for _, propChange in ipairs(prop_changes) do
                if propChange.prop then
                    local item = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                    local weaponpriceature = item:GetFeature(EFeatureType.Weapon)
                    if weaponFeature and weaponFeature:IsWeaponSkin() then
                        -- 蓝图需要手动设置一下枪械信息
                        local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                        local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                        item:SetRawPropInfo(propinfo)
                    else
                        item:SetRawPropInfo(propChange.prop)
                    end
                    table.insert(itemList, item)
                end
            end
        end

        -- if dataChange.currency_changes then
        --     local currency_changes = dataChange.currency_changes
        --     for _, currencyChange in ipairs(currency_changes) do
        --         if currencyChange.delta ~= 0 then
        --             local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
        --             table.insert(itemList, item)
        --         end
        --     end
        -- end
    end

    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, nil, nil, true)
end


function CollectionHangingDetailPage:_OnStoreBuyRecordUpdate()
    if self.shopData ~= nil then
        self._wtPurchaseBtn:Visible()
        local buyLimted = self:GetMallGiftIsBuyLimitedByShopData(self.shopData)
        if buyLimted then
            self._wtPurchaseBtn:BP_SetMainTitle(Module.Store.Config.Loc.RecommendBundleAllItemBuyButtonTip)
            self._wtPurchaseBtn:SetIsEnabled(false)
        else
            self._wtPurchaseBtn:SetIsEnabled(true)
            local priceStr = string.format(Module.Store.Config.Loc.ItemPriceNormal,
                        Module.Currency:GetRichTxtImgByItemId(self.shopData.CurrencyType),
                        MathUtil.GetNumberFormatStr(self.shopData.Price))

            self._wtPurchaseBtn:BP_SetMainTitle(priceStr)
        end
    end
end

function CollectionHangingDetailPage:_OnRelayConnected()
    Server.StoreServer:SendShopGetBuyRecordReq()
end

function CollectionHangingDetailPage:CreateWeaponItem(skinId)
    local item = ItemBase:NewIns(skinId, 0)
    local weaponDesc = item:GetRawDescObj()
    local propInfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDesc)
    if propInfo then 
        propInfo.weapon.pendant_id = self._item.id
        propInfo.weapon.pendant_gid = self._item.gid
        local mystical_pendant_data = self._item.rawPropInfo.mystical_pendant_data
        if mystical_pendant_data then
            ItemBase.SetWeaponMysticalPendantInfo2PropInfo(propInfo, mystical_pendant_data)
       end
        -- Server.InventoryServer:OverrideWeaponMysticalPendantFromCollectionServer(propInfo)
        item:SetRawPropInfo(propInfo)
        local modularDesc = WeaponAssemblyTool.PropInfo_To_Desc(propInfo)
        item:SetRawDescObj(modularDesc)
    end
    table.insert(self._listWeapon,item) 
end

function CollectionHangingDetailPage:_RefreshWeaponInfoList()
    self._mainTabList = {}
    local weaponPresent = CollectionLogic.GetWeaponPreset()
    for seriesIndex, weaponSeriesData in ipairs(weaponPresent) do
        local template = {}
        template.mainTabText = CollectionConfig.WeaponType2Name[weaponSeriesData.gunTypeId]
        template.subTabDataList = {}
        for weaponIndex, baseWeaponId in ipairs(weaponSeriesData.weapons) do
            table.insert(template.subTabDataList , {
                baseWeaponId = baseWeaponId,
                subTabText = ItemConfigTool.GetItemName(baseWeaponId),
            })
        end
        table.insert(self._mainTabList, template)
    end
    self._listWeapon = {}
    self._curMainIdx = -1
    self._curSubIdx = -1
    local weaponId = self._mainTabList[1].subTabDataList[1].baseWeaponId
    local skinIDs = Server.GunsmithServer:GetDataStore():GetWeaponSkinIDsFromBaseWeaponID(weaponId)
    if skinIDs then 
        for k,skinId in ipairs(skinIDs) do 
            if Server.CollectionServer:IsVisibleProp(skinId) then 
                self:CreateWeaponItem(skinId)
            end
        end
    end
    local presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(weaponId)
    self:CreateWeaponItem(presetId)
    table.sort(self._listWeapon,function(a,b)  return a.quality > b.quality  end)
end

function CollectionHangingDetailPage:_OnTabChanged(mainTabIdx, subTabIdx, component)
    loginfo("CollectionHangingDetailPage:_OnTabClicked mainTabIdx="..mainTabIdx.." subTabIdx="..subTabIdx)
    if mainTabIdx == 1 and subTabIdx == 1 then 
        self:_CommonSecondTabSubItemFirstShow()
    end
    if self._curMainIdx == mainTabIdx and self._curSubIdx == subTabIdx then 
        return
    end
    self._curMainIdx = mainTabIdx
    self._curSubIdx = subTabIdx
    if mainTabIdx <= 0 then
        return
    end
    local weaponId = self._mainTabList[ self._curMainIdx].subTabDataList[self._curSubIdx].baseWeaponId
    local weaponChange = false
    if weaponId ~= self.seletedWeaponId then
        weaponChange = true
        self.seletedWeaponId = weaponId
    end
    local name = ItemConfigTool.GetItemName(weaponId)
    self._wtWeaponButton:SetMainTitleText4AllState(string.format(Module.Collection.Config.Loc.WeaponPreview,name))
    local skinIDs = Server.GunsmithServer:GetDataStore():GetWeaponSkinIDsFromBaseWeaponID(weaponId)
    self._listWeapon = {}
    if skinIDs then 
        for k,skinId in ipairs(skinIDs) do 
            if Server.CollectionServer:IsVisibleProp(skinId) then 
                self:CreateWeaponItem(skinId)
            end
        end
    end
    local presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(weaponId)
    self:CreateWeaponItem(presetId)
    table.sort(self._listWeapon,function(a,b)
        if a.quality ~= b.quality then
            return a.quality > b.quality
        end
        local weaponTypeA = a.itemSubType
        local weaponTypeB = b.itemSubType
        if weaponTypeA ~= weaponTypeB then
            return weaponTypeA < weaponTypeB
        end
        local bIsMysticalA = ItemHelperTool.IsMysticalSkin(a.id)
        local bIsMysticalB = ItemHelperTool.IsMysticalSkin(b.id)
        if bIsMysticalA ~= bIsMysticalB then
            return bIsMysticalA
        end
        return a.id < b.id
    end)
    local childCount = self:_OnGetWeaponListCount()
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtWeaponPanel)
    local Size = canvasSlot:GetSize()
    if IsHD() then 
        if childCount >= 5  then 
            canvasSlot:SetSize(FVector2D(Size.X, self.MaxHeight[1]))
        else
            canvasSlot:SetSize(FVector2D(Size.X, childCount * 240+ 40)) --硬编码只是为了找到合适的适配效果
        end
       
    else
        if childCount > 3 then 
            canvasSlot:SetSize(FVector2D(Size.X, self.MaxHeight[2]))
        else
            canvasSlot:SetSize(FVector2D(Size.X, childCount * 240 + 40)) --硬编码只是为了找到合适的适配效果
        end
    end
    if weaponChange then 
        self.selectWeapon = 1
        Facade.LuaFramingManager:RegisterFrameTask(self.RefreshWeaponList, self)
        local item = self._listWeapon[self.selectWeapon]
        self:_OnRefreshModel(ESubStage.HallMall,item)
    end
end

function CollectionHangingDetailPage:RefreshWeaponList()
    self._wtScrollGridBox:RefreshAllItems()
end

function CollectionHangingDetailPage:_OnWeaponOpenStateChanged(eCheckButtonState)
    if eCheckButtonState then 
        self._wtWeaponPanel:SelfHitTestInvisible()
    else
        self._wtWeaponPanel:Collapsed()
    end
end

function CollectionHangingDetailPage:_OnWeaponOpenStateChanged_Pad()
    local isChecked = self._wtWeaponButton:IsChecked()
    self._wtWeaponButton:SetIsChecked(not isChecked)
    self:_OnWeaponOpenStateChanged(not isChecked)
end

function CollectionHangingDetailPage:_OnGetWeaponListCount()
    if self._listWeapon and next(self._listWeapon) then
        return #self._listWeapon
    end
    return 0
end

function CollectionHangingDetailPage:_OnProcessWeaponWidget(position, itemWidget)
    local item = self._listWeapon[position]
    local fClickCb = CreateCallBack(self._OnWeaponSkinItemClick, self,itemWidget, position)
    itemWidget:BindClickCallback(fClickCb)
    local fHoverCb = CreateCallBack(self._OnWeaponSkinHoverClick, self,itemWidget, position)
    itemWidget:BindHoverCallback(fHoverCb)
    itemWidget:InitWeaponSkinItem(item)
    itemWidget:SetSelected(itemWidget,self.selectWeapon == position)
    if self.selectWeapon == position then 
        self._selectWeaponCell = itemWidget
    end
end

function CollectionHangingDetailPage:_OnWeaponSkinHoverClick(itemWidget, position)
    if WidgetUtil.IsGamepad() then
        self:_OnWeaponSkinItemClick(itemWidget,position)
    end
end

function CollectionHangingDetailPage:_OnWeaponSkinItemClick(itemCell, position)
    if self._selectWeaponCell then 
        self._selectWeaponCell:SetSelected(self._selectWeaponCell,false)
    end
    local item = self._listWeapon[position]
    self.selectWeapon = position
    itemCell:SetSelected(itemCell,true)
    self._selectWeaponCell = itemCell
    self:_OnRefreshModel(ESubStage.HallMall,item)
end

function CollectionHangingDetailPage:_OnPressed(isPressed)
    if isPressed then 
        if self._wtWeaponButton:IsChecked() == true then
            self._wtWeaponButton:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
            self._wtWeaponPanel:Collapsed()
        end

        if IsHD() then
            self:_HideTip()
            self._ShowCount = 0
        else
            self:_HideDetailViewTips()
        end
    end
end
-----------------------------------------------------------------------
return CollectionHangingDetailPage
