----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- PlatformLogic
--- *兼容多个平台的适配逻辑
--------------------------------------------------------------------------
local PlatformLogic = {}

--- 全屏撑满
PlatformLogic.anchorData = import("AnchorData")()
local defaultOffsets = FMargin()
local defaultAnchors = import("Anchors")()
defaultAnchors.Maximum = FVector2D(1, 1)
local defaultAlignment = FVector2D(0, 0)
PlatformLogic.anchorData.Anchors = defaultAnchors
PlatformLogic.anchorData.Offsets = defaultOffsets
PlatformLogic.anchorData.Alignment = defaultAlignment


PlatformLogic.GetAnchorData = function(boundingBoxCoordinate)
    if boundingBoxCoordinate then
        local anchorData = import("AnchorData")()
        local fitAnchors = import("Anchors")()
        local minX = boundingBoxCoordinate.Left
        local minY = boundingBoxCoordinate.Top
        local maxX = boundingBoxCoordinate.Right
        local maxY = boundingBoxCoordinate.Bottom
        fitAnchors.Minimum = FVector2D(minX, minY)
        fitAnchors.Maximum = FVector2D(maxX, maxY)
        anchorData.Anchors = fitAnchors
        anchorData.Offsets = defaultOffsets
        anchorData.Alignment = defaultAlignment
        return anchorData
    else
        return PlatformLogic.anchorData
    end
end

PlatformLogic.GetAnchorDataFromOffsets = function(offsets)
    if offsets then
        local anchorData = import("AnchorData")()
        local fitAnchors = import("Anchors")()
        fitAnchors.Minimum = FVector2D(0, 0)
        fitAnchors.Maximum = FVector2D(1, 1)
        anchorData.Anchors = fitAnchors
        anchorData.Offsets = offsets
        anchorData.Alignment = defaultAlignment
        return anchorData
    else
        return PlatformLogic.anchorData
    end
end

--- 当前划定为除了Sub、HUD的UI受框架层级Layer管理
---@param layerType EUILayer
---@return boolean
PlatformLogic.IsOutOfAreaType = function(layerType)
    if layerType == EUILayer.Sub or layerType == EUILayer.HUD then
        return true
    else
        return false
    end
end

--- 当前划定为Loading和Admin为强类型层级
---@param layerType EUILayer
---@return boolean
PlatformLogic.IsForceAreaType = function(layerType)
    if layerType == EUILayer.Loading or layerType == EUILayer.Admin then
        return true
    else
        return false
    end
end

--- 当前划定为HUD层级
---@param layerType EUILayer
---@return boolean
PlatformLogic.IsHUDAreaType = function(layerType)
    if layerType == EUILayer.HUD_ScreenEffect
        or layerType == EUILayer.HUD_Mark
        or layerType == EUILayer.HUD_Hint
        or layerType == EUILayer.HUD_Common
        or layerType == EUILayer.HUD_Feedback
        or layerType == EUILayer.HUD_Touch
        or layerType == EUILayer.HUD_Popup
        or layerType == EUILayer.HUD_LargePopup then
        return true
    else
        return false
    end
end


--- 清理指定容器内的ui实例（注意需要跳过的框架容器）
---@param wtContainer UIWidgetBase
---@param wtSkipIns UIWidgetBase 通常为框架级容器
PlatformLogic.ClearFrameContainer = function(wtContainer, wtSkipIns)
    if wtContainer and not hasdestroy(wtContainer) then
        local wtContainerCppIns = wtContainer.__cppinst
        -- logframe("[ GameFlow Debug ] ******* Lua Clean All UI-------------platform layers")
        local bImmediate = true
        if wtContainer.GetAllChildren then
            for i, uiIns in pairs(wtContainer:GetAllChildren()) do
                if uiIns ~= wtSkipIns then
                    if not hasdestroy(uiIns) then
                        if issubclass(LAI.GetObjectClass(uiIns), UE.BaseUIView) then
                            --DO NOTHING
                        else
                            local bLuaPendingKill = true
                            Facade.UIManager:FinalCloseUI(uiIns, bImmediate, bLuaPendingKill)
                            logframe("[ GameFlow Debug ] ******* Lua Clean All UI-------------", wtContainer, wtContainerCppIns, " GetAllChildren FinalCloseUI", uiIns._cname)
                        end
                    else
                        logwarning("[ GameFlow Debug ] ******* Lua Clean All UI-------------", wtContainer, wtContainerCppIns," GetAllChildren FinalCloseUI but ui hasdestroy", hasdestroy(uiIns))
                    end
                else
                    logwarning("[ GameFlow Debug ] ******* Lua Clean All UI-------------", wtContainer, wtContainerCppIns," GetAllChildren should skip frame container", wtSkipIns)
                end
            end
        end
        if wtSkipIns == nil then
            if wtContainer.ClearChildren then
                wtContainer:ClearChildren()
                logwarning("[ GameFlow Debug ] ******* Lua Clean All UI-------------", wtContainer, wtContainerCppIns," ClearChildren")
            end
        else
            for i, uiIns in pairs(wtContainer:GetAllChildren()) do
                if uiIns ~= wtSkipIns then
                    uiIns:RemoveFromParent()
                    logwarning("[ GameFlow Debug ] ******* Lua Clean All UI-------------", wtContainer, wtContainerCppIns," RemoveFromParent", uiIns._cname)
                end
            end
        end
    end
end

return  PlatformLogic