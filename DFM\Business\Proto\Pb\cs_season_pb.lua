require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require("DFM.Business.Proto.Pb.errcode_pb")
require("DFM.Business.Proto.Pb.cs_rank_pb")
require("DFM.Business.Proto.Pb.cs_mpdeposit_pb")
require("DFM.Business.Proto.Pb.zone2dsa_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_season_editor_pb"
end

SeasonRankList = {
RankList_Invalid = 0,
SOL_Global = 1,
SOL_Friends = 2,
RAID_Global = 3,
Raid_Friends = 4,
TDM_Global = 5,
TDM_Friends = 6,
}
pb.__pb_CSSeasonGetInfoReq = {
    serial = 0,
    player_id = 0,
    mode = 0,
}
pb.__pb_CSSeasonGetInfoReq.__name = "CSSeasonGetInfoReq"
pb.__pb_CSSeasonGetInfoReq.__index = pb.__pb_CSSeasonGetInfoReq
pb.__pb_CSSeasonGetInfoReq.__pairs = __pb_pairs

pb.CSSeasonGetInfoReq = { __name = "CSSeasonGetInfoReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetInfoReq : ProtoBase
---@field public serial number
---@field public player_id number
---@field public mode number

---@return pb_CSSeasonGetInfoReq
function pb.CSSeasonGetInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetInfoRes = {
    result = 0,
    cur_serial = 0,
    begin_serial = 0,
    prev_serial = 0,
    prev_commander_serial = 0,
    start_time = 0,
    end_time = 0,
    UIStartTime = 0,
    UIEndTime = 0,
    next_serial_start_time = 0,
}
pb.__pb_CSSeasonGetInfoRes.__name = "CSSeasonGetInfoRes"
pb.__pb_CSSeasonGetInfoRes.__index = pb.__pb_CSSeasonGetInfoRes
pb.__pb_CSSeasonGetInfoRes.__pairs = __pb_pairs

pb.CSSeasonGetInfoRes = { __name = "CSSeasonGetInfoRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetInfoRes : ProtoBase
---@field public result number
---@field public cur_serial number
---@field public begin_serial number
---@field public season_info pb_ProfileSeasonInfo
---@field public prev_serial number
---@field public prev_commander_serial number
---@field public start_time number
---@field public end_time number
---@field public awards pb_PropInfo[]
---@field public mp_awards pb_PropInfo[]
---@field public mp_commander_awards pb_PropInfo[]
---@field public UIStartTime number
---@field public UIEndTime number
---@field public next_serial_start_time number

---@return pb_CSSeasonGetInfoRes
function pb.CSSeasonGetInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetTotalDataReq = {
    player_id = 0,
    is_career = false,
}
pb.__pb_CSSeasonGetTotalDataReq.__name = "CSSeasonGetTotalDataReq"
pb.__pb_CSSeasonGetTotalDataReq.__index = pb.__pb_CSSeasonGetTotalDataReq
pb.__pb_CSSeasonGetTotalDataReq.__pairs = __pb_pairs

pb.CSSeasonGetTotalDataReq = { __name = "CSSeasonGetTotalDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetTotalDataReq : ProtoBase
---@field public mode pb_MatchModeInfo
---@field public player_id number
---@field public is_career boolean

---@return pb_CSSeasonGetTotalDataReq
function pb.CSSeasonGetTotalDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetTotalDataRes = {
    result = 0,
    total_fight = 0,
    kd = 0,
    total_escaped = 0,
    avg_survival_time = 0,
    max_escape_streak = 0,
    total_game_time = 0,
    carry_teammate_assets = 0,
}
pb.__pb_CSSeasonGetTotalDataRes.__name = "CSSeasonGetTotalDataRes"
pb.__pb_CSSeasonGetTotalDataRes.__index = pb.__pb_CSSeasonGetTotalDataRes
pb.__pb_CSSeasonGetTotalDataRes.__pairs = __pb_pairs

pb.CSSeasonGetTotalDataRes = { __name = "CSSeasonGetTotalDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetTotalDataRes : ProtoBase
---@field public result number
---@field public total_fight number
---@field public kd number
---@field public total_escaped number
---@field public avg_survival_time number
---@field public max_escape_streak number
---@field public total_game_time number
---@field public carry_teammate_assets number
---@field public sol_data pb_IrisSOLData
---@field public raid_data pb_IrisRaidData
---@field public mp_data pb_MPData

---@return pb_CSSeasonGetTotalDataRes
function pb.CSSeasonGetTotalDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetDetailDataReq = {
    player_id = 0,
    season_no = 0,
    is_rank = false,
    is_victory_unite = false,
    is_victory_unite_commander = false,
}
pb.__pb_CSSeasonGetDetailDataReq.__name = "CSSeasonGetDetailDataReq"
pb.__pb_CSSeasonGetDetailDataReq.__index = pb.__pb_CSSeasonGetDetailDataReq
pb.__pb_CSSeasonGetDetailDataReq.__pairs = __pb_pairs

pb.CSSeasonGetDetailDataReq = { __name = "CSSeasonGetDetailDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetDetailDataReq : ProtoBase
---@field public mode pb_MatchModeInfo
---@field public player_id number
---@field public season_no number
---@field public is_rank boolean
---@field public is_victory_unite boolean
---@field public is_victory_unite_commander boolean

---@return pb_CSSeasonGetDetailDataReq
function pb.CSSeasonGetDetailDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetDetailDataRes = {
    result = 0,
}
pb.__pb_CSSeasonGetDetailDataRes.__name = "CSSeasonGetDetailDataRes"
pb.__pb_CSSeasonGetDetailDataRes.__index = pb.__pb_CSSeasonGetDetailDataRes
pb.__pb_CSSeasonGetDetailDataRes.__pairs = __pb_pairs

pb.CSSeasonGetDetailDataRes = { __name = "CSSeasonGetDetailDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetDetailDataRes : ProtoBase
---@field public result number
---@field public sol_data pb_CSSOLSeasonData
---@field public mp_data pb_CSMPSeasonData

---@return pb_CSSeasonGetDetailDataRes
function pb.CSSeasonGetDetailDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSOLSeasonData = {
    total_fight = 0,
    total_escape = 0,
    total_game_time = 0,
    total_kill = 0,
    total_killed = 0,
    carry_teammate_assets = 0,
    total_collection_price = 0,
    total_gained_price = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_down = 0,
    total_shoot_head_down = 0,
    total_pickup_teammate = 0,
    total_revive_teammate = 0,
    total_contract_price = 0,
    total_bring_mander_box = 0,
    kill_low_stakes = 0,
    kill_med_stakes = 0,
    kill_high_stakes = 0,
    killed_low_stakes = 0,
    killed_med_stakes = 0,
    killed_high_stakes = 0,
    recent_not_enough = false,
    season_not_enough = false,
    rank_attended = false,
    rank_score = 0,
    rank_score_max = 0,
    rank_max_season_no = 0,
}
pb.__pb_CSSOLSeasonData.__name = "CSSOLSeasonData"
pb.__pb_CSSOLSeasonData.__index = pb.__pb_CSSOLSeasonData
pb.__pb_CSSOLSeasonData.__pairs = __pb_pairs

pb.CSSOLSeasonData = { __name = "CSSOLSeasonData", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSOLSeasonData : ProtoBase
---@field public total_fight number
---@field public total_escape number
---@field public total_game_time number
---@field public total_kill number
---@field public total_killed number
---@field public carry_teammate_assets number
---@field public total_collection_price number
---@field public total_gained_price number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_down number
---@field public total_shoot_head_down number
---@field public total_pickup_teammate number
---@field public total_revive_teammate number
---@field public total_contract_price number
---@field public total_bring_mander_box number
---@field public kill_low_stakes number
---@field public kill_med_stakes number
---@field public kill_high_stakes number
---@field public killed_low_stakes number
---@field public killed_med_stakes number
---@field public killed_high_stakes number
---@field public season_avg_radar pb_CSSOLRadarData
---@field public recent_avg_radar pb_CSSOLRadarData
---@field public season_avg_radar_dims pb_CSRadarDim[]
---@field public recent_avg_radar_dims pb_CSRadarDim[]
---@field public recent_not_enough boolean
---@field public season_not_enough boolean
---@field public rank_attended boolean
---@field public rank_score number
---@field public rank_score_max number
---@field public rank_max_season_no number

---@return pb_CSSOLSeasonData
function pb.CSSOLSeasonData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRadarDim = {
    dim = 0,
    score = 0,
}
pb.__pb_CSRadarDim.__name = "CSRadarDim"
pb.__pb_CSRadarDim.__index = pb.__pb_CSRadarDim
pb.__pb_CSRadarDim.__pairs = __pb_pairs

pb.CSRadarDim = { __name = "CSRadarDim", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRadarDim : ProtoBase
---@field public dim number
---@field public score number

---@return pb_CSRadarDim
function pb.CSRadarDim:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSOLRadarData = {
    team = 0,
    fight = 0,
    search = 0,
    survive = 0,
    wealth = 0,
}
pb.__pb_CSSOLRadarData.__name = "CSSOLRadarData"
pb.__pb_CSSOLRadarData.__index = pb.__pb_CSSOLRadarData
pb.__pb_CSSOLRadarData.__pairs = __pb_pairs

pb.CSSOLRadarData = { __name = "CSSOLRadarData", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSOLRadarData : ProtoBase
---@field public team number
---@field public fight number
---@field public search number
---@field public survive number
---@field public wealth number

---@return pb_CSSOLRadarData
function pb.CSSOLRadarData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMPSeasonData = {
    total_fight = 0,
    total_win = 0,
    total_kill = 0,
    total_game_time = 0,
    total_score = 0,
    total_help = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_kill_head = 0,
    total_capture_point = 0,
    total_vehicle_use_time = 0,
    total_damage_to_vehicle = 0,
    total_vehicle_kill = 0,
    total_vehicle_destroyed = 0,
    recent_not_enough = false,
    season_not_enough = false,
    rank_attended = false,
    rank_score = 0,
    rank_score_max = 0,
    rank_max_season_no = 0,
}
pb.__pb_CSMPSeasonData.__name = "CSMPSeasonData"
pb.__pb_CSMPSeasonData.__index = pb.__pb_CSMPSeasonData
pb.__pb_CSMPSeasonData.__pairs = __pb_pairs

pb.CSMPSeasonData = { __name = "CSMPSeasonData", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMPSeasonData : ProtoBase
---@field public total_fight number
---@field public total_win number
---@field public total_kill number
---@field public total_game_time number
---@field public total_score number
---@field public total_help number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_kill_head number
---@field public total_capture_point number
---@field public total_vehicle_use_time number
---@field public total_damage_to_vehicle number
---@field public total_vehicle_kill number
---@field public total_vehicle_destroyed number
---@field public season_avg_radar pb_CSMPRadarData
---@field public recent_avg_radar pb_CSMPRadarData
---@field public season_avg_radar_dims pb_CSRadarDim[]
---@field public recent_avg_radar_dims pb_CSRadarDim[]
---@field public recent_not_enough boolean
---@field public season_not_enough boolean
---@field public rank_attended boolean
---@field public rank_score number
---@field public rank_score_max number
---@field public rank_max_season_no number

---@return pb_CSMPSeasonData
function pb.CSMPSeasonData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMPRadarData = {
    vehicle = 0,
    shoot = 0,
    survive = 0,
    target = 0,
    team = 0,
}
pb.__pb_CSMPRadarData.__name = "CSMPRadarData"
pb.__pb_CSMPRadarData.__index = pb.__pb_CSMPRadarData
pb.__pb_CSMPRadarData.__pairs = __pb_pairs

pb.CSMPRadarData = { __name = "CSMPRadarData", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMPRadarData : ProtoBase
---@field public vehicle number
---@field public shoot number
---@field public survive number
---@field public target number
---@field public team number

---@return pb_CSMPRadarData
function pb.CSMPRadarData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetRecordListReq = {
    page = 0,
    num = 0,
    player_id = 0,
}
pb.__pb_CSSeasonGetRecordListReq.__name = "CSSeasonGetRecordListReq"
pb.__pb_CSSeasonGetRecordListReq.__index = pb.__pb_CSSeasonGetRecordListReq
pb.__pb_CSSeasonGetRecordListReq.__pairs = __pb_pairs

pb.CSSeasonGetRecordListReq = { __name = "CSSeasonGetRecordListReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetRecordListReq : ProtoBase
---@field public page number
---@field public num number
---@field public mode pb_MatchModeInfo
---@field public player_id number

---@return pb_CSSeasonGetRecordListReq
function pb.CSSeasonGetRecordListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetRecordListRes = {
    result = 0,
    page = 0,
    num = 0,
    max_page = 0,
    is_hidden = false,
}
pb.__pb_CSSeasonGetRecordListRes.__name = "CSSeasonGetRecordListRes"
pb.__pb_CSSeasonGetRecordListRes.__index = pb.__pb_CSSeasonGetRecordListRes
pb.__pb_CSSeasonGetRecordListRes.__pairs = __pb_pairs

pb.CSSeasonGetRecordListRes = { __name = "CSSeasonGetRecordListRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetRecordListRes : ProtoBase
---@field public result number
---@field public record_list pb_MatchBaseRecord[]
---@field public page number
---@field public num number
---@field public max_page number
---@field public is_hidden boolean

---@return pb_CSSeasonGetRecordListRes
function pb.CSSeasonGetRecordListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetRecordReq = {
    key = "",
    show_teammates_only = false,
    show_player_id = 0,
}
pb.__pb_CSSeasonGetRecordReq.__name = "CSSeasonGetRecordReq"
pb.__pb_CSSeasonGetRecordReq.__index = pb.__pb_CSSeasonGetRecordReq
pb.__pb_CSSeasonGetRecordReq.__pairs = __pb_pairs

pb.CSSeasonGetRecordReq = { __name = "CSSeasonGetRecordReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetRecordReq : ProtoBase
---@field public key string
---@field public mode pb_MatchModeInfo
---@field public show_teammates_only boolean
---@field public show_player_id number

---@return pb_CSSeasonGetRecordReq
function pb.CSSeasonGetRecordReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetRecordRes = {
    result = 0,
    game_result = 0,
}
pb.__pb_CSSeasonGetRecordRes.__name = "CSSeasonGetRecordRes"
pb.__pb_CSSeasonGetRecordRes.__index = pb.__pb_CSSeasonGetRecordRes
pb.__pb_CSSeasonGetRecordRes.__pairs = __pb_pairs

pb.CSSeasonGetRecordRes = { __name = "CSSeasonGetRecordRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetRecordRes : ProtoBase
---@field public result number
---@field public player_record pb_PlayerMatchRecord[]
---@field public game_result number

---@return pb_CSSeasonGetRecordRes
function pb.CSSeasonGetRecordRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonRaidMapListReq = {
}
pb.__pb_CSSeasonRaidMapListReq.__name = "CSSeasonRaidMapListReq"
pb.__pb_CSSeasonRaidMapListReq.__index = pb.__pb_CSSeasonRaidMapListReq
pb.__pb_CSSeasonRaidMapListReq.__pairs = __pb_pairs

pb.CSSeasonRaidMapListReq = { __name = "CSSeasonRaidMapListReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonRaidMapListReq : ProtoBase

---@return pb_CSSeasonRaidMapListReq
function pb.CSSeasonRaidMapListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RaidMapItem = {
    map_name = "",
    map_id = 0,
}
pb.__pb_RaidMapItem.__name = "RaidMapItem"
pb.__pb_RaidMapItem.__index = pb.__pb_RaidMapItem
pb.__pb_RaidMapItem.__pairs = __pb_pairs

pb.RaidMapItem = { __name = "RaidMapItem", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RaidMapItem : ProtoBase
---@field public map_name string
---@field public map_id number

---@return pb_RaidMapItem
function pb.RaidMapItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonRaidMapListRes = {
    result = 0,
}
pb.__pb_CSSeasonRaidMapListRes.__name = "CSSeasonRaidMapListRes"
pb.__pb_CSSeasonRaidMapListRes.__index = pb.__pb_CSSeasonRaidMapListRes
pb.__pb_CSSeasonRaidMapListRes.__pairs = __pb_pairs

pb.CSSeasonRaidMapListRes = { __name = "CSSeasonRaidMapListRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonRaidMapListRes : ProtoBase
---@field public result number
---@field public maps pb_RaidMapItem[]

---@return pb_CSSeasonRaidMapListRes
function pb.CSSeasonRaidMapListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchDeathHurtDataReq = {
    player_id = 0,
    room_id = 0,
}
pb.__pb_CSSeasonGetMatchDeathHurtDataReq.__name = "CSSeasonGetMatchDeathHurtDataReq"
pb.__pb_CSSeasonGetMatchDeathHurtDataReq.__index = pb.__pb_CSSeasonGetMatchDeathHurtDataReq
pb.__pb_CSSeasonGetMatchDeathHurtDataReq.__pairs = __pb_pairs

pb.CSSeasonGetMatchDeathHurtDataReq = { __name = "CSSeasonGetMatchDeathHurtDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchDeathHurtDataReq : ProtoBase
---@field public player_id number
---@field public room_id number

---@return pb_CSSeasonGetMatchDeathHurtDataReq
function pb.CSSeasonGetMatchDeathHurtDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchDeathHurtDataRes = {
    result = 0,
}
pb.__pb_CSSeasonGetMatchDeathHurtDataRes.__name = "CSSeasonGetMatchDeathHurtDataRes"
pb.__pb_CSSeasonGetMatchDeathHurtDataRes.__index = pb.__pb_CSSeasonGetMatchDeathHurtDataRes
pb.__pb_CSSeasonGetMatchDeathHurtDataRes.__pairs = __pb_pairs

pb.CSSeasonGetMatchDeathHurtDataRes = { __name = "CSSeasonGetMatchDeathHurtDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchDeathHurtDataRes : ProtoBase
---@field public result number
---@field public death_hurt_data pb_Ds2AnyDeathAndHurtDetailInfoNtf

---@return pb_CSSeasonGetMatchDeathHurtDataRes
function pb.CSSeasonGetMatchDeathHurtDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchScoreDataReq = {
    player_id = 0,
    room_id = 0,
}
pb.__pb_CSSeasonGetMatchScoreDataReq.__name = "CSSeasonGetMatchScoreDataReq"
pb.__pb_CSSeasonGetMatchScoreDataReq.__index = pb.__pb_CSSeasonGetMatchScoreDataReq
pb.__pb_CSSeasonGetMatchScoreDataReq.__pairs = __pb_pairs

pb.CSSeasonGetMatchScoreDataReq = { __name = "CSSeasonGetMatchScoreDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchScoreDataReq : ProtoBase
---@field public player_id number
---@field public room_id number

---@return pb_CSSeasonGetMatchScoreDataReq
function pb.CSSeasonGetMatchScoreDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchScoreDataRes = {
    result = 0,
    rank_match_score = 0,
    rank_match_score_delta = 0,
}
pb.__pb_CSSeasonGetMatchScoreDataRes.__name = "CSSeasonGetMatchScoreDataRes"
pb.__pb_CSSeasonGetMatchScoreDataRes.__index = pb.__pb_CSSeasonGetMatchScoreDataRes
pb.__pb_CSSeasonGetMatchScoreDataRes.__pairs = __pb_pairs

pb.CSSeasonGetMatchScoreDataRes = { __name = "CSSeasonGetMatchScoreDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchScoreDataRes : ProtoBase
---@field public result number
---@field public rank_score_changes pb_RankMatchScoreChange[]
---@field public rank_match_score number
---@field public rank_match_score_delta number

---@return pb_CSSeasonGetMatchScoreDataRes
function pb.CSSeasonGetMatchScoreDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchKillDataReq = {
    player_id = 0,
    room_id = 0,
}
pb.__pb_CSSeasonGetMatchKillDataReq.__name = "CSSeasonGetMatchKillDataReq"
pb.__pb_CSSeasonGetMatchKillDataReq.__index = pb.__pb_CSSeasonGetMatchKillDataReq
pb.__pb_CSSeasonGetMatchKillDataReq.__pairs = __pb_pairs

pb.CSSeasonGetMatchKillDataReq = { __name = "CSSeasonGetMatchKillDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchKillDataReq : ProtoBase
---@field public player_id number
---@field public room_id number

---@return pb_CSSeasonGetMatchKillDataReq
function pb.CSSeasonGetMatchKillDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchKillDataRes = {
    result = 0,
}
pb.__pb_CSSeasonGetMatchKillDataRes.__name = "CSSeasonGetMatchKillDataRes"
pb.__pb_CSSeasonGetMatchKillDataRes.__index = pb.__pb_CSSeasonGetMatchKillDataRes
pb.__pb_CSSeasonGetMatchKillDataRes.__pairs = __pb_pairs

pb.CSSeasonGetMatchKillDataRes = { __name = "CSSeasonGetMatchKillDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchKillDataRes : ProtoBase
---@field public result number
---@field public kill_array pb_GspPlayerKillInfo[]

---@return pb_CSSeasonGetMatchKillDataRes
function pb.CSSeasonGetMatchKillDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchGainedDataReq = {
    player_id = 0,
    room_id = 0,
}
pb.__pb_CSSeasonGetMatchGainedDataReq.__name = "CSSeasonGetMatchGainedDataReq"
pb.__pb_CSSeasonGetMatchGainedDataReq.__index = pb.__pb_CSSeasonGetMatchGainedDataReq
pb.__pb_CSSeasonGetMatchGainedDataReq.__pairs = __pb_pairs

pb.CSSeasonGetMatchGainedDataReq = { __name = "CSSeasonGetMatchGainedDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchGainedDataReq : ProtoBase
---@field public player_id number
---@field public room_id number

---@return pb_CSSeasonGetMatchGainedDataReq
function pb.CSSeasonGetMatchGainedDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMatchGainedDataRes = {
    result = 0,
    carry_out_profit_price = 0,
    blue_print_special_id = 0,
    blue_print_type = 0,
    blue_print_price = 0,
    contract_quest_price = 0,
    carry_out_new_props_price = 0,
    friend_add_exp_buf = 0,
    safehouse_add_exp_buf = 0,
    cost_price = 0,
    safebox_skin_id = 0,
}
pb.__pb_CSSeasonGetMatchGainedDataRes.__name = "CSSeasonGetMatchGainedDataRes"
pb.__pb_CSSeasonGetMatchGainedDataRes.__index = pb.__pb_CSSeasonGetMatchGainedDataRes
pb.__pb_CSSeasonGetMatchGainedDataRes.__pairs = __pb_pairs

pb.CSSeasonGetMatchGainedDataRes = { __name = "CSSeasonGetMatchGainedDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMatchGainedDataRes : ProtoBase
---@field public result number
---@field public carry_out_props pb_EquipPosition[]
---@field public carry_out_health_list pb_EquipHealth[]
---@field public carry_out_profit_price number
---@field public blue_print_special_id number
---@field public blue_print_type number
---@field public blue_print_price number
---@field public contract_quest_price number
---@field public carry_out_new_props_price number
---@field public friend_add_exp_buf number
---@field public safehouse_add_exp_buf number
---@field public cost_price number
---@field public safebox_skin_id number

---@return pb_CSSeasonGetMatchGainedDataRes
function pb.CSSeasonGetMatchGainedDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonRankRecvAwardReq = {
    mode = 0,
    season = 0,
}
pb.__pb_CSSeasonRankRecvAwardReq.__name = "CSSeasonRankRecvAwardReq"
pb.__pb_CSSeasonRankRecvAwardReq.__index = pb.__pb_CSSeasonRankRecvAwardReq
pb.__pb_CSSeasonRankRecvAwardReq.__pairs = __pb_pairs

pb.CSSeasonRankRecvAwardReq = { __name = "CSSeasonRankRecvAwardReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonRankRecvAwardReq : ProtoBase
---@field public mode number
---@field public season number
---@field public levels number[]

---@return pb_CSSeasonRankRecvAwardReq
function pb.CSSeasonRankRecvAwardReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonRankRecvAwardRes = {
    result = 0,
}
pb.__pb_CSSeasonRankRecvAwardRes.__name = "CSSeasonRankRecvAwardRes"
pb.__pb_CSSeasonRankRecvAwardRes.__index = pb.__pb_CSSeasonRankRecvAwardRes
pb.__pb_CSSeasonRankRecvAwardRes.__pairs = __pb_pairs

pb.CSSeasonRankRecvAwardRes = { __name = "CSSeasonRankRecvAwardRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonRankRecvAwardRes : ProtoBase
---@field public result number
---@field public data_change pb_DataChange

---@return pb_CSSeasonRankRecvAwardRes
function pb.CSSeasonRankRecvAwardRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonCommanderRecvAwardReq = {
    season = 0,
}
pb.__pb_CSSeasonCommanderRecvAwardReq.__name = "CSSeasonCommanderRecvAwardReq"
pb.__pb_CSSeasonCommanderRecvAwardReq.__index = pb.__pb_CSSeasonCommanderRecvAwardReq
pb.__pb_CSSeasonCommanderRecvAwardReq.__pairs = __pb_pairs

pb.CSSeasonCommanderRecvAwardReq = { __name = "CSSeasonCommanderRecvAwardReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonCommanderRecvAwardReq : ProtoBase
---@field public season number
---@field public levels number[]

---@return pb_CSSeasonCommanderRecvAwardReq
function pb.CSSeasonCommanderRecvAwardReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonCommanderRecvAwardRes = {
    result = 0,
}
pb.__pb_CSSeasonCommanderRecvAwardRes.__name = "CSSeasonCommanderRecvAwardRes"
pb.__pb_CSSeasonCommanderRecvAwardRes.__index = pb.__pb_CSSeasonCommanderRecvAwardRes
pb.__pb_CSSeasonCommanderRecvAwardRes.__pairs = __pb_pairs

pb.CSSeasonCommanderRecvAwardRes = { __name = "CSSeasonCommanderRecvAwardRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonCommanderRecvAwardRes : ProtoBase
---@field public result number
---@field public data_change pb_DataChange

---@return pb_CSSeasonCommanderRecvAwardRes
function pb.CSSeasonCommanderRecvAwardRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonRankScoreChangedNtf = {
    mode = 0,
    score = 0,
    score_old = 0,
    score_shoot = 0,
    score_tactics = 0,
    score_vehicle = 0,
}
pb.__pb_CSSeasonRankScoreChangedNtf.__name = "CSSeasonRankScoreChangedNtf"
pb.__pb_CSSeasonRankScoreChangedNtf.__index = pb.__pb_CSSeasonRankScoreChangedNtf
pb.__pb_CSSeasonRankScoreChangedNtf.__pairs = __pb_pairs

pb.CSSeasonRankScoreChangedNtf = { __name = "CSSeasonRankScoreChangedNtf", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonRankScoreChangedNtf : ProtoBase
---@field public mode number
---@field public score number
---@field public score_old number
---@field public score_shoot number
---@field public score_tactics number
---@field public score_vehicle number

---@return pb_CSSeasonRankScoreChangedNtf
function pb.CSSeasonRankScoreChangedNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMallPropsReq = {
    mode = 0,
    page_no = 0,
    page_sz = 0,
}
pb.__pb_CSSeasonGetMallPropsReq.__name = "CSSeasonGetMallPropsReq"
pb.__pb_CSSeasonGetMallPropsReq.__index = pb.__pb_CSSeasonGetMallPropsReq
pb.__pb_CSSeasonGetMallPropsReq.__pairs = __pb_pairs

pb.CSSeasonGetMallPropsReq = { __name = "CSSeasonGetMallPropsReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMallPropsReq : ProtoBase
---@field public mode number
---@field public page_no number
---@field public page_sz number

---@return pb_CSSeasonGetMallPropsReq
function pb.CSSeasonGetMallPropsReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankMatchMallProp = {
    id = 0,
    season_no = 0,
    prop_id = 0,
    prop_name = "",
    sol_unlock_minorid = 0,
    refresh_interval = 0,
    limit = 0,
    price = 0,
    weight = 0,
    need_reddot = false,
    bought_num = 0,
}
pb.__pb_RankMatchMallProp.__name = "RankMatchMallProp"
pb.__pb_RankMatchMallProp.__index = pb.__pb_RankMatchMallProp
pb.__pb_RankMatchMallProp.__pairs = __pb_pairs

pb.RankMatchMallProp = { __name = "RankMatchMallProp", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankMatchMallProp : ProtoBase
---@field public id number
---@field public season_no number
---@field public prop_id number
---@field public prop_name string
---@field public sol_unlock_minorid number
---@field public refresh_interval number
---@field public limit number
---@field public price number
---@field public weight number
---@field public need_reddot boolean
---@field public bought_num number

---@return pb_RankMatchMallProp
function pb.RankMatchMallProp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonGetMallPropsRes = {
    result = 0,
    coins = 0,
    total_pages = 0,
}
pb.__pb_CSSeasonGetMallPropsRes.__name = "CSSeasonGetMallPropsRes"
pb.__pb_CSSeasonGetMallPropsRes.__index = pb.__pb_CSSeasonGetMallPropsRes
pb.__pb_CSSeasonGetMallPropsRes.__pairs = __pb_pairs

pb.CSSeasonGetMallPropsRes = { __name = "CSSeasonGetMallPropsRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonGetMallPropsRes : ProtoBase
---@field public result number
---@field public coins number
---@field public props pb_RankMatchMallProp[]
---@field public total_pages number

---@return pb_CSSeasonGetMallPropsRes
function pb.CSSeasonGetMallPropsRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonBuyMallPropsReq = {
    mode = 0,
    id = 0,
    num = 0,
}
pb.__pb_CSSeasonBuyMallPropsReq.__name = "CSSeasonBuyMallPropsReq"
pb.__pb_CSSeasonBuyMallPropsReq.__index = pb.__pb_CSSeasonBuyMallPropsReq
pb.__pb_CSSeasonBuyMallPropsReq.__pairs = __pb_pairs

pb.CSSeasonBuyMallPropsReq = { __name = "CSSeasonBuyMallPropsReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonBuyMallPropsReq : ProtoBase
---@field public mode number
---@field public id number
---@field public num number

---@return pb_CSSeasonBuyMallPropsReq
function pb.CSSeasonBuyMallPropsReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonBuyMallPropsRes = {
    result = 0,
    left_coins = 0,
    bought_times = 0,
}
pb.__pb_CSSeasonBuyMallPropsRes.__name = "CSSeasonBuyMallPropsRes"
pb.__pb_CSSeasonBuyMallPropsRes.__index = pb.__pb_CSSeasonBuyMallPropsRes
pb.__pb_CSSeasonBuyMallPropsRes.__pairs = __pb_pairs

pb.CSSeasonBuyMallPropsRes = { __name = "CSSeasonBuyMallPropsRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonBuyMallPropsRes : ProtoBase
---@field public result number
---@field public left_coins number
---@field public bought_times number

---@return pb_CSSeasonBuyMallPropsRes
function pb.CSSeasonBuyMallPropsRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonLoadTotalDataReq = {
    player_id = 0,
}
pb.__pb_CSSeasonLoadTotalDataReq.__name = "CSSeasonLoadTotalDataReq"
pb.__pb_CSSeasonLoadTotalDataReq.__index = pb.__pb_CSSeasonLoadTotalDataReq
pb.__pb_CSSeasonLoadTotalDataReq.__pairs = __pb_pairs

pb.CSSeasonLoadTotalDataReq = { __name = "CSSeasonLoadTotalDataReq", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonLoadTotalDataReq : ProtoBase
---@field public player_id number

---@return pb_CSSeasonLoadTotalDataReq
function pb.CSSeasonLoadTotalDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSeasonLoadTotalDataRes = {
    result = 0,
    total_fight = 0,
    kd = 0,
    total_escaped = 0,
    avg_survival_time = 0,
    max_escape_streak = 0,
    total_game_time = 0,
}
pb.__pb_CSSeasonLoadTotalDataRes.__name = "CSSeasonLoadTotalDataRes"
pb.__pb_CSSeasonLoadTotalDataRes.__index = pb.__pb_CSSeasonLoadTotalDataRes
pb.__pb_CSSeasonLoadTotalDataRes.__pairs = __pb_pairs

pb.CSSeasonLoadTotalDataRes = { __name = "CSSeasonLoadTotalDataRes", __service="season", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSeasonLoadTotalDataRes : ProtoBase
---@field public result number
---@field public info pb_PlayerSimpleInfo
---@field public total_fight number
---@field public kd number
---@field public total_escaped number
---@field public avg_survival_time number
---@field public max_escape_streak number
---@field public total_game_time number

---@return pb_CSSeasonLoadTotalDataRes
function pb.CSSeasonLoadTotalDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


