----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



--方案管理
---@class CustomLayoutSchemeItem
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local CustomLayoutSchemeItem = ui("CustomLayoutSchemeItem")

function CustomLayoutSchemeItem:Ctor()
    self._wtSaveBtn = self:Wnd("wtSaveBtn", UIWidgetBase)
    self._wtSaveBtn:Event("OnClicked",self.SaveCustomScheme, self)

    self._wtLoadBtn = self:Wnd("wtLoadBtn", UIWidgetBase)
    self._wtLoadBtn:Event("OnClicked",self.LoadCustomScheme, self)

    self._wtShareBtn = self:Wnd("wtShareBtn", UIWidgetBase)
    self._wtShareBtn:Event("OnClicked",self.ShareCustomScheme, self)
    
    self._wtTitle = self:Wnd("wtTitle", UITextBlock)

    self._wtCustomImage = self:Wnd("DFImage_385", UIImage)
    self._isSave = false
end

function CustomLayoutSchemeItem:OnInitExtraData(layoutName, index)
    self:SetItemData(layoutName, index)
end

function CustomLayoutSchemeItem:SetSchemeItemData(layoutName, index)
    self._layoutName = layoutName
    self._index = index
    self._wtCustomImage:AsyncSetImagePath(Module.SystemSetting.Config.Loc.CustomImageTbl[layoutName], false)
end

function CustomLayoutSchemeItem:SetNotSave()
    self._wtLoadBtn:SetIsEnabled(false)
    self._wtShareBtn:SetIsEnabled(false)
    self._wtTitle:SetText(Module.SystemSetting.Config.Loc.CustomSchemeTitle .. self._index)
    self._isSave = false
end

function CustomLayoutSchemeItem:SetHaveSave(value)
    self._wtLoadBtn:SetIsEnabled(true)
    self._wtShareBtn:SetIsEnabled(true)
    self._customValue = value
    self._wtTitle:SetText(value.title)
    self._isSave = true
end

function CustomLayoutSchemeItem:SaveCustomScheme()
    if self._isSave then
        local function fOnConfirmCallbackIns()
            Facade.UIManager:AsyncShowUI(UIName2ID.CustomLayoutSchemeName, nil, nil, tostring(self._wtTitle:GetText()), self._layoutName, self._index)
            Module.SystemSetting.Config.Event.evtCloseSchemePanel:Invoke()
        end
        Module.CommonTips:ShowConfirmWindow(string.format(Module.SystemSetting.Config.Loc.NewCloudSave, self._customValue.title) , fOnConfirmCallbackIns, nil,Module.SystemSetting.Config.Loc.CancelShare,Module.SystemSetting.Config.Loc.Save)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.CustomLayoutSchemeName, nil, nil, tostring(self._wtTitle:GetText()), self._layoutName, self._index)
        Module.SystemSetting.Config.Event.evtCloseSchemePanel:Invoke()
    end

end

function CustomLayoutSchemeItem:LoadCustomScheme()
    Server.SystemSettingServer:LoadCustomScheme(self._layoutName, self._customValue)
    Module.SystemSetting.Config.Event.evtCloseSchemePanel:Invoke()
end

function CustomLayoutSchemeItem:ShareCustomScheme()
    local function fOnConfirmCallbackIns()
        Server.SystemSettingServer:DoPresetShareKeyValueReq(self._customValue.key, self._customValue.title)
    end
    Module.CommonTips:ShowConfirmWindow(Module.SystemSetting.Config.Loc.NewShareCode, fOnConfirmCallbackIns, nil,Module.SystemSetting.Config.Loc.CancelShare,Module.SystemSetting.Config.Loc.GanerateNewShareCode)
    Module.SystemSetting.Config.Event.evtCloseSchemePanel:Invoke()
end

function CustomLayoutSchemeItem:OnOpen()
end

function CustomLayoutSchemeItem:OnClose()
end





return CustomLayoutSchemeItem