----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------


local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local CurrencyHelperTool         = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local ItemHelperTool             = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local RentalDataLogic            = require "DFM.Business.Module.ArmedForceModule.Logic.Rental.RentalDataLogic"
local ItemConfigTool             = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UAmmoDataManager = import "AmmoDataManager"
local ammoMgr = UAmmoDataManager.Get()
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EMouseCursor = import("EMouseCursor")

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import("EGPInputType")
-- END MODIFICATION

local Speed = 360*10
local DefaultColor = "C001" -- 手游
if IsHD() then
    DefaultColor = "C002" -- pc
end
local AbnormalColor = "C004"

---@class AssemblyHDRentalItem : LuaUIBaseView
local AssemblyHDRentalItem = ui("AssemblyHDRentalItem")

local containerTypeList = {
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.BagContainer,
}
function AssemblyHDRentalItem:Ctor()
    self._rentalData = nil
    self._bEnoughNum = false
    self._bCanApply = false
    self._tipsIndex = 0
    self._bContainerReadyMap = {}
    self._bContainerReady = false
    self._slotGroup = self.BP_SlotGroup or ESlotGroup.Player
    self._planIndex = self.BP_SlotGroup and Module.ArmedForce.Config.ERentalType[self.BP_SlotGroup]
    self._mapSlotType2EquipViews = {}
    self._wtTitle = self:Wnd("DFTextBlock_354", UITextBlock)
    self._wtPrice = self:Wnd("DFRichTextBlock_95", UITextBlock)
    self._wtRefreshBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtRefreshBtn:Event("OnClicked", self._OnRefreshBtnClicked, self)

    self._wtApplyBtn = self:Wnd("WBP_DFCommonButtonV1S1", DFCommonButtonOnly)
    self._wtApplyBtn:Event("OnClicked", self._OnApplyBtnClicked, self)
    self._wtApplyBtn:Event("OnDeClicked", self._OnApplyBtnClicked, self)
    self._wtApplyBtn:SetStretchState(EStretch.None)

    self._wtRentalCancelTips = self:Wnd("WBP_EquipmentOnLoan", UIWidgetBase)
    self._wtRentalTipsText = self._wtRentalCancelTips:Wnd("DFTextBlock_127", UITextBlock)
    self._wtRentalTipsText:SetText(Module.ArmedForce.Config.Loc.EquipmentRentalStr)
    self._wtRentalCancelBtn = self._wtRentalCancelTips:Wnd("DFButton_124", UIButton)
    self._wtRentalCancelBtn:Event("OnClicked", self._OnCancelBtnClicked, self)

    self:_InitEquipmentSlotViews()
    self:_InitContainerSlotViews()
    self:_DisabelHoverTipsFocus()

    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self:SetCppValue("bIsFocusable", true)
        -- -- 初始化关闭图标
        -- self._wtRentalCancelIcon = self._wtRentalCancelTips:Wnd("KeyIcon", UIWidgetBase)
        -- if self._wtRentalCancelIcon then
        --     --设置是否只在Gamepad上显示
        --     self._wtRentalCancelIcon:SetOnlyDisplayOnGamepad(true)
        --     --设置当前KeyIcon绑定的Action
        --     -- self._wtRentalCancelIcon:InitByDisplayInputActionName("Assembly_CancelRental_Gamepad", true, 0.0, true)
        --     self._wtRentalCancelIcon:InitByDisplayInputActionName("AssemblyQuickOperation_Remove", true, 0.0, true)
            
        -- end
        self._wtApplyBtn:SetDisplayInputAction("Assembly_RedeemSet", false, nil, true)
        self._wtApplyBtn:SetKeyIconVisibility(false)
    end
    -- END MODIFICATION
end

function AssemblyHDRentalItem:OnOpen()
end

function AssemblyHDRentalItem:OnShowBegin()
    -- self:_RefreshView()
    self._rentalData = nil
    self._bEquipReady = false
    self._bContainerReadyMap = {}
    self:_ResetContainerSlotViewsContainerSlot()
    self:AddListeners()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

function AssemblyHDRentalItem:OnHideBegin()
    self._rentalData = nil
    self._bEquipReady = false
    self._bContainerReadyMap = {}
    self:ReleaseTimer()
    self:RemoveAllLuaEvent()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end

function AssemblyHDRentalItem:OnClose()
    
end

function AssemblyHDRentalItem:OnInitExtraData()

end

function AssemblyHDRentalItem:AddListeners()
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalChanged, self._OnRentalDataChanged, self)
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged, self._OnRentalStatusChanged, self)
    self:AddLuaEvent(Server.CurrencyServer.Events.evtCurrencyNumChanged, self._RefreshPrice, self)
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtRentalApplySuccess, self._OnRentalApplySuccess, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtOnRentalWeaponBulletChanged, self._OnRentalWeaponBulletChanged, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    
    --BEGIN MODIFICATION @ VIRTUOS : 绑定回调
    if IsHD() then
        self:Event("OnFocusReceivedEvent", self._InitShortcuts, self)
        self:Event("OnFocusLostEvent", self._RemoveShortcuts, self)
    end
    --END MODIFICATION

end

function AssemblyHDRentalItem:_InitEquipmentSlotViews()
    ---@type ArmedForceNormalSlotView[]
    local allEquipSlotViews = self:MultiWnd("WBP_WarehouseEquipSlotView")
    self._mapSlotType2EquipViews = {}
    for _, equipSlotView in ipairs(allEquipSlotViews) do
        equipSlotView:SetIsFocusable(false)
        equipSlotView:InitGroup(self._slotGroup)
        local slotType = equipSlotView:GetSlotType()
        local fPostItemRefresh1 = function (itemView)
            -- itemView:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(true)
            itemView:SetCppValue("bIsFocusable", false)
            itemView:SetCustomInteractivity(true)
            itemView:SetHandleDrag(false)
            itemView:SetHandleDoubleClick(false)
            itemView:SetCppValue("bHandleClick", true)
            itemView:SetCursor(EMouseCursor.Hand)

            local function fOnClickCallback()
                CommonWidgetConfig.Events.evtItemClicked:Invoke(itemView, false)
            end
            itemView:BindCustomOnClicked(fOnClickCallback)

            itemView:SetCppValue("bHandleClickR", false)
            if itemView.item and (slotType == ESlotType.MainWeaponLeft or slotType == ESlotType.MainWeaponRight or slotType == ESlotType.Pistrol) then
                self:_RefreshWeaponAmmo(itemView)
            end
        end
        equipSlotView:BindPostRefreshFunc(fPostItemRefresh1)
        self._mapSlotType2EquipViews[slotType] = equipSlotView
    end
end

function AssemblyHDRentalItem:_RefreshWeaponAmmo(itemView)
    local item = itemView.item
    ---@type WeaponFeature
    local weawponFeature = item:GetFeature(EFeatureType.Weapon)
    if weawponFeature and not weawponFeature:IsMeleeWeapon() then
        local curNum, maxNum = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(item:GetRawPropInfo())
        local allMatchWeaponAmmoNum = self:_GetAllMatchWeaponAmmoNum(item)
        local param = {
            ["countNum"] = curNum,
            ["maxNum"] = allMatchWeaponAmmoNum
        }
        local txt = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.NumFormat, param)
        local iconTextPriceComp = itemView:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRightText, EIVCompOrder.Order1)

        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_ItemProp_Icon_0016.CommonHall_ItemProp_Icon_0016'"
        iconTextPriceComp:ShowIconAndText(iconPath, txt)
        -- iconTextPriceComp._wtMainIcon:SelfHitTestInvisible()
        -- iconTextPriceComp._wtMainIcon:AsyncSetImagePath(iconPath, false)
        -- iconTextPriceComp:RefreshComponent()
        -- iconTextPriceComp._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColorByRowName(DefaultColor))
        -- TextStyleBlueprintLib.Blueprint_SetTextStyle(iconTextPriceComp._wtMainText, DefaultColor)
    else
        self._wtBottomRightIconText:Collapsed()
    end
end

local ContainerTypeList = {
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.BagContainer,
}
function AssemblyHDRentalItem:_GetAllMatchWeaponAmmoNum(weaponitem)
    -- 弹匣内+口袋背包胸挂保险箱 这把枪可用子弹总数
    local allMatchWeaponAmmoNum = 0
    if weaponitem then
        local curNum, maxNum = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(weaponitem:GetRawPropInfo())
        allMatchWeaponAmmoNum = allMatchWeaponAmmoNum + curNum
        for _, containerSlotType in ipairs(ContainerTypeList) do
            local slot = Server.InventoryServer:GetSlot(containerSlotType, self._slotGroup)
            local items = slot:GetItems()
            if items then
                for _, item in pairs(items) do
                    if ammoMgr:IsMatchWeapon(weaponitem.id, item.id) then
                        allMatchWeaponAmmoNum = allMatchWeaponAmmoNum + item.num
                    end
                end
            end
        end
    end
    return allMatchWeaponAmmoNum
end

function AssemblyHDRentalItem:_InitContainerSlotViews()
    local fPostItemRefresh1 = function (itemView)
        -- itemView:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(true)
        itemView:SetCppValue("bIsFocusable", false)
        itemView:SetCustomInteractivity(true)
        itemView:SetHandleDrag(false)
        itemView:SetHandleDoubleClick(false)
        itemView:SetCppValue("bHandleClick", true)

        local function fOnClickCallback()
            CommonWidgetConfig.Events.evtItemClicked:Invoke(itemView, false)
        end
        itemView:BindCustomOnClicked(fOnClickCallback)


        itemView:SetCppValue("bHandleClickR", false)
        itemView:SetCursor(EMouseCursor.Hand)
        itemView.OnImmediateClicked = function() return true end
    end
    local fPostEquipItemRefresh = function (itemView)
        -- itemView:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(true)
        itemView:SetCppValue("bIsFocusable", false)
        if itemView.item then
            itemView:SetCustomInteractivity(true)
            itemView:SetHandleDrag(false)
            itemView:SetHandleDoubleClick(false)
            itemView:SetCppValue("bHandleClick", true)

            local function fOnClickCallback()
                CommonWidgetConfig.Events.evtItemClicked:Invoke(itemView, false)
            end
            itemView:BindCustomOnClicked(fOnClickCallback)

            itemView:SetCppValue("bHandleClickR", false)
            itemView.OnImmediateClicked = function() return true end
            -- local maskComponent = itemView:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            -- if maskComponent then
            --     itemView:EnableComponent(EComp.GreyMask, true)
            --     itemView:SetCursor(EMouseCursor.Default)
            -- end
        end
    end
    logerror("【AssemblyHDRentalItem】管理warehouse对象池 InitContainerSlotViews")
    local fObtainCustomWarehouseIVFromPool = function ()
        return Module.ArmedForce.Field:ObtainWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.Rental)
    end

    local fFreeWarehouseIVToPool = function (warehouseIV)
        Module.ArmedForce.Field:FreeWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.Rental, warehouseIV)
    end

    self._wtMainScrollBox = self:Wnd("wtMainScrollBox", UIScrollBox)

    -- 胸挂
    self._wtCHContainerView = self:Wnd("wtCHContainerView", WarehouseContainerPanel_HD)
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, self._slotGroup)
    self._wtCHContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtChestHangingCapacity = self._wtCHContainerView:GetCapacityText()
    self._wtChestHangingSlotView = self._wtCHContainerView:GetContainerSlotView()
    self._wtChestHangingSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtChestHangingSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 口袋
    self._wtPocketContainerView = self:Wnd("wtPocketContainerView", WarehouseContainerPanel_HD)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, self._slotGroup)
    self._wtPocketCapacity = self._wtPocketContainerView:GetCapacityText()
    self._wtPocketSlotView = self._wtPocketContainerView:GetContainerSlotView()
    self._wtPocketSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtPocketSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 背包
    self._wtBagContainerView = self:Wnd("wtBagContainerView", WarehouseContainerPanel_HD)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, self._slotGroup)
    self._wtBagContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtBagCapacity = self._wtBagContainerView:GetCapacityText()
    self._wtBagSlotView = self._wtBagContainerView:GetContainerSlotView()
    self._wtBagSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtBagSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)
end

function AssemblyHDRentalItem:_ResetContainerSlotViewsContainerSlot()
    Server.InventoryServer:ClearSlotGroup(self._slotGroup)
    for _, equipSlotView in pairs(self._mapSlotType2EquipViews) do
        equipSlotView:RefreshView()
    end
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, self._slotGroup)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, self._slotGroup)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, self._slotGroup)
end

-- 根据slotType获取slotView
function AssemblyHDRentalItem:_GetSlotView(slotType)
    local slotView
    if slotType == ESlotType.BagContainer then
        slotView = self._wtBagSlotView
    elseif slotType == ESlotType.ChestHangingContainer then
        slotView = self._wtChestHangingSlotView
    elseif slotType == ESlotType.Pocket then
        slotView = self._wtPocketSlotView
    end
    -- todo 多个容器
    return slotView
end

-- 根据slotType获取ContainerView
function AssemblyHDRentalItem:_GetContainerView(slotType)
    local containerView
    if slotType == ESlotType.BagContainer then
        containerView = self._wtBagContainerView
    elseif slotType == ESlotType.ChestHangingContainer then
        containerView = self._wtCHContainerView
    elseif slotType == ESlotType.Pocket then
        containerView = self._wtPocketContainerView
    end
    return containerView
end

function AssemblyHDRentalItem:SetRentalData(rentalData)
    if not rentalData then
        logerror("AssemblyHDRentalItem:SetRentalData rentalData is nil !!!!")
        return
    end
    if not self._rentalData or (self._rentalData and (self._rentalData.consumable_id ~= rentalData.consumable_id or self._rentalData.type_id ~= rentalData.type_id or self._rentalData.preset_id ~= rentalData.preset_id)) then
        self._rentalData = rentalData
        self:_RefreshView()
    end
end

function AssemblyHDRentalItem:GetSlotGroup()
    return self._slotGroup
end

function AssemblyHDRentalItem:SetTitleEnable(bEnable)
    if bEnable then
        self._wtTitle:SelfHitTestInvisible()
    else
        self._wtTitle:Collapsed()
    end

end


function AssemblyHDRentalItem:_RefreshView()
    self._bEquipReady = false
    self._bContainerReadyMap = {}
    self:_ResetContainerSlotViewsContainerSlot()
    self:_GenSlotGroupData(CreateCallBack(self._RefreshEquipSlotView, self), CreateCallBack(self._RefreshContainerView, self))
    self:_RefreshWidget()
end

function AssemblyHDRentalItem:_RefreshWidget()
    self:_RefreshTitle()
    self:_RefreshApplyBtn()
    self:_RefreshPrice()
    self:_RefreshRefreshBtn()
    -- self:_RefreshSlotGroupWidget()
end

function AssemblyHDRentalItem:_RefreshTitle()
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_RefreshTitle self._rentalData is nil !!!")
        return
    end
    self._wtTitle:SetText(self._rentalData.preset_title)
end

function AssemblyHDRentalItem:_RefreshPrice()
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_RefreshPrice self._rentalData is nil !!!")
        return
    end

    local presetPrice = self._rentalData.preset_price
    local priceStr = CurrencyHelperTool.GetCurrencyNumFormatStr(presetPrice, CurrencyHelperTool.EKMThousandsType.None)
    local priceIconText = ECurrencyClientType2RichIconTxtV2[ECurrencyClientType.OnlyUnBind]
    self._wtPrice:SetText(StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalPresetPriceTxt,{["priceIconText"] = priceIconText, ["priceStr"] = priceStr}))
end

function AssemblyHDRentalItem:_RefreshRefreshBtn()
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_RefreshRefreshBtn self._rentalData is nil !!!")
        return
    end
    if self._rentalData.refresh_enable then
        self._wtRefreshBtn:Visible()
    else
        self._wtRefreshBtn:Collapsed()
    end
end

-- function AssemblyHDRentalItem:_RefreshSlotGroupWidget()
--     for _, equipSlotView in pairs(self._mapSlotType2EquipViews) do
--         equipSlotView:RefreshView()
--     end

--     self:_ResetContainerSlotViewsContainerSlot()

--     for _, slotType in pairs(containerTypeList) do
--         local containerView = self:_GetContainerView(slotType)
--         containerView:RefreshView()
--         containerView:RefreshCapacity()
--     end
-- end

function AssemblyHDRentalItem:_RefreshEquipSlotView()
    for _, equipSlotView in pairs(self._mapSlotType2EquipViews) do
        equipSlotView:RefreshView()
    end
    self._bEquipReady = true
end

function AssemblyHDRentalItem:_RefreshContainerView(slotType)
    -- for _, slotType in pairs(containerTypeList) do
        local containerView = self:_GetContainerView(slotType)
        containerView:InitContainerSlot(slotType, self._slotGroup)
        containerView:RefreshView()
        containerView:RefreshCapacity()
    -- end
    self._bContainerReadyMap[slotType] = true
end

function AssemblyHDRentalItem:_RefreshApplyBtn()
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_RefreshApplyBtn self._rentalData is nil !!!")
        return
    end
    
    local consumableID = self._rentalData.consumable_id
    local rentalVoucherNum = Server.CollectionServer:GetCollectionItemsNumById(consumableID)

    local bUsed = Server.ArmedForceServer:CheckRentalDataUsed(self._rentalData)
    self._bCanApply = Module.ArmedForce:CheckConsumableIDCanBeApply(consumableID)
    self._bEnoughNum = rentalVoucherNum > 0
    if bUsed then
        self._wtRentalCancelTips:Visible()
        self._wtApplyBtn:Collapsed()

    else
        self._wtRentalCancelTips:Collapsed()
        self._wtApplyBtn:Visible()
        self._wtApplyBtn:SetIsEnabledStyle(self._bEnoughNum and self._bCanApply)
        local btnTxt = ""
        local quality = ItemConfigTool.GetItemQuality(consumableID)
        local rentalID2RichIconTxt = ""
        if ItemConfig.ERentalQuality2RichIconTxt[quality] then
            rentalID2RichIconTxt = string.format(ItemConfig.ERentalQuality2RichIconTxt[quality], 70,70)
        end
        if not self._bCanApply then
            btnTxt = string.format(Module.ArmedForce.Config.Loc.RentalVoucherNotApplyBtnStr, rentalID2RichIconTxt, 1)
        elseif not self._bEnoughNum then
            btnTxt = string.format(Module.ArmedForce.Config.Loc.RentalVoucherNotEnoughBtnStr, rentalID2RichIconTxt, 1)
        else
            btnTxt = string.format("%s %s", rentalID2RichIconTxt,1)
        end

        self._wtApplyBtn:SetMainTitle(btnTxt)
    end
    if self.BP_SetSelect then
        self:BP_SetSelect(bUsed)
    end
    self:_CheckApplyBtnKeyIconShow()
end

function AssemblyHDRentalItem:_OnRefreshBtnClicked()
    if Module.ArmedForce.Field:CheckApplyRentalProcessing() then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ClickTooSoonPleaseWait)
        return
    end
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_OnRefreshBtnClicked self._rentalData is nil !!!")
        return
    end
    local bUsed = Server.ArmedForceServer:CheckRentalDataUsed(self._rentalData)
    if bUsed then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.RentalDataIsUsingCanNotBeRefreshed)
    else
        local needCurrencyValue = self._rentalData and self._rentalData.refresh_price or 0
        local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(ECurrencyClientType.OnlyUnBind) 
        local bCanRefreshPrice = needCurrencyValue <= currencyNum

        local fConfirmbackIns = function ()
            if bCanRefreshPrice then
                RentalDataLogic.ReqRefreshRental(self._rentalData)
            else
                Module.CommonTips:ShowSimpleTip(ServerTipCode.MaintainanceFail)
            end
        end

        local function fFinishCallback(uiIns)
            uiIns:SetConfirmIsEnabledStyle(bCanRefreshPrice)
        end

        Module.CommonTips:ShowConfirmWindow(
            string.format(Module.ArmedForce.Config.Loc.RentalRefreshConfirmTips, CurrencyHelperTool.GetCurrencyNumFormatStr(needCurrencyValue, CurrencyHelperTool.EKMThousandsType.None)),
            fConfirmbackIns,
            nil,
            nil,
            nil,
            nil,
            nil,
            nil,
            nil,
            fFinishCallback)
    end
end

function AssemblyHDRentalItem:_OnApplyBtnClicked()
    if Module.ArmedForce.Field:CheckApplyRentalProcessing() then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ClickTooSoonPleaseWait)
        return
    end
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_OnApplyBtnClicked self._rentalData is nil !!!")
        return
    end
    
    if table.nums(self._bContainerReadyMap) == 3 and self._bEquipReady then
        local consumableID = self._rentalData.consumable_id
        local itemName = ItemConfigTool.GetItemName(consumableID)
        if not self._bCanApply then
            local matchMode = Server.GameModeServer:GetMatchMode()
            local matchModeId = matchMode.match_mode_id
            local mapName = Module.GameMode:GetStandardMapNameByMatchModeId(matchModeId, false)
            Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.RentalVoucherNotApply, itemName, mapName))
        elseif not self._bEnoughNum then
            Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.RentalVoucherNotEnough, itemName))
        else
            local curRentalPlan_ConsumableID = Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
            local name1 = ItemConfigTool.GetItemName(curRentalPlan_ConsumableID)
            local name2 = ItemConfigTool.GetItemName(self._rentalData.consumable_id)
            local text = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVocherExpirationSwitchConfirmation,{["Name1"] = name1, ["Name2"] = name2, ["Name3"] = name1})
            RentalDataLogic.CheckUsedRentalPlanExpirationConfirmation(CreateCallBack(self.ApplyRental, self), text)
        end
    else
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.RentalGeneratingPlaning)
    end
end

function AssemblyHDRentalItem:ApplyRental()
    self:ReleaseTimer()
    Module.ArmedForce.Field:SetApplyRentalProcessing(true)
    self._processingTimerHandle = Timer.DelayCall(15, function ()
        if Module.ArmedForce.Field:CheckApplyRentalProcessing() then
            -- Module.CommonTips:ShowSimpleTip("It took 20 seconds to complete the recommended equipment")
            logerror("AssemblyRentalItem:ApplyRental 耗时15秒还未处理完租借")
            Module.ArmedForce.Field:SetApplyRentalProcessing(false)
        end
    end, self)
    local function fCallback(res)
        if res.result ~= 0 then
            Module.Inventory:UpdateItemUnitSize(130)
            self:ReleaseTimer()
            Module.ArmedForce.Field:SetApplyRentalProcessing(false)
        end
    end
    Module.Inventory:InitBaseItemSize()
    RentalDataLogic.ApplyRental(self._rentalData, fCallback)
end

function AssemblyHDRentalItem:ReleaseTimer()
    if self._processingTimerHandle then
        Timer.CancelDelay(self._processingTimerHandle)
        self._processingTimerHandle = nil
    end
end

function AssemblyHDRentalItem:_OnCancelBtnClicked()
    if Module.ArmedForce.Field:CheckApplyRentalProcessing() then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ClickTooSoonPleaseWait)
        return
    end
    if not self._rentalData then
        logerror("AssemblyHDRentalItem:_OnCancelBtnClicked self._rentalData is nil !!!")
        return
    end
    local bUsed = Server.ArmedForceServer:CheckRentalDataUsed(self._rentalData)
    if bUsed then
        RentalDataLogic.CancelRental()
    end
end

function AssemblyHDRentalItem:_OnRentalDataChanged(rentalData)
    if self._rentalData and rentalData.consumable_id == self._rentalData.consumable_id and rentalData.type_id == self._rentalData.type_id then
        self:SetRentalData(rentalData)
        self:PlayAnimation(self.Anim_refresh, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        self._wtMainScrollBox:ScrollToStart()
    end
end

function AssemblyHDRentalItem:_OnRentalStatusChanged()
    self:_RefreshApplyBtn()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() and self._bIsFocusReceived then
        self:_InitShortcuts()
    end
    --- END MODIFICATION
end

function AssemblyHDRentalItem:_GenSlotGroupData(fRefreshEquipSlotView, fRefreshContainerView)
    if self._rentalData then
        RentalDataLogic.GenSlotGroupData(self._slotGroup, self._rentalData, fRefreshEquipSlotView, fRefreshContainerView)
    end
end

function AssemblyHDRentalItem:_OnRentalApplySuccess(rentalData)
    if self._rentalData and rentalData and self._rentalData.type_id == rentalData.consumable_id then
        self:_RefreshApplyBtn()
    end
end

function AssemblyHDRentalItem:_OnRentalWeaponBulletChanged(weaponItem)
    if not weaponItem then
        return
    end
    for _, equipSlotView in pairs(self._mapSlotType2EquipViews) do
        local slotType = equipSlotView:GetSlotType()
        if slotType == ESlotType.MainWeaponLeft or slotType == ESlotType.MainWeaponRight or slotType == ESlotType.Pistrol then
            local equipItem = equipSlotView:CDDL_GetSlot():GetEquipItem()
            if equipItem and equipItem == weaponItem then
                equipSlotView:RefreshView()
            end
        end
    end
end

function AssemblyHDRentalItem:_OnUpdateCollectionData()
    self:_RefreshApplyBtn()
end

-- function AssemblyHDRentalItem:OnNativeOnMouseMove(inGeometry, inGestureEvent)
--     -- local absolutePoint = inGestureEvent:GetScreenSpacePosition()

-- 	-- local bInside = UIUtil.CheckAbsolutePointInsideWidget(self, absolutePoint)
--     if IsHD() and WidgetUtil.IsGamepad() and self._bIsFocusReceived then
--         self:_CheckApplyBtnKeyIconShow()
--     end
-- end

function AssemblyHDRentalItem:_OnUsingFreeAnalogCursorStateChanged(bIsEnable)
    if not (IsHD() and WidgetUtil.IsGamepad())then
        return
    end
    if self._bIsFocusReceived then
        self:_CheckApplyBtnKeyIconShow()
    end
end


--- BEGIN MODIFICATION @ VIRTUOS
function AssemblyHDRentalItem:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end
    self._wtApplyBtn:RemoveButtonStateChangedEvent()
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end

    -- WidgetUtil.SetFreeAnalogCursorAutoFocusDisabled(self, true)
end
function AssemblyHDRentalItem:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    self:_RemoveShortcuts()

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    -- WidgetUtil.SetFreeAnalogCursorAutoFocusDisabled(self, false)
end

-- 关闭页面中 “问号” 提示的可聚焦
function AssemblyHDRentalItem:_DisabelHoverTipsFocus()
    if not IsHD() then
        return 
    end

    local containerSlotType = {
        ESlotType.ChestHangingContainer, -- 胸挂
        ESlotType.Pocket, -- 口袋
        ESlotType.BagContainer,
    }
    for _, slotType in ipairs(containerSlotType) do
        local containerView = self:_GetContainerView(slotType)
        if containerView then
            local wtTipsCheckBox = containerView:Wnd("wtHoverBtn", UIWidgetBase)
            local checkBtn = wtTipsCheckBox:Wnd("DFCheckBox_Icon", UICheckBox)
            if checkBtn then
                checkBtn:SetCppValue("IsFocusable", false)
            end
        end
    end
end

function AssemblyHDRentalItem:_InitShortcuts()
    self:_RemoveShortcuts()
    self._bIsFocusReceived = true
    local bIsUsingFreeAnalogCursor = WidgetUtil.IsUsingFreeAnalogCursor()
    self:_CheckApplyBtnKeyIconShow()
    -- 设置底部快捷栏
    self:_UpdateInputSummaryList()

    if not self._wtRentalCancelTips:IsVisible() and not bIsUsingFreeAnalogCursor then
        -- 兑换套装
        self._RedeemSet = self:AddInputActionBinding("Assembly_RedeemSet", EInputEvent.IE_Pressed, self._OnApplyBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    end

    if IsHD() then
        local navMgr =  WidgetUtil.GetNavigationMgr()
        if self._usingFreeAnalogCursorStateChangedEventHandle then
            navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
            self._usingFreeAnalogCursorStateChangedEventHandle = nil
        end
        self._usingFreeAnalogCursorStateChangedEventHandle = navMgr.OnUsingFreeAnalogCursorStateChanged:Add(CreateCPlusCallBack(self._OnUsingFreeAnalogCursorStateChanged,self))
    end
end

function AssemblyHDRentalItem:_RemoveShortcuts()
    if not IsHD() then
        return 
    end
    self:_CloseDetailsTips()
    self._bIsFocusReceived = false
    self:_CheckApplyBtnKeyIconShow()

    if self._RedeemSet then
        self:RemoveInputActionBinding(self._RedeemSet)
        self._RedeemSet = nil
    end

    if self._CancelRental then
        self:RemoveInputActionBinding(self._CancelRental)
        self._CancelRental = nil
    end

    if self._BrowseSet_R_Y then
        self:RemoveInputActionBinding(self._BrowseSet_R_Y)
        self._BrowseSet_R_Y = nil
    end

    local navMgr =  WidgetUtil.GetNavigationMgr()
    if self._usingFreeAnalogCursorStateChangedEventHandle then
        navMgr.OnUsingFreeAnalogCursorStateChanged:Remove(self._usingFreeAnalogCursorStateChangedEventHandle)
        self._usingFreeAnalogCursorStateChangedEventHandle = nil
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function AssemblyHDRentalItem:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end
    self:_CloseDetailsTips()
end

function AssemblyHDRentalItem:_CheckApplyBtnKeyIconShow()
    if not IsHD() then
        return 
    end
    local bIsUsingFreeAnalogCursor = WidgetUtil.IsUsingFreeAnalogCursor()
    -- 检查使用按钮快捷键图标
    self._wtApplyBtn:SetKeyIconVisibility(false)
    if WidgetUtil.IsGamepad() then 
        if self._bIsFocusReceived then
            if self._wtApplyBtn and not bIsUsingFreeAnalogCursor then
                self._wtApplyBtn:SetKeyIconVisibility(true)
            end
        end
    end
end

function AssemblyHDRentalItem:_UpdateInputSummaryList()
    -- 刷新套装
    local summaryList = {}
 
    if self._rentalData then
        -- 详情
        table.insert(summaryList, {actionName = "Assembly_Details_Gamepad",func = self._SwitchDetailsTips, caller = self ,bUIOnly = false, bHideIcon = false})
        -- 浏览套装
        table.insert(summaryList, {actionName = "Assembly_BrowseSet",func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
        self._BrowseSet_R_Y = self:AddAxisInputActionBinding("Assembly_BrowseSet", self._ScrollDetailView, self, EDisplayInputActionPriority.UI_Stack)
        local bUsed = Server.ArmedForceServer:CheckRentalDataUsed(self._rentalData)
        if bUsed then
            -- 取消使用
            table.insert(summaryList, {actionName = "Assembly_CancelRental_Gamepad",func = self._OnCancelBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            if self._rentalData.refresh_enable then
                -- 刷新方案
                table.insert(summaryList, {actionName = "Assembly_RefreshSet",func = self._OnRefreshBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
            end
        end
    end

    if self._wtParent and self._wtParent.SetScrollRecipient and self._wtMainScrollBox then
        self._wtParent:SetScrollRecipient(self._wtMainScrollBox)
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList)
end

function AssemblyHDRentalItem:_SwitchDetailsTips()
    self._tipsIndex = (self._tipsIndex + 1) % 3
    Module.ArmedForce.Config.evtRentalShowTipsChanged:Invoke(self._tipsIndex)
end

function AssemblyHDRentalItem:_CloseDetailsTips()
    self._tipsIndex = 0
    Module.ArmedForce.Config.evtRentalShowTipsChanged:Invoke(self._tipsIndex)
end

function AssemblyHDRentalItem:_ScrollDetailView(axis)
    if not WidgetUtil.IsGamepad() or (WidgetUtil.IsEquipZero(axis) and WidgetUtil.IsEquipZero(axis)) then
        return 
    end
    WidgetUtil.ScrollByAxis(self._wtMainScrollBox, axis, Speed)
end
--- END MODIFICATION

return AssemblyHDRentalItem