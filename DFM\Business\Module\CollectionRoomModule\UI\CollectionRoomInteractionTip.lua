----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
---@class CollectionRoomInteractionTip : LuaUIBaseView
local CollectionRoomInteractionTip = ui("CollectionRoomInteractionTip")

local function log(...)
    loginfo("[CollectionRoomInteractionTip]", ...)
end

function CollectionRoomInteractionTip:Ctor()
    self._wtBuildingName = self:Wnd("wtBuildingName", UITextBlock)
    self._wtBuildingDeviceLevel = self:Wnd("wtBuildingDeviceLevel", UITextBlock)
    self._wtEnterBtn = self:Wnd("DFButton_339", DFButtonOnly)
    self._wtEnterBtn:Event("OnClicked", self._OnEnterBtnClicked, self)

    self._cabinetType = nil
    self._cabinetId = nil
    self._bUnLocked = false
end

function CollectionRoomInteractionTip:SetCabinetTypeAndId(cabinetType, cabinetId)
    self._cabinetType = cabinetType
    self._cabinetId = cabinetId
    if cabinetType == EShowCabinetType.Display then
        self._wtBuildingName:SetText(CollectionRoomConfig.Loc.DisplayCabinetName)
        self:SetIcon(0)
    elseif cabinetType == EShowCabinetType.Special then
        self._wtBuildingName:SetText(CollectionRoomConfig.Loc.SpecialCabinetName)
        self:SetIcon(1)
    elseif cabinetType == EShowCabinetType.DIY then
        self._wtBuildingName:SetText(CollectionRoomConfig.Loc.DIYCabinetName)
        self:SetIcon(2)
    end
    self._bUnLocked = Server.CollectionRoomServer:IsCabinetUnlocked(cabinetType, cabinetId)
    if self._bUnLocked then
        local itemNum = CollectionRoomLogic.GetItemNumByCabinet(cabinetType, cabinetId)
        local itemCapacity = CollectionRoomLogic.GetItemCapacityByCabinet(cabinetType, cabinetId)
        self._wtBuildingDeviceLevel:SetText(string.format("%d/%d", itemNum, itemCapacity))
    else
        self._wtBuildingDeviceLevel:SetText(CollectionRoomConfig.Loc.CabinetLocked)
    end
    self:SetType(self._bUnLocked and 1 or 0)
end

function CollectionRoomInteractionTip:_OnEnterBtnClicked()
    if self._bUnLocked then
        if self._cabinetType == EShowCabinetType.Display then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDisplaySpecialCabinetPanel, nil, nil, true)
            LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, self._cabinetType)
        elseif self._cabinetType == EShowCabinetType.Special then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDisplaySpecialCabinetPanel, nil, nil, false)
            LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, self._cabinetType)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomDIYCabinetPanel, nil, nil, self._cabinetId)
            LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.CabinetEntrance, self._cabinetType .. "," .. self._cabinetId)
        end
    else
        Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.CabinetLockedTips)
    end
end

return CollectionRoomInteractionTip