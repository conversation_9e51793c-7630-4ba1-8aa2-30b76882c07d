local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
---@class QuestSeasonMainPanel : LuaUIBaseView

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local QuestSeasonMainPanel = ui("QuestSeasonMainPanel")

function QuestSeasonMainPanel:Ctor()

    self._wtSeasonNameText = self:Wnd("DFTextBlock_156", UITextBlock)
    self._wtSeasonRemainTime = self:Wnd("DFTextBlock_52", UITextBlock)

    self._wtEntranceMain = self:Wnd("SeasonalTasks_Entrance", UIWidgetBase)
    self._wtEntranceCollection = self:Wnd("SeasonalTasks_Entrance_01", UIWidgetBase)
    self._wtEntranceFactContract = self:Wnd("SeasonalTasks_Entrance_02", UIWidgetBase)

    -- 赛季目标
    self._wtCheckBtn = self:Wnd("wtTipsBtn", DFCheckBoxOnly)
    if self._wtCheckBtn then 
        -- self._wtCheckBtn:Event("OnClicked", self._ShowTutorial, self)
        self._wtCheckBtn:SetCallback(self._ShowTutorial, self)
    end

    self._wtGoalItem = self:Wnd("WBP_CommonItemTemplate_1", IVCommonItemTemplate)
    self._wtGoalSelected = self:Wnd("DFCanvas_Selected", UIWidgetBase)
    self._wtGoalItemOvertime = self:Wnd("WBP_CommonItemTemplate_2", IVCommonItemTemplate)
    self._wtGoalSelectedOvertime = self:Wnd("DFCanvas_Selected_1", UIWidgetBase)

    self._wtConditionList = self:Wnd("DFWrapBox_0", UIWidgetBase)

    self._wtCommonBg = self:Wnd("DFImage_265", UIWidgetBase)
    self._wtOvertimeBg = self:Wnd("WBP_SeasonalTasks_BG_01", UIWidgetBase)

    self._lineInfo = nil
    self._canTakeReward = false

    if IsHD() then
        self._wtRightPanel = self:Wnd("DFCanvasPanel_66", UIWidgetBase)
        self._wtCanvasPanel = self:Wnd("DFCanvasPanel_87", UIWidgetBase)
    end

end

function QuestSeasonMainPanel:OnShowBegin()

    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.Main, Server.TipsRecordServer.keys.QuestSeasonalTutorial)

    Server.QuestServer:GetQuestLineSeasonDataReq()
    self._lineInfo = Server.QuestServer:GetCurrentSeasonLine()

    if self._lineInfo == nil then 
		logerror("Invalid Current Season Questline")
		return
	end

    self._wtSeasonNameText:SetText(self._lineInfo.name)

    -- 赛季剩余时间
    local remianTime = self._lineInfo:GetSeasonTimeDelta()
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remianTime)
    self._wtSeasonRemainTime:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonRemainTime, day))

    -- 功能入口
    self._wtEntranceMain:SetBgImg(self._lineInfo._seasonQuestGuideImg1.AssetPathName)
    self._wtEntranceCollection:SetBgImg(self._lineInfo._seasonQuestGuideImg2.AssetPathName)
    self._wtEntranceFactContract:SetBgImg(self._lineInfo._seasonQuestGuideImg3.AssetPathName)

    local seasonLevel = Server.RoleInfoServer.seasonLevel
    local openLevel = self._lineInfo.openLevel
    if seasonLevel >= openLevel then
        self:_SetIsLocked(false,openLevel)
        self._wtEntranceMain:InitInfo(self._lineInfo)
        self._wtEntranceCollection:InitInfo(true)
        self._wtEntranceFactContract:InitInfo(false)
    else
        self:_SetIsLocked(true,openLevel)
    end

    -- 赛季目标
    local finalQuest = Server.QuestServer:GetQuestInfoById(self._lineInfo._finalQuestID)
    self._wtGoalItem:InitItem(finalQuest:GetRewardList()[1])
    if Server.QuestServer:IsQuestRewarded(self._lineInfo._finalQuestID) then
        self._wtGoalItem:SetAlreadyGetState()
    elseif Server.QuestServer:IsQuestCompleted({self._lineInfo._finalQuestID}) then
        self._wtGoalItem:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0, EUMGSequencePlayMode.Forward, 1, true)
    end

    self._wtGoalItemOvertime:InitItem(ItemBase:New(self._lineInfo._overTimeRewardID, self._lineInfo._overTimeRewardCount))
    if Server.QuestServer._bIsTakeOvertimeReward then
        self._wtGoalItemOvertime:SetAlreadyGetState()   
    elseif QuestLogic:IsAllConditionCompeleted(self._lineInfo._overConditionList) then
        self._wtGoalItemOvertime:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0, EUMGSequencePlayMode.Forward, 1, true)
        self._canTakeReward = true
    end

    self._wtGoalItem:BindCustomOnClicked(self._OnGoalClicked, self)
    self._wtGoalItemOvertime:BindCustomOnClicked(self._OnOvertimeClicked, self)

    self._bIsShowOvertime = Module.Quest.Field:IsInSeasonOvertime()
    self:_UpdateShowOvertime(self._bIsShowOvertime)
    if self._bIsShowOvertime then
        self._wtCommonBg:Collapsed()
        self._wtOvertimeBg:Visible()
    else
        self._wtCommonBg:Visible()
        self._wtOvertimeBg:Collapsed()
    end

    self:_AddListeners()

    if IsHD() then
        self:_EnableGamepad()
    end

end

function QuestSeasonMainPanel:_AddListeners()
    self:AddLuaEvent(Server.QuestServer.Events.evtSeasonOvertimeRewardTaken, self._UpdateOvertimeReward, self)
end

function QuestSeasonMainPanel:OnHide()
    self:RemoveAllLuaEvent()
    self._wtGoalItem:BindCustomOnClicked(nil, nil)
    self._wtGoalItemOvertime:BindCustomOnClicked(nil, nil)
    if IsHD() then
        self:_DisableGamepad()
    end
end

function QuestSeasonMainPanel:_UpdateConditionItems(conditionList)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtConditionList)
    for index, value in ipairs(conditionList) do
        local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,self._wtConditionList,nil, value.desc)
        local condWidget = getfromweak(uiIns)
        if condWidget then
            condWidget:SetProgressByType(index, value.param)
            if index == #conditionList then
                condWidget:HideDecoLine()
            end            
        end
    end
end

function QuestSeasonMainPanel:_SetIsLocked(bisLocked, openLevel)
    self._wtEntranceMain:SetIsLocked(bisLocked, openLevel)
    self._wtEntranceCollection:SetIsLocked(bisLocked, openLevel)
    self._wtEntranceFactContract:SetIsLocked(bisLocked, openLevel)
end

function QuestSeasonMainPanel:_ShowTutorial(bIsChecked)
    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.Main, nil)
end

function QuestSeasonMainPanel:_UpdateShowOvertime(bIsShowOvertime)
    if bIsShowOvertime then
        -- 加时赛阶段
        self._wtGoalSelected:Collapsed()
        self._wtGoalSelectedOvertime:SelfHitTestInvisible()
        self:_UpdateConditionItems(self._lineInfo._overConditionList)
    else
        self._wtGoalSelected:SelfHitTestInvisible()
        self._wtGoalSelectedOvertime:Collapsed()
        self:_UpdateConditionItems(self._lineInfo._unLockConditionList)
    end
end

function QuestSeasonMainPanel:_OnGoalClicked()
    if self._bIsShowOvertime then
        self._bIsShowOvertime = false
        self:_UpdateShowOvertime(self._bIsShowOvertime)
    else
        self._wtGoalItem:_DefaultOnClicked(false)
    end
end

function QuestSeasonMainPanel:_OnOvertimeClicked()

    if QuestLogic:IsAllConditionCompeleted(self._lineInfo._overConditionList) and 
    not Server.QuestServer._bIsTakeOvertimeReward then
        Server.QuestServer:GetOvertimeReward()
        if self._PressTakeReward then
            self:RemoveInputActionBinding(self._PressTakeReward)
            self._PressTakeReward= nil
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "Back", func = nil, caller = self , bUIOnly = true, bHideIcon = false},
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
        return
    end

    if self._bIsShowOvertime then
        self._wtGoalItemOvertime:_DefaultOnClicked(false)
    else
        self._bIsShowOvertime = true
        self:_UpdateShowOvertime(self._bIsShowOvertime)
    end
end

function QuestSeasonMainPanel:_UpdateOvertimeReward()
    self._wtGoalItemOvertime:SetAlreadyGetState()   
    self._wtGoalItemOvertime:StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
end

-------- Gamepad ------------

function QuestSeasonMainPanel:_EnableGamepad()
    self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtCanvasPanel, self, "Hittest")
    if self._wtNavGroup1 then
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceMain)
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceCollection)
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceFactContract)
        self._wtNavGroup1:SetAnalogCursorStickySlowdownFactor(1.0) 
    end

    self._wtNavGroup2 = WidgetUtil.RegisterNavigationGroup(self._wtRightPanel, self, "Hittest")
    if self._wtNavGroup2 then
        self._wtNavGroup2:AddNavWidgetToArray(self._wtGoalItem)
        self._wtNavGroup2:AddNavWidgetToArray(self._wtGoalItemOvertime)
    end
    self:_InitShortcuts()
    
    WidgetUtil.SetUserFocusToWidget(self._wtEntranceMain,true)
end

function QuestSeasonMainPanel:_DisableGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtNavGroup1 = nil
    self._wtNavGroup2 = nil
    self:_RemoveShortcuts()
end

function QuestSeasonMainPanel:_InitShortcuts()
    if self._PressTipsButton == nil then 
        self._PressTipsButton = self:AddInputActionBinding("QuestSeason_Tips", 
        EInputEvent.IE_Pressed, self._ShowTutorial, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._canTakeReward then 
        if self._PressTakeReward == nil then 
            self._PressTakeReward = self:AddInputActionBinding("QuestSeason_GetOvertimeReward", 
            EInputEvent.IE_Pressed, self._ShowTutorial, self, EDisplayInputActionPriority.UI_Stack)
        end
        
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "Back", func = nil, caller = self , bUIOnly = true, bHideIcon = false},
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
            {actionName = "QuestSeason_GetOvertimeReward",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
    else        
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "Back", func = nil, caller = self , bUIOnly = true, bHideIcon = false},
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
    end

end

function QuestSeasonMainPanel:_RemoveShortcuts()
    if self._PressTipsButton then
        self:RemoveInputActionBinding(self._PressTipsButton)
        self._PressTipsButton= nil
    end
	if self._PressTakeReward then
        self:RemoveInputActionBinding(self._PressTakeReward)
        self._PressTakeReward= nil
    end
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

return QuestSeasonMainPanel