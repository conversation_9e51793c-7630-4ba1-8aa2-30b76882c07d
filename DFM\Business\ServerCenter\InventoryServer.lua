----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



---dataStruct
require "DFM.Business.DataStruct.ItemStruct.ItemBase"
local ItemSortTool = require "DFM.StandaloneLua.BusinessTool.ItemSortTool"
local SlotConfig = require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local WeaponPerkFeature = require "DFM.Business.DataStruct.ItemStruct.WeaponPerkFeature"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local UItemIDUtil = import "ItemIDUtil"
local UInventoryUtil = import "InventoryUtil"
local FItemSpaceRepData = import "ItemSpaceRepData"
require "DFM.Business.DataStruct.InventoryStruct.ItemLocationDefine"
require "DFM.Business.DataStruct.InventoryStruct.ItemSlot"
require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"
require "DFM.Business.DataStruct.InventoryStruct.ItemMoveCmd"

local UClientBaseSetting = import "ClientBaseSetting"

---@class InventoryServer : ServerBase
local InventoryServer = class("InventoryServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
--------------------------------------------------------------------------
--- 依赖ShopServer/TeamServer
--------------------------------------------------------------------------
local function log(...)
    loginfo("InventoryServer", ...)
end

local PVE_WEAPON_LEFT_ID = ESlotType.PVEMainWeaponLeft
local PVE_WEAPON_RIGHT_ID = ESlotType.PVEMainWeaponRight
local emptyRet = {}

function InventoryServer:Ctor()
    self.bScav = false
    self._lastUseItemId = 0

    self._bAutoSort = UClientBaseSetting.Get(GetWorld()).bIsInventoryAutoOrganize

    self.Events = {
        evtUpdateItemCapacity = LuaEvent:NewIns("InventoryServer.evtUpdateItemCapacity"),
        evtSlotSpaceChange = LuaEvent:NewIns("InventoryServer.evtSlotSpaceChange"),
        evtItemMove = LuaEvent:NewIns("InventoryServer.evtItemMove"),
        evtItemMoveBatch = LuaEvent:NewIns("InventoryServer.evtItemMoveBatch"),
        evtItemMoveFail = LuaEvent:NewIns("InventoryServer.evtItemMoveFail"),
        evtSlotTagChange = LuaEvent:NewIns("InventoryServer.evtSlotTagChange"),

        evtExtensionBoxRes = LuaEvent:NewIns("InventoryServer.evtExtensionBoxRes"),

        evtCurrencyUpdated = LuaEvent:NewIns("InventoryServer.Events.evtCurrencyUpdated"),
        
        evtInventoryFetchFinished = LuaEvent:NewIns("InventoryServer.evtInventoryFetchFinished"),
        evtFreshEditModeChange = LuaEvent:NewIns("InventoryServer.evtFreshEditModeChange"),
        evtSupplyDataUpdate = LuaEvent:NewIns("InventoryServer.evtSupplyDataUpdate"),
        evtPostUpdateDeposData = LuaEvent:NewIns("InventoryServer.evtPostUpdateDeposData"),
        
        evtItemRepaireSuccess = LuaEvent:NewIns("InventoryServer.evtItemRepaireSuccess"),

        evtItemMarkStatusChanged = LuaEvent:NewIns("InventoryServer.evtItemMarkStatusChanged"),
        evtItemMarkFailed = LuaEvent:NewIns("InventoryServer.evtItemMarkFailed"),
        evtItemUseRes = LuaEvent:NewIns("InventoryServer.evtItemUseRes"),
        evtExtItemUpgraded = LuaEvent:NewIns("InventoryServer.evtExtItemUpgraded"),

        evtAllCarryItemsWeightChanged = LuaEvent:NewIns("InventoryServer.evtAllCarryItemsWeightChanged"),

        evtPostSortMultiPos = LuaEvent:NewIns("InventoryServer.evtPostSortMultiPos"),

        evtPostMPDataInit = LuaEvent:NewIns("InventoryServer.evtPostMPDataInit"),
        evtPostMPDataChange = LuaEvent:NewIns("InventoryServer.evtPostMPDataChange"),                            --- MP 仓库数据变化 (任何原因都触发)
        evtMPDataChangedFromWeaponModify = LuaEvent:NewIns("ArmedForceServer.evtMPDataChangedFromWeaponModify"), --- MP 仓库数据变化（只有改枪台数据修改会触发）
        evtMPItemUseRes = LuaEvent:NewIns("InventoryServer.evtMPItemUseRes"),

        evtNotifyExtOrderChanged = LuaEvent:NewIns("InventoryServer.evtNotifyExtOrderChanged"),

        evtCurrencyDataUpdate = LuaEvent:NewIns("InventoryServer.evtCurrencyDataUpdate"),
        evtCurrencyDataInit = LuaEvent:NewIns("InventoryServer.evtCurrencyDataInit"),
        evtCurrencyMaxLimitKeyFrame = LuaEvent:NewIns("InventoryServer.evtCurrencyMaxLimitKeyFrame"),

        evtPostUpdateVirtualDeposData = LuaEvent:NewIns("InventoryServer.evtPostUpdateVirtualDeposData"),
        evtSituationBtnStatusChanged = LuaEvent:NewIns("InventoryServer.evtSituationBtnStatusChanged"),

        evtArrangeFaild = LuaEvent:NewIns("InventoryServer.evtArrangeFaild"),

        evtLocalMoveResult = LuaEvent:NewIns("InventoryServer.evtLocalMoveResult"),
        evtRenewalSuccess = LuaEvent:NewIns("InventoryServer.evtRenewalSuccess"),

        evtPostSOLMeleeDataChange = LuaEvent:NewIns("InventoryServer.evtPostSOLMeleeDataChange"),                            --- SOL 近战武器仓库数据变化 (任何原因都触发)

        evtRecoveryAmountUpdate = LuaEvent:NewIns("InventoryServer.evtRecoveryAmountUpdate"),
    }
    ---@type table<number, table<number, ItemBase[]>>
    self.Items = {}
    ---@type table<number, table<number, ItemBase>>
    self._mapGid2Items = {}
    ---@type table<number, table<number, ItemSlot>>
    self._allItemSlots = {}

    --- sol武器皮肤
    self._weaponSkinSetups = {}                 ---@type table<number, pb_WeaponSkinSetup>

    -- 可能是从局内初始化的
    if ItemOperaTool.CheckRunWarehouseLogic() then
        self:_SetupInGameConfig(false)
    else
        self:_SetupInGameConfig(true)
    end
    
    self.curEditMode = EEditMode.Normal
    
    ---@type pb_EquipPropCommand[]
    self._itemMoveCmdBuffer = {}

    self._allRecParts = {}
    self._allWeaponParts = {}
    self._allDisabledParts = {}

    -- 玩家身上新添加的道具
    self.newItemGids = {}

    -- 玩家身上所有装备和容器携带道具总重量
    self._curAllItemsWeight = {}
    -- 玩家身上容器携带道具总重量
    self._curContainerItemsWeight = {}
    self._loadState = ELoadState.Normal
end

-----------------------------------------------------------------------
--region Lifecycle

function InventoryServer:OnInitServer()    
    self:_BindPbListener()
end


function InventoryServer:Update(dt)
    self:TickSyncItemMove(dt)
end

--- 每个Server用于拉取全量数据的接口
function InventoryServer:FetchServerData()
    self:FetchAllItems()
    self:DoFetchInvLabelData()
    self:DoFetchSituationBtnStatuss()
    self:FetchServerData_MP()
end

function InventoryServer:OnDestroyServer()
    self:RemoveAllLuaEvent()
end


function InventoryServer:_BindPbListener()
    Facade.ProtoManager:AddNtfListener("CSDepositChangeNtf", self._OnCSDepositChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSMeleeUnlockChangeNtf", self._OnCSMeleeUnlockChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSMPDepositDataChangeNtf", self._OnCSMPDepositChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSPlayerInfoAddButtonChangedNtf", self._OnCSPlayerInfoAddButtonChangedNtf, self)
    Facade.ProtoManager:AddNtfListener("CSPlayerInfoWishListChangedNtf", self._OnCSPlayerInfoWishListChangedNtf, self)
    self:AddLuaEvent(ItemBase.EItemRemoved, self._OnItemRemoved, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self.OnFetchCollectionData, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self.OnUpdateCollectionData, self)
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)
end

function InventoryServer:_OnRelayConnected()
    self:FetchServerData_MP()
end

function InventoryServer:OnFetchCollectionData()
end

function InventoryServer:OnUpdateCollectionData()
end

function InventoryServer:OnLoadingLogin2Frontend(gameFlowType)
    log("OnLoadingLogin2Frontend")

    self:FetchServerData()
    LuaTickController:Get():RegisterTick(self)
end

function InventoryServer:OnLoadingGame2Frontend(gameFlowType)
    log("OnLoadingGame2Frontend")

    self:FetchServerData()
    self:_SetupInGameConfig(false)
    LuaTickController:Get():RegisterTick(self)
end

function InventoryServer:OnLoadingFrontend2Game(gameFlowType)
    log("OnLoadingFrontend2Game")
    
    self:ResetServerData()

    LuaTickController:Get():RemoveTick(self)
    self:_SetupInGameConfig(true)
    
end

function InventoryServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse then
        if self._hasFetchSettlementItems then
            log("OnGameFlowChangeEnter _FetchAllItems_1")
            self:_FetchAllItems_1(EGetPropInvokeEvent.Login)
        end
    elseif gameFlowType == EGameFlowStageType.Game then
        self:ClearOldWeaponChangeDatas()
    end
end

-- function InventoryServer:OnGameFlowChangeLeave(gameFlowType)
--     if gameFlowType == EGameFlowStageType.SafeHouse
--     or gameFlowType == EGameFlowStageType.Lobby then
--         LuaTickController:Get():RemoveTick(self)
--     elseif gameFlowType == EGameFlowStageType.Game then
--         self:_SetupInGameConfig(false)
--     end
-- end

function InventoryServer:_SetupInGameConfig(bEnterInGame)
    log("SetupInGameConfig", bEnterInGame)
    if bEnterInGame then
        ESlotType.PVEMainWeaponLeft = ESlotType.None
        ESlotType.PVEMainWeaponRight = ESlotType.None

        EActualSlotType.PVEMainWeaponLeft = ESlotType.None
        EActualSlotType.PVEMainWeaponRight = ESlotType.None
    else
        ESlotType.PVEMainWeaponLeft = PVE_WEAPON_LEFT_ID
        ESlotType.PVEMainWeaponRight = PVE_WEAPON_RIGHT_ID

        EActualSlotType.PVEMainWeaponLeft = PVE_WEAPON_LEFT_ID
        EActualSlotType.PVEMainWeaponRight = PVE_WEAPON_RIGHT_ID
    end
end

--endregion
-----------------------------------------------------------------------




-----------------------------------------------------------------------
--region Public API

function InventoryServer:ResetServerData()
    -----------------------------------------------------------------------
    --- 全清空不再适用,MP需要保留
    -- ---@type table<number, table<number, ItemBase[]>>
    -- self.Items = {}
    -- ---@type table<number, table<number, ItemBase>>
    -- self._mapGid2Items = {}
    -- ---@type table<number, table<number, ItemSlot>>
    -- self._allItemSlots = {}
    -- self._allRecParts = {}
    -- self._allWeaponParts = {}

    -----------------------------------------------------------------------
    --region From InventoryServer_MP
    
    --- MP独占数据入局不清理
    self:ResetServerData_MP()
    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region From InventoryServer
    --- 仅清空非MPApply的道具组数据
    self:ResetServerData_SOL()

    ---@type table<number, number> @金钱Id到数量的映射
    self._currencyId2Num = {}

    --endregion
    -----------------------------------------------------------------------
end

function InventoryServer:ResetServerData_SOL()
    --- 仅清空非MPApply的道具组数据
    if self._allItemSlots then
        for groupId, slotGroup in pairs(self._allItemSlots) do
            if groupId ~= ESlotGroup.MPApply then
                self.Items[groupId] = {}
                self._mapGid2Items[groupId] = {}
                self._allItemSlots[groupId] = {}
                self._allRecParts[groupId] = {}
                self._allWeaponParts[groupId] = {}
                log('clear slot group success, groupId:', groupId)
            end
        end
    end

    self._allDisabledParts = {}
    --- sol武器皮肤
    ---@type table<number, pb_WeaponSkinSetup>
    self._weaponSkinSetups = {}
    
    self.curEditMode = EEditMode.Normal

    ---@type pb_EquipPropCommand[]
    self._itemMoveCmdBuffer = {}
    -- 玩家身上新添加的道具
    self.newItemGids = {}
    -- 玩家身上所有装备和容器携带道具总重量
    self._curAllItemsWeight = {}
    -- 玩家身上容器携带道具总重量
    self._curContainerItemsWeight = {}
end

function InventoryServer:GetAllSlots(groupId)
    groupId = setdefault(groupId, ESlotGroup.Player)

    return self._allItemSlots[groupId] or {}
end

---@return ItemSlot
function InventoryServer:GetSlot(slotType, groupId)
    groupId = groupId or ESlotGroup.Player

    local group = self._allItemSlots[groupId]
    if not group then
        group = {}
        self._allItemSlots[groupId] = group
    end

    local slot = group[slotType]
    if not slot then
        -- 检查这个槽类型是否有对应的配置
        if SlotConfig.GetSlotConfigByType(slotType) == nil then
            log(string.format("SlotConfig of [Slot Id: %d] not found!", slotType))
            return nil
        end

        slot = ItemSlot:New(slotType, groupId)
        group[slotType] = slot
    end

    return slot
end

function InventoryServer:RemoveSlot(slotType, groupId)
    groupId = groupId or ESlotGroup.Player
    local group = self._allItemSlots[groupId]
    if group then
        group[slotType] = nil
    end
end

---计算玩家身上不同groupId所携带装备和背包等容器中的所有物品重量
function InventoryServer:CalculateAllCarryItemsWeight(groupId)
    groupId = setdefault(groupId, Server.ArmedForceServer:GetCurSlotGroupId())
    local calculateGroupIds = {
        groupId,
    }
    if Server.ArmedForceServer:CheckIsRentalStatus() and groupId ~= ESlotGroup.MainRental then
        table.insert(calculateGroupIds, ESlotGroup.MainRental)
    end

    for _, calculateGroupId in ipairs(calculateGroupIds) do
        local preAllItemsWeight = self._curAllItemsWeight[groupId]
        local allItemsWeight = 0
        local containerItemsWeight = 0
        for slotKey, itemSlot in pairs(self:GetAllSlots(calculateGroupId)) do
            if table.contains(CalculateWeightSlotTypes, slotKey) then
                local item = itemSlot:GetEquipItem()
                if item then
                    if item.itemMainType ~= EItemType.Hero then
                        local weight = item.weight
                        allItemsWeight = allItemsWeight + weight
                    end
                end
            elseif table.contains(AllContainerSlots, slotKey) then
                local itemList = itemSlot:GetItems()
                if itemList then
                    for _,item in pairs(itemList) do
                        if item.itemMainType ~= EItemType.Hero then
                            local weight = item.weight
                            containerItemsWeight = containerItemsWeight + weight
                        end
                    end
                end
            end
        end

        if calculateGroupId == ESlotGroup.MainRental then
            local extraSlotTypes = {
                ESlotType.SafeBox,
                ESlotType.KeyChain
            }
            for _, extraSlotType in ipairs(extraSlotTypes) do
                local itemSlot = self:GetSlot(extraSlotType, ESlotGroup.Player)
                if itemSlot then
                    local equipItem = itemSlot:GetEquipItem()
                    if equipItem then
                        if equipItem.itemMainType ~= EItemType.Hero then
                            local weight = equipItem.weight
                            allItemsWeight = allItemsWeight + weight
                        end
                    end
                    local containerSlot = itemSlot:GetContainerSlot()
                    if containerSlot then
                        local itemList = containerSlot:GetItems()
                        if itemList then
                            for _,item in pairs(itemList) do
                                if item.itemMainType ~= EItemType.Hero then
                                    local weight = item.weight
                                    containerItemsWeight = containerItemsWeight + weight
                                end
                            end
                        end
                    end
                end
            end
        end

        --角色总负重加上携带物品总重量
        allItemsWeight = allItemsWeight + containerItemsWeight
        --精确小数点后一位
        self._curAllItemsWeight[calculateGroupId] = allItemsWeight
        self._curContainerItemsWeight[calculateGroupId] = containerItemsWeight
        if preAllItemsWeight ~= self._curAllItemsWeight[calculateGroupId] then
            self.Events.evtAllCarryItemsWeightChanged:Invoke(calculateGroupId, allItemsWeight, containerItemsWeight)
        end
        log("CalculateAllCarryItemsWeight  finished==>calculateGroupId:",calculateGroupId," AllWeight:",self._curAllItemsWeight[calculateGroupId])
    end
end

---计算局内玩家身上不同groupId所携带装备和背包等容器中的所有物品重量
function InventoryServer:CalculateLootingAllCarryItemsWeight(cacheItemsInfo)
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        local groupId = ESlotGroup.Player
        local preAllItemsWeight = self._curAllItemsWeight[groupId] or 0
        local allItemsWeight = self:GetAllCarryItemsWeight()
        self._curAllItemsWeight[groupId] = allItemsWeight
        if preAllItemsWeight ~= 0 and preAllItemsWeight ~= self._curAllItemsWeight[groupId] then
            self:CheckLoadState(allItemsWeight)
            self.Events.evtAllCarryItemsWeightChanged:Invoke(groupId, allItemsWeight, 1)
        end
        log("CalculateLootingAllCarryItemsWeight  finished==>groupId:",groupId," AllWeight:",allItemsWeight)
    end
end

function InventoryServer:CheckLoadState(curAllItemsWeight)
    local function fGetLoadState(weight)
        local littleWeight, overWeight = Server.HeroServer:GetRoleLoad()
        local loadState = ELoadState.Normal
        if weight > overWeight then
            loadState = ELoadState.SuperOverLoad
        elseif weight > littleWeight then
            loadState = ELoadState.OverLoad
        end
        return loadState
    end
    local preState = self._loadState
    self._loadState = fGetLoadState(curAllItemsWeight)
    if self._loadState ~= ELoadState.Normal and self._loadState > preState then
        LuaGlobalEvents.evtServerShowTip:Invoke(self._loadState == ELoadState.OverLoad and ServerTipCode.OverLoadTips or ServerTipCode.SuperOverLoadTips)
    end
end

---获得玩家身上对应Gruopid所携带装备和背包等容器中的所有物品重量
---@param groupId ESlotType
function InventoryServer:GetAllCarryItemsWeight(groupId)
    local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
    if bFrontEnd or ItemOperaTool.bInSettlement then
        -- 局外使用
        groupId = groupId or ESlotGroup.Player
        local allItemsWeight = self._curAllItemsWeight[groupId] or 0
        return allItemsWeight
    else
        local allItemsWeight = 0
        -- 局内使用
        local character = Facade.GameFlowManager:GetCharacter()
        if character then
            allItemsWeight = character:GetCurrentRoleLoadValue() or 0
        end
        return allItemsWeight
    end
end

---获得玩家身上对应Gruopid背包等容器中的所有物品重量
---@param groupId ESlotType
function InventoryServer:GetContainerCarryItemsWeight(groupId)
    groupId = groupId or ESlotGroup.Player
    local containerItemsWeight = self._curContainerItemsWeight[groupId] or 0
    return containerItemsWeight
end

---@param propInfo pb_PropInfo
---@param targetSlot ItemSlot
function InventoryServer:CreateNewItem(propInfo, targetSlot, bForceLegit)
    local newItem = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid, nil, nil, bForceLegit)
    newItem:SetRawPropInfo(propInfo)
    
    newItem:AddToSlot(targetSlot, false)
    -- if propInfo.loc.x > 0 and propInfo.loc.y > 0 then
        targetSlot:SetItemPosFromPb(newItem, propInfo.loc)
    -- end

    self:AddItemToList(newItem, targetSlot)

    return newItem
end

local function checkItemExistInList(item, itemList)
    for _, inItem in ipairs(itemList) do
        if item.instanceId == inItem.instanceId then
            return true
        end
    end
    return false
end

---@param targetSlot ItemSlot
function InventoryServer:AddItemToList(item, targetSlot)
    local slotGroup = targetSlot:GetSlotGroup()
    if not self.Items[slotGroup] then
        self.Items[slotGroup] = {}
    end
    local groupItems = self.Items[slotGroup]
    if not groupItems[item.id] then
        groupItems[item.id] = {}
    end

    if not checkItemExistInList(item, groupItems[item.id]) then
        table.insert(groupItems[item.id], item)
    end

    -- Cache mapping relationship
    if not self._mapGid2Items[slotGroup] then
        self._mapGid2Items[slotGroup] = setmetatable({}, {__mode = "kv"})
    end
    self._mapGid2Items[slotGroup][item.instanceId] = item
end

function InventoryServer:RemoveItemFromList(itemGid, slotGroup)
    local slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    local groupItems = self.Items[slotGroup]
    if not groupItems then
        return
    end

    local groupGidItems = self._mapGid2Items[slotGroup]
    if groupGidItems then
        local item = groupGidItems[itemGid]
        groupGidItems[itemGid] = nil

        if item then
            local id = item.id

            local idItems = groupItems[id]
            local findKey = 0
            if idItems then
                for key,Item in pairs(idItems) do
                    if Item.gid == itemGid then
                        findKey = key
                        break
                    end
                end
                if findKey > 0 and findKey <= #idItems then
                    table.remove(idItems,findKey)
                end

                if #idItems == 0 then
                    groupItems[id] = nil
                end
            end
        end
    end
end


function InventoryServer:ClearSlotGroup(groupId)
    if self._allItemSlots and self._allItemSlots[groupId] then
        self.Items[groupId] = {}
        self._mapGid2Items[groupId] = {}
        self._allItemSlots[groupId] = {}
        log('clear slot group success')
    else
        log('clear slot group fail, GroupId:', groupId)
    end
end

--- 请求保养某个道具
---@param item ItemBase
function InventoryServer:DoRepairItem(item, repairType, fRequestCallBack)
    ---@param res pb_CSDepositRepairRes
    local function fOnItemRepairResult(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.RepairSuccess))
            self:ProcessPbDataChange(res.deposit_change, true)
            self.Events.evtItemRepaireSuccess:Invoke(item)
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHRepairSuccess)
            if fRequestCallBack then
                fRequestCallBack()
            end
        else
            -- LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.MaintainanceFail))
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHRepairError)
        end
    end

    local req = pb.CSDepositRepairReq:New()
    req.prop_id = item.id
    req.prop_gid = item.instanceId
    if repairType == 1 then
        req.repair_type = DepositRepairType.RepairTypeBasic
    elseif repairType == 2 then
        req.repair_type = DepositRepairType.RepairTypeInterMediate
    else
        req.repair_type = DepositRepairType.RepairTypeAdvanced
    end
    req:Request(fOnItemRepairResult)
end

function InventoryServer:DoSwitchAutoSortMode(bAutoSort)
	---@param res pb_CSDepositSwitchRes
	local fCallback =  CreateCallBack(function(self, res)
        if res.result == 0 then
			self:_LocalSetAutoSort(res.auto_sort)
		end
    end, self)
    
	local req = pb.CSDepositSwitchReq:New()
	req.auto_sort = bAutoSort
	req:Request(fCallback)
end

function InventoryServer:DoMovePerk(srcPerkGid, targetPerkGid, srcWeaponGid, targetWeaponGid, targetSlot, targetPosX, targetPosY)
    if targetWeaponGid > 0 then
        -- 装上 
        if srcWeaponGid > 0 then
            -- 交换两把枪的perk，有单独的协议
            self:DoSwapPerk(srcWeaponGid, targetWeaponGid)
        else
            -- 非交换的情况
            self:DoEquipPerk(srcPerkGid, targetWeaponGid)
        end
        
    else
        -- 卸下
        self:DoUnequipPerk(srcWeaponGid, targetSlot, targetPosX, targetPosY)
    end
end

function InventoryServer:DoEquipPerk(srcPerkGid, targetWeaponGid)
    ---@param res pb_CSWAssemblyPerkEquipRes
    local function fOnPerkEquipRes(res)
        if res.result == 0 then
            self:ProcessPbDataChange(res.changes, true)
        else
        end
    end

    local req = pb.CSWAssemblyPerkEquipReq:New()
    req.root_gid = targetWeaponGid
    req.target_gid = srcPerkGid

    local weaponItem = self:GetItemByGid(targetWeaponGid)
    req.root_id = weaponItem.id
    req.target_id = WeaponPerkFeature.GetPerkIdByGid(srcPerkGid)
    req:Request(fOnPerkEquipRes)
end

---@param targetSlot ItemSlot
function InventoryServer:DoUnequipPerk(srcWeaponGid, targetSlot, targetPosX, targetPosY)
    ---@param res pb_CSWAssemblyPerkUnEquipRes
    local function fOnPerkUnequipRes(res)
        if res.result == 0 then
            self:ProcessPbDataChange(res.changes, true)
        else
            
        end
    end

    local req = pb.CSWAssemblyPerkUnEquipReq:New()
    req.root_gid = srcWeaponGid
    local weaponItem = self:GetItemByGid(srcWeaponGid)
    req.root_id = weaponItem.id
    req.pos = targetSlot.SlotType
    if targetPosX < 0 or targetPosY < 0 then
        req.space_id = 0    -- 任意位置
    else
        req.space_id = 1
    end
    
    req.x = targetPosX
    req.y = targetPosY
    req:Request(fOnPerkUnequipRes)
end

function InventoryServer:DoSwapPerk(srcWeaponGid, targetWeaponGid)
    ---@param res pb_CSWAssemblyPerkSwapRes
    local function fOnPerkSwapRes(res)
        if res.result == 0 then
            self:ProcessPbDataChange(res.changes, true)
        else

        end
    end

    local req = pb.CSWAssemblyPerkSwapReq:New()
    req.root_gun_gid = srcWeaponGid
    req.target_gun_gid = targetWeaponGid
    local rootGunItem = self:GetItemByGid(srcWeaponGid)
    local targetGunItem = self:GetItemByGid(targetWeaponGid)
    req.root_gun_id = rootGunItem.id
    req.target_gun_id = targetGunItem.id

    req:Request(fOnPerkSwapRes)
end

--- 使用道具
---@param items ItemBase[]
---@param nums number[]
function InventoryServer:DoUseMultiItems(items, nums, targetIds, targetGids, fCustomCallback)
    ensure(#items == #nums, "InventoryServer:DoUseMultiItems 道具和数量不匹配。")

    if #items == 0 then
        return false
    end
    local sampleItem = items[1]
    if sampleItem.InSlot:GetSlotGroup() == ESlotGroup.MPApply then
        -- 如果是MP道具，走MP道具使用的逻辑
        return self:DoUseMultiMPItems(items, nums, targetIds, targetGids, fCustomCallback)
    end

    ---@param res pb_CSDepositUsePropRes
    local function fOnUseItemRes(res)
        local bSucceed = res.result == 0
        if bSucceed then
            self:ProcessPbDataChange(res.changes, true)
        end

        -- 取第一个道具来判断是什么类型的道具使用
        if #items > 0 then
            local pickOneItem = items[1]
            self._lastUseItemId = pickOneItem.id
            
            local featureType = pickOneItem:GetFeatureType()
            if featureType == EFeatureType.KeyBoxUnlockItem then
                -- 使用钥匙扣解锁道具
                if bSucceed then
                    local unlockNum = 0
                    for _, item in ipairs(items) do
                        ---@type KeyBoxUnlockItemFeature
                        local unlockFeature = item:GetFeature(EFeatureType.KeyBoxUnlockItem)
                        if unlockFeature then
                            unlockNum = unlockNum + unlockFeature.unlockSlotNum
                        end
                    end
                    LuaGlobalEvents.evtServerShowTip:Invoke(StringUtil.PluralTextFormat(ServerTipCode.UnlockKeySpaceSuccess, {["SlotNum"]=unlockNum})) --单复数改造[aidenliao]
                elseif res.result == Err.DepositPropSpaceCantUnlock then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.UnlockKeySpaceFail_LvlNotMatch)
                else
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.UnlockKeySpaceFail_Common)
                end
            end
        end

        self.Events.evtItemUseRes:Invoke(res)

        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSDepositUsePropReq:New()
    req.cmds = {}

    for i, item in ipairs(items) do
        local cmd = pb.UsePropCmd:New()
        cmd.prop_id = item.id
        cmd.prop_gid = item.gid
        cmd.num = nums[i]
        cmd.target_id = targetIds[i]    -- could be nil
        cmd.target_gid = targetGids[i]  -- could be nil

        table.insert(req.cmds, cmd)
    end
    
    req:Request(fOnUseItemRes)

    return true
end

--- 使用单个道具
---@param item ItemBase
function InventoryServer:DoUseItem(item, num, targetId, targetGid, fCustomCallback)
    num = setdefault(num, item.num)

    return self:DoUseMultiItems({item}, {num}, {targetId}, {targetGid}, fCustomCallback)
end

function InventoryServer:DoUseItemsById(id, totalNum, fCustomCallback)
    local allMatchItems = Server.InventoryServer:GetItemsById(id)
    
    local targetItems = {}
    local targetNums = {}
    for _, item in ipairs(allMatchItems) do
        if totalNum <= 0 then
            break
        end

        table.insert(targetItems, item)
        local useNum = math.min(totalNum, item.num)
        table.insert(targetNums, useNum)

        totalNum = totalNum - useNum
    end
    return self:DoUseMultiItems(targetItems, targetNums, nil, nil, fCustomCallback)
end

function InventoryServer:GetLastUseItemId()
    return self._lastUseItemId
end

--- 升级扩容道具
---@param item ItemBase
function InventoryServer:DoLevelUpExtItem(item)
    local req = pb.CSDepositUpgradeExtensionBoxReq:New()
    req.prop_gid = item.gid
    req.materal_prop_list = {}

    local allMaterials = ItemConfigTool.GetExtItemLevelUpMaterialList(item.id)
    for id, num in pairs(allMaterials) do
        if id > 0 and num > 0 then
            if id == item.id then
                -- 材料中的扩容箱必须填自己
                local materialProp = pb.PropInfo:New()
                materialProp.id = item.id
                materialProp.gid = item.gid
                table.insert(req.materal_prop_list, materialProp)
            else
                local matchMaterials = self:GetItemsById(id)
                for _, matchItem in ipairs(matchMaterials) do
                    if num > 0 then
                        local materialProp = pb.PropInfo:New()
                        materialProp.id = matchItem.id
                        materialProp.gid = matchItem.gid
                        if matchItem.num < num then
                            materialProp.num = matchItem.num
                            num = num - matchItem.num
                        else
                            -- satisfy condition
                            materialProp.num = num
                            num = 0
                        end
    
                        table.insert(req.materal_prop_list, materialProp)
                    end
                end

                if num > 0 then
                    -- no enough material items
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.LevelUpExtItemFail_MaterialNotEnough)
                    return false
                else
    
                end
            end
        end
    end

    ---@param res pb_CSDepositUpgradeExtensionBoxRes
    local function fLevelUpExtItemRes(res)
        if res.result == 0 then
            self:ProcessPbDataChange(res.changes, true)

            -- show success tip
            local extDesRow = ItemConfigTool.GetExtentionBoxDescRowById(item.id)
            local nextLevelItemId = extDesRow.NextLvItemID
            local itemConfig = ItemConfigTool.GetItemConfigById(nextLevelItemId)

            local tip = string.format(ServerTipCode.LevelUpExtItemSuccess, itemConfig.Name)
            LuaGlobalEvents.evtServerShowTip:Invoke(tip)
            
            self.Events.evtExtItemUpgraded:Invoke(res)
        else

        end
    end
    req:Request(fLevelUpExtItemRes)

    return true
end

function InventoryServer:CheckExtItemCanLevelUp(item)
    local allMaterials = ItemConfigTool.GetExtItemLevelUpMaterialList(item.id)
    if table.isempty(allMaterials) then
        return false
    end

    for id, num in pairs(allMaterials) do
        local matchMaterials = self:GetItemsById(id)
        for _, matchItem in ipairs(matchMaterials) do
            if num > 0 then
                if matchItem.num < num then
                    num = num - matchItem.num
                else
                    -- satisfy condition
                    num = 0
                end

            end
        end

        if num > 0 then
            -- no enough material items
            return false
        else

        end
    end

    return true
end

function InventoryServer:GetAutoSort()
	return self._bAutoSort
end

function InventoryServer:CheckWeaponBothEquipped(slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    local leftWeaponSlot = self:GetSlot(ESlotType.MainWeaponLeft, slotGroup)
    local rightWeaponSlot = self:GetSlot(ESlotType.MainWeaponRight, slotGroup)
    return leftWeaponSlot:GetEquipItem() and rightWeaponSlot:GetEquipItem()
end

--- 返回所有道具的迭代器
---@param slotGroup ESlotGroup
---@return fun():number, ItemBase
function InventoryServer:GetItemsIterator(slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    local groupItems = self.Items[slotGroup] or {}
    local itemId
    local totalIndex = 0
    local id2Items
    local itemIndex
    return function()
        local item
        while item == nil do
            -- 先从当前取到的slot里取新的item
            if id2Items ~= nil then
                itemIndex, item = next(id2Items, itemIndex)
            end
            -- 如果取不到item,要么是slot还没赋值,要么是slot为空,要么是slot里的所有item都被取过了,不管是哪种情况,都从group里取新的slot
            if itemIndex == nil then
                itemId, id2Items = next(groupItems, itemId)
                -- 如果取不到新的slot,说明group为空或者里面的slot都被取过了,结束迭代器
                if itemId == nil then
                    return
                end
            end
        end
        totalIndex = totalIndex + 1
        return totalIndex, item
    end
end

--- 根据一个条件方法查找所有满足条件的道具
---@param conditionFunc fun(Item:ItemBase):boolean
---@return ItemBase[]
function InventoryServer:GetItemsByCondition(conditionFunc)
    local ret = {}
    for _, Item in self:GetItemsIterator() do
        if conditionFunc(Item) then
            table.insert(ret, Item)
        end
    end

    return ret
end

function InventoryServer:ContainsItemID(itemID, slotGroup)
    local num = self:GetItemNumById(itemID, slotGroup)
    return num > 0
end

-- 获取道具（包括仓库、安全箱、胸甲和已装备的道具）
function InventoryServer:GetItemsById(itemId, slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    local groupItems = self.Items[slotGroup]
    if groupItems then
        local items = groupItems[itemId]
        if items then
            return items
        end
    end

    table.clear(emptyRet)
    return emptyRet
end

function InventoryServer:GetItemFromID(itemID, slotGroup, index)
    index = setdefault(index, 1)
    local items = self:GetItemsById(itemID, slotGroup)
    local item = items[index]
    return item
end

--- 获取ItemId的数量
function InventoryServer:GetItemNumById(itemId, slotGroup)
    local items = self:GetItemsById(itemId, slotGroup)
    local num = 0
    for key, value in pairs(items) do
        num = num + value.num
    end
    return num
end

--- 获取指定条件Item的数量
---@param conditionFunc fun(Item:ItemBase):boolean
function InventoryServer:GetItemNumByCondition(conditionFunc)
    local items = self:GetItemsByCondition(conditionFunc)
    local num = 0
    for key, value in pairs(items) do
        num = num + value.num
    end
    return num
end

--- 根据Gid查找道具
---@return ItemBase
function InventoryServer:GetItemByGid(itemGid, slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    if not itemGid then
        return
    end
    if slotGroup and self._mapGid2Items[slotGroup] then
        return self._mapGid2Items[slotGroup][itemGid]
    end
end

function InventoryServer:FindItemByGid(itemGid, bIgnorePlayerSlotGroup)
    bIgnorePlayerSlotGroup = setdefault(bIgnorePlayerSlotGroup, false)
    for GroupId, Group in pairs(self._mapGid2Items) do
        if not bIgnorePlayerSlotGroup or GroupId ~= ESlotGroup.Player then
            for ItemGid, Item in pairs(Group) do
                if itemGid == ItemGid then
                    return self._mapGid2Items[GroupId][ItemGid]
                end
            end
        end
    end
end

function InventoryServer:GetAllContainerSpaceSize()
    local slot = self:GetSlot(ESlotType.Application)
    return slot:GetRemainingSpaceSize()
end

function InventoryServer:GetMaxStacksNumById(id)
    local itemConfig = Facade.TableManager:GetTable("GameItem")[tostring(id)]
    if itemConfig then
        local stackNum = itemConfig.MaxStackCount
        return stackNum == 0 and 1 or stackNum
    else
        logwarning(id)
        return 1
    end
end

--- 计算这些槽能放下多少个ItemId的道具
---@param itemId number
---@param targetSlotTypes ESlotType[]
function InventoryServer:CalMaxNumOfItemCanPlace(itemId, targetSlotTypes, slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    local ret = 0
    for _, slotType in ipairs(targetSlotTypes) do
        local slot = self:GetSlot(slotType, slotGroup)
        local num = slot:CalNumOfItemCanPlaced(itemId)
        ret = ret + num
    end

    return ret
end

---@param weaponGID number
---@return pb_PropInfo
function InventoryServer:GetWeaponPropInfoFromUIContext(weaponGID)
    local slotGroupID = Server.ArmedForceServer:GetCurSlotGroupId()
    local bagID = Server.ArmedForceServer:GetMPCurTempBagId()
    local propinfo = self:GetWeaponPropInfo(weaponGID, slotGroupID, bagID)
    return propinfo
end

---@param weaponGID number
---@param slotGroup number
---@param bagID number
---@return pb_PropInfo
function InventoryServer:GetWeaponPropInfo(weaponGID, slotGroup, bagID)
    if slotGroup ~= ESlotGroup.MPApply then
        local itemBase = self:GetItemByGid(weaponGID, slotGroup)
        if  itemBase == nil then
            return nil
        else
            local propinfo = itemBase:GetRawPropInfo()
            Server.GunsmithServer:GetDataStore():OverrideSkinInfo2PropInfo(propinfo)
            return propinfo
        end
    end

    local propinfo = Server.ArmedForceServer:GetModifyPropInfoByExpertBagIdAndGid(bagID, weaponGID)
    if propinfo ~= nil then
        return propinfo
    end

    local itemBase = self:GetItemByGid(weaponGID, ESlotGroup.MPApply)
    if  itemBase == nil then
        return nil
    end
    local propinfo = itemBase:GetRawPropInfo()
    Server.GunsmithServer:GetDataStore():OverrideSkinInfo2PropInfo(propinfo)
    return propinfo
end

function InventoryServer:GetWeaponPropInfoByWeaponID(weaponID, slotGroup, bagID)
    if slotGroup ~= ESlotGroup.MPApply then
        local itemBase = self:GetItemFromID(weaponID, slotGroup)
        if  itemBase == nil then
            return nil
        else
            local propinfo = itemBase:GetRawPropInfo()
            Server.GunsmithServer:GetDataStore():OverrideSkinInfo2PropInfo(propinfo)
            return propinfo
        end
    end

    local propinfo = Server.ArmedForceServer:GetModifyPropInfoByExpertBagIdAndWeaponId(bagID, weaponID)
    if propinfo ~= nil then
        return propinfo
    end

    local itemBase = self:GetItemFromID(weaponID, ESlotGroup.MPApply)
    if  itemBase == nil then
        return nil
    end
    local propinfo = itemBase:GetRawPropInfo()
    Server.GunsmithServer:GetDataStore():OverrideSkinInfo2PropInfo(propinfo)
    return propinfo
end

function InventoryServer:GetWeaponSkinID(weaponGID, slotGroup, bagID)
    local propinfo = self:GetWeaponPropInfo(weaponGID, slotGroup, bagID)
    if propinfo == nil or propinfo.weapon == nil then
        return 0
    end
    return propinfo.weapon.skin_id
end

function InventoryServer:GetWeaponSkinGID(weaponGID, slotGroup, bagID)
    local propinfo = self:GetWeaponPropInfo(weaponGID, slotGroup, bagID)
    if propinfo == nil or propinfo.weapon == nil then
        return 0
    end
    return propinfo.weapon.skin_gid
end

-- TODO DELETE
--- 获取以机匣的pb_PropInfo
--- @param recPartGID number
--- @return pb_PropInfo
function InventoryServer:GetRecPartPropInfo(recPartGID, slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    if not self._allRecParts[slotGroup] then
        return nil
    end
    return self._allRecParts[slotGroup][recPartGID]
end

function InventoryServer:GetAllRecParts(slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    return self._allRecParts[slotGroup]
end

function InventoryServer:GetAllWeaponParts(slotGroup)
    slotGroup = setdefault(slotGroup, ESlotGroup.Player)
    return self._allWeaponParts[slotGroup]
end

-- 获取所有仓库相关的槽位列表
---@return ESlotType[]
function InventoryServer:GetWarehouseRefSlotTypes()
    local warehouseRefSlots = {
        ESlotType.MainWeaponLeft,
        ESlotType.MainWeaponRight,
        ESlotType.Helmet,
        ESlotType.BreastPlate,
        ESlotType.Bag,
        ESlotType.ChestHanging,
        ESlotType.KeyChain,
        ESlotType.SafeBox,
        ESlotType.Pocket,
        ESlotType.ChestHangingContainer,
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer,
        -- ESlotType.BagSpaceContainer,
        ESlotType.KeyChainContainer,
        ESlotType.Pistrol,
        ESlotType.MeleeWeapon,
        -- ESlotType.MainContainer
    }

    local allDepositIds = self:GetAllDepositIds()
    table.append(warehouseRefSlots, allDepositIds)

    return warehouseRefSlots
end

-- 获取仓库总价值
---@return number
function InventoryServer:GetWarehouseValue()
    local total = 0
    local items = 0
    local warehouseRefSlots = self:GetWarehouseRefSlotTypes()
    for _, slotType in ipairs(warehouseRefSlots) do
        local slot = self:GetSlot(slotType)
        if slot then
            for _, item in ipairs(slot:GetItems()) do
                if not item:IsInvisibleItem() then  -- 只考虑可见的道具
                    total = total + item:GetTotalSellPrice()
                    items = items + 1
                end
            end
        end
    end
    -- 已装备的扩容箱
    for extId, depositId in pairs(self._extMapping) do
        local extSlot = Server.InventoryServer:GetSlot(extId)
        local extItem = extSlot:GetEquipItem()
        if not extItem then
            logerror("extItem not exist, extId:" .. extId .. " depositId:" .. depositId)
        else
            total = total + extItem:GetTotalSellPrice()
        end
    end
    return total
end

-- 计算背包价值是否超过20w，是否含有ID为15开头的道具
function InventoryServer:FindValuableItem()
    local containerSlot = {
        ESlotType.Pocket,
        ESlotType.ChestHangingContainer,
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer,
    }
    local carryLimit = Server.GameModeServer:GetCarryInLimitValue()--DFMGlobalConst.GetGlobalConstNumber("CarryInLimit", 3000000)
    if carryLimit ==nil then
        carryLimit=3000000
    end
    local value = 0
    -- 背包+胸挂+口袋+保险箱内所有道具，入局总带入不能超过300W，300W配表，就用之前的那个配置
    for _, slotType in ipairs(containerSlot) do
        local slot = self:GetSlot(slotType)
        if slot then
            for _, item in ipairs(slot:GetItems()) do
                -- if item.itemMainType == 15 then
                --     value = value + item:GetTotalSellPrice()
                --     loginfo("[InventoryServer]itemid:" .. item.id .. " value:" .. value)
                -- end
                -- if value > 200000 then
                --     return false
                -- end

                value = value + item:GetTotalSellPrice()
                if value >= carryLimit then
                    return false, carryLimit
                end
            end
        end
    end
    return true, value
end

function InventoryServer:SetEditMode(mode)
    self.curEditMode = mode
    self.Events.evtFreshEditModeChange:Invoke(mode)
end

function InventoryServer:GetEditMode()
    return self.curEditMode
end

---@return ItemBase
function InventoryServer:GetKeyChainItem()
    local keyChainSlot = self:GetSlot(ESlotType.KeyChain)
    if keyChainSlot then
        local keyChain = keyChainSlot:GetEquipItem()
        if keyChain then
            return keyChain
        end
    end
end

function InventoryServer:GetBagItem()
    local bagSlot = self:GetSlot(ESlotType.Bag, ItemOperaTool.GetSelfSlotGroup())
    if bagSlot then
        local bag = bagSlot:GetEquipItem()
        if bag then
            return bag
        end
    end
end

function InventoryServer:GetChestHangingItem()
    local chestHangingSlot = self:GetSlot(ESlotType.ChestHanging, ItemOperaTool.GetSelfSlotGroup())
    if chestHangingSlot then
        local chestHanging = chestHangingSlot:GetEquipItem()
        if chestHanging then
            return chestHanging
        end
    end
end

function InventoryServer:CheckMapLocked(mapId)
    local keyContSlot = self:GetSlot(ESlotType.KeyChainContainer)
    local refSpace = keyContSlot:GetSpaceByIndex(mapId)

    local bFullyLocked = true
    if refSpace then
        bFullyLocked = refSpace:IsFullyLocked()
    end

    return bFullyLocked
end

function InventoryServer:CheckExtItemCanLvlUp(extItemId)
    if not self._mapExtItemToExchangeIdList then
        self._mapExtItemToExchangeIdList = {}

        local curExchangeShopItems = Server.ShopServer:GetShopItemsForExchange()
        for _, shopItem in pairs(curExchangeShopItems) do
            local item = shopItem.item
            if ItemBaseTool.IsExtendItem(item) then
                local costItemIdNumList = shopItem:GetCostList() 
                for _, costItemNumInfo in ipairs(costItemIdNumList) do
                    local costItemId = costItemNumInfo.itemId
                    local itemMainType = ItemHelperTool.GetMainTypeById(costItemId)
                    local itemSubType = ItemHelperTool.GetSubTypeById(costItemId)
                    local keyBoxUnlockItemSubType = 11
                    if itemMainType == EItemType.ExtendItem and itemSubType ~= keyBoxUnlockItemSubType then
                        if not self._mapExtItemToExchangeIdList[costItemId] then
                            self._mapExtItemToExchangeIdList[costItemId] = {}
                        end
                        local tb = self._mapExtItemToExchangeIdList[costItemId]
                        table.insert(tb, shopItem.exchangeId)
                    end
                end
            end
        end
    end

    local exchangeIds = self._mapExtItemToExchangeIdList[extItemId]

    if exchangeIds then
        for _, exchangeId in ipairs(exchangeIds) do
            local shopItem = Server.ShopServer:GetShopItemByExchangeId(exchangeId)
            if shopItem and shopItem:GetIsSufficient() and shopItem:GetIsCostEnough(1) then
                return true
            end
        end
    end

    return false
end

function InventoryServer:NotifyEnterSettlement()
    -- 特殊处理一下，由结算模块通知Server，进入结算了
    LuaTickController:Get():RegisterTick(self)
end

function InventoryServer:HasEquipThisSlot(slotType)
    local slot = Server.InventoryServer:GetSlot(slotType)
    return slot:GetEquipItem() ~= nil
end

---@return number 皮肤
function InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(baseWeaponID)
    local setup = self._weaponSkinSetups[baseWeaponID]
    if setup == nil then
        return 0
    end
    return setup.skin_id
end

---@return number 皮肤
function InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(baseWeaponID)
    local setup = self._weaponSkinSetups[baseWeaponID]
    if setup == nil then
        return 0
    end
    return setup.skin_gid
end

---@return number 挂饰
function InventoryServer:GetWeaponPendantIDFromBaseWeaponID(baseWeaponID)
    local setup = self._weaponSkinSetups[baseWeaponID]
    if setup == nil then
        return 0
    end
    return setup.pendant_id
end

---@return number 挂饰
function InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(baseWeaponID)
    local setup = self._weaponSkinSetups[baseWeaponID]
    if setup == nil then
        return 0
    end
    return setup.pendant_gid
end

-- 检查主仓库是否已满
---@return boolean 仓库是否已满
function InventoryServer:CheckMainWarehouseFull()
    local mainWarehouseSlot = self:GetSlot(ESlotType.MainContainer)
    local capacity = mainWarehouseSlot:GetUsedCapacity()
    local maxCapacity = mainWarehouseSlot:GetTotalCapacity()
    return capacity >= maxCapacity
end

-- 检查主仓库是否到达一定容量比例
---@return boolean 仓库是否已满
function InventoryServer:CheckMainWarehousePercent(percent)
    local mainWarehouseSlot = self:GetSlot(ESlotType.MainContainer)
    local capacity = mainWarehouseSlot:GetUsedCapacity()
    local maxCapacity = mainWarehouseSlot:GetTotalCapacity()
    return capacity >= maxCapacity * percent
end

-- 检查主仓库是否到达一定容量比例
function InventoryServer:GetMainWarehousePercent()
    local mainWarehouseSlot = self:GetSlot(ESlotType.MainContainer)
    local capacity = mainWarehouseSlot:GetUsedCapacity()
    local maxCapacity = mainWarehouseSlot:GetTotalCapacity() or 1
    return capacity / maxCapacity
end

-- 检查玩家是否装备背包

-- 对玩家的本地ItemGids进行存取
function InventoryServer:GetAllItemGids()
    return Facade.ConfigManager:GetUserArray("UserObtained" , {})
end
function InventoryServer:AddAllItemGid(itemGid)
    local allItemGids = Facade.ConfigManager:GetUserArray("UserObtained" , {})
    table.insert(allItemGids , itemGid)
    Facade.ConfigManager:SetUserArray("UserObtained",allItemGids)
end
function InventoryServer:RemoveAllItemGid(itemGid)
    local allItemGids = Facade.ConfigManager:GetUserArray("UserObtained" , {})
    table.removebyvalue(allItemGids , itemGid)
    Facade.ConfigManager:SetUserArray("UserObtained",allItemGids)
end
function InventoryServer:SetAllItemGids(allItemGids)
    Facade.ConfigManager:SetUserArray("UserObtained", allItemGids)
end

function InventoryServer:GetSOLMeleeWeaponsItemsById(itemId)
    self._meleeWeapons = setdefault(self._meleeWeapons, {})
    return self._meleeWeapons[itemId]
end

function InventoryServer:GetSafeBoxItems()
    local items = {}
    for _, permissionItem in pairs(self._permissionItems) do
        local permissionItemFeature = permissionItem:GetFeature(EFeatureType.Equipment)
        if permissionItemFeature:IsSafeBox() then
            if not permissionItemFeature:IsNetBarSafeBox() then
                table.insert(items, permissionItem)
            else
                if Server.RoleInfoServer:GetNetBarPrivilegeLevel() >= 4 then
                    table.insert(items, permissionItem)
                end
            end
        end
    end
    local function fsortfunc(a, b)
        local id_a = a.id
        local id_b = b.id
        return id_a < id_b
    end
    table.sort(items, fsortfunc)
    return items
end

function InventoryServer:GetKeyChainItems()
    local items = {}
    for _, permissionItem in pairs(self._permissionItems) do
        local permissionItemFeature = permissionItem:GetFeature(EFeatureType.Equipment)
        -- 可能会有过期道具残留，所以删除已过期道具
        local bExpiredItem = permissionItemFeature:GetExpiredStatus()
        if permissionItemFeature:IsKeyChain() and not bExpiredItem then
            table.insert(items, permissionItem)
        end
    end
    local function fsortfunc(a, b)
        local id_a = a.id
        local id_b = b.id
        return id_a < id_b
    end
    table.sort(items, fsortfunc)
    return items
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private

---@param propInfo pb_PropInfo
---@param targetSlot ItemSlot
function InventoryServer:_InternalCreateNewItem(propInfo, targetSlot, bForceLegit)
    local newItem
    if targetSlot and self._allRecParts and ItemConfigTool.CheckItemHasConfig(propInfo.id) then
        newItem = self:CreateNewItem(propInfo, targetSlot, bForceLegit)
        local slotGroup = targetSlot:GetSlotGroup()
        if (slotGroup == ESlotGroup.Player or slotGroup == ESlotGroup.MPApply) and 
            not (ItemHelperTool.IsArmedForceUniquePropByItem(newItem)) and
            self._allRecParts[slotGroup] and WeaponAssemblyTool.IsAssembledWeapon(newItem.id) then
            --保存机匣
            if newItem.itemMainType == EItemType.Receiver then
                self._allRecParts[slotGroup][propInfo.gid] = newItem
            elseif newItem.itemMainType == EItemType.Adapter then
                self._allWeaponParts[slotGroup][propInfo.gid] = newItem
            end
        end
    end
    return newItem
end

local preloadSlotTypes = {
    [ESlotType.MainWeaponLeft] = true,
    [ESlotType.MainWeaponRight] = true,
    [ESlotType.MeleeWeapon] = true,
    [ESlotType.Bag] = true,
    [ESlotType.ChestHanging] = true,
    [ESlotType.BagContainer] = true,
    [ESlotType.ChestHangingContainer] = true,
    [ESlotType.MainContainer] = true
}
local firstPageHeight = 10

---@param item ItemBase
function InventoryServer:_PreloadItemIcon(item)
    if not InventoryServer.PRELOAD_ITEM_ICON then
        return
    end

    local bShouldPreload = false
    if InventoryServer.PRELOAD_ITEM_ICON_ONLY_FIRST_PAGE then
        local loc = item:GetRawPropInfo().loc
        if preloadSlotTypes[loc.pos] then
            if loc.pos == ESlotType.MainContainer then
                if loc.y <= firstPageHeight then
                    bShouldPreload = true
                end
            else
                bShouldPreload = true
            end
        end
    else
        bShouldPreload = true 
    end

    if bShouldPreload then
        RuntimeIconTool.PreLoadItemIcon(item) 
    end
end

function InventoryServer:_LocalSetAutoSort(bAutoSort)
	if bAutoSort ~= self._bAutoSort then
		log("LocalSetAutoSort", bAutoSort)
		self._bAutoSort = bAutoSort
	end
end

---@param item ItemBase
function InventoryServer:_OnItemRemoved(item, slotGroup)
    self:RemoveItemFromList(item.instanceId, slotGroup)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 待移除

--- 查找角色是否有匹配的预设道具（只看Id和配件）
--- 如果存在多件，优先已装备的，其次返回耐久高的
---@param presetItem ItemBase
---@return ItemBase
function InventoryServer:FindMatchPresetItem(presetItem)
    local matchItems = self:GetItemsById(presetItem.id)
    if #matchItems == 0 then
        return nil
    else
        table.sort(matchItems, function(aItem, bItem)
            local curDurability = 0
            local curDurability1 = 0
            local itemFeature = aItem:GetFeature()
            local itemFeature1 = bItem:GetFeature()
            if itemFeature and itemFeature.GetDurabilityCurValue then
                curDurability = itemFeature:GetDurabilityCurValue()
            end
            if itemFeature1 and itemFeature1.GetDurabilityCurValue then
                curDurability1 = itemFeature1:GetDurabilityCurValue()
            end
            if aItem:IsEquipped() ~= bItem:IsEquipped() then
                return aItem:IsEquipped()
            elseif aItem:CheckIsBind() ~= aItem:CheckIsBind() then
                return aItem:CheckIsBind()
            elseif curDurability ~= curDurability1 then
                return curDurability > curDurability1
            else
                return aItem.instanceId < bItem.instanceId
            end
        end)
        local realMatchItems = {}
        for _,v in pairs(matchItems) do
            if ItemBaseTool.CompareItem(v, presetItem) then
                table.insert(realMatchItems,v)
            end
        end
        if #realMatchItems == 0 then
            return nil
        else
            return realMatchItems[1]
        end
    end
end
--endregion
-----------------------------------------------------------------------

function InventoryServer:GetAllKeys()
	local ret = {}
    for _, item in Server.InventoryServer:GetItemsIterator() do
        if not item.InSlot:IsDepositorySlot() and item:GetFeatureType() == EFeatureType.Key then
            table.insert(ret, item.id)
        end
    end
    ret = table.unique(ret)
    return ret
end

extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_DepositLogic")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_Network")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_CurrencyLogic")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_SlotLogic")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_LabelLogic")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_SortLogic")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_MP")
extendclass(InventoryServer, require "DFM.Business.ServerCenter.InventoryServer_SkinLogic")

return InventoryServer