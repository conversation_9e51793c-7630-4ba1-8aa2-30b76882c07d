----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 回流活动SOL/MP大厅签到+引导到活动主界面弹窗

local PlayerReturnConfig        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig" 
local PlayerReturnStatistics    = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local InputSummaryItemHD        = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local GuideConfig               = require "DFM.Business.Module.GuideModule.GuideConfig"
local AnimManager               = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local Promise                   = require "DFM.Business.DataStruct.Common.Base.Promise"
local InputBindingAgent       = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local OnceListener              = require "DFM.Business.DataStruct.Common.Agent.Event.OnceListener"
local PlayerReturnConfig        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local IPlayerReturnSubActivity  = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"
local WidgetUtil                = require "DFM.YxFramework.Util.WidgetUtil"

local function ShowRewardsByDataChange(propChanges)
    if propChanges ~= nil and propChanges.prop_changes ~= nil or propChanges.currency_changes ~= nil then
        local itemList = {}
        for i = 1, #propChanges.prop_changes, 1 do
            local propInfo = propChanges.prop_changes[i].prop
            local giftItem = ItemBase:New(propInfo.id, propInfo.num, propInfo.gid, false)
            giftItem:SetRawPropInfo(propInfo)
            --干员没有详情页，暂时屏蔽
            if giftItem.itemMainType ~= EItemType.Hero then
                table.insert(itemList, giftItem)
            end
        end
        local currency_changes = propChanges.currency_changes
        for _, currencyChange in ipairs(currency_changes) do
            if currencyChange.delta > 0 then
                local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
                table.insert(itemList, item)
            end
        end
        Module.Reward:OpenRewardPanel(nil, nil, itemList)
    end
end

---@class PlayerReturnLobbyPopup : LuaUIBaseView, HasAnimManager
local PlayerReturnLobbyPopup = ui("PlayerReturnLobbyPopup")

function PlayerReturnLobbyPopup:Ctor()
    -- 初始化辅助功能
    self._inputMgr    = InputBindingAgent.New(self)
    self._animManager = AnimManager.Create(self)

    -- 弹窗流程管理相关Promise
    self._popupPromise = nil            ---@type Promise
    self._interruptPromise = nil        ---@type Promise

    -- 绑定控件
    self._wtModeTag         = self:Wnd("WBP_Activity_ModeTag"           , UIWidgetBase)
    self._wtTitle           = self:Wnd("DFTextBlock_72"                 , UITextBlock)
    self._wtFxText          = self:Wnd("DFTextBlock_114"                , UITextBlock)
    self._wtDayCountLabel   = self:Wnd("DFTextBlock_213"                , UITextBlock)
    self._wtPlayerName      = self:Wnd("DFTextBlock"                    , UITextBlock)
    self._wtWelcomeMessage  = self:Wnd("DFTextBlock_1"                  , UITextBlock)
    self._wtRewardsList     = self:Wnd("DFHorizontalBox_68"             , UIWidgetBase)
    self._wtRewardsPanel    = self:Wnd("DFCanvasPanel_414"              , UIWidgetBase)
    self._wtConfirmButton   = self:Wnd("WBP_DFCommonButtonV1S3"         , DFCommonButtonOnly)
    self._wtHDBackKeyHint   = self:Wnd("WBP_TopBarHD_InputSummary"      , InputSummaryItemHD)
    self._wtCancelButton    = self:Wnd("wtCommonButtonClose"            , UIButton)
    
    -- 绑定事件
    self._wtConfirmButton:Event("OnClicked", self._OnConfirm, self)
end

---------------------------------------------------------------------------------------------
--#region 弹窗流程管理

---@param popupPromise      Promise
---@param interruptPromise  Promise
function PlayerReturnLobbyPopup:SetPopupPromises(popupPromise, interruptPromise)
    self._popupPromise     = popupPromise
    self._interruptPromise = interruptPromise
    if self._interruptPromise then
        self._interruptPromise:Then(CreateCPlusCallBack(self._OnInterrupt, self))
    end
end

---弹窗被打断
function PlayerReturnLobbyPopup:_OnInterrupt()
    Module.PlayerReturn:CloseLobbyPopup()
    -- 非正常关闭视为没有弹窗，下次继续弹窗
    local currMode = Server.ArmedForceServer:GetCurArmedForceMode()
    ---@type PlayerReturnSignInImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, currMode)
    actv:SetHasShownLobbyPopupToday(false)
    
    if self._popupPromise then
        self._popupPromise:Resolve()
    end
end

---用户取消本弹窗
---弹对局爽打弹窗，视用户选择，跳转到开局界面或继续弹窗流程，报告结果
function PlayerReturnLobbyPopup:_OnCancel()
    -- 首日，无论用户如何选择都相当于确认领取
    if self._bFirstDay then
        self:_OnConfirm()
        return
    end

    Module.PlayerReturn:CloseLobbyPopup()
    Module.PopupSequencer:DoPopupSequence(nil, nil, {"PlayerReturnDailyMatchPopup"}):Then(
        function(reply)
            -- 由对局爽打弹窗决定结算的去向
            if self._popupPromise then
                self._popupPromise:Resolve(reply)
            end
        end
    )
end

---用户确认本弹窗
---进行签到，然后弹对局爽打弹窗，视用户选择，跳转到回流界面或开局界面，报告结果
function PlayerReturnLobbyPopup:_OnConfirm()
    -- 关闭，报告弹窗结果
    Module.PlayerReturn:CloseLobbyPopup()

    -- 完成签到
    ---@type PlayerReturnSignInImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, Server.ArmedForceServer:GetCurArmedForceMode())
    local day = actv:GetSignInCount()
    Server.ActivityServer:SendSignAwardReq(actv:GetActivityInfo().actv_id, day,
        -- 等待签到完毕，展示获得奖励弹窗
        ---@param res pb_CSActivityRecvAttendAwardRes
        function(res)
            ShowRewardsByDataChange(res.data_change)
            Module.PlayerReturn:ShowTipsIfBoostReceived(res.data_change)
           
            OnceListener.New({Module.Reward.Config.Events.evtCloseRewardPanel},
                -- 等待奖励弹窗完毕，展示对局爽打弹窗
                function()
                    Module.PopupSequencer:ShowPopup("PlayerReturnDailyMatchPopup"):Then(
                        function(reply)
                            if reply.reply == EPopupReply.Continue then
                                -- 弹窗流程继续，则前往回流界面
                                local enterPanel = self._bFirstDay and ReturnActivityType.ReturnActivityTypeNormalTask or ReturnActivityType.ReturnActivityTypeMainPage
                                Module.PlayerReturn:ShowMainPanel(false, enterPanel)
                                if self._popupPromise then
                                    self._popupPromise:Resolve({reply = EPopupReply.Jump})
                                end
                            else
                                -- 对局爽打弹窗已跳转，透传弹窗结算结果到上一层
                                if self._popupPromise then
                                    self._popupPromise:Resolve({reply = reply})
                                end
                            end
                        end
                    )
                end
            )
        end
    , 1)
end

---返回：视为取消
function PlayerReturnLobbyPopup:OnNavBack()
    self:_OnCancel()
    return false
end

--#endregion
---------------------------------------------------------------------------------------------

function PlayerReturnLobbyPopup:OnOpen()
    local currentMode   = Server.ArmedForceServer:GetCurArmedForceMode()
    local actv          = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, currentMode) ---@type PlayerReturnSignInImpl
    local day           = actv:GetSignInCount()
    local activityInfo  = actv:GetActivityInfo()
    
    self._wtPlayerName:SetText(Server.RoleInfoServer.nickName .. ":")                           --玩家名称
    self._wtWelcomeMessage:SetText(activityInfo.desc)                                           --回流文案
    self._wtTitle:SetText(activityInfo.name)                                                    --标题
    self._wtConfirmButton:SetMainTitle(PlayerReturnConfig.Localization.LobbyPopup.Claim)	   --领取按键

    --奖励列表
    ---@type IVCommonItemTemplate[]
    self._wtRewardsListItems = {}                                                               

    local items = {}
    for i, award in ipairs(activityInfo.attend_info.states[day].daily_award) do
        items[i] = ItemBase:NewIns(award.prop.id, award.prop.num, award.prop.gid)
    end

    local size = IsHD() and 256 or 192
    for i = 1, #items do
        local weakIns, insID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtRewardsList)
        self._wtRewardsListItems[i] = getfromweak(weakIns)
        self._wtRewardsListItems[i]:InitItem(items[i])
        self._wtRewardsListItems[i]:SetSize(size, size)
        self._wtRewardsListItems[i]:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0)
    end
end

---入场动画
---@return Promise2
function PlayerReturnLobbyPopup:DoAnimation(bPlayVideo)
    local p = Promise:NewIns()

    if bPlayVideo then
        local t = os.time()
        local videoLength = -1000

        ---@diagnostic disable-next-line: missing-parameter
        Module.CommonWidget:ShowFullScreenVideoView("Activity_Return_She3", false, true, 
            function()
                ---@type PlayerReturnSignInImpl
                local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend)
                actv:SetIntroVideoPlayed(true)

                -- 上报，播放时间和视频时间相差不到 1 秒视为完播，时间固定为 99
                -- 否则上报时间为播放的秒数
                local statID
                local playedLength = os.time() - t
                if math.abs(videoLength - playedLength) < 1 then
                    statID = PlayerReturnStatistics:GetCGTimeStatID(99)
                else
                    statID = PlayerReturnStatistics:GetCGTimeStatID(playedLength)
                end
                LogAnalysisTool.SignButtonClicked(statID)

                p:Resolve()
            end, 
            nil, nil, nil, nil, 
            function(len) videoLength = len end
        )

        return p
    else
        Facade.SoundManager:PlayUIAudioEvent(PlayerReturnConfig.Audio.LobbyPopupNormal)
        p:Resolve()
        return p
    end
end

function PlayerReturnLobbyPopup:SetUpInputBinding()
    -- 初始化输入绑定

    -- 确认
    self._inputMgr:AddBinding(
        "ConfirmBtn", -- id
        {
            actionName      = "Common_ButtonLeft_Gamepad", -- X键
            callback        = self._OnConfirm,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop
        }
    )
    self._wtConfirmButton:SetDisplayInputAction("Common_ButtonLeft_Gamepad", nil, nil, true)

    -- 跳过
    self._inputMgr:AddBinding(
        "Skip", -- id
        {
            actionName      = "JumpOver", -- A键
            callback        = self._OnCancel,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop,
        }
    )
end

function PlayerReturnLobbyPopup:OnShowBegin()
    LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetLobbyPopupStatID())

    ---@type PlayerReturnSignInImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeAttend, Server.ArmedForceServer:GetCurArmedForceMode()) 
    local day = actv:GetSignInCount()
    self._bFirstDay = (day == 1)
    self._bPlayVideo = self._bFirstDay and (not actv:IsIntroVideoPlayed())

    local p = self:DoAnimation(self._bPlayVideo)
    p:Then(
        function()
            self:PlayAnimThen(self.WBP_Reflow_PopWindow_in)
            if self._bFirstDay then
                actv:CheckAndPlayVOFirstDayPopup()
            end
            self:SetUpInputBinding()
        end
    )

    if IsHD() then
        local cb = CreateCallBack(self._OnCancel, self)
        self._wtHDBackKeyHint:SetData("JumpOver", cb) -- 在Ctor调用文本显示会不对，只能放到这里
    else
        self._wtCancelButton:Event("OnClicked", self._OnCancel, self)
    end
end

function PlayerReturnLobbyPopup:OnHideBegin()
    self._inputMgr:ClearAll()
end

function PlayerReturnLobbyPopup:OnClose()
    self:RemoveAllLuaEvent()
end

return PlayerReturnLobbyPopup
