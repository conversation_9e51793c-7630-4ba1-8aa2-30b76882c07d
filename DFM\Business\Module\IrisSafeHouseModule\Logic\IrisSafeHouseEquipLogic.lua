local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE -----------
----- LOG FUNCTION AUTO GENERATE END -----------

local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ULuaSubsystem = import "LuaSubsystem"
local ESwitchWeaponContext = import "ESwitchWeaponContext"
local UGPCharacterAudioProxy = import "GPCharacterAudioProxy"
local UGameplayStatics = import "GameplayStatics"
local FCommercializationItemInfo = import "CommercializationItemInfo"

local function log(...)
    loginfo("[IrisSafeHouseEquipLogic]", ...)
end

local IrisSafeHouseEquipLogic = {}

IrisSafeHouseEquipLogic.WEAPON_SLOT_TYPES = {
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Pistrol,
    ESlotType.MeleeWeapon
}

IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
IrisSafeHouseEquipLogic.lastShowWeaponGID = 0
IrisSafeHouseEquipLogic.lastHeroId = 0
IrisSafeHouseEquipLogic.lastFashionId = 0
IrisSafeHouseEquipLogic.lastWatchId = 0

function IrisSafeHouseEquipLogic.Reset()
    IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
    IrisSafeHouseEquipLogic.lastShowWeaponGID = 0
end

function IrisSafeHouseEquipLogic.InitCharacterEquip(bForceRefreshAllEquip)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        return false
    end
    local dfmCharacter = localCtrl:GetPawn()
    if not isvalid(dfmCharacter) then
        return false
    end
    --只有单机才生效，非单机时直接返回true
    if not dfmCharacter:HasAuthority() then
        return true
    end

    IrisSafeHouseEquipLogic._OnSOLUsedHeroIdChanged()

    --装备和武器
    if IrisSafeHouseEquipLogic.bShouldRefreshAllEquip or bForceRefreshAllEquip then
        IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = false
        IrisSafeHouseEquipLogic.RefreshAllEquip()
    end
    return true
end

function IrisSafeHouseEquipLogic.InitCommertialRoulette()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    local dfmCharacter = localCtrl:GetPawn()
    if not isvalid(dfmCharacter) then
        return false
    end

    local heroIdStr = Server.HeroServer:GetCurUsedHeroId()
    ---@type table<number, pb_CSHeroAccessory>
    local accessories = Server.HeroServer:GetAccessoriesInRoulette(heroIdStr)

    local commertialComp = dfmCharacter.DFCommercializationComponent
    local items = {}
    for _, accessory in pairs(accessories) do
        local item = accessory.item
        local propId = item.prop_id
        local slot = item.slot

        local commercializationItemInfo = FCommercializationItemInfo()
        commercializationItemInfo.HeroId = heroIdStr
        commercializationItemInfo.ItemId = propId
        commercializationItemInfo.Slot = slot
        table.insert(items, commercializationItemInfo)
    end
    commertialComp.CommercializationItems = items
    commertialComp:OnItemsUpdated()

    return true
end

function IrisSafeHouseEquipLogic.AddListener()
    -- Server.InventoryServer.Events.evtInventoryFetchFinished:AddListener(IrisSafeHouseEquipLogic.RefreshAllEquip)
    -- Server.InventoryServer.Events.evtPostUpdateDeposData:AddListener(IrisSafeHouseEquipLogic.RefreshAllEquip)
    Server.InventoryServer.Events.evtItemMove:AddListener(IrisSafeHouseEquipLogic._OnItemMove)
    Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged:AddListener(IrisSafeHouseEquipLogic._OnArmedForceRentalStatusChanged)
    Server.ArmedForceServer.Events.evtRentalApplySuccess:AddListener(IrisSafeHouseEquipLogic._OnRentalApplySuccess)
    -- Server.HeroServer.Events.evtSOLUsedHeroIdChanged:AddListener(IrisSafeHouseEquipLogic._OnSOLUsedHeroIdChanged)
    Server.HeroServer.Events.evtHeroFashionPriorChange:AddListener(IrisSafeHouseEquipLogic._OnHeroFashionPriorChange)
    ULuaSubsystem.Get().OnLuaClientSeamlessTravelEnd:Add(IrisSafeHouseEquipLogic.InitCharacterEquip)
end

function IrisSafeHouseEquipLogic.RemoveListener()
    -- Server.InventoryServer.Events.evtInventoryFetchFinished:RemoveListener(IrisSafeHouseEquipLogic.RefreshAllEquip)
    -- Server.InventoryServer.Events.evtPostUpdateDeposData:RemoveListener(IrisSafeHouseEquipLogic.RefreshAllEquip)
    Server.InventoryServer.Events.evtItemMove:RemoveListener(IrisSafeHouseEquipLogic._OnItemMove)
    Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged:RemoveListener(IrisSafeHouseEquipLogic._OnArmedForceRentalStatusChanged)
    Server.ArmedForceServer.Events.evtRentalApplySuccess:RemoveListener(IrisSafeHouseEquipLogic._OnRentalApplySuccess)
    -- Server.HeroServer.Events.evtSOLUsedHeroIdChanged:RemoveListener(IrisSafeHouseEquipLogic._OnSOLUsedHeroIdChanged)
    Server.HeroServer.Events.evtHeroFashionPriorChange:RemoveListener(IrisSafeHouseEquipLogic._OnHeroFashionPriorChange)
    ULuaSubsystem.Get().OnLuaClientSeamlessTravelEnd:Remove(IrisSafeHouseEquipLogic.InitCharacterEquip)
end

function IrisSafeHouseEquipLogic.RefreshAllEquip()
    log("RefreshAllEquip")

    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    local dfmCharacter = localCtrl:GetPawn()
    if not isvalid(dfmCharacter) then
        return
    end
    -- 只有单机才生效，非单机时直接返回true
    if not dfmCharacter:HasAuthority() then
        return
    end

    -- 清理道具
    local inventoryMgr = UE.GameplayBlueprintHelper.FindComponentByClass(dfmCharacter, UE.InventoryManager)
    if inventoryMgr then
        inventoryMgr:ClearAllInventory()
    end

    -- 添加武器
    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(dfmCharacter, UE.WeaponManagerComponent)
    if not isvalid(weaponCmp) then
        return
    end
    weaponCmp:RemoveAllWeapon(true)

    local switchToWeaponType = nil
    IrisSafeHouseEquipLogic.lastShowWeaponGID = 0

    local weaponItem = IrisSafeHouseEquipLogic.GetCurrentShouldShowWeaponItem()
    if weaponItem then
        IrisSafeHouseEquipLogic.lastShowWeaponGID = weaponItem.gid
        switchToWeaponType = weaponItem.InSlot.SlotType

        local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(weaponItem.rawPropInfo)
        local newWeapon = nil
        weaponCmp:BP_AddWeaponDesc(weaponDesc, 0, switchToWeaponType, newWeapon)
    end

    if switchToWeaponType then
        weaponCmp:CancelSwitchCurrentWeapon()
        weaponCmp:SwitchToWeapon(ESwitchWeaponContext.SafeHouse, switchToWeaponType, UE.EWeaponSwitchType.Instant)
    end
end

function IrisSafeHouseEquipLogic.GetCurrentShouldShowWeaponItem()
    local inventorySvr = Server.InventoryServer
    local switchToWeaponType = nil
    local targetSlotGroup = ESlotGroup.Player
    if Server.ArmedForceServer:CheckIsRentalStatus() then
        targetSlotGroup = ESlotGroup.MainRental
    end

    for _, slotType in ipairs(IrisSafeHouseEquipLogic.WEAPON_SLOT_TYPES) do
        local slot = inventorySvr:GetSlot(slotType, targetSlotGroup)
        if slot then
            ---@type ItemBase
            local item = slot:GetEquipItem()
            if item then
                return item
            end
        end
    end

    return nil
end

function IrisSafeHouseEquipLogic.PreloadCharacterMesh()
    return IrisSafeHouseEquipLogic._OnSOLUsedHeroIdChanged()
end

local function getCurrentClientWatchID(dfmCharacter, heroId)
    local currentUsingHeroFashionData = dfmCharacter.CharacterFashionComponent.currentUsingHeroFashionData
    for _, data in currentUsingHeroFashionData:PairsLessGC() do
        if data.heroId == heroId then
            return data.watchItemId 
        end
    end

    return 0
end

--修改人物模型
function IrisSafeHouseEquipLogic._OnSOLUsedHeroIdChanged()
    -- local avatarId = 0
    -- local heroId = tonumber(Server.HeroServer:GetCurUsedHeroId())
    -- if Server.HeroServer:GetFashionPriorByCategory(FashionPriorCategory.FashionSOLHouse) then
    --     avatarId = Server.HeroServer:GetCurUsedHeroFashionId()
    -- else
    --     avatarId = heroId
    -- end

    local heroId = tonumber(Server.HeroServer:GetCurUsedHeroId())
    local fashionAvatarId = Server.HeroServer:GetCurUsedHeroFashionId()
    heroId = heroId or 0
    if heroId == 0 then
        heroId = IrisSafeHouseEquipLogic.lastHeroId
    end
    fashionAvatarId = fashionAvatarId or 0
    if fashionAvatarId == 0 then
        fashionAvatarId = IrisSafeHouseEquipLogic.lastFashionId
    end
    local watchId = Server.HeroServer:GetUsedWatchAccessory(fashionAvatarId)

    local dfmCharacter = Facade.GameFlowManager:GetCharacter()
    if dfmCharacter then
        local currentHeroId = dfmCharacter.CharacterFashionComponent.currentCharacterHeroID
        local currentFashionId = dfmCharacter:GetCurrentClientCharacterAvatarID()
        local currentWatchId = getCurrentClientWatchID(dfmCharacter, heroId)
        currentFashionId = tonumber(currentFashionId)
        if heroId == currentHeroId 
        and fashionAvatarId == currentFashionId
        and watchId == currentWatchId then
            log("_OnSOLUsedHeroIdChanged don't change")

            return false
        end

        -- 更新PlayerState
        local ps = Facade.GameFlowManager:GetPlayerState()
        if ps then
            ps.HeroID = heroId or 0
            ps:OnRep_HeroIDChanged()

            UGPCharacterAudioProxy.RegisterCharacterVOService(dfmCharacter)
        end

        dfmCharacter.CharacterFashionComponent:UpdateUsingHeroFashionData(heroId, fashionAvatarId, true, true, true)
        dfmCharacter.CharacterFashionComponent:UpdateUsingHeroFashionWatchData(heroId, watchId)

        dfmCharacter.CharacterFashionComponent.currentCharacterHeroID = heroId
        dfmCharacter.CharacterFashionComponent:OnRep_CharacterAvatarID()

        IrisSafeHouseEquipLogic.lastHeroId = heroId
        IrisSafeHouseEquipLogic.lastFashionId = fashionAvatarId
        IrisSafeHouseEquipLogic.lastWatchId = watchId
        log("_OnSOLUsedHeroIdChanged", heroId, fashionAvatarId)

        return true
    end

    return false
end

function IrisSafeHouseEquipLogic._OnHeroFashionPriorChange(iCategory)
    if FashionPriorCategory.FashionSOLHouse == iCategory then
        local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        local dfmCharacter = localCtrl:GetPawn()

        -- local avatarId = 0
        -- if Server.HeroServer:GetFashionPriorByCategory(FashionPriorCategory.FashionSOLHouse) then
        --     avatarId = Server.HeroServer:GetCurUsedHeroFashionId()
        -- else
        --     avatarId = tonumber(Server.HeroServer:GetCurUsedHeroId())
        -- end
        local heroId = tonumber(Server.HeroServer:GetCurUsedHeroId())
        local fashionAvatarId = Server.HeroServer:GetCurUsedHeroFashionId()
        dfmCharacter.CharacterFashionComponent:UpdateUsingHeroFashionData(heroId, fashionAvatarId, true, true, true)
        dfmCharacter.CharacterFashionComponent:UpdateUsingHeroFashionWatchData(heroId, Server.HeroServer:GetUsedWatchAccessory(fashionAvatarId))
        loginfo("IrisSafeHouseEquipLogic._OnHeroFashionPriorChange ", heroId, fashionAvatarId)
        dfmCharacter.CharacterFashionComponent.currentCharacterHeroID = heroId
        dfmCharacter.CharacterFashionComponent:OnRep_CharacterAvatarID()
    end
end

---@param moveItemInfo itemMoveInfo
function IrisSafeHouseEquipLogic._OnItemMove(moveItemInfo)
    local bInternalMove = moveItemInfo.bInternalMove
    local item = moveItemInfo.item
    local oldSlot = moveItemInfo.OldLoc and moveItemInfo.OldLoc.ItemSlot
    local newSlot = moveItemInfo.NewLoc and moveItemInfo.NewLoc.ItemSlot

    if item.gid == IrisSafeHouseEquipLogic.lastShowWeaponGID then
        log("_OnItemMove weapon update")
        IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
    else
        local weaponItem = IrisSafeHouseEquipLogic.GetCurrentShouldShowWeaponItem()
        if weaponItem and weaponItem.gid ~= IrisSafeHouseEquipLogic.lastShowWeaponGID then
            log("_OnItemMove weapon change")
            IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
        end
    end
end

function IrisSafeHouseEquipLogic._OnArmedForceRentalStatusChanged(bRentalStatus)
    log("_OnArmedForceRentalStatusChanged", bRentalStatus)
    IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
end

function IrisSafeHouseEquipLogic._OnRentalApplySuccess(rentalData)
    log("_OnRentalApplySuccess")
    if rentalData then
        IrisSafeHouseEquipLogic.bShouldRefreshAllEquip = true
    end
end

--缓存配装价值
function IrisSafeHouseEquipLogic.SetEquipTotalValue(totalValue)
    Module.IrisSafeHouse.Field:SetEquipTotalValue(totalValue)
end

function IrisSafeHouseEquipLogic.GetEquipTotalValue()
    return Module.IrisSafeHouse.Field:GetEquipTotalValue()
end

return IrisSafeHouseEquipLogic