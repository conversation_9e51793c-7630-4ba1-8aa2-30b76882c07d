----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionRoomField : FieldBase
local CollectionRoomField = class("CollectionRoomField", require"DFM.YxFramework.Managers.Module.FieldBase")

---@class CabinetGridAddress
---@field cabinetType EShowCabinetType
---@field cabinetId number
---@field gridId number

--- Field用于存储加工、排序过的二级数据、缓存数据、或者临时数据
function CollectionRoomField:Ctor()
    self.collectionDIYCabinetData = {}
    self.limitedGridId = {}
    self.gridIdToItemMap = {}
    self.upgradeItems = {}
    self.displayGridIdToLevel = {}
    self.itemIdToSpecialGridIdMap = {}
    self.bFirstEnterAfterLogin = true
    self:ResetField()
end

function CollectionRoomField:ResetField()
    self.mainPanel = nil
    self.fromSafeHouseLoc = nil
    self.fromSafeHouseRot = nil
    self.framingTaskHandles = {}
    self.bIsSpawnOrDestroyingAllPickups = false
    self.customLevelsLoadedCallback = nil
    self.bIsLoadingLevels = false
    self.mainPanelCurrentCameraId = nil
    self.playedTriggerNames = {}
    self.lastDragItemGid = nil
    self.limitedPickupActor = nil
    self.bSpecialCabinetAnimHasTriggered = false
    ---@type CabinetGridAddress
    self.currFocusGridAddr = nil
    ---@type CabinetGridAddress
    self.targetFocusGridAddr = nil
    self.enterFrom = nil
    self.extraParam = nil
    self.bHasPlayedWholeLevelSequence = false
    self.enterCollectionRoomMethod = nil
    self.enterCollectionRoomTime = nil
    self.pickupTimes = 0
    self.enterMainPanelMethod = nil
    self.fromSafeHouseLoc = nil
    self.fromSafeHouseRot = nil
    self.displayGridLevelUpInfo = {}
    self.bCallbackIsEnterCollectionRoom = false
    self.bIsFirstLoadLevel = true
    self.oldRadiusThreshold = -1
    -- 是否在打开收藏室场景或界面的异步流程中
    self.bIsInCollectionRoomProcess = false
    self.bInCollectionRoomSubStage = false
    self.bIsPopingAllUIForEnterCollectionRoom = false
    self.diyCabinetInteractNum = {}
    self:CancelLoadingCharacterMeshTimer()
end

function CollectionRoomField:LoadDataTable()
    if self._hasLoadDataTable then
        return
    end
    self._hasLoadDataTable = true
    self:_GetDIYCabinetBoothInfo()
    self:_LoadDisplayCabinetSToCMap()
    self:GetCabinetUpgradeData()
    self:LoadDisplayCabinetLevels()
end

function CollectionRoomField:CancelLoadingCharacterMeshTimer()
    if self.loadingCharacterMeshTimer then
        Timer.CancelDelay(self.loadingCharacterMeshTimer)
        self.loadingCharacterMeshTimer = nil
    end
end

function CollectionRoomField:SetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId, handle)
    local key = cabinetType * 100 * 1000 + cabinetId * 1000 + cabinetGridId
    self.framingTaskHandles[key] = handle
end

function CollectionRoomField:GetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId)
    local key = cabinetType * 100 * 1000 + cabinetId * 1000 + cabinetGridId
    return self.framingTaskHandles[key]
end

function CollectionRoomField:CancelAllFramingTasks()
    for _, handle in pairs(self.framingTaskHandles) do
        Facade.LuaFramingManager:CancelFrameTask(handle)
    end
    table.empty(self.framingTaskHandles)
end

function CollectionRoomField:HasNoFramingTasks()
    return table.isempty(self.framingTaskHandles)
end

-----------------------------------------------------------------------
--region   收藏柜（图鉴展柜）

function CollectionRoomField:_LoadDisplayCabinetSToCMap()
    self.limitedGridId = {}
    self.gridIdToItemMap = {}
    self.itemIdToSpecialGridIdMap = {}
    local table = Facade.TableManager:GetTable("CollectionRoomCollection")
    if table then
        for _, config in pairs(table) do
            if not config.IsLimited then
                if config.SlotType == 1 then
                    declare_if_nil(self.gridIdToItemMap, EShowCabinetType.Display, {})
                    self.gridIdToItemMap[EShowCabinetType.Display][config.SlotID] = config.ItemID
                elseif config.SlotType == 2 then
                    declare_if_nil(self.gridIdToItemMap, EShowCabinetType.Special, {})
                    self.gridIdToItemMap[EShowCabinetType.Special][config.SlotID] = config.ItemID
                    self.itemIdToSpecialGridIdMap[tonumber(config.ItemID)] = config.SlotID
                end
            else
                self.limitedGridId[config.SlotID] = true
            end
        end
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region    DIY展柜

function CollectionRoomField:_GetDIYCabinetBoothInfo()
    self.collectionDIYCabinetData = {}
    local collectionDIYCabinetInfo = Facade.TableManager:GetTable("CollectionDIYCabinet")

    -- 表中包含所有展柜的信息
    --[[
        collectionDIYCabinetInfo.DIYRackID：柜子id
        collectionDIYCabinetInfo.SortID：展位分类id
        collectionDIYCabinetInfo.RackSlotID：格子id
    ]]--

    for _, tableInfo in pairs(collectionDIYCabinetInfo) do
        loginfo("CollectionRoomField:_GetDIYCabinetBoothInfo")
        declare_if_nil(self.collectionDIYCabinetData, tableInfo.DIYRackID, {})
        declare_if_nil(self.collectionDIYCabinetData[tableInfo.DIYRackID], tableInfo.SortID, {})
        -- local rackTable = self.collectionDIYCabinetData[tableInfo.DIYRackID][tableInfo.SortID]
        -- 将展位信息数据放入sortTable中
        local rackSlotInfo = {}
        rackSlotInfo.RackSlotID = tableInfo.RackSlotID
        rackSlotInfo.SlotType = tableInfo.SlotType
        rackSlotInfo.BannedItemID = tableInfo.BannedItemID
        rackSlotInfo.DisplayID = string.split(tableInfo.DisplayID, "-")
        table.map(rackSlotInfo.DisplayID, function(v, k) return tonumber(v) end)
        table.insert(self.collectionDIYCabinetData[tableInfo.DIYRackID][tableInfo.SortID], rackSlotInfo)
    end
    for _, cabinetData in pairs(self.collectionDIYCabinetData) do
        for _, sortGroup in pairs(cabinetData) do
            table.sort(sortGroup, function(a, b)
                if a == nil or b == nil or a == b then
                    return false
                end
                if a.DisplayID == nil or b.DisplayID == nil then
                    return false
                end
                if a.DisplayID[1] == b.DisplayID[1] and a.DisplayID[2] == b.DisplayID[2] then
                    return false
                end
                if a.DisplayID[1] == nil or a.DisplayID[2] == nil or b.DisplayID[1] == nil or b.DisplayID[2] == nil then
                    return false
                end
                return a.DisplayID[1] < b.DisplayID[1] or (a.DisplayID[1] == b.DisplayID[1] and a.DisplayID[2] < b.DisplayID[2])
            end)
        end
    end
end

--endregion
-----------------------------------------------------------------------

-- 展柜升级数据缓存
function CollectionRoomField:GetCabinetUpgradeData()
    self.upgradeItems = {}
    local gridInfo = Facade.TableManager:GetTable("CollectionRoomCollection")
    if gridInfo then
        for _, config in ipairs(gridInfo) do
            declare_if_nil(self.upgradeItems, config.SlotType, {})
            declare_if_nil(self.upgradeItems[config.SlotType], config.SlotID, {})
            if config.Level1Item and config.Level1ItemBind and config.Level1ItemQuan then
                local itemList1 = {}
                itemList1.items = string.split(config.Level1Item, ",")
                itemList1.bindInfo = string.split(config.Level1ItemBind, ",")
                itemList1.quantity = string.split(config.Level1ItemQuan, ",")
                table.insert(self.upgradeItems[config.SlotType][config.SlotID], itemList1)
            end
            if config.Level2Item and config.Level2ItemBind and config.Level2ItemQuan then
                local itemList2 = {}
                itemList2.items = string.split(config.Level2Item, ",")
                itemList2.bindInfo = string.split(config.Level2ItemBind, ",")
                itemList2.quantity = string.split(config.Level2ItemQuan, ",")
                table.insert(self.upgradeItems[config.SlotType][config.SlotID], itemList2)
            end
        end
    end
end

function CollectionRoomField:LoadDisplayCabinetLevels()
    self.displayGridIdToLevel = {}
    local str = Facade.ConfigManager:GetUserString("DisplayCabinetLevels", "")
    loginfo("CollectionRoomLogic.LoadDisplayCabinetLevels", str)
    local array = string.split(str, ",")
    for _, pair in ipairs(array) do
        local arr = string.split(pair, ":")
        if #arr == 2 then
            self.displayGridIdToLevel[tonumber(arr[1])] = tonumber(arr[2])
        end
    end
end

return CollectionRoomField
