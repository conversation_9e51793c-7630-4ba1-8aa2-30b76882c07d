----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
-- END MODIFICATION

---@class CollectionCommonVideoListView : LuaUIBaseWindow
local CollectionCommonVideoListView = ui("CollectionCommonVideoListView", require("DFM.YxFramework.Managers.UI.LuaUIBaseWindow"))
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local CommonVideoComponent = require "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoComponent"

function CollectionCommonVideoListView:Ctor()
    self._wtSkinEffectGridBox = UIUtil.WndScrollGridBox(self, "wtSkinEffectGridBox", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtVideoPlayer = self:Wnd("wtVideoPlayer", CommonVideoComponent)
    self._wtEffectNameTxt = self:Wnd("wtEffectNameTxt", UITextBlock)
    self._wtEffectDesTxt = self:Wnd("wtEffectDesTxt", UITextBlock)
    self._wtVideoPlayer:InitComponent(true)
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarTitle(self, CollectionConfig.Loc.PreviewEffects)
    self._selectedPos = -1
    self._selectedCell = nil
end


function CollectionCommonVideoListView:OnInitExtraData(videoNames, videoDescs, videoPaths)
    self._VideoNames = videoNames
    self._VideoDescs = videoDescs
    self._VideoPaths = videoPaths
end

---@overload fun(LuaUIBaseView, OnOpen)
function CollectionCommonVideoListView:OnOpen()
    self:_AddListeners()
end

---@overload fun(LuaUIBaseView, OnClose)
function CollectionCommonVideoListView:OnClose()
    self:RemoveAllLuaEvent()
    self._wtVideoPlayer:Stop()
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionCommonVideoListView:OnShowBegin()
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:_OnRefreshItemDetail()
    self:_EnableGamepadFeature()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionCommonVideoListView:OnHideBegin()
    self._wtVideoPlayer:Stop()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionCommonVideoListView:OnAnimFinished(anim)
    if anim == self.WBP_CollectionsVideos_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end


function CollectionCommonVideoListView:_AddListeners()
end

function CollectionCommonVideoListView:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtNavGroup then
    	self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSkinEffectGridBox, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self._wtSkinEffectGridBox)
            self._wtNavGroup:SetScrollRecipient(self._wtSkinEffectGridBox)
            self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    CollectionLogic.RegStackUIInputSummary({
        {actionName = "Collection_PlayOrPause_Gamepad", func = self._OnPlayOrPause, caller = self, bUIOnly = false, bHideIcon = false},
    }, false)
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionCommonVideoListView:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self._wtNavGroup = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionCommonVideoListView:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self:_EnableGamepadFeature()
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    else
        self:_DisableGamepadFeature()
    end
    self._wtSkinEffectGridBox:RefreshVisibleItems()
end

function CollectionCommonVideoListView:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup) 
    end
end

function CollectionCommonVideoListView:_OnPlayOrPause()
    self._wtVideoPlayer:PlayOrPauseVideo()
end

function CollectionCommonVideoListView:_OnAdjustProgress()
end

function CollectionCommonVideoListView:_OnRefreshItemDetail()
    if #self._VideoNames > 0 then
        self._selectedPos = 0
    end
    self._wtEffectNameTxt:SetText(self._VideoNames[self._selectedPos + 1] or CollectionConfig.Loc.SkinEffect)
    self._wtEffectDesTxt:SetText(self._VideoDescs[self._selectedPos + 1] or CollectionConfig.Loc.SkinEffect)
    if self._VideoPaths[self._selectedPos + 1] then
        self._wtVideoPlayer:Play(self._VideoPaths[self._selectedPos + 1])
    else
        self._wtVideoPlayer:Stop()
    end
    self._wtSkinEffectGridBox:RefreshAllItems()
end

function CollectionCommonVideoListView:_OnGetItemsCount()
    if self._VideoNames then
        return #self._VideoNames
    else
        return 0
    end
end

function CollectionCommonVideoListView:_OnProcessItemWidget(position, itemWidget)
    local index = position + 1
    local videoName = self._VideoNames[index]
    itemWidget:RemoveEvent("OnUncheckedClicked", self._OnSkinEffectItemClick, self)
    itemWidget:Event("OnUncheckedClicked", self._OnSkinEffectItemClick, self, position, itemWidget)
    itemWidget:SetMainTitle(videoName or CollectionConfig.Loc.Unknown)
    itemWidget:SetIsChecked(self._selectedPos == position)
    if self._selectedPos == position then
        self._selectedCell = itemWidget
    end
end

function CollectionCommonVideoListView:_OnSkinEffectItemClick(position, itemWidget)
    if self._selectedPos ~= position then
        if isvalid(self._selectedCell) and self._selectedPos ~= position then
            self._selectedCell:SetIsChecked(false)
        end
        self._selectedPos = position
        self._selectedCell = itemWidget
        self._selectedCell:SetIsChecked(true)
        self._wtEffectNameTxt:SetText(self._VideoNames[self._selectedPos + 1] or CollectionConfig.Loc.SkinEffect)
        self._wtEffectDesTxt:SetText(self._VideoDescs[self._selectedPos + 1] or CollectionConfig.Loc.SkinEffect)
        if self._VideoPaths[self._selectedPos + 1] then
            self._wtVideoPlayer:Play(self._VideoPaths[self._selectedPos + 1])
        else
            self._wtVideoPlayer:Stop()
        end
    end
end

return CollectionCommonVideoListView
