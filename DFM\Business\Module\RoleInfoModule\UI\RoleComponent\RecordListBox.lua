----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
----- LOG FUNCTION AUTO GENERATE END -----------


local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local RecordListBox = ui("RecordListBox")

function RecordListBox:Ctor()
    self._wtResult = self:Wnd("wtResult", UIWidgetBase)
    self._wtModelMap = self:Wnd("wtModelMap", UIWidgetBase)
    self._wtTime = self:Wnd("wtTime", UIWidgetBase)
    self._wtScoreText = self:Wnd("wtScoreText", UITextBlock)
    self._wtKillNum = self:Wnd("wtKillNum", UIWidgetBase)
    self._wtDetailBtn = self:Wnd("wtDetailBtn", UIButton)
    self._wtHeroAvatar = self:Wnd("wtHeroAvatar", UIImage)
    self._wtMoreAchievement = self:Wnd("wtMoreAchievement", UIWidgetBase)

    self._wtRankPanel = self:Wnd("wtRankPanel", UIWidgetBase)
    self._wtRank = self:Wnd("wtRank", UIWidgetBase)
    self._wtRankIconItem = self:Wnd("wtRankIcon", UIWidgetBase)
    self._wtSoreUpdata = self:Wnd("wtSoreUpdata", UITextBlock)
    self._wtStartNum = self:Wnd("wtStartNum", UIWidgetBase)
    self._wtImageStar = self:Wnd("wtImageStar", UIWidgetBase)
    self._wtHeroUpIcon = self:Wnd("wtHeroUpIcon", UIImage)
    self._wtRankNore = self:Wnd("wtRankNore", UIWidgetBase)

    self._wtAchievement = UIUtil.WndScrollGridBox(self, "wtAchievement", self._OnGetAchieveCount,
        self._OnProcessItemWidget)

    self._wtRaidLevelImage = self:Wnd("DFImage_RAIDLevel", UIImage)

    self._wtDetailBtn:Event("OnUnHovered", self.OnUnHoverDetailbtn, self)
    self._wtDetailBtn:Event("OnHovered", self.OnHoverDetailbtn, self)
    self._type = 0
end

function RecordListBox:OnOpen()

end

function RecordListBox:OnInitExtraData()

end

function RecordListBox:OnUnHoverDetailbtn()
    self:SetStyle(0)
end

function RecordListBox:OnHoverDetailbtn()
    self:SetStyle(1)
end

function RecordListBox:ShowSolUI(matchInfo, playerId)
    self._playerId = playerId
    self._matchInfo = matchInfo
    self._solInfo = matchInfo.sol
    RoleInfoLogic.SortAchievement(self._matchInfo.achievements)
    self._wtRaidLevelImage:Collapsed()
    self._wtKillNum:SetText(self._solInfo.killPlayer + self._solInfo.killAI + self._solInfo.killBoss)
    if self._solInfo.gained_price then
        self._wtScoreText:SetText(MathUtil.GetNumberFormatStr(self._solInfo.gained_price))
    end
    self._wtResult:SetText(RoleInfoLogic.GetResultStr(matchInfo.game_result, matchInfo.match_mode.match_mode_id))
    self._wtResult:SetColorAndOpacity(Module.RoleInfo.Config.Loc.SolResultColor[matchInfo.game_result])
    self._keyTable = StringUtil.StringSplit(matchInfo.key, "_")
    self._key = matchInfo.key
    local time = TimeUtil.TransTimestamp2MMDDHHMMStr(self._matchInfo.match_start_time)
    self._wtModelMap:SetText(Module.GameMode:GetStandardMapNameByMatchModeId(matchInfo.match_mode.match_mode_id) ..
        " " .. time)
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OpenDetailSolRecord, self)
    self:RefreshMatchShow()
    self:RefreshRankShow(true)
    self:SetMode(0)
end

function RecordListBox:ShowMPUI(matchInfo, playerId)
    self._playerId = playerId
    self._matchInfo = matchInfo
    RoleInfoLogic.SortAchievement(self._matchInfo.achievements)
    self._mpInfo = matchInfo.mp
    self._keyTable = StringUtil.StringSplit(matchInfo.key, "_")
    self._key = matchInfo.key
    self._wtRaidLevelImage:Collapsed()
    if self._mpInfo.leave then
        self._wtResult:SetText(Module.RoleInfo.Config.Loc.RoleQuitTxt)
        self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
    else
        if self._mpInfo.is_winner then
            self._wtResult:SetText(Module.Friend.Config.TDMResult[0])
            self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("LightPositive"))
        else
            self._wtResult:SetText(Module.Friend.Config.TDMResult[1])
            self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
        end
    end

    local time = TimeUtil.TransTimestamp2MMDDHHMMStr(self._matchInfo.match_start_time)
    local MapTimeStr = Module.GameMode:GetStandardMapNameByMatchModeId(matchInfo.match_mode.match_mode_id) .. " " .. time
    self._wtModelMap:SetText(MapTimeStr)
    self._wtKillNum:SetText(self._mpInfo.kill)
    self._wtScoreText:SetText(self._mpInfo.score)
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OpenDetailMPRecord, self)

    self:RefreshMatchShow()
    self:RefreshRankShow(false)

    self:SetMode(1)
end

function RecordListBox:ShowRaidUI(matchInfo, playerId)
    self._playerId = playerId
    self._matchInfo = matchInfo
    self._raidInfo = matchInfo.raid
    self._key = matchInfo.key

    if self._raidInfo.level == 0 then
        self._wtRaidLevelImage:Collapsed()
    else
        self._wtRaidLevelImage:SelfHitTestInvisible()
        self._wtRaidLevelImage:AsyncSetImagePath(Module.SandBoxMap.Config.RaidFinishLevelIcon[self._raidInfo.level])
    end

    if self._raidInfo.leave then
        self._wtResult:SetText(Module.RoleInfo.Config.Loc.RoleQuitTxt)
        self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
    else
        if self._raidInfo.success then
            self._wtResult:SetText(Module.RoleInfo.Config.Loc.RaidGameSuccess)
            self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("LightPositive"))
        else
            self._wtResult:SetText(Module.RoleInfo.Config.Loc.RaidGameFail)
            self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
        end
    end
    local time = TimeUtil.TransTimestamp2MMDDHHMMStr(self._matchInfo.match_start_time)
    local MapTimeStr = Module.GameMode:GetStandardMapNameByMatchModeId(matchInfo.match_mode.match_mode_id) .. " " .. time
    self._wtModelMap:SetText(MapTimeStr)
    self._wtScoreText:SetText(self._raidInfo.kill_num)
    self._wtKillNum:SetText(self._raidInfo.assit_num)
    self._wtRankNore:Visible()
    self._wtRankPanel:Collapsed()
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OpenDetailRaidRecord, self)
    --self._wtDetailBtn:Event("OnClicked", self._OpenDetailArenaRecord,self)


    --self:RefreshMatchShow()
    local heroIcon = HeroHelperTool.GetHeroAvatarIcon(self._raidInfo.hero_id)
    if heroIcon then
        self._wtHeroAvatar:AsyncSetImagePath(heroIcon, true)
    end
    self:SetMode(2)
end

function RecordListBox:ShowArenaUI(matchInfo, playerId)
    self._playerId = playerId
    self._matchInfo = matchInfo
    self._arenaInfo = matchInfo.arena
    self._key = matchInfo.key
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OpenDetailArenaRecord, self)
    self._wtRankPanel:Collapsed()
    self._wtResult:SetText(Module.RoleInfo.Config.Loc.ArenaResultTxt[self._arenaInfo.result])
    if self._arenaInfo.result == ArenaResult.AR_Win then
        self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("LightPositive"))
    else
        self._wtResult:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
    end
    local time = TimeUtil.TransTimestamp2MMDDHHMMStr(self._matchInfo.match_start_time)
    local MapTimeStr = Module.GameMode:GetStandardMapNameByMatchModeId(matchInfo.match_mode.match_mode_id) .. " " .. time
    self._wtModelMap:SetText(MapTimeStr)
    local heroIcon = HeroHelperTool.GetHeroAvatarIcon(self._arenaInfo.hero_id)
    if heroIcon then
        self._wtHeroAvatar:AsyncSetImagePath(heroIcon, true)
    end
    self._wtScoreText:SetText(self._arenaInfo.kill)
    self._wtKillNum:SetText(self._arenaInfo.assist)
    self:SetMode(3)
end

function RecordListBox:RefreshMatchShow()
    self._wtAchievement:RefreshAllItems()
    local heroIcon = HeroHelperTool.GetHeroAvatarIcon(self._matchInfo.hero_id)
    if heroIcon then
        self._wtHeroAvatar:AsyncSetImagePath(heroIcon, true)
    end
end

function RecordListBox:_OnGetAchieveCount()
    local MAXNUMBER = 0
    if IsHD() then
        MAXNUMBER = 10
    else
        MAXNUMBER = 4
    end
    if #self._matchInfo.achievements > MAXNUMBER then
        self._wtMoreAchievement:Visible()
        self._wtMoreAchievement:SetText("+" .. #self._matchInfo.achievements - MAXNUMBER)
    else
        self._wtMoreAchievement:Collapsed()
    end
    return #self._matchInfo.achievements >= MAXNUMBER and MAXNUMBER or #self._matchInfo.achievements
end

function RecordListBox:_OnProcessItemWidget(index, widget)
    widget:SetInfoBySelf(self._matchInfo.achievements[index + 1].id)
end

function RecordListBox:RefreshRankShow(isSol)
    local rankInfo = nil
    local startNum = nil
    local lastRankInfo = nil
    local rankScore = self._matchInfo.ranked_score

    local ShowRank = function(bShow)
        if bShow then
            self._wtRankNore:Collapsed()
            self._wtRankPanel:Visible()
        else
            self._wtRankNore:Visible()
            self._wtRankPanel:Collapsed()
        end
    end

    local refreshSolRank = function()
        if self._matchInfo.is_ranked_match then
            ShowRank(true)

            rankInfo = Module.Ranking:GetMinorDataByScore(rankScore)
            startNum = Module.Ranking:GetStarNumByScore(rankScore)
            self._wtRank:SetText(rankInfo.RankName)
            self._wtRankIconItem:SetRankingIconByScore(rankScore)
            self._wtStartNum:SetText(startNum)
            self._wtImageStar:SelfHitTestInvisible()
            lastRankInfo = Module.Ranking:GetMinorDataByScore(rankScore -
                self._matchInfo.ranked_score_delta)
            if rankInfo.MinorRankID > lastRankInfo.MinorRankID then
                self._wtHeroUpIcon:Visible()
            else
                self._wtHeroUpIcon:Collapsed()
            end
        else
            ShowRank(false)
        end
    end

    local refreshMpRank = function()
        if (self._matchInfo.is_ranked_match or self._matchInfo.is_victory_unite_match) then
            ShowRank(true)
            local tournamentModule = Module.Tournament
            if self._matchInfo.is_ranked_match then
                rankInfo = tournamentModule:GetRankDataByScore(rankScore)
                lastRankInfo = tournamentModule:GetRankDataByScore(rankScore - self._matchInfo.ranked_score_delta)
                self._wtRankIconItem:SetTournamentIconByScore(rankScore)
            elseif self._matchInfo.is_victory_unite_match then
                rankInfo = tournamentModule:GetCommanderRankDataByScore(rankScore)
                lastRankInfo = tournamentModule:GetCommanderRankDataByScore(rankScore -
                    self._matchInfo.ranked_score_delta)
                self._wtRankIconItem:SetCommanderIconByScore(rankScore)
            end

            self._wtImageStar:SelfHitTestInvisible()
            self._wtStartNum:SetText(tournamentModule:GetStarNumByScore(rankScore) or "?")
            if rankInfo then
                self._wtRank:SetText(rankInfo.Name)
                if rankInfo.MinPoint > lastRankInfo.MinPoint then
                    self._wtHeroUpIcon:Visible()
                else
                    self._wtHeroUpIcon:Collapsed()
                end
            end
        else
            ShowRank(false)
        end
    end

    if isSol then
        refreshSolRank()
    else
        refreshMpRank()
    end

    if self._matchInfo.ranked_score_delta < 0 then
        self._wtSoreUpdata:SetText(string.format("%s(%s)", self._matchInfo.ranked_score,
            self._matchInfo.ranked_score_delta))
    else
        self._wtSoreUpdata:SetText(string.format(Module.RoleInfo.Config.Loc.RankScoreUpdateAdd,
            self._matchInfo.ranked_score, "+" .. self._matchInfo.ranked_score_delta))
    end
end

function RecordListBox:_OpenDetailMPRecord()
    Facade.UIManager:AsyncShowUI(IsHD() and UIName2ID.RecordDetailMPMainHD or UIName2ID.MPDetailRecord, nil, nil,
        self._key, self._matchInfo, self._playerId)
end

function RecordListBox:_OpenDetailSolRecord()
    if IsHD() then
        if self._playerId == Server.AccountServer:GetPlayerId() then
            Facade.UIManager:AsyncShowUI(UIName2ID.SolDetailMainPanelHD, nil, nil, self._key, self._matchInfo,
                self._solInfo, self._playerId)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.SolDetailRecord, nil, nil, self._key, self._matchInfo, self._solInfo,
                self._playerId)
        end
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SolDetailRecord, nil, nil, self._key, self._matchInfo, self._solInfo,
            self._playerId)
    end
end

function RecordListBox:_OpenDetailRaidRecord()
    Facade.UIManager:AsyncShowUI(UIName2ID.RaidDetailRecord, nil, nil, self._key, self._matchInfo, self._playerId)
end

function RecordListBox:_OpenDetailArenaRecord()
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.ArenaDetailMainPanel, nil, nil, self._key, self._matchInfo, self._playerId)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.ArenaDetailRecord, nil, nil, self._key, self._matchInfo, self._playerId)
    end
end

return RecordListBox

--27898748900
