----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

local QuestLogic = {}
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UGPBlueprintUtils = import "GPBlueprintUtils"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
------------------------------------ Event listener function ------------------------------------
function QuestLogic._OnLockedQuestLineOpen(questLine)
    Module.Quest.Field:CachingNewlyOpenQuestLine(questLine)
end

function QuestLogic._UpdateQuestTips()
    local function fShowTips(uiInst, bObjective, tipsTitle, tipsDesc, tipsState, tipsProgerss, npcIcon, npcName)
        if uiInst and not hasdestroy(uiInst) then
            uiInst:UpdateTips(bObjective, tipsTitle, tipsDesc, tipsState, tipsProgerss, npcIcon, npcName)
            loginfo("_UpdateShowTips, Tip:",tostring(bObjective), ", ",tipsTitle,", ",tipsDesc,", ", tipsState, ", ",tipsProgerss,", ",Module.Quest.Field.questUpdateTipsHandle,", ",#Module.Quest.Field.cacheQuestObjUpdateTips)
            uiInst:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end

    if Module.Quest.Field.questUpdateTipsHandle == nil and #Module.Quest.Field.cacheQuestObjUpdateTips >= 1 then

        local navID = UIName2ID.QuestUpdateTips_PC
        if DFHD_LUA ~= 1 then
            navID = UIName2ID.QuestUpdateTips
        end

        Module.Quest.Field.questUpdateTipsHandle =
                Facade.UIManager:AsyncShowUI(navID,
                function(uiInst)
                    local tipsData = table.remove(Module.Quest.Field.cacheQuestObjUpdateTips, 1)
                    fShowTips(
                        uiInst,
                        tipsData[1],
                        tipsData[2],
                        tipsData[3],
                        tipsData[4],
                        tipsData[5],
                        tipsData[6],
                        tipsData[7]
                    )

                    Module.Quest.Field.questUpdateTipsTimeHandle = Timer:NewIns(5, 0)
                    Module.Quest.Field.questUpdateTipsTimeHandle:AddListener(function ()
                        if #Module.Quest.Field.cacheQuestObjUpdateTips > 0 and Module.Quest.Field.questUpdateTipsHandle then
                            local tipsData2 = table.remove(Module.Quest.Field.cacheQuestObjUpdateTips, 1)
                            fShowTips(
                                Module.Quest.Field.questUpdateTipsHandle.objIns,
                                tipsData2[1],
                                tipsData2[2],
                                tipsData2[3],
                                tipsData2[4],
                                tipsData2[5],
                                tipsData2[6],
                                tipsData2[7]
                            )
                        else
                            if #Module.Quest.Field.cacheQuestUpdateTipsData > 0 and Module.Quest.Field.questUpdateTipsHandle then
                                local questTipData = table.remove(Module.Quest.Field.cacheQuestUpdateTipsData, 1)
                                fShowTips(
                                    Module.Quest.Field.questUpdateTipsHandle.objIns,
                                    false,
                                    questTipData[1],
                                    questTipData[2],
                                    questTipData[3],
                                    questTipData[4],
                                    questTipData[5],
                                    questTipData[6]
                                )

                            else
                                loginfo("_UpdateShowTips Close")
                                if Module.Quest.Field.questUpdateTipsTimeHandle then
                                    Module.Quest.Field.questUpdateTipsTimeHandle:Release()
                                    Module.Quest.Field.questUpdateTipsTimeHandle = nil
                                end
                                if Module.Quest.Field.questUpdateTipsHandle then
                                    Facade.UIManager:CloseUIByHandle(Module.Quest.Field.questUpdateTipsHandle)
                                    Module.Quest.Field.questUpdateTipsHandle = nil
                                end
                            end
                        end
                    end, self)
                Module.Quest.Field.questUpdateTipsTimeHandle:Start()    
                end
            )
        
        
    end
end

function QuestLogic._UpdateQuestObjectState(objective, ownerQuestData)
    logwarning("_UpdateQuestObjectState_Outside")
    if objective then
        local tipsDesc = nil
        local tipsProgressTxt = nil
        local tipsTitle, tipsState = Module.Quest.Config.Loc.objectiveUpdate, 1
        local questObjectID = 0
        if objective.ownerQuestInfo then
            if objective:IsNeedShowUpdateTip() == false or (objective.numNeeded > 0 and objective.num <= 0) then
                logwarning(string.format("_UpdateQuestObjectState_Inside_questObjective_NtShowTip_[%d] type=[%d] need=[%d]  NtShowTip", objective.id, objective.type, objective.numNeeded))
                return
            end
            questObjectID = objective.id
            logwarning(string.format("_UpdateQuestObjectState_Inside_questObjective_[%d]", objective.id))
            tipsDesc = objective:GetQuestObjectDesc()
            tipsProgressTxt =
                objective.numNeeded > 0 and
                string.format(Module.Quest.Config.Loc.progressText, objective.num, objective.numNeeded) or
                ""
            if objective.bIsFinsih then
                tipsTitle, tipsState = Module.Quest.Config.Loc.objectiveComplete, 2
            elseif objective.ownerQuestInfo.state == QuestState.Accepted then
                if objective.bResetWhenQuestFailed then
                    tipsTitle, tipsState = Module.Quest.Config.Loc.objectiveReset, 3
                end
            end
        else
            questObjectID = objective.quest_objective_id
            logwarning(
                string.format("_UpdateQuestObjectState_Outside_questObjective_[%d]", objective.quest_objective_id)
            )
            local objectivesCfg = Facade.TableManager:GetTable("QuestObjectives", false)
            if objectivesCfg == nil then
                return
            end
            local objectiveTable = objectivesCfg[tostring(objective.quest_objective_id)]
            if objectiveTable == nil then
                return
            end

            -- 记录属于局内 不需要局外弹tip的目标类型
            local NtShowTyps = {
                QuestObjectiveType.Gather, --3
                QuestObjectiveType.Escaped, --4
                QuestObjectiveType.Kill, --5
                QuestObjectiveType.ExploreMapPosition, --8
                QuestObjectiveType.PlaceProp, --9
                QuestObjectiveType.DSGameInteractive, --12
                13,
                14,
                16,
                17,
                18,
                19,
                22,
                23,
                24,
                33,
                34,
                36
            }

            if table.isInList(objectiveTable.Type, NtShowTyps) then
                return
            end

            local _, objDesc = UGPBlueprintUtils.CovertSpecialString(tostring((objectiveTable.ObjectiveDesc)))
            tipsDesc = objDesc
            tipsProgressTxt =
                tonumber(objectiveTable.RequiredCount) > 0 and
                string.format(Module.Quest.Config.Loc.progressText, objective.value, objectiveTable.RequiredCount) or
                ""
            if objective.has_completed then
                tipsTitle, tipsState = Module.Quest.Config.Loc.objectiveComplete, 2
            elseif ownerQuestData.quest_state == QuestState.Accepted then
                if objectiveTable.ResetWhenQuestFailed then
                    tipsTitle, tipsState = Module.Quest.Config.Loc.objectiveReset, 3
                end
            end
        end

        local inSafe = false
        if
            Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse or
                Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.GameToSafeHouse or
                Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby
         then
            inSafe = true
        end

        if inSafe == false then
            if objective.ownerQuestInfo then
                if objective.type == QuestObjectiveType.Escaped or objective.type == QuestObjectiveType.CarryOutProps then
                    logwarning(
                        string.format("_UpdateQuestObjectState_Inside_questObjective_GameObject_[%d]", objective.id)
                    )
                else
                    return
                end
            else
                local objectivesCfg = Facade.TableManager:GetTable("QuestObjectives", false)
                local objectiveTable = objectivesCfg[objective.quest_objective_id]
                if
                    objectiveTable and
                        (objectiveTable.Type == QuestObjectiveType.Escaped or
                            objectiveTable.Type == QuestObjectiveType.CarryOutProps)
                 then
                    logwarning(
                        string.format(
                            "_UpdateQuestObjectState_Outside_questObjective_GameObject_[%d]",
                            objective.quest_objective_id
                        )
                    )
                else
                    return
                end
            end
        end

        local hasExit = false
        for index, value in ipairs(Module.Quest.Field.cacheQuestObjUpdateTips) do
            if value and #value >= 8 and value[8] == questObjectID and tostring(value[5]) == tostring(tipsProgressTxt) then
                hasExit = true
            end
        end

        if not hasExit then
            table.insert(
                Module.Quest.Field.cacheQuestObjUpdateTips,
                {true, tostring(tipsTitle), tipsDesc, tipsState, tipsProgressTxt, "", "", questObjectID}
            )
        end

        if inSafe then
            QuestLogic._UpdateQuestTips()
        end
    end
end

function QuestLogic._UpdateQuestState(questId)
    if questId then
        local questInfo = Server.QuestServer:GetQuestInfoById(questId)
        if questInfo then
            if questInfo.state == QuestState.Failed or (questInfo.state == QuestState.Completed and questInfo:IsNeedShowUpdateTip()) then
                local tipsTitle = Module.Quest.Config.Loc.questFinish
                if questInfo.state == QuestState.Failed then
                    tipsTitle = Module.Quest.Config.Loc.questFailed
                end
                local npcIconPath = ""
                local npcName = ""
                local questLineInfo = Server.QuestServer:GetQuestLineInfoById(questInfo:GetQuestLine())
                if questLineInfo then
                    local questLineCfg = Facade.TableManager:GetTable("QuestLine")
                    local questLineRow = questLineCfg[tostring(questLineInfo.questLineId)]
                    local npcCfg = Facade.TableManager:GetTable("NPCRoleInfoTable")
                    local npcRow = npcCfg[tostring(questLineInfo.questPublishNPCId)]
                    if npcRow then
                        npcName = npcRow.Name
                        npcIconPath = npcRow.IconPath
                    end
                end

                local hasExit = false
                for index, value in ipairs(Module.Quest.Field.cacheQuestUpdateTipsData) do
                    if value and #value >= 7 and value[7] == questId and value[3] == questInfo.name then
                        hasExit = true
                    end
                end

                if not hasExit then
                    table.insert(
                        Module.Quest.Field.cacheQuestUpdateTipsData,
                        {
                            tostring(tipsTitle),
                            questInfo.name,
                            questInfo.state,
                            "",
                            npcIconPath,
                            npcName,
                            questId
                        }
                    )
                    loginfo("QuestLogic._UpdateQuestState, questId :", questId)
                end
            end
        end
    end
end

------------------------------------ public function ------------------------------------
function QuestLogic.AddEventListener()
    Server.QuestServer.Events.evtCacheLockedQuestLineOpen:AddListener(QuestLogic._OnLockedQuestLineOpen)
    Server.QuestServer.Events.evtUpdateObjectiveState:AddListener(QuestLogic._UpdateQuestObjectState)
    Server.QuestServer.Events.evtQuestObjectiveUpdateTips:AddListener(QuestLogic._UpdateQuestObjectState)
    Server.QuestServer.Events.evtUpdateQuestState:AddListener(QuestLogic._UpdateQuestState)
    Server.QuestServer.Events.evtUpdateSeasonLevel:AddListener(QuestLogic._OnUnlockQuestChapter)

    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(QuestLogic._OnRelayConnected)
    -- 断线重连
end

function QuestLogic.RemoveEventListener()
    Server.QuestServer.Events.evtCacheLockedQuestLineOpen:RemoveListener(QuestLogic._OnLockedQuestLineOpen)
    Server.QuestServer.Events.evtUpdateObjectiveState:RemoveListener(QuestLogic._UpdateQuestObjectState)
    Server.QuestServer.Events.evtQuestObjectiveUpdateTips:RemoveListener(QuestLogic._UpdateQuestObjectState)
    Server.QuestServer.Events.evtUpdateQuestState:RemoveListener(QuestLogic._UpdateQuestState)
    Server.QuestServer.Events.evtUpdateSeasonLevel:RemoveListener(QuestLogic._OnUnlockQuestChapter)

    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(QuestLogic._OnRelayConnected)
end

function QuestLogic._OnRelayConnected()
    loginfo("QuestLogic._OnRelayConnected PullRoomInfoList")
    Server.QuestServer:GetAllQuest()
end

function QuestLogic._OnUnlockQuestChapter()
    Module.ModuleUnlock:NotifyUnlockStateChanged()
end

-- 接受任务
function QuestLogic.DoAcceptQuest(questInfo)
    local callback = nil
    if questInfo.acceptDiaIogId ~= "" then
        callback = function(questInfo)
            Module.StoryDialog:OpenStoryDialogPanel(questInfo.acceptDiaIogId)
        end
    end
    Server.QuestServer:AcceptQuest(questInfo.id, callback)
end

--放弃任务
function QuestLogic.DoGiveUpQuest(questInfo)
    Server.QuestServer:GiveUpQuset(questInfo.id)
end


-- 领取任务奖励
function QuestLogic.DoGetQuestRewards(questInfo)
    local callback = function(bRewardFullToMail, bCurrencyFullToMail, data_change)
        local ShowRewardPanel = function(bRewardFullToMail, bCurrencyFullToMail, data_change)

            local itemList = questInfo:GetRewardList()
            local updateItemList = {}

            for index, item in pairs(itemList) do 
                if item.id ~= ECurrencyItemId.UnBindBankNote then
                    table.insert(updateItemList, item)
                end
            end
        
            for _, change in pairs(data_change.currency_changes) do
                if change.delta and change.currency_id == ECurrencyItemId.UnBindBankNote then
                    table.insert(updateItemList, ItemBase:NewIns(change.currency_id, change.delta))
                    updateItemList[#updateItemList].src_id = change.src_id
                end
            end

            Module.Reward:OpenRewardPanel(
                questInfo.bCgQuest and Module.Quest.Config.Loc.WatchFinish or Module.Quest.Config.Loc.questFinish,
                nil,
                updateItemList
            )
            if bRewardFullToMail and bCurrencyFullToMail then
                Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questRewardCurrencyBothFullToMail, 2)
            elseif bRewardFullToMail then
                Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questRewardFullToMail, 2)
            elseif bCurrencyFullToMail then
                Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questCurrencyFullToMail, 2)
            else
                -- nothing
            end
        end
        if questInfo.rewardDialogId ~= "" then
            Module.StoryDialog:OpenStoryDialogPanel(questInfo.rewardDialogId, ShowRewardPanel)
        else
            ShowRewardPanel(bRewardFullToMail, bCurrencyFullToMail, data_change)
        end
    end
    Server.QuestServer:GetQuestRewards(questInfo.id, callback)
end

-- 获取任务道具提交道具信息
function QuestLogic.Get3ItemSubmitInfos(questInfo)
    -- availableitemStructList :{ItemStruct, matchSubmitObjId} 可提交道具列表和对应提交物品任务目标的id
    -- requiredSubmitDatas : {submitObjId:num, numSubmited:number, numNeeded:number, submitItemStruct:ItemStruct, bindType:number} 需要提交道具的信息,bindType:-1不限制，0 绑定，1 非绑定
    local availableitemStructList = {}
    local requiredSubmitDatas = {}
    local zeroStoredItemStructList = {}
    local gameItemCfg = Facade.TableManager:GetTable("GameItem")
    for _, objective in ipairs(questInfo:GetSubmitObjectives()) do
        -- -1不限制，0 绑定，1 非绑定
        local bindType = tonumber(objective.param2) or -1
        local objectiveItem = nil
        local storedItemList = {}
        local function fIsComplexItem(item)
            if
                objective.complexPropRequirment.itemType and
                    objective.complexPropRequirment.itemType ~= item.itemSubTagID
             then
                return false
            end
            if
                objective.complexPropRequirment.bIsMoreThanQuality and
                    objective.complexPropRequirment.itemQuality > item.quality
             then
                return false
            end
            if
                objective.complexPropRequirment.bIsMoreThanPrice and
                    objective.complexPropRequirment.itemPrice > item:GetTotalSellPrice()
             then
                return false
            end
            return true
        end

        local function fCompressStoredItems(storedItemList)
            local result = {}
            local map = {}
            if #storedItemList == 0 then
                return {}
            end
            for i = 1, #storedItemList do
                local itemFeature = storedItemList[i]:GetFeature()
                local featureType = itemFeature:GetFeatureType()
                if featureType == EFeatureType.Weapon or (itemFeature.maxDurability and itemFeature.maxDurability > 0) then
                    -- 有耐久度/使用次数的道具和枪械，一行仅显示一个
                    if bindType == -1 or bindType == storedItemList[i].bindType then
                        table.insert(result, storedItemList[i])
                    end
                else
                    -- 一般道具，则堆叠显示，仅区分绑定和非绑定
                    local key = nil
                    --if bindType == -1 then
                    --    key = storedItemList[i].id
                    --else
                        key = storedItemList[i].id .. "-" .. storedItemList[i].bindType
                   -- end
                    if not map[key] then
                        map[key] = true
                        table.insert(result, storedItemList[i])
                    end
                end
            end
            return result
        end

        if not objective.bIsComplexPropObjective then
            local itemId = tonumber(objective.param1) or tonumber(objective.listParam[1])
            objectiveItem = ItemBase:New(itemId, 1)
            objectiveItem.bindType = bindType
            local weaponFeature = objectiveItem:GetFeature(EFeatureType.Weapon)
            if weaponFeature and weaponFeature:IsWeapon() then
                local gun = WeaponAssemblyTool.GetPresetPreviewGunFromRecId(itemId, objective.listParam)
                if gun then
                    objectiveItem:SetRawDescObj(gun:GetModularDesc())
                    objectiveItem:SetRawPropInfo(gun:GetPropInfo())
                end
            end
            if not objective.bIsFinsih then
                storedItemList = fCompressStoredItems(Server.InventoryServer:GetItemsById(itemId))
                if #storedItemList == 0 and not (weaponFeature and weaponFeature:IsWeapon()) then
                    table.insert(zeroStoredItemStructList, objectiveItem)
                end
            end
        else
            -- if not objective.bIsFinsih then
            -- 	for _, gameItemTable in pairs(gameItemCfg) do
            -- 		local gameItem = ItemBase:New(gameItemTable.ItemID, 1)
            -- 		if fIsComplexItem(gameItem) then
            -- 			local bZeroCount = true
            -- 			for _, itemData in ipairs(storedItemList) do
            -- 				if itemData.id ==  gameItem.id then
            -- 					bZeroCount = false
            -- 					break
            -- 				end
            -- 			end
            -- 			if bZeroCount then
            -- 				local weaponFeature = gameItem:GetFeature(EFeatureType.Weapon)
            -- 				if weaponFeature and weaponFeature:IsWeapon() then
            -- 					local gun = WeaponAssemblyTool.GetPresetPreviewGunFromRecId(gameItemTable.ItemID, objective.listParam)
            -- 					if gun then
            -- 						gameItem:SetRawDescObj(gun:GetModularDesc())
            -- 						gameItem:SetRawPropInfo(gun:GetPropInfo())
            -- 					end
            -- 				end
            -- 				table.insert(zeroStoredItemStructList, gameItem)
            -- 			end
            -- 		end
            -- 	end
            -- end
            -- 复杂物品规则
            if not objective.bIsFinsih then
                storedItemList = fCompressStoredItems(Server.InventoryServer:GetItemsByCondition(fIsComplexItem))
            end
        end
        table.insert(
            requiredSubmitDatas,
            {
                submitObjId = objective.id,
                numSubmited = objective.num,
                numNeeded = objective.numNeeded,
                submitItemStruct = objectiveItem,
                bindType = bindType
            }
        )

        if not objective.bIsFinsih then
            for _, itemStructData in ipairs(storedItemList) do
                availableitemStructList[itemStructData] = {itemStruct = itemStructData, matchSubmitObjId = objective.id}
            end
        end
    end
    return availableitemStructList, requiredSubmitDatas, zeroStoredItemStructList
end

-- 处理任务道具提交
function QuestLogic.ProcessItemSubmit(
    submitItemInfoList,
    submitObjectiveInfo,
    submitItemName,
    submitItemNum,
    bHasEquippedItem)
    local fConfirmbackIns =
        SafeCallBack(
        function()
            Server.QuestServer:SubmitItems(
                Module.Quest.Field.submitQuestId,
                submitObjectiveInfo.submitObjId,
                submitItemInfoList
            )
        end,
        nil
    )
    if not bHasEquippedItem then
        local text = Module.Quest.Config.Loc.SubmitConfirm
        local bHasNoBind = false
        for index, value in ipairs(submitItemInfoList) do
            if value.bindType == 1 then
                bHasNoBind = true
            end
        end
        if submitObjectiveInfo.bindType == -1 and bHasNoBind then
            text = Module.Quest.Config.Loc.SubmitConfirmBindTypeTip
        end
        return Module.CommonTips:ShowConfirmWindow(
            string.format(text, submitItemName, submitItemNum),
            fConfirmbackIns
        )
    else
        return Module.CommonTips:ShowConfirmWindow(
            string.format(Module.Quest.Config.Loc.SubmitEquippedConfirm, submitItemName),
            fConfirmbackIns
        )
    end
end

function QuestLogic.TryOpenSOLQuestLinePanelProcess(questLineInfo, jumpToQuestInfo)
    if questLineInfo and questLineInfo:IsLineOpend() then
        return Facade.UIManager:AsyncShowUI(UIName2ID.QuestLinePanel, nil, nil, questLineInfo, jumpToQuestInfo)
    else
        Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questLineLocked)
        return nil
    end
end

function QuestLogic.TryOpenSOLQuestSeasonLinePanelProcess(stageInfo, jumpToQuestInfo)
    return Facade.UIManager:AsyncShowUI(UIName2ID.QuestLinePanel, nil, nil, stageInfo, jumpToQuestInfo, true)
end

function QuestLogic.QuestLineCreateEndToJump()
    -- 跳转定位方案三
    if Module.Quest.Field.jumpToQuestInfo then
        local pos = Module.Quest.Field:GetCurQuestLineItemPos(Module.Quest.Field.jumpToQuestInfo.id)
        if pos and Module.Quest.Field.jumpToQuestInfo.state >= QuestState.Locked then
            Module.Quest.Config.evtJumpToQuestLineItem:Invoke(pos)
            Module.Quest.Field.jumpToQuestInfo = nil
        end
    end
end

-- 获取收集者任务道具提交道具信息
function QuestLogic.GetCollectorItemSubmitInfos(itemList)
    local availableitemStructList = {}
    local requiredSubmitDatas = {}
    for _, value in ipairs(itemList) do
        local itemId = value.itemId
        local objectiveItem = ItemBase:New(itemId, value.num)
        local storedItemList = Server.InventoryServer:GetItemsById(itemId)
        table.insert(
            requiredSubmitDatas,
            {
                submitObjId = value.itemId,
                numNeeded = value.num,
                submitItemStruct = objectiveItem
            }
        )

        for _, itemStructData in ipairs(storedItemList) do
            availableitemStructList[itemStructData] = {itemStruct = itemStructData}
        end
    end
    return availableitemStructList, requiredSubmitDatas
end

function QuestLogic.IsCanSubmitCollector(itemList)
    local bCanSubmit = true
    for _, value in ipairs(itemList) do
        local itemId = value.itemId
        local storedItemList = Server.InventoryServer:GetItemsById(itemId)
        if #storedItemList < value.num then
            bCanSubmit = false
            break
        end
    end
    return bCanSubmit
end

-- 领取任务奖励
function QuestLogic.DoGetSeasonCollectorRewards(collectorRewardId)
    local fCallback = function(bRewardFullToMail, bCurrencyFullToMail, data_change)
        local itemList = {}
        local collectRewardTable = Facade.TableManager:GetRowByKey("CollectorRewards", tostring(collectorRewardId))
        for _, questRewardId in ipairs(collectRewardTable.RewardIdArr) do
            local item = QuestLogic._GenerateRewardItem(questRewardId)
            table.insert(itemList, item)
        end
    
        local updateItemList = {}

        for index, item in pairs(itemList) do 
            if item.id ~= ECurrencyItemId.UnBindBankNote then
                table.insert(updateItemList, item)
            end
        end
    
        for _, change in pairs(data_change.currency_changes) do
            if change.delta and change.currency_id == ECurrencyItemId.UnBindBankNote then
                table.insert(updateItemList, ItemBase:NewIns(change.currency_id, change.delta))
                updateItemList[#updateItemList].src_id = change.src_id
            end
        end

        Module.Reward:OpenRewardPanel(
            Module.Quest.Config.Loc.questFinish,
            nil,
            updateItemList
        )
        if bRewardFullToMail and bCurrencyFullToMail then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questRewardCurrencyBothFullToMail, 2)
        elseif bRewardFullToMail then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questRewardFullToMail, 2)
        elseif bCurrencyFullToMail then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questCurrencyFullToMail, 2)
        else
            -- nothing
        end
    end
    Server.QuestServer:CollectorTaskRewardsReq(collectorRewardId, fCallback)
end

function QuestLogic._GenerateRewardItem(questRewardId)
    local rewardTable = Facade.TableManager:GetRowByKey("QuestRewards", tostring(questRewardId))
    local rewardInfo = {
        id = rewardTable.ItemID,
        type = rewardTable.Type,
        num = rewardTable.Number,
        bindType = rewardTable.bindType,
        important = rewardTable.ImportantReward
    }
    local item = ItemBase:New(rewardInfo.id, rewardInfo.num)
    if ItemHelperTool.GetMainTypeById(rewardInfo.id) == EItemType.Weapon then
        --item = WeaponAssemblyTool.GetWeaponItemByPresetId(rewardInfo.id)
        local weaponDesc = WeaponAssemblyTool.PresetRow_to_Desc(item.id)
        item:SetRawDescObj(weaponDesc)
        local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDesc)
        if propinfo then
            propinfo.bind_type = rewardInfo.bindType
        end
        local partsPropInfo = propinfo.components

        ---@param partInfo pb_Component
        local function fRecursiveGetPart(partInfo)
            if partInfo.prop_data then
                partInfo.prop_data.bind_type = rewardInfo.bindType
                for _, subPartInfo in ipairs(partInfo.prop_data.components) do
                    fRecursiveGetPart(subPartInfo)
                end
            end
        end
        for _, partInfo in ipairs(partsPropInfo) do
            fRecursiveGetPart(partInfo)
        end
        item:SetRawPropInfo(propinfo)
    end

    if rewardInfo.type == QuestRewardType.RewardProp then
        item:ForceSetBindType(rewardInfo.bindType)
    end

    return item
end

function QuestLogic.SortQuestList(questInfos, type)
    local runQuestInfos = {}
    local newQuestInfos = {}
    local lockQuestInfos = {}
    local rewardQuestInfos = {}
    local completedQuestInfos = {}
    for _, questInfo in ipairs(questInfos) do
        if type == 0 or questInfo.type == type then
            if
                (questInfo.state == QuestState.Unread or questInfo.state == QuestState.Unaccepted) and
                    Server.QuestServer:IsQuestAcceptable(questInfo) == false
             then
                table.insert(lockQuestInfos, questInfo)
            elseif questInfo.state >= QuestState.Rewarded then
                table.insert(rewardQuestInfos, questInfo)
            elseif questInfo.state == QuestState.Completed then
                table.insert(completedQuestInfos, questInfo)
            elseif questInfo.state == QuestState.Accepted then
                table.insert(runQuestInfos, questInfo)
            else
                if questInfo.state > QuestState.Unread and questInfo.state < QuestState.Accepted then
                    table.insert(newQuestInfos, questInfo)
                end
            end
        end
    end

    table.sort(
        newQuestInfos,
        function(q1, q2)
            return q1.acceptTime > q2.acceptTime
        end
    )

    table.sort(
        rewardQuestInfos,
        function(q1, q2)
            return q1.acceptTime > q2.acceptTime
        end
    )
    
    table.append(newQuestInfos, runQuestInfos)
    table.append(newQuestInfos, completedQuestInfos)
    table.append(newQuestInfos, lockQuestInfos)
    table.append(newQuestInfos, rewardQuestInfos)
    
    return newQuestInfos
end

---------------- 赛季任务 ----------------

function QuestLogic:IsAllConditionCompeleted(conditionList)
    for index, value in ipairs(conditionList) do
        local progress = self:GetConditionProgress(index)
        if progress < value.param then
            return false
        end
    end
    return true
end

function QuestLogic:GetConditionProgress(type)
    local EQuestSeasonConditionType = Module.Quest.Config.EQuestSeasonConditionType
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()

    if lineInfo == nil then 
		logerror("Invalid Current Season Questline")
		return
	end

    if type == EQuestSeasonConditionType.Star then 
        return lineInfo:CalGainStar()
    elseif type == EQuestSeasonConditionType.Collection then
        local collectorData = Server.QuestServer:GetCollectorDataInfo()
        local finish, total = collectorData:GetCollectorProgress()
        return finish
    elseif type == EQuestSeasonConditionType.Fate then 
        local count, total, name, selectIndex = Server.QuestServer._questFactData:GetFactProgress()
        return count
    end
    logerror("Undefined Condition Type")
end

return QuestLogic
