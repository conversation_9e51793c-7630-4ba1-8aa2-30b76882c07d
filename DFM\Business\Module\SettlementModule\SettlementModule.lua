----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SettlementModule : ModuleBase
local SettlementModule = class("SettlementModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

local SettlementLogic = require("DFM.Business.Module.SettlementModule.SettlementLogic")
local CrackedSettlementDisplayLogic = require "DFM.Business.Module.SettlementModule.Logic.CrackedSettlementDisplayLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local EReportMode = import "EReportMode"
local UDFMSettlementManager = import "DFMSettlementManager"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local FMPQuestInfoSupplement = import "MPQuestInfoSupplement"
local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION
local EBattleFieldQuestType = import "EBattleFieldQuestType"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
-- 依赖 InventoryServer/GVoiceModule/AccountServer/RoomServer

local function xxwwinfo(...)
    loginfo("[xxww] ", ...)
end

local function xxwwwarning(...)
    logwarning("[xxww] @warning@", ...)
end

local function xxwwerror(...)
    logerror("[xxww] @error@", ...)
end

local function printtable(t, prefix)
    xxwwinfo(prefix)
    logtable(t)
end

local UIManager = Facade.UIManager

function SettlementModule:Ctor()
    xxwwinfo("SettlementModule:Ctor")
    --[xxww] 正在结算中 其他模块可能依赖此值展示不同的表现
    ItemOperaTool.bInSettlement = false
end

function SettlementModule:OnInitModule()
    SettlementLogic.AddListener()
    CrackedSettlementDisplayLogic:AddListener()
    --UI Cache 读表
    local settlementConfig = Facade.TableManager:GetTable("SettlementConfig")
    if settlementConfig then
        for _, v in pairs(settlementConfig) do
            --单人胜利
            if v.ModeID == 11 then
                Module.Settlement.Config.SettlementMode2UI.SingleEscape = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.SingleEscape,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Module.Settlement.Config.SettlementMediaNameTable.SingleEscape = v.MediaName
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            logtable(Module.Settlement.Config.SettlementMode2UI.SingleEscape)
            --单人失败
            if v.ModeID == 10 then
                Module.Settlement.Config.SettlementMode2UI.SingleFailed = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.SingleFailed,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Module.Settlement.Config.SettlementMediaNameTable.SingleFailed = v.MediaName
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            logtable(Module.Settlement.Config.SettlementMode2UI.SingleFailed)
            --多人胜利
            if v.ModeID == 21 then
                Module.Settlement.Config.SettlementMode2UI.TeamEscape = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.TeamEscape,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Module.Settlement.Config.SettlementMediaNameTable.TeamEscape = v.MediaName
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            logtable(Module.Settlement.Config.SettlementMode2UI.TeamEscape)
            --多人失败
            if v.ModeID == 20 then
                Module.Settlement.Config.SettlementMode2UI.TeamFail = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.TeamFail,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Module.Settlement.Config.SettlementMediaNameTable.TeamFail = v.MediaName
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            logtable(Module.Settlement.Config.SettlementMode2UI.TeamFail)
            --大战场胜利
            if v.ModeID == 31 then
                Module.Settlement.Config.SettlementMode2UI.BattleFieldSuccess = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.BattleFieldSuccess,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Server.SettlementServer.tdmShowOrder = v.BitShowOrder
                logtable(Module.Settlement.Config.SettlementMode2UI.BattleFieldSuccess)
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            --大战场失败
            if v.ModeID == 30 then
                Module.Settlement.Config.SettlementMode2UI.BattleFieldFail = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.BattleFieldFail,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            logtable(Module.Settlement.Config.SettlementMode2UI.BattleFieldFail)
            --Raid胜利
            if v.ModeID == 41 then
                Module.Settlement.Config.SettlementMode2UI.RaidSuccess = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.RaidSuccess,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                logtable(Module.Settlement.Config.SettlementMode2UI.RaidSuccess)
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            --Raid失败
            if v.ModeID == 40 then
                Module.Settlement.Config.SettlementMode2UI.RaidFail = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.RaidFail,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                logtable(Module.Settlement.Config.SettlementMode2UI.RaidFail)
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            --攻防胜利
            if v.ModeID == 50 then
                Module.Settlement.Config.SettlementMode2UI.BreakthroughSuccess = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.BreakthroughSuccess,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                Server.SettlementServer.gfShowOrder = v.BitShowOrder
                logtable(Module.Settlement.Config.SettlementMode2UI.BreakthroughSuccess)
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            --攻防失败
            if v.ModeID == 51 then
                Module.Settlement.Config.SettlementMode2UI.BreakthroughFail = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.BreakthroughFail,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                logtable(Module.Settlement.Config.SettlementMode2UI.BreakthroughFail)
                if v.WaitTime and v.WaitTime > 60 then
                    Module.Settlement.Config.SettlementSuccessWaitTime = v.WaitTime
                end
            end
            --SOL新手关胜利
            if v.ModeID == 61 then
                Module.Settlement.Config.SettlementMode2UI.SOLGuideSuccess = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.SOLGuideSuccess,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                logtable(Module.Settlement.Config.SettlementMode2UI.SOLGuideSuccess)
            end
            --SOL新手关失败
            if v.ModeID == 60 then
                Module.Settlement.Config.SettlementMode2UI.SOLGuideFail = {}
                for k, eachUI in pairs(string.split(v.PageOrder, ",")) do
                    table.insert(
                        Module.Settlement.Config.SettlementMode2UI.SOLGuideFail,
                        UIUtil.CreateCacheInfo(UIName2ID[eachUI], k == 1)
                    )
                end
                logtable(Module.Settlement.Config.SettlementMode2UI.SOLGuideFail)
            end
        end
    end
end

-- 懒加载的情况下，这里才能注册有后init的模块中的事件
function SettlementModule:OnLoadModule()
end

function SettlementModule:OnUnLoadModule()
end

function SettlementModule:TryOpenSolSettlementUI()
    Module.Settlement.Config.Events.evtBeginSolSettlementPop:Invoke()
    self:OpenSOLSettlementUI("solSettlementStart")
end

--[xxww]弱网情况下漏了ntf，主动请求
function SettlementModule:SettlementFailShowTipsAndQuit(DFMGamePlayerMode)
    xxwwerror(
        "SettlementModule.SettlementFailShowTipsAndQuit settlement timeout 2 fail! DFMGamePlayerMode = ",
        DFMGamePlayerMode
    )

    if Facade.GameFlowManager:IsInOBMode() then
        xxwwinfo("SettlementServer:SettlementFailShowTipsAndQuit obmode doesn't need settlement")
        return
    end

    if Server.SettlementServer:GetSettlementInfoSource() ~= SettlementDefine.ESettlementInfoSource.None then
        return
    end

    if Server.SettlementServer.bGetNtf then
        xxwwinfo("SettlementModule:SettlementFailShowTipsAndQuit skip because get ntf")
        return
    end
    if Module.Login:IsPlayerKickOut() then
        xxwwinfo("SettlementModule:SettlementFailShowTipsAndQuit skip because player is kick out")
        return
    end
    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow ~= EGameFlowStageType.Game and currentGameFlow ~= EGameFlowStageType.GameSettlement then
        xxwwinfo("SettlementModule:SettlementFailShowTipsAndQuit skip because currentGameFlow is", currentGameFlow)
        return
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        local function BtnFunc()
            Module.InGame.Config.flowEvtReturnToLobby:Invoke()
        end

        Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
            Module.Settlement.Config.Loc.SettlementFail2Retry_Gen9,
            BtnFunc,
            Module.Settlement.Config.Loc.ReturnToTheLobby
        )
    else
        Module.CommonTips:ShowConfirmWindow(
            Module.Settlement.Config.Loc.SettlementFail2Retry,
            function()
                if not Server.SettlementServer.bGetNtf then
                    if DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_SOL then --SOL
                        Server.SettlementServer:ReqForSOLSettlementInfo()
                        return
                    elseif DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Raid then --Raid
                        Server.SettlementServer:ReqForRaidSettlementInfo()
                        return
                    elseif
                        DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough or
                            DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest
                     then --TDM
                        Server.SettlementServer:ReqForTDMSettlementInfo()
                        return
                    end
                end
                self:UECall_QuitGame()
            end,
            function()
                self:UECall_QuitGame()
            end,
            Module.Settlement.Config.Loc.Settlement2SafeHouse,
            Module.Settlement.Config.Loc.SettlementRetry
        )
    end
    --- END MODIFICATION
end

--[Rayjyliu]首次死亡观战时，退出按钮显示二次确认界面
function SettlementModule:FirstDeathExitShowConfirmWindow(cppUIIns)
    Module.CommonTips:ShowConfirmWindow(
        Module.Settlement.Config.Loc.FirstDeathReturnTips,
        function()
            if isvalid(cppUIIns) then
                cppUIIns:OnExitOB()
            end
            if not IsHD() and InGameController:Get():IsRaid() then
                xxwwinfo("SettlementModule:FirstDeathExitShowConfirmWindow UECall_QuitGame")
                self:UECall_QuitGame()
            end
        end,
        nil,
        Module.Settlement.Config.Loc.cancel,
        Module.Settlement.Config.Loc.Confirm
    )
end

--[Rayjyliu]Raid死亡观战时不可救活时，退出按钮显示二次确认界面
function SettlementModule:RaidDeathExitShowConfirmWindow(cppUIIns)
    Module.CommonTips:ShowConfirmWindow(
        Module.Settlement.Config.Loc.RaidDeathReturnTips,
        function()
            if isvalid(cppUIIns) then
                cppUIIns:OnExitOB()
            end
            self:UECall_QuitGame()
        end,
        nil,
        Module.Settlement.Config.Loc.cancel,
        Module.Settlement.Config.Loc.Confirm
    )
end

--[leonhxzhao]死亡观战时，语音举报按钮
function SettlementModule:OnWatchFightReportVoiceBtnClick(WatchPlayerUin, WatchPlayerName)
    xxwwinfo("OnWatchFightReportVoiceBtnClick ", WatchPlayerUin, WatchPlayerName)
    Module.GVoice:ReportPlayer({WatchPlayerUin}, "")
end

--[Rayjyliu]首次死亡观战时，观战界面举报按钮
function SettlementModule:OnWatchFightReportBtnClick(WatchPlayerUin, WatchPlayerName)
    xxwwinfo("OnWatchFightReportBtnClick ", WatchPlayerUin, WatchPlayerName)

    local playerInfo = {
        player_id = WatchPlayerUin,
        nick_name = WatchPlayerName,
        player_type = Module.Report.Config.ReportPlayerType.Teammate
    }

    local reportScene = 3
    local matchId = Server.MatchServer:GetDsRoomId()
    local reportStr = ""
    local matchTime
    if Server.SettlementServer.matchInfo then
        matchTime = Server.SettlementServer.matchInfo.start_time
    end
    local reportEntrance = ReportEntrance.ReportTeammateWatchFight
    local gameMode = EReportMode.ReportSol
    local reportEndCall = nil
    xxwwinfo("OnWatchFightReportBtnClick ReportPlayer")

    Module.Report:ReportPlayer(
        playerInfo,
        reportScene,
        matchId,
        reportStr,
        matchTime,
        reportEntrance,
        gameMode,
        reportEndCall,
        {2, 1, 3}
    )
end

--[rannliu]死亡回放举报
function SettlementModule:OnKillCamReplayReportBtnClick(KillerPlayerUin, KillerName)
    xxwwinfo("OnKillCamReplayReportBtnClick ", KillerPlayerUin, KillerName)

    local playerInfo = {
        player_id = KillerPlayerUin,
        nick_name = KillerName,
        player_type = Module.Report.Config.ReportPlayerType.Teammate
    }

    local reportScene = 3
    local matchId = Server.MatchServer:GetDsRoomId()
    local reportStr = ""
    local matchTime
    if Server.SettlementServer.matchInfo then
        matchTime = Server.SettlementServer.matchInfo.start_time
    end
    local reportEntrance = ReportEntrance.ReportTeammateWatchFight
    local gameMode = EReportMode.ReportSol
    local reportEndCall = nil
    xxwwinfo("OnKillCamReplayReportBtnClick ReportPlayer")

    Module.Report:ReportPlayer(
        playerInfo,
        reportScene,
        matchId,
        reportStr,
        matchTime,
        reportEntrance,
        gameMode,
        reportEndCall,
        {2, 1, 3}
    )
end

function SettlementModule:SetInSettlementing(status)
    status = setdefault(status, false)
    if ItemOperaTool.bInSettlement ~= status then
        xxwwinfo("SettlementModule:SetInSettlementing", status)
        ItemOperaTool.bInSettlement = status
    end
end

function SettlementModule:GetInSettlementing()
    return ItemOperaTool.bInSettlement
end

function SettlementModule:ShowBFUI()
    local UICacheList = {}
    table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.BattleFieldSuccess)
    UIManager:BatchShowStackUI(UICacheList)
end

function SettlementModule:OnDestroyModule()
    xxwwinfo("SettlementModule:OnDestroyModule")
    SettlementLogic.RemoveListener()
    CrackedSettlementDisplayLogic:RemoveListener()
end

---@param nextStageType ESubStage
function SettlementModule:OnSubStageChangeEnter(nextStageType)
    SettlementLogic.OnSubStageChangeEnterProcess(nextStageType)
end

function SettlementModule:ShowPreSettlementTitleView(matchOverReason)
    return SettlementLogic.ShowPreSettlementTitleViewProcess(matchOverReason)
end

function SettlementModule:UECall_QuitGame()
    xxwwinfo("SettlementModule:UECall_QuitGame")
    SettlementLogic.EndSettlement()
end

function SettlementModule:UECall_ShowRaidSettlementUIStacks(raidSettlementType)
    xxwwinfo("SettlementModule:UECall_ShowRaidSettlementUIStacks", raidSettlementType)
    SettlementLogic.ShowRaidSettlementUIStacks(raidSettlementType)
end

function SettlementModule:InvokeGameNotifyCloseNetDirver()
    SettlementLogic.InvokeGameNotifyCloseNetDirver()
end

function SettlementModule:OnGameFlowChangeEnter(gameFlowType)
    xxwwinfo("SettlementModule:OnGameFlowChangeEnter", gameFlowType)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DiscardVTDecal 0", nil)
    if gameFlowType == EGameFlowStageType.Game then
        self.Field.AvgPing = 0
        self.Field.SDPing = 0
        LogAnalysisTool.SetIsInGame(true)
        --进局内再打开声音
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
        if Server.SettlementServer.bAlreadySettlement == true and not Server.SettlementServer.bIsGuide then
            Server.SettlementServer.bAlreadySettlement = false
            local gamePlayMode = InGameController:Get():GetGamePlayerMode()
            --- BEGIN MODIFICATION @ VIRTUOS
            if IsConsole() then
                local function BtnFunc()
                    Module.InGame.Config.flowEvtReturnToLobby:Invoke()
                end

                Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
                    Module.Settlement.Config.Loc.SettlementExceptionQuit,
                    BtnFunc,
                    Module.Settlement.Config.Loc.Quit
                )
            elseif gamePlayMode == EDFMGamePlayMode.GamePlayMode_Breakthrough then
                Module.CommonTips:ShowConfirmWindowWithSingleBtn(
                    Module.Settlement.Config.Loc.SettlementExceptionQuit,
                    function()
                        self:UECall_QuitGame()
                    end,
                    Module.Settlement.Config.Loc.Quit
                )
            elseif
                gamePlayMode == EDFMGamePlayMode.GamePlayMode_SOL or gamePlayMode == EDFMGamePlayMode.GamePlayMode_Raid
             then
                Module.CommonTips:ShowConfirmWindow(
                    Module.Settlement.Config.Loc.SettlementExceptionQuit,
                    function()
                        self:UECall_QuitGame()
                    end,
                    function()
                        Server.SettlementServer:ReqForSOLSettlementInfo()
                    end,
                    Module.Settlement.Config.Loc.ReviewSettlement,
                    Module.Settlement.Config.Loc.Quit
                )
            end
        --- END MODIFICATION
        end
        SettlementLogic.AddListenerToClientPlayerMatchOver()
        self.Field.bHasLoadCutSceneData = false
        self.Field.mapIdTagSector = nil
        if SettlementLogic.CheckNeedToEarlyLoadLevel() then
            SettlementLogic.ClearLevelSequenceAndFocusLocation()
            SettlementLogic._TryLoadSettlementCutSceneLevel()
        end
    end
    if gameFlowType == EGameFlowStageType.GameSettlement then
        if SettlementLogic.CheckNeedToLatelyLoadLevel() then
            SettlementLogic._TryLoadSettlementCutSceneLevel()
        end
        Module.CommonBar:ShowTopBar()
        if DFHD_LUA == 1 then
            xxwwinfo("EGameFlowStageType.GameSettlement")
            Module.CommonBar:ShowBottomBar()
        end

        if Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.MP then
            Module.Chat:CreateSettlementChat()
            return
        end
        
    end
    --给关闭背景视频界面加个保底，这个保底大概率用不上，因为切GameFlow的时候UIManager会销毁u并把handle置为nil
    --bug=129984563 [【CN】【PC】【CCB接】#技术服 SOL主动退出对局结算流程偶现卡底图，无法正常返回大厅](https://tapd.woa.com/r/t?id=129984563&type=bug)
    if gameFlowType == EGameFlowStageType.GameToSafeHouse or gameFlowType == EGameFlowStageType.SafeHouse then
        self.Field:CloseBGVideoView()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() and gameFlowType == EGameFlowStageType.Lobby then
        Module.Settlement.Config.Events.evtStartToShowSettlement:Invoke()
    end
    --- END MODIFICATION
end

function SettlementModule:OnGameFlowChangeLeave(gameFlowType)
    xxwwinfo("SettlementModule:OnGameFlowChangeLeave", gameFlowType)
    -- 离开任何GameFlow都可视为结算已结束
    Module.Settlement:SetInSettlementing(false)
    self.Field.bHasTriggerLoadLevelInThisGF = false
    self.Field.bHasFinishLoadLevelInThisGF = false
    -- 清理结算缓存数据应当依赖GameFlow流转,不要放到EndSettlement里,因为EndSettlement不一定会走
    --todo 所有的清理都应当放到这里来
    Module.Settlement.Field:ClearDeathDamageInfo()
    self.Field.cutSceneSector = nil
    self.Field.contributionTypeToName = {}
    if self.Field.delayStartSettlementTimerHandle then
        Timer.CancelDelay(self.Field.delayStartSettlementTimerHandle)
        self.Field.delayStartSettlementTimerHandle = nil
    end
    SettlementLogic.CloseEmptyStackUI()
    if gameFlowType == EGameFlowStageType.Game then
        -- todo 清理结算时压黑遮罩
        local UDFMHudHelper = import "DFMHudHelper"
        if UDFMHudHelper.ClearBlackScreen then
            UDFMHudHelper.ClearBlackScreen(GetWorld())
        end

        LogAnalysisTool.SetIsInGame(false)
    end

    -- 对局正常结束（非玩家中途退出游戏）弹出经验结算窗口
    -- if gameFlowType == EGameFlowStageType.GameToLobby and Server.SettlementServer.tdmSettlementAccountExp and
    --     Server.SettlementServer.tdmSettlementAccountExp.exp ~= 0 and Server.SettlementServer.isNewMPSettlementData then
    --     Facade.UIManager:PreloadLoadingUIAssetList({ UIName2ID.WBP_Settlement_Battle_WeaponUpgrades,
    --         UIName2ID.WBP_Settlement_Battle_WeaponsUnlocked })
    --     -- azhengzheng:MS24 延迟弹出视觉效果不理想 暂时取消
    --     Server.SettlementServer.isNewMPSettlementData = nil
    --     Facade.UIManager:AsyncShowUI(UIName2ID.WBP_Settlement_Battle_ExperienceDetails, nil, nil,
    --         Server.SettlementServer.tdmSettlementAccountExp, Server.SettlementServer.tdmResult)
    -- end

    -- azhengzheng:SOL局外结算
    if gameFlowType == EGameFlowStageType.GameToSafeHouse then -- 暂时关闭 在迭代界面
        -- 暂时写死结算流程
        self._solSettlementFlow = {
            "solSettlementStart",
            UIName2ID.SOLRankSettlement, -- 排位赛
            UIName2ID.PathOfGrowthLevelUpPanel, --SOL成长之路
            UIName2ID.BattlePassSettlement, -- 赛季通行证
            UIName2ID.SOLUnlockWeapons -- SOL武器解锁
        }

        -- Facade.UIManager:PreloadLoadingUIAssetList({
        --     UIName2ID.SOLRankSettlement, -- 排位赛
        --     UIName2ID.BattlePassSettlement, -- 赛季通行证
        --     UIName2ID.PathOfGrowthLevelUpPanel, --SOL成长之路
        --     UIName2ID.SOLUnlockWeapons, -- SOL武器解锁
        --     UIName2ID.SOLUnlockWeaponsBtn -- SOL武器解锁按钮
        -- })

        -- azhengzheng:delay久一些 防止卡顿及被误关界面
        -- Timer.DelayCall(
        --     1,
        --     function()
        --         Module.Settlement.Config.Events.evtBeginSolSettlementPop:Invoke()
        --         self:OpenSOLSettlementUI("solSettlementStart")
        --     end,
        --     self
        -- )

        Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(true)
    end

    -- azhengzheng:MS24MP局外结算
    if gameFlowType == EGameFlowStageType.GameToLobby then
        self._mpSettlementFlow = {
            "mpSettlementStart",
            UIName2ID.MPExpAndWeaponMain, -- 战场经验
            UIName2ID.PathOfGrowthLevelUpPanel, --成长之路
            UIName2ID.MPRankSettlement, -- MP排位赛
            UIName2ID.MPTopTournamentSettlement, -- MP巅峰赛
            UIName2ID.BattlePassSettlement, -- 赛季通行证
            UIName2ID.MandelBrickGainPop -- 特殊曼德尔砖掉落
        }

        if not Facade.UIManager:GetIsLowMemoryState() then -- 只在非低内存设备上预加载
            Facade.UIManager:PreloadLoadingUIAssetList(
                {
                    UIName2ID.MPExpAndWeaponMain, -- 战场经验
                    UIName2ID.PathOfGrowthLevelUpPanel, --成长之路
                    UIName2ID.MPRankSettlement, -- MP排位赛
                    UIName2ID.MPTopTournamentSettlement, -- MP巅峰赛
                    UIName2ID.BattlePassSettlement, -- 赛季通行证
                    UIName2ID.SOLUnlockWeaponsBtn, -- 暂用
                    UIName2ID.MandelBrickGainPop -- 特殊曼德尔砖掉落
                }
            )
        end
    end

    if gameFlowType == EGameFlowStageType.GameToLobby or gameFlowType == EGameFlowStageType.GameToSafeHouse then
        self:_OnSettlementStart()
    end
    Module.Chat:DestroySettlementChat()
end

function SettlementModule:TryOpenMPSettlementUI()
    Module.Settlement.Config.Events.evtBeginMpSettlementPop:Invoke()
    self:_CheckAccountExpIsZero()
    self:OpenMPSettlementUI("mpSettlementStart")
    -- azhengzheng:预加载武器图标 防止模糊
    self:_PreLoadItemIcon()

    Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(true)
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        Module.Settlement.Config.Events.evtReadyToShowSettlement:Invoke()
    end

    -- END MODIFICATION
end

function SettlementModule:OpenSOLSettlementUI(curUIId)
    if
        not self._solSettlementFlow or not Server.SettlementServer:CheckIsNewSOLSettlement() or
            Server.SettlementServer:CheckIsGuide()
     then
        loginfo(
            "OpenSOLSettlementUI _solSettlementFlow:",
            self._solSettlementFlow,
            "CheckIsNewSOLSettlement:",
            Server.SettlementServer:CheckIsNewSOLSettlement(),
            "CheckIsGuide:",
            Server.SettlementServer:CheckIsGuide()
        )
        Module.Settlement.Config.Events.evtEndSolSettlementPop:Invoke()
        Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.SOL)
        Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)
        Server.HeroServer:_LatentUnlockCSHeroEvt()
        return
    end

    self:_CloseAllUILoopAudio()

    loginfo("OpenSOLSettlementUI, curUIId", curUIId)
    for key, value in ipairs(self._solSettlementFlow) do
        if curUIId == value then
            local nextUIId = self._solSettlementFlow[key + 1]

            if nextUIId then
                if nextUIId == UIName2ID.SOLRankSettlement then
                    if not self:_OpenSOLRankSettlement() then
                        self:OpenSOLSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.BattlePassSettlement then
                    if not self:_OpenSOLBattlePassSettlement() then
                        self:OpenSOLSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.PathOfGrowthLevelUpPanel then
                    if not self:_OpenSOLPathOfGrowthLevelUpPanel() then
                        self:OpenSOLSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.SOLUnlockWeapons then
                    if not self:_OpenSOLUnlockWeapons() then
                        self:OpenSOLSettlementUI(nextUIId)
                    end

                    return
                end
            end

            Module.Settlement.Config.Events.evtEndSolSettlementPop:Invoke()
            self:_FriendGameEndsRecommend()
            Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.SOL)

            Server.SettlementServer:RecoverSOLSettlement()
            self:_CloseAllUILoopAudio()
            Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)
            Server.HeroServer:_LatentUnlockCSHeroEvt()

            return
        end
    end

    logerror("OpenSOLSettlementUI, invalid ui id:", curUIId)
    Module.Settlement.Config.Events.evtEndSolSettlementPop:Invoke()
    self:_FriendGameEndsRecommend()
    Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.SOL)
end

function SettlementModule:OpenMPSettlementUI(curUIId)
    if not self._mpSettlementFlow or not Server.SettlementServer:CheckIsNewMPSettlement() or self:_CheckMPIsAIMatch() then
        Module.Settlement.Config.Events.evtEndMpSettlementPop:Invoke()
        Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.MP)
        Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)
        Server.HeroServer:_LatentUnlockCSHeroEvt()
        return
    end

    for key, value in ipairs(self._mpSettlementFlow) do
        if curUIId == value then
            local nextUIId = self._mpSettlementFlow[key + 1]

            if nextUIId then
                if nextUIId == UIName2ID.MPExpAndWeaponMain then
                    Facade.UIManager:AsyncShowUI(nextUIId)
                    return
                end

                if nextUIId == UIName2ID.PathOfGrowthLevelUpPanel then
                    if not self:_OpenMPPathOfGrowth() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.MPWeaponsUpgrade then
                    if not self:_OpenMPWeaponsUpgrade() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end

                -- Module.Settlement.Config.Events.evtEndWeaponUpgradePop:Invoke()

                if nextUIId == UIName2ID.MPRankSettlement then
                    if not self:_OpenMPRankSettlement() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.MPTopTournamentSettlement then
                    if not self:_OpenMPTopTournamentSettlement() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.BattlePassSettlement then
                    if not Module.BattlePass:ShowSettlementByCache() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end

                if nextUIId == UIName2ID.MandelBrickGainPop then
                    if not self:_OpenMPRewardMandel() then
                        self:OpenMPSettlementUI(nextUIId)
                    end

                    return
                end
            end
            Module.Settlement.Config.Events.evtEndWeaponUpgradePop:Invoke()
            Module.Settlement.Config.Events.evtEndMpSettlementPop:Invoke()
            self:_FriendGameEndsRecommend()
            Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.MP)
            Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)
            Server.HeroServer:_LatentUnlockCSHeroEvt()
            Server.SettlementServer:RecoverMPSettlement()
            self:_CloseAllUILoopAudio()

            return
        end
    end
end

function SettlementModule:_CloseAllUILoopAudio()
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UILobbyMedalsProgressLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarDownLoop)
end

function SettlementModule:_OpenSOLPathOfGrowthLevelUpPanel()
    local preSeasonLevel, seasonLevel = Server.RoleInfoServer.preSeasonLevel, Server.RoleInfoServer.seasonLevel

    if not preSeasonLevel then
        loginfo("azhengzheng:pre season level is nil")
        return false
    end

    if not seasonLevel then
        loginfo("azhengzheng:season level is nil")
        return false
    end

    if preSeasonLevel and seasonLevel then
        loginfo("azhengzheng:success open sol path of growth level up panel")
        Module.Reward:OpenSOLPathOfGrowthLevelUpPanel(seasonLevel, preSeasonLevel)
        return true
    end

    return false
end

function SettlementModule:_OpenSOLRankSettlement()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if settlementInfo and settlementInfo.player and settlementInfo.player.is_ranked_match then
        Facade.UIManager:AsyncShowUI(UIName2ID.SOLRankSettlement)
        return true
    end

    loginfo("azhengzheng:Isn't ranked match.")

    return false
end

function SettlementModule:_OpenSOLBattlePassSettlement()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if
        settlementInfo and settlementInfo.match_info and
            settlementInfo.match_info.match_type ~= EDSMatchType.kDSMatchTypeGuide
     then
        return Module.BattlePass:ShowSettlementByCache()
    end

    return false
end

-- slua.Do Module.Settlement:_OpenMPRankSettlement()
-- slua.Do Facade.UIManager:AsyncShowUI(UIName2ID.MPRankSettlement)
function SettlementModule:_OpenMPRankSettlement()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.is_ranked_match then
        Facade.UIManager:AsyncShowUI(UIName2ID.MPRankSettlement)
        return true
    end

    return false
end

function SettlementModule:_OpenMPTopTournamentSettlement()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        (mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.commander_score) and
            Server.SettlementServer:IsTDMCommanderGame()
     then
        Facade.UIManager:AsyncShowUI(UIName2ID.MPTopTournamentSettlement)
        return true
    end

    return false
end

function SettlementModule:_CheckMPIsAIMatch()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        mpSettlemengInfo and mpSettlemengInfo.match_info and mpSettlemengInfo.match_info.mode_info and
            mpSettlemengInfo.match_info.mode_info.game_rule == 11
     then
        return true
    end

    return false
end

function SettlementModule:_OpenMPPathOfGrowth()
    local preAccountLevel, accountLevel = Server.RoleInfoServer.preAccountLevel, Server.RoleInfoServer.accountLevel

    if preAccountLevel and accountLevel and preAccountLevel ~= accountLevel then
        Module.Reward:OpenMPPathOfGrowthLevelUpPanel(accountLevel, preAccountLevel)
        return true
    end

    return false
end

function SettlementModule:_OpenMPRewardMandel()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.drop_mantel_brick_id and
            mpSettlemengInfo.tdm_data.drop_mantel_brick_id ~= 0
     then
        return Module.Reward:OpenMandelBrickGainPop(mpSettlemengInfo.tdm_data.drop_mantel_brick_id)
    end

    return false
end

function SettlementModule:_OpenMPWeaponsUpgrade()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.weapon_change and
            #mpSettlemengInfo.tdm_data.weapon_change ~= 0
     then
        Facade.UIManager:AsyncShowUI(UIName2ID.MPWeaponsUpgrade, nil, nil, mpSettlemengInfo.tdm_data.weapon_change)
        return true
    end

    return false
end

function SettlementModule:_PreLoadItemIcon()
    if Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.MP then
        local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

        if
            not mpSettlemengInfo or not mpSettlemengInfo.tdm_data or not mpSettlemengInfo.tdm_data.weapon_change or
                #mpSettlemengInfo.tdm_data.weapon_change == 0
         then
            return
        end

        local delayTime = 0

        for _, value in pairs(mpSettlemengInfo.tdm_data.weapon_change) do
            local items = Server.InventoryServer:GetItemsById(value.ID, ESlotGroup.MPApply)

            if items and items[1] then
                Timer.DelayCall(
                    delayTime,
                    function()
                        RuntimeIconTool.PreLoadItemIcon(items[1])
                    end,
                    self
                )

                delayTime = delayTime + 0.025
            end
        end

        return
    end
end

function SettlementModule:_CheckAccountExpIsZero()
    if self:GetMPAccountExp() ~= 0 then
        return
    end

    Server.RoleInfoServer:ReSetAccountLevelInfo()
end

function SettlementModule:_OpenSOLUnlockWeapons()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if
        settlementInfo and settlementInfo.player and settlementInfo.player.unlock_mp_guns and
            #settlementInfo.player.unlock_mp_guns ~= 0
     then
        Facade.UIManager:AsyncShowUI(UIName2ID.SOLUnlockWeapons, nil, nil, settlementInfo.player.unlock_mp_guns)
        return true
    end

    return false
end

local ERaidSettlementType = {
    RaidSettlementSuccess = 0, -- // raid成功
    RaidSettlementFail = 1 -- // raid失败
}

function SettlementModule:CreateHallSettlementPreloadCallback()
    local CrackedSettlmentDisplayCtrl = CrackedSettlementDisplayLogic:GetCrackedSettlementDisplayCtrl()
    if CrackedSettlmentDisplayCtrl then
        return CrackedSettlmentDisplayCtrl.OnHallSettlementResReadyEvent:Add(
            CreateCPlusCallBack(self.OnHallSettlementResReadyEvent, self)
        )
    end
end

function SettlementModule:OnHallSettlementResReadyEvent()
    CrackedSettlementDisplayLogic:OnHallSettlementResReadyEvent()
end

function SettlementModule:CreateHallCharacterSetupDelegate(Character, slot, isTeam)
    if Character then
        Character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnHallCharacterSetup, self, Character, slot, isTeam))
    end
end

function SettlementModule:OnHallCharacterSetup(Character, slot, isTeam)
    CrackedSettlementDisplayLogic:OnHallCharacterSetup(Character, slot, isTeam)
end

function SettlementModule:ShowSettlementReturnConfirmWindow()
    SettlementLogic.ShowSettlementReturnConfirmWindow()
end

function SettlementModule:ShowSettlementBindingTeammateTips(bFail, teammateReturnMode)
    SettlementLogic.ShowSettlementBindingTeammateTips(bFail, teammateReturnMode)
end

function SettlementModule:GetFirstTeammateConfirm()
    return Module.Settlement.Field:GetFirstTeammateConfirm()
end

function SettlementModule:SetFirstTeammateConfirm(bFalg)
    Module.Settlement.Field:SetFirstTeammateConfirm(bFalg)
end

function SettlementModule:GetIgnoreTeammateConfirm()
    return Module.Settlement.Field:GetIgnoreTeammateConfirm()
end

function SettlementModule:SetIgnoreTeammateConfirm(bFalg)
    Module.Settlement.Field:SetIgnoreTeammateConfirm(bFalg)
end

function SettlementModule:GetTeammateReturnMode()
    return SettlementLogic.GetTeammateReturnMode()
end

function SettlementModule:SetSettlementUIIDList(uiIdList)
    Module.Settlement.Field:SetSettlementUIIDList(uiIdList)
end

function SettlementModule:GetSettlementUIIDList()
    return Module.Settlement.Field:GetSettlementUIIDList()
end

function SettlementModule:SetSettlementTabNameList(tabNameList)
    Module.Settlement.Field:SetSettlementTabNameList(tabNameList)
end

function SettlementModule:GetSettlementTabNameList()
    return Module.Settlement.Field:GetSettlementTabNameList()
end

function SettlementModule:SetSwitchedTabList(tabList)
    Module.Settlement.Field:SetSwitchedTabList(tabList)
end

function SettlementModule:GetSwitchedTabList()
    return Module.Settlement.Field:GetSwitchedTabList()
end

-- MP武器经验结算界面
-- weaponChangeDatas : WeaponChange结构，武器变更数据列表
-- accountExp        : TDMSettlementAccountExp，大战场账号等级变化信息
-- battleResult      : TDMResult,对局结果
function SettlementModule:OpenWeaponUpgradeDetailPanel(weaponChangeDatas, accountExp, battleResult)
    Facade.UIManager:AsyncShowUI(
        UIName2ID.WeaponUpgradeDetailPanel,
        nil,
        nil,
        weaponChangeDatas,
        accountExp,
        battleResult
    )
end

function SettlementModule:SetNotAutoContineFlag(bNotAuto)
    self.Field.bNotAutoContinue = bNotAuto
end

-- azhengzheng:局外结算
function SettlementModule:OpenNextPop(index)
    local openSuccess = true
    if index == 0 then
        openSuccess = self:Open1Pop()
    elseif index == 1 then
        openSuccess = self:Open2Pop()
    elseif index == 2 then
        openSuccess = self:Open3Pop()
    end
    if not openSuccess then
        self:OpenNextPop(index + 1)
    end

    if index > 2 then
    -- Module.Settlement.Config.Events.evtEndWeaponUpgradePop:Invoke()
    -- Module.Settlement.Config.Events.evtEndMpSettlementPop:Invoke()
    end
end

function SettlementModule:Open1Pop()
    if
        Server.RoleInfoServer.preAccountLevel and
            Server.RoleInfoServer.preAccountLevel ~= Server.RoleInfoServer.accountLevel
     then
        local accountUnlockweaponList =
            Server.RoleInfoServer:GetAccountUnlockweaponList(
            Server.RoleInfoServer.preAccountLevel,
            Server.RoleInfoServer.accountLevel
        )
        if #accountUnlockweaponList ~= 0 then
            Facade.UIManager:AsyncShowUI(
                UIName2ID.WBP_Settlement_Battle_WeaponsUnlocked,
                nil,
                nil,
                true,
                accountUnlockweaponList
            )
            return true
        end
    end
    return false
end

function SettlementModule:Open2Pop()
    if
        Server.RoleInfoServer.preAccountLevel and
            Server.RoleInfoServer.preAccountLevel ~= Server.RoleInfoServer.accountLevel
     then
        local set = {}
        local accountUnlockItems = {}
        for level = Server.RoleInfoServer.preAccountLevel + 1, Server.RoleInfoServer.accountLevel do
            local accountLevelUnlockItems = Server.RoleInfoServer:GetAccountUnlockItemsByLevel(level)
            for _, item in pairs(accountLevelUnlockItems) do
                local feature = item:GetFeature(EFeatureType.Weapon)
                if not feature or not feature:IsWeapon() then
                    if set[item.id] then
                        accountUnlockItems[set[item.id]].num = accountUnlockItems[set[item.id]].num + item.num
                    else
                        table.insert(accountUnlockItems, {item = item, num = item.num})
                        set[item.id] = #accountUnlockItems
                    end
                end
            end
        end
        if #accountUnlockItems ~= 0 then
            Facade.UIManager:AsyncShowUI(
                UIName2ID.WBP_Settlement_Battle_WeaponsUnlocked,
                nil,
                nil,
                nil,
                accountUnlockItems
            )
            return true
        end
    end
    return false
end

function SettlementModule:Open3Pop()
    if Server.SettlementServer:GetTDMSettlementWeaponChangeFlag() then
        local weaponChangeInfo = Server.SettlementServer:GetTDMWeaponChangeInfo()
        if weaponChangeInfo then
            Server.SettlementServer.bShowWeaponChangeUI = false
            Facade.UIManager:AsyncShowUI(UIName2ID.WBP_Settlement_Battle_WeaponUpgrades, nil, nil, weaponChangeInfo)
            return true
        end
    end
    return false
end

function SettlementModule:OpenWeaponUpgradesPop(weaponChangeInfo)
    Facade.UIManager:AsyncShowUI(UIName2ID.MPWeaponUpMain, nil, nil, weaponChangeInfo)
end

function SettlementModule:GetFriendAddExpBuf()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    return settlementInfo and settlementInfo.player and settlementInfo.player.end_game and
        settlementInfo.player.end_game.friend_add_exp_buf or
        0
end

function SettlementModule:GetSafehouseAddExpBuf()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    return settlementInfo and settlementInfo.player and settlementInfo.player.end_game and
        settlementInfo.player.end_game.safehouse_add_exp_buf or
        0
end

function SettlementModule:GetSOLAccountAddExpBuf()
    local channel = Server.SDKInfoServer:GetChannel()

    if not channel or (channel ~= EChannelType.kChannelQQ and channel ~= EChannelType.kChannelWechat) then
        return 0
    end

    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if settlementInfo and settlementInfo.player then
        if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
            return settlementInfo.player.end_game and settlementInfo.player.end_game.account_add_exp_buf or 0
        end

        if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
            return settlementInfo.player.quit_game and settlementInfo.player.quit_game.account_add_exp_buf or 0
        end
    end

    return 0
end

function SettlementModule:GetSOLCybercafeSettlementBuff()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    return settlementInfo and settlementInfo.player and settlementInfo.player.end_game and
        settlementInfo.player.end_game.cybercafe_settlement_buff or
        0
end

function SettlementModule:GetEndGameType()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    return settlementInfo and settlementInfo.player and settlementInfo.player.end_game and
        settlementInfo.player.end_game.type or
        0
end

function SettlementModule:GetMPAccountExp()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    return mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.account_exp and
        mpSettlemengInfo.tdm_data.account_exp.exp or
        0
end

---@desc 获取成就id列表
---@return number[]
function SettlementModule:GetAchievements()
    return SettlementLogic.GetAchievements()
end

---@desc 获取分享页面展示数据
---@return number[]
function SettlementModule:GetSharePlayerData()
    return SettlementLogic.GetSharePlayerData()
end

function SettlementModule:SetBGVideoViewHandle(handle)
    self.Field.bgVideoViewHandle = handle
end

function SettlementModule:GetBGVideoViewHandle()
    return self.Field.bgVideoViewHandle
end

function SettlementModule:GetSOLUniversitySettlementBuff()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if settlementInfo and settlementInfo.player then
        if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypePlaying then
            return settlementInfo.player.playing and settlementInfo.player.playing.university_settlement_buff or 0
        end

        if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
            return settlementInfo.player.end_game and settlementInfo.player.end_game.university_settlement_buff or 0
        end

        return settlementInfo.player.quit_game and settlementInfo.player.quit_game.university_settlement_buff or 0
    end

    return 0
end

-- azhengzheng:用于快速测试MP积分赛升段效果
function SettlementModule:OpenMPRankSettlementWithFakeData(beginScore, scoreDelta)
    if beginScore < 0 then
        Module.CommonTips:ShowSimpleTip("Begin Score can't less 0")
        return
    end

    if beginScore + scoreDelta < 0 then
        Module.CommonTips:ShowSimpleTip("Begin Score + score delta can't less 0")
        return
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.MPRankSettlement, nil, nil, beginScore, scoreDelta)
end

function SettlementModule:SetMapInfo(widget, bFromWhere)
    if not widget or not bFromWhere then
        return
    end

    local wtMapNameTB = widget:Wnd("wtMapNameTB", UITextBlock)
    local wtMapDifficultyTB = widget:Wnd("wtMapDifficultyTB", UITextBlock)

    -- 来自raid
    if bFromWhere == 1 then
        local mapName, mapDifficulty = Server.SettlementServer:GetRaidMapNameAndDifficulty()

        if wtMapNameTB and mapName then
            wtMapNameTB:SetText(mapName)
        end

        if wtMapDifficultyTB and mapDifficulty then
            wtMapDifficultyTB:SetText(mapDifficulty)
        end

        return
    end
end

function SettlementModule:ShowCampEmptyTips()
    xxwwerror("SettlementModule:ShowCampEmptyTips")
    if not VersionUtil.IsShipping() or VersionUtil.IsPreRelease() then
        Module.GCloudSDK:ShowCommonTip(
            "这里卡结算是设计如此，因为对面阵营没人。开房间需要给对面阵营安排机器人或真人玩家，否则后台结算服务会报错。",
            "知道了",
            nil,
            true,
            nil,
            nil,
            true,
            false
        )
    end
end

function SettlementModule:PlayMPEndMusic(bWin, avatarID)
    xxwwerror("SettlementModule:PlayMPEndMusic", bWin, avatarID)
    --Facade.SoundManager:PlayIngameMusicByName(DFMAudioRes.MusicBFSettlementWin)
    if bWin and avatarID and avatarID > 0 and Module.Hero:IsHighLevel(avatarID) then
        local musicName = Module.Hero:GetFashionMusicName(avatarID)
        if musicName and musicName ~= "" then
            Facade.SoundManager:PlayIngameMusicByName(musicName)
        end
    end
end

function SettlementModule:PlaySettlementBackgroundVideo()
    local handle = self:GetBGVideoViewHandle()
    local ui = handle and handle:GetUIIns()

    if ui then
        ui:Show()
    else
        handle = Facade.UIManager:AsyncShowUI(UIName2ID.SettlementCutscene2)
        Module.Settlement:SetBGVideoViewHandle(handle)
    end
end

function SettlementModule:_OnSettlementStart()
    Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(true)
end

function SettlementModule:TryOpenSettlementUI()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self:_PreLoadItemIcon()
        self:_CheckMpExpIsZero()
        Module.Settlement.Config.Events.evtBeginMpSettlementPop:Invoke()
    else
        Module.Settlement.Config.Events.evtBeginSolSettlementPop:Invoke()
    end
    --局内到局外(预加成长之路部分载资源)
    Module.Gunsmith:PreloadingImages()

    self:OpenSettlementUI("settlementStart")
end

function SettlementModule:_CheckMpExpIsZero()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.account_exp and
            mpSettlemengInfo.tdm_data.account_exp.exp and
            mpSettlemengInfo.tdm_data.account_exp.exp ~= 0
     then
        return
    end

    Server.RoleInfoServer:ReSetAccountLevelInfo()
end

function SettlementModule:OpenSettlementUI(curUIID)
    -- azhengzheng:防止局内打开结算界面
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        return
    end

    if not curUIID then
        loginfo("azhengzheng:Open settlement UI fail!Please check cur UI ID.")
        self:_OnSettlementEnd()
        return
    end

    local settlementInfoSource = Server.SettlementServer:GetSettlementInfoSource()
    local settlementStep = Module.Settlement.Config.ESettlementInfoSource2Step[settlementInfoSource]
    logerror("weixinhuang: SettlementModule:OpenSettlementUI settlementInfoSource = ", settlementInfoSource)
    if not settlementStep then
        loginfo(
            "azhengzheng:Open settlement UI fail!Please check settlement info source " ..
                settlementInfoSource .. " step."
        )
        self:_OnSettlementEnd()
        return
    end

    -- azhengzheng:针对MP模式的特殊处理
    if settlementInfoSource == SettlementDefine.ESettlementInfoSource.MP then
        -- azhengzheng:针对SOL模式的特殊处理
        -- azhengzheng:人机模式没有结算流程
        if self:_CheckMpIsHumanMachineGameRule() then
            loginfo("azhengzheng:Open mp settlement UI fail!Because is human machine game rule.")
            self:_OnSettlementEnd()
            return
        end
    elseif settlementInfoSource == SettlementDefine.ESettlementInfoSource.SOL then
        -- azhengzheng:新手引导没有结算流程
        if self:_CheckSOLIsGuide() then
            loginfo("azhengzheng:Open sol settlement UI fail!Because is guide.")
            self:_OnSettlementEnd()
            return
        end
    end

    -- azhengzheng:多停一次，防止音频泄露
    self:_StopAllLoopUIAudioEvent()

    for key, value in ipairs(settlementStep) do
        if curUIID == value then
            local nextUIID = settlementStep[key + 1]

            if nextUIID then
                if nextUIID == UIName2ID.MPExpAndWeaponMain then
                    Facade.UIManager:AsyncShowUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.SOLRankSettlement then
                    if self:_OpenSOLRankSettlementUI() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.PathOfGrowthLevelUpPanel then
                    logerror("azhengzheng:try open path of growth level up panel.")
                    if self:_OpenPathOfGrowthLevelUpPanel(settlementInfoSource) then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.MPWeaponsUpgrade then
                    if self:_OpenMPWeaponsUpgradeUI() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.MPRankSettlement then
                    if self:_OpenMPRankSettlement() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.MPTopTournamentSettlement then
                    if self:_OpenMPTopTournamentSettlement() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.BattlePassSettlement then
                    if Module.BattlePass:ShowSettlementByCache() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.MandelBrickGainPop then
                    if self:_OpenMandelBrickGainPop() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                if nextUIID == UIName2ID.SOLUnlockWeapons then
                    if self:_OpenSOLUnlockWeaponsUI() then
                        return
                    end

                    self:OpenSettlementUI(nextUIID)
                    return
                end

                self:_OnSettlementEnd()
                return
            end

            self:_OnSettlementEnd()
            return
        end
    end

    self:_OnSettlementEnd()
end

function SettlementModule:_OnSettlementEnd()
    self:_StopAllLoopUIAudioEvent()
    self:_FriendGameEndsRecommend()
    Module.Settlement.Field:SetSOLEliminationReplayWatched()

    if Server.SettlementServer:GetSettlementInfoSource() ~= SettlementDefine.ESettlementInfoSource.None then
        if Server.SettlementServer:GetSettlementInfo() and Server.SettlementServer:GetEndGameType() ~= EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
            Module.Settlement.Config.Events.evtEndAllSettlementPop:Invoke()
        end
        if Server.SettlementServer:GetMPSettlementInfo() and Server.SettlementServer:GetMPQuitGameIsLeave() == false then
            Module.Settlement.Config.Events.evtEndAllSettlementPop:Invoke()
        end
    end

    Server.SettlementServer:ResetSettlementInfoSource()
    Server.SettlementServer:SetSOLEliminationReplayStartTime()
    Server.SettlementServer:ClearSettlementInfo()
    Server.HeroServer:_ShouldTriggerUnlockEvtImmediate(false)
    Server.HeroServer:_LatentUnlockCSHeroEvt()

    -- azhengzheng:MP大厅
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        Module.Settlement.Config.Events.evtEndWeaponUpgradePop:Invoke()
        Module.Settlement.Config.Events.evtEndMpSettlementPop:Invoke()
        Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.MP)
        Module.NetworkBusiness:CheckAndShowNetworkQualityWindow()
        return
    end

    -- azhengzheng:SOL大厅
    logerror("azhengzheng:evtEndSolSettlementPop:Invoke()")
    Module.Settlement.Config.Events.evtEndSolSettlementPop:Invoke()
    Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.SOL)
    Module.NetworkBusiness:CheckAndShowNetworkQualityWindow()
end

function SettlementModule:_CheckMpIsHumanMachineGameRule()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()
    return mpSettlemengInfo and mpSettlemengInfo.match_info and mpSettlemengInfo.match_info.mode_info and
        mpSettlemengInfo.match_info.mode_info.game_rule == MatchGameRule.TDMHumanMachineGameRule
end

function SettlementModule:_CheckSOLIsGuide()
    local solSettlementInfo = Server.SettlementServer:GetSettlementInfo()
    return solSettlementInfo and solSettlementInfo.match_info and solSettlementInfo.match_info.is_guide
end

function SettlementModule:_FriendGameEndsRecommend()
    local settlementInfoSource = Server.SettlementServer:GetSettlementInfoSource()

    if settlementInfoSource == SettlementDefine.ESettlementInfoSource.None then
        return
    end

    local req = pb.CSFriendGameEndsRecommendReq:New()

    req:Request(
        function(res)
            if not res or res.result ~= 0 or not res.player_list or #res.player_list == 0 then
                return
            end

            Module.Social:ShowRecommendAddFriendPop(res.player_list, res.recommend_cd)
        end
    )
end

function SettlementModule:_StopAllLoopUIAudioEvent()
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarDownLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UILobbyMedalsProgressLoop)
end

function SettlementModule:_OpenSOLRankSettlementUI()
    local solSettlementInfo = Server.SettlementServer:GetSettlementInfo()

    if solSettlementInfo and solSettlementInfo.player and solSettlementInfo.player.is_ranked_match then
        Facade.UIManager:AsyncShowUI(UIName2ID.SOLRankSettlement)
        return true
    end

    loginfo("azhengzheng:Isn't ranked match.")
    return false
end

function SettlementModule:_OpenPathOfGrowthLevelUpPanel(settlementInfoSource)
    if settlementInfoSource == SettlementDefine.ESettlementInfoSource.Arena then
        return false
    end

    if settlementInfoSource == SettlementDefine.ESettlementInfoSource.MP then
        local preAccountLevel, accountLevel = Server.RoleInfoServer.preAccountLevel, Server.RoleInfoServer.accountLevel

        if preAccountLevel and accountLevel and preAccountLevel ~= accountLevel then
            Module.Reward:OpenMPPathOfGrowthLevelUpPanel(accountLevel, preAccountLevel)
            return true
        end

        return false
    end

    local preSeasonLevel, seasonLevel = Server.RoleInfoServer.preSeasonLevel, Server.RoleInfoServer.seasonLevel

    if not preSeasonLevel then
        loginfo("azhengzheng:pre season level is nil")
        return false
    end

    if not seasonLevel then
        loginfo("azhengzheng:season level is nil")
        return false
    end

    Module.Reward:OpenSOLPathOfGrowthLevelUpPanel(seasonLevel, preSeasonLevel)
    return true
end

function SettlementModule:_OpenMPWeaponsUpgradeUI()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if
        mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.weapon_change and
            #mpSettlemengInfo.tdm_data.weapon_change ~= 0
     then
        Facade.UIManager:AsyncShowUI(UIName2ID.MPWeaponsUpgrade, nil, nil, mpSettlemengInfo.tdm_data.weapon_change)
        return true
    end

    return false
end

function SettlementModule:_OpenMPRankSettlementUI()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if mpSettlemengInfo and mpSettlemengInfo.tdm_data and mpSettlemengInfo.tdm_data.is_ranked_match then
        Facade.UIManager:AsyncShowUI(UIName2ID.MPRankSettlement)
        return true
    end

    return false
end

function SettlementModule:_OpenMandelBrickGainPop()
    local mpSettlemengInfo = Server.SettlementServer:GetMPSettlementInfo()

    if mpSettlemengInfo and mpSettlemengInfo.tdm_data then
        if mpSettlemengInfo.tdm_data.tdm_point_weight_list and #mpSettlemengInfo.tdm_data.tdm_point_weight_list ~= 0 then
            for _, value in pairs(mpSettlemengInfo.tdm_data.tdm_point_weight_list) do
                if value.rule_id == 1 and value.is_award_succ and value.drop_item_id and value.drop_item_id ~= 0 then
                    return Module.Reward:OpenMandelBrickGainPop(value.drop_item_id)
                end
            end
        end

        if mpSettlemengInfo.tdm_data.drop_mantel_brick_id and mpSettlemengInfo.tdm_data.drop_mantel_brick_id ~= 0 then
            return Module.Reward:OpenMandelBrickGainPop(mpSettlemengInfo.tdm_data.drop_mantel_brick_id)
        end
    end

    return false
end

function SettlementModule:_OpenSOLUnlockWeaponsUI()
    local solSettlementInfo = Server.SettlementServer:GetSettlementInfo()

    if
        solSettlementInfo and solSettlementInfo.player and solSettlementInfo.player.unlock_mp_guns and
            #solSettlementInfo.player.unlock_mp_guns ~= 0
     then
        Facade.UIManager:AsyncShowUI(UIName2ID.SOLUnlockWeapons, nil, nil, solSettlementInfo.player.unlock_mp_guns)
        return true
    end

    return false
end

function SettlementModule:GMShowPlayTime(bShow)
    self.Field.bGMEnableShowPlayTime = bShow
    self.Config.Events.evtGMSetShowPlayTime:Invoke()
end

function SettlementModule:GMIsShowPlayTime()
    return self.Field.bGMEnableShowPlayTime
end

function SettlementModule:CheckSOLTeachingVideoCanPlay()
    -- azhengzheng:应策划要求，she1手游暂时屏蔽撤离教学https://tapd.woa.com/tapd_fe/20421949/story/detail/1020421949122775369?menu_workitem_type_id=0
    if not IsHD() then
        return false
    end

    local solMapID = Server.SettlementServer:GetSOLMapID()

    if not solMapID then
        return false
    end

    local globalConst = DFMGlobalConst.GetGlobalConst("SOLTeachingVideoMapIDWithLevelLimitList")

    if not globalConst then
        logerror("azhengzheng:Please check SOLTeachingVideoMapIDWithLevelLimitList")
        return false
    end

    local globalConstList = string.split(globalConst, ";")

    for _, value in pairs(globalConstList) do
        if not string.isempty(value) then
            local mapIDWithLevelLimit = string.split(value, ":")

            if not string.isempty(mapIDWithLevelLimit[1]) and not string.isempty(mapIDWithLevelLimit[2]) then
                local mapID, levelLimit = tonumber(mapIDWithLevelLimit[1]), tonumber(mapIDWithLevelLimit[2])

                if mapID and levelLimit and solMapID == mapID and Server.RoleInfoServer.seasonLevel <= levelLimit then
                    return true
                end
            end
        end
    end

    return false
end

-- 任务信息获取
function SettlementModule:SupplementMPQuestInfo(PlayerActivitycomponent)
    if not isvalid(PlayerActivitycomponent) then
        loginfo("xxxxxxxxxxxxxx  PlayerActivitycomponent isvalid ")
        return
    end
    local GoalTable = Facade.TableManager:GetTable("ActivityTaskGoals")
    local HeroLevel = Facade.TableManager:GetTable("HeroLevel")
    local GunSkinRewardsConfig = Facade.TableManager:GetTable("WeaponSkin/GunSkinRewards")
    local MasterGunSkinRewardsConfig = Facade.TableManager:GetTable("WeaponSkin/MasterGunSkinRewards")
    local HZAchievementTable = Facade.TableManager:GetTable("HZAchievement")
    local AchievementTable = Facade.TableManager:GetTable("Achievement")

    local _taskInfoList = {}
    for _, data in pairs(PlayerActivitycomponent.MPQuestObjectionList) do
        local task_Id = data.task_id
        loginfo("xxxxxxxxxxxxxx  MPQuestObjectionList task_Id = ", task_Id)
        local needShow = false
        if
            (data.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Hero and
                (data.type == 1 or data.type == 2)) or
                (data.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Achievement and
                    HZAchievementTable and
                    HZAchievementTable[task_Id]) or
                (data.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Collection) or
                (data.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Quest)
         then
            needShow = true
        end
        if needShow then
            if _taskInfoList[data.source] == nil then
                _taskInfoList[data.source] = {}
            end
            if _taskInfoList[data.source][task_Id] == nil then
                _taskInfoList[data.source][task_Id] = {
                    taskID = task_Id,
                    objectIDs = {},
                    source = data.source,
                    activity_id = data.activity_id,
                    type = data.type
                }
            end
            table.insert(_taskInfoList[data.source][task_Id].objectIDs, data.id)
        end
    end

    local sortList = {}
    for _, taskInfos in pairs(_taskInfoList) do
        for _, taskInfo in pairs(taskInfos) do
            table.insert(sortList, taskInfo)
        end
    end

    local items = {}
    for index, value in ipairs(sortList) do
        local questInfo = FMPQuestInfoSupplement()
        questInfo.QuestID = value.taskID
        questInfo.ObjectiveList = value.objectIDs
        loginfo("xxxxxxxxxxxxxx  FMPQuestInfoSupplement QuestID = ", value.taskID)
        local reward1ID = nil
        local name = ""
        if value.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Hero then
            if value.type == 1 then
                questInfo.QuestType = EBattleFieldQuestType.EBattleFieldQuestType_AgentFile
                for _, heroLevelInfo in pairs(HeroLevel) do
                    if heroLevelInfo and heroLevelInfo.OpMetaTaskID == value.taskID then
                        local heroID = heroLevelInfo.HeroID
                        local level = heroLevelInfo.OpMetaLevel
                        name =
                            string.format(
                            "%s-%s",
                            HeroHelperTool.GetHeroName(tonumber(heroID)),
                            self:GetHeroRankByLevel(level)
                        )
                        local awards = Server.HeroServer:GetHeroLineRewards(tostring(heroID), level)
                        if awards then
                            for index, value in ipairs(awards) do
                                reward1ID = tonumber(value)
                            end
                        end
                    end
                end
            end
            if value.type == 2 then
                questInfo.QuestType = EBattleFieldQuestType.EBattleFieldQuestType_AgentRecruitment
                name = HeroHelperTool.GetHeroName(value.taskID)
                reward1ID = value.taskID
            end
        elseif value.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Collection then
            questInfo.QuestType = EBattleFieldQuestType.EBattleFieldQuestType_FirearmsAppearance
            local rewardConfigRow = GunSkinRewardsConfig[value.taskID]
            if rewardConfigRow then
                if
                    table.contains(value.objectIDs, rewardConfigRow.BFConnectionID1) or
                        table.contains(value.objectIDs, rewardConfigRow.BFConnectionID2)
                 then
                    reward1ID = rewardConfigRow.GunSkinRewards
                end
            else
                loginfo("xxxxxxxxxxxxxxxxx SettlementModule:SupplementMPQuestInfo rewardConfigRow = nil  taskid = ", value.taskID)
                rewardConfigRow = MasterGunSkinRewardsConfig[value.taskID]
                if rewardConfigRow then
                    local goldContains = false
                    for _, goalsBFID in ipairs(rewardConfigRow.GoalsBF) do
                        if table.contains(value.objectIDs, goalsBFID) then
                            goldContains = true
                            break
                        end
                    end
                    if goldContains then
                        reward1ID = rewardConfigRow.RewardSkinIDs[1]
                        for _, skinId in ipairs(rewardConfigRow.RewardSkinIDs) do
                            if not Server.CollectionServer:IsOwnedWeaponSkin(skinId) then
                                reward1ID = skinId
                                break
                            end
                        end
                    end
                end
            end

            if reward1ID then
                local ItemTable = Facade.TableManager:GetRowByKey("GameItem", tostring(reward1ID))
                if ItemTable then
                    name = ItemTable.Name
                end
            end
        elseif value.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Achievement then
            questInfo.QuestType = EBattleFieldQuestType.EBattleFieldQuestType_Agentbadge
            local achievementTask = HZAchievementTable[value.taskID]
            if achievementTask == nil then
                for _, info in pairs(HZAchievementTable) do
                    if table.contains(info.MissionBR, value.objectIDs[1]) then
                        achievementTask = info
                    end
                end
            end
            if achievementTask then
                name = achievementTask.Name
                for _, badgeid in ipairs(achievementTask.Badgeid) do
                    reward1ID = badgeid
                end
            end
        elseif value.source == Module.Settlement.Config.EObjectiveSource.ObjectiveSource_Quest then
            questInfo.QuestType = EBattleFieldQuestType.EBattleFieldQuestType_Armory
            local items = Server.ArmoryServer:GetArmoryItems()

            ---@type ArmoryItemData[]
            local found = {}
            for _, item in pairs(items) do
                for _, task in pairs(item.unlockTasks) do
                    if task and table.contains(value.objectIDs, task.goalID) then
                        table.insert(found, item)
                    end
                end
            end

            local Sort = require "DFM.Business.DataStruct.Common.Base.Sort"
            Sort.MergeSort(
                found,
                Sort.Preferenced(
                    {
                        -- 优先返回正激活的项目
                        Sort.MatchCriteria(
                            function(item)
                                return item.bActivated
                            end
                        ),
                        -- 然后是队列最靠前的项目
                        Sort.ByField("queueOrder")
                    }
                )
            )
            if found [1] then
                name = found[1]:GetItemBase().name
                reward1ID = found[1]:GetItemBase().id
            end
        end
        questInfo.Name = tostring(name)
        if reward1ID then
            questInfo.QuestAwardID = tostring(reward1ID)
            local itemData = ItemBase:New(reward1ID)
            if itemData and itemData.itemIconPath and itemData.itemIconPath.AssetPathName then
                questInfo.QuestAwardIcon = tostring(itemData.itemIconPath.AssetPathName)
            else
                loginfo("xxxxxxxxxxxxxx itemData.itemIconPath = nil taskId =", reward1ID, value.taskID)
            end
        else
            loginfo("xxxxxxxxxxxxxx itemData.reward1ID = nil taskId = ", value.taskID)
        end

        table.insert(items, questInfo)
    end
    loginfo("xxxxxxxxxxxxxx")
    PlayerActivitycomponent.QuestInfoSupplementList = items
end

function SettlementModule:GetHeroRankByLevel(level)
    local hero2RankTable = Facade.TableManager:GetTable("HeroLevel2Rank")
    if hero2RankTable then
        for _, rankData in pairs(hero2RankTable) do
            if rankData.Level == level then
                return rankData.Rank
            end
        end
    end
    return ""
end

function SettlementModule:GetLastRoomAvgPing()
    return self.Field.AvgPing
end

function SettlementModule:GetLastRoomSDPing()
    return self.Field.SDPing
end

return SettlementModule
