----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------


local HeroBadgeDetail = ui("HeroBadgeDetail")
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"

function HeroBadgeDetail:Ctor()
    self._wtBadgeName = self:Wnd("DFTextBlock_27", UITextBlock)
    self._wtConditionTxt = self:Wnd("TextBlock", UITextBlock)

    self._wtDesc = self:Wnd("DFTextBlock_71", UITextBlock)

    self.wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self.wtCondition1 = self:Wnd("WBP_Achievement_UnlockConditions", UIWidgetBase)
    self.wtCondition2 = self:Wnd("WBP_Achievement_UnlockConditions_1", UIWidgetBase)
    self.wtCondition3 = self:Wnd("WBP_Achievement_UnlockConditions_2", UIWidgetBase)
    self.wtCondition4 = self:Wnd("WBP_Achievement_UnlockConditions_3", UIWidgetBase)

    self._wtBadgeLevels = self:Wnd("WBP_Achievement_Badge_Level_1", UIWidgetBase)
    self._wtBadgeLevelsItem1 = self._wtBadgeLevels:Wnd("WBP_Achievement_BadgeLeve_ltem", UIWidgetBase)
    self._wtBadgeLevelsItem2 = self._wtBadgeLevels:Wnd("WBP_Achievement_BadgeLeve_ltem_1", UIWidgetBase)
    self._wtBadgeLevelsItem3 = self._wtBadgeLevels:Wnd("WBP_Achievement_BadgeLeve_ltem_2", UIWidgetBase)
    self._wtBadgeLevelsItem4 = self._wtBadgeLevels:Wnd("WBP_Achievement_BadgeLeve_ltem_3", UIWidgetBase)

    self._wtCareerModeTipsPanel = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)
    self._wtCareerModeTips = self._wtCareerModeTipsPanel:Wnd("DFCheckBox_Icon", UICheckBox)
    self._wtCareerModeTips:SetCallback(self._ShowAllUnlockConditions, self)

    self.wtExchangeBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self.wtExchangeBtn:Event("OnClicked", self._OnJumpClick, self)

    self._heroBadgeId = -1
    self._achieveId = -1
    self._achieveData = {}
    self._badgeLevel = 1
    self._badgeIds = {}
    self._seriesType = -1
end

function HeroBadgeDetail:OnOpen()
end

function HeroBadgeDetail:_ShowAllUnlockConditions()
    local function fFinishCallback(uiIns)
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.HeroBadgeAchievementPop, fFinishCallback, nil, self._achieveId,
        Module.Hero.Config.EAchievementType.Badge)

    self._wtCareerModeTips:SetIsChecked(false, false)
end

function HeroBadgeDetail:_OnJumpClick()
    Module.RoleInfo.Field:SetSocialBadgeId(self._achieveId)

    Module.CommonBar:CheckTopTabGroup(5, true, 2)
    Module.CommonBar:CheckTopTabGroup(4, true, 4)
end

function HeroBadgeDetail:_OnChangeLevel(lv)
    self._badgeLevel = lv
    self._heroBadgeId = self._badgeIds[self._badgeLevel]
    self:RefreshConciton()
    self:SetCareer()
    self:RefreshBadge()
end

function HeroBadgeDetail:SetCareer()
    local achieveTable = HeroHelperTool.GetHZAchievementDataTableRow(self._achieveId)
    if not achieveTable then return end
    local len = #achieveTable.BadgeId

    for i = 1, 4, 1 do
        local widget = self['_wtBadgeLevelsItem' .. i]
        if widget then
            local textBlock = widget:Wnd("DFTextBlock_148", UITextBlock)
            local image = widget:Wnd("DFImage_52", UIImage)
            local selectImg = widget:Wnd("DFImage_111", UIImage)
            local btn = widget:Wnd("DFButton_0", UIButton)

            btn:RemoveEvent('OnClicked')
            btn:Event("OnClicked", self._OnChangeLevel, self, i)

            if self._badgeLevel == i then
                selectImg:Visible()
            else
                selectImg:Collapsed()
            end

            if achieveTable.BadgeId and achieveTable.BadgeId[i] then
                widget:Visible()
                local careerBadgeId = achieveTable.BadgeId[i]
                local heroBadgeData = Facade.TableManager:GetTable("HeroBadgeData")
                local heroBadgeInfo = heroBadgeData[careerBadgeId]
                textBlock:SetText(StringUtil.Key2StrFormat(Module.Hero.Config.Loc.HeroBadgeLevel,
                    { ["Level"] = i }))

                if heroBadgeInfo then
                    image:AsyncSetImagePath(heroBadgeInfo.BadgeImage)
                end

                local isUnlocked = Server.HeroServer:IsAccessoryUnlocked(Module.Hero:GetCurShowHeroId(), careerBadgeId)
                widget:SetDis(not isUnlocked, i == len)
            else
                widget:Collapsed()
            end
        end
    end
end

function HeroBadgeDetail:Refresh(achieveId, achieveData, seriesType)
    local info = HeroHelperTool.GetHZAchievementDataTableRow(achieveId)
    if not info then
        logerror("HeroBadgeDetail:Show info is nil", achieveId)
        return
    end

    if not achieveData then
        return
    end

    self:HideAllCondition()

    -- if seriesType == 0 then
    --     self._seriesType = RoleInfoLogic:IsInMp() and 2 or 1
    -- else
    self._seriesType = seriesType
    -- end

    self._achieveId = achieveId
    self._achieveData = achieveData
    self._badgeIds = info.BadgeId

    if achieveData.bCareer then --生涯徽章 显示4个图标
        self._wtBadgeLevels:SetVisibility(ESlateVisibility.Visible)
        self._badgeLevel = achieveData.maxBadgeLv ~= 4 and achieveData.maxBadgeLv + 1 or achieveData.maxBadgeLv
        self:SetCareer()
    else
        self._wtBadgeLevels:SetVisibility(ESlateVisibility.Collapsed)
        self._badgeLevel = 1
    end
    self._heroBadgeId = info.BadgeId[self._badgeLevel]

    if achieveData.bShowQuestion then --生涯徽章 显示4个图标
        self._wtCareerModeTipsPanel:Visible()
    else
        self._wtCareerModeTipsPanel:Collapsed()
    end

    self:RefreshConciton()
    self:RefreshBadge()
end

function HeroBadgeDetail:RefreshBadge()
    local curAccessoryData = Server.HeroServer:GetSelectedAccessory(Module.Hero:GetCurShowHeroId(), self._heroBadgeId)
    local curAccessoryUIData = HeroHelperTool.GetHeroBadgeDataRow(self._heroBadgeId)

    if curAccessoryUIData then
        local nameStr = curAccessoryUIData.BadgeName
        if IsInEditor() then
            nameStr = nameStr .. ' ' .. self._achieveId
        end

        self._wtBadgeName:SetText(nameStr)
        self.wtQualityIcon:AsyncSetImagePath(Module.Hero.Config.QualityIconMapping[curAccessoryUIData.BadgeLevel])
        self.wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(curAccessoryUIData.BadgeLevel))
        -- 徽章描述
        if curAccessoryUIData.BadgeDescription then
            self._wtDesc:SetText(curAccessoryUIData.BadgeDescription)
        end
    end
    local HeroConfig = Module.Hero.Config

    if curAccessoryData and curAccessoryUIData then
        if curAccessoryData.is_unlock then
            -- 解锁时间数据
            self._wtConditionTxt:SetText(string.format(HeroConfig.Loc.HeroUnlockTime,
                TimeUtil.TransTimestamp2MMDDHHMMStr(curAccessoryData.unlocked_timestamp or 0, "YYMMDDHHMM")))
            self.wtExchangeBtn:Visible()
        else
            self._wtConditionTxt:SetText(HeroConfig.Loc.HeroCondition)
            self.wtExchangeBtn:Collapsed()
        end
    end
end

function HeroBadgeDetail:HideAllCondition()
    for i = 1, 4, 1 do
        local wt = self['wtCondition' .. i]
        if not wt then return end
        wt:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function HeroBadgeDetail:RefreshConciton()
    local processInfos

    if self._achieveData.bCareer then
        processInfos = self._achieveData.progressInfo[self._badgeLevel] -- 等级
    else
        processInfos = self._seriesType == 0 and self._achieveData.solProgressInfo or
            self._achieveData.mpProgressInfo
    end

    for i = 1, 4, 1 do
        local wt = self['wtCondition' .. i]
        if not wt then
            return
        end

        local v = processInfos[i]
        if not v then
            wt:SetVisibility(ESlateVisibility.Collapsed)
        else
            if v.progressDesc and v.progressDesc ~= "" then
                local conditinonTxt = wt:Wnd("DFTextBlock_52", UILabel)
                local progressTxt = wt:Wnd("DFTextBlock", UILabel)
                local progressBar = wt:Wnd("DFProgressBar_50", UILabel)
                local slider = wt:Wnd("DFSlider_107", UIWidgetBase)
                slider:SetMinValue(0)
                slider:SetMaxValue(1)
                slider:SetValue(v.progress)
                slider:SetVisibility(ESlateVisibility.HitTestInvisible) --可见child不可交互
                local TaskPrgressText = StringUtil.Key2StrFormat(Module.Hero.Config.Loc.AchievementTaskProgress,
                    { ["taskprogress"] = math.floor(v.progress * 100) })

                conditinonTxt:SetText(v.progressDesc)
                progressBar:SetPercent(v.progress)
                progressTxt:SetText(TaskPrgressText)
                wt:SetVisibility(ESlateVisibility.Visible)
                wt:SetDisabled(v.progress < 1)
            else
                wt:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
    end
end

return HeroBadgeDetail
