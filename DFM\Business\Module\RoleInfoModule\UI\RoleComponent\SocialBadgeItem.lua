----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local EViewDragMode = import "EViewDragMode"

---@class SocialBadgeItem : LuaUIBaseView
local SocialBadgeItem = ui("SocialBadgeItem")

function SocialBadgeItem:Ctor()
    self._wtBadgeIcon = self:Wnd("DFImage_Icon", UIImage)
    -- self._wtSeclectBtn = self:Wnd("DFButton_0", UIWidgetBase)
    self._wtSelectImg = self:Wnd("WBP_SlotCompSelected_1", UIWidgetBase) --选中
    self._wtSlotCompMaskSmallLock = self:Wnd("WBP_SlotCompMaskSmallLock", UIWidgetBase) -- 遮挡

    -- self._wtSeclectBtn:Event("OnClicked", self._SelectOnClicked, self)
    self._wtHoverComp = self:Wnd("WBP_CommonHoverBg_V2", UIWidgetBase)
    self._wtRoot = self:Wnd("wtRoot", UIWidgetBase)
    self._wtUsing = self:Wnd("WBP_SlotCompUsing", UIWidgetBase)

    self:SetCppValue("bHandleClick", true)
    self:SetCppValue("bHandleHover", true)

    self._selectId = nil
    self._selectIns = nil
    self._usingIns = nil

    self._info = nil
    self._badgeId = -1
    self._heroId = -1
end

function SocialBadgeItem:OnOpen()
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtSocialBadgeSelectChange, self.RefreshSelect, self)
    self:Event("OnHovered", self._OnButtonHovered, self)
    self:Event("OnUnhovered", self._OnButtonUnhovered, self)
end

function SocialBadgeItem:OnClose()
    self:RemoveAllLuaEvent()
end

function SocialBadgeItem:Refresh(info, heroId)
    self._info = info
    self._heroId = heroId

    local badgeId = info.BadgeId[1]
    local HeroConfig = Module.Hero.Config

    function Callback()
        Timer.DelayCall(0.1, function()
            Server.RoleInfoServer.Events.evtSocialAvatarChange:Invoke()  -- 红点更新 
        end)
    end

    local curAccessoryData = Server.HeroServer:GetSelectedAccessory(heroId, badgeId)
    if curAccessoryData and curAccessoryData.is_unlock and not curAccessoryData.is_read then
        Server.HeroServer:DoChangeAccessoryReadStat(badgeId, Callback)
    end

    if info.Model == HeroConfig.EBadgeType.Career then
        for _, id in ipairs(info.BadgeId) do
            curAccessoryData = Server.HeroServer:GetSelectedAccessory(heroId, id)
            if curAccessoryData and curAccessoryData.is_unlock then
                badgeId = id

                if not curAccessoryData.is_read then
                    Server.HeroServer:DoChangeAccessoryReadStat(id, Callback)
                end
            end
        end
    end

    local row = HeroHelperTool.GetHeroBadgeDataRow(badgeId)
    self._badgeId = badgeId
    self._wtBadgeIcon:AsyncSetImagePath(row.BadgeImage)
    self:RefreshSelect()
    self:SetUsing(badgeId, heroId)

    if IsHD() then
        self:SetCppValue("DragMode", EViewDragMode.Normal)
    else
        self:SetCppValue("DragMode", EViewDragMode.InverseValidateAngle)
    end

    if not Server.HeroServer:IsAccessoryUnlocked(heroId, badgeId) then
        self:SetCppValue("bHandleDrag", false)
        if IsHD() then
            self:SetCppValue("bPreciseClick", false)
        else
            self:SetCppValue("bPreciseClick", true)
        end

        self._wtSlotCompMaskSmallLock:SelfHitTestInvisible()
    else
        self:SetCppValue("bHandleDrag", true)
        self:SetCppValue("bPreciseClick", false)
        self._wtSlotCompMaskSmallLock:Collapsed()
    end

    self:SetVisibility(ESlateVisibility.Visible)
end

function SocialBadgeItem:OnDragDetected(inGeometry, inDragDropEvent, operation)
    if not Server.HeroServer:IsCanUseHero(self._heroId) then
        Module.CommonTips:ShowSimpleTip(Module.Hero.Config.Loc.HeroCardUnlock)
        return
    end

    if not Server.HeroServer:IsAccessoryUnlocked(self._heroId, self._badgeId) then
        return
    end

    local weakUins = Facade.UIManager:AddSubUI(self, UIName2ID.DragBadgeItemPreview)
    ---@type DragBadgeItemPreview
    local previewItem = getfromweak(weakUins)
    local geometry = self:GetCachedGeometry()
    local viewSize = geometry:GetLocalSize()
    if previewItem then
        local BadgeItemTable = Facade.TableManager:GetTable("HeroBadgeData")
        local itemData = BadgeItemTable[tostring(self._badgeId)]
        if itemData then
            local item = ItemBase:NewIns(self._badgeId)
            previewItem:SetItem(item, itemData.BadgeImage)
        end
    end
    operation.WidgetReference = self
    operation.DefaultDragVisual = previewItem
    return operation
end

function SocialBadgeItem:SetUsing(badgeId, heroId)
    local bUse = Server.HeroServer:IsAccessoryUsed(heroId, badgeId)
    if bUse then
        self._wtUsing:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    else
        self._wtUsing:Collapsed()
    end
end

function SocialBadgeItem:RefreshSelect()
    if self._info.ID == Module.RoleInfo.Field:GetSocialBadgeId() then
        self._wtSelectImg:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    else
        self._wtSelectImg:Collapsed()
    end
end

function SocialBadgeItem:GetIndex()
    return self._index
end

function SocialBadgeItem:OnClicked()
    Module.RoleInfo.Field:SetSocialBadgeId(self._info.ID)
end

function SocialBadgeItem:_OnButtonHovered()
    self:OnHoverCard(true)
end

function SocialBadgeItem:_OnButtonUnhovered()
    self:OnHoverCard(false)
end

function SocialBadgeItem:OnHoverCard(bHovered)
    if bHovered then
        self._wtSelectImg:SelfHitTestInvisible()
    else
        -- self._wtSelectImg:Collapsed()
        self:RefreshSelect()
    end
end

return SocialBadgeItem
