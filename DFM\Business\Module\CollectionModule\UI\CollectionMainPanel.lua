----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionMainPanel : LuaUIBaseView
local CollectionMainPanel = ui("CollectionMainPanel")
local CollectionConfig = Module.Collection.Config
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"


function CollectionMainPanel:Ctor()
    self._wtSubViewRoot = self:Wnd("CanvasPanel_SubView", UIWidgetBase)
    self._wtImgPanel = self:Wnd("wtImgPanel", UIImage)
    self._wtPropImg_1 = self:Wnd("wtPropImg_1", DFCDNImage)
    self._wtPropImg_2 = self:Wnd("wtPropImg_2", DFCDNImage)
    self._wtPropImgBox = self:Wnd("wtPropImgBox", UIWidgetBase)
    self._wtPropImg_3 = self:Wnd("wtPropImg_3", DFCDNImage)
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._targetTabIndex = -1
    self._selectedSubUI = nil
    self._bFirstEnter = true
    self._bCanHandleMouseButtonEvent = true
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    Module.CommonBar:RegStackUITopBarTitle(self, CollectionConfig.Loc.Title)
    local tabInfo = Module.Collection.Field:GetTabInfo()
    local tabTxt = {}
    local imagePath = {}
    local uiNavIDList = {}
    local targetTabIndex = 1
    local reddotTrieList = {}
    local tertiaryTabGroup = {}
    self._tabIndexToSubUIIndex = {}
    for mainTabIndex, mainTabInfo in ipairs(tabInfo) do
        local tabConfig = CollectionConfig.CollectionTabId2Info[mainTabInfo.mainTabId]
        if tabConfig ~= nil then
            table.insert(tabTxt, mainTabInfo.mainTabName)
            table.insert(imagePath, tabConfig.iconPath)
            self._tabIndexToSubUIIndex[mainTabIndex] = {}
            if tabConfig.tertiaryUiNavIDs and #tabConfig.tertiaryUiNavIDs > 0 then
                for index, uiNavID in ipairs(tabConfig.tertiaryUiNavIDs) do
                    table.insert(uiNavIDList, uiNavID)
                    self._tabIndexToSubUIIndex[mainTabIndex][index] = #uiNavIDList
                end
            else
                table.insert(uiNavIDList, tabConfig.uiNavID)
                self._tabIndexToSubUIIndex[mainTabIndex][1] = #uiNavIDList
            end
            local key = string.format("CollectionUnlock_Props_MainTab%s", mainTabInfo.mainTabId)
            table.insert(reddotTrieList, {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Collection, key = key}}
            }) 
            if (IsHD() or tabConfig.bAlwaysUseTertiaryTab) and #mainTabInfo.subTabList > 0 then
                local tertiaryTabTxt = {}
                local tertiaryIconPath = {}
                local tertiaryIconSizeListHD = {}
                local tertiaryReddotTrieList = {}
                for subTabIndex, subTabInfo in ipairs(mainTabInfo.subTabList) do
                    table.insert(tertiaryTabTxt, subTabInfo.subTabName)
                    if IsHD() then
                        if tabConfig.tertiaryIconPaths and tabConfig.tertiaryIconPaths[subTabIndex] then
                            table.insert(tertiaryIconPath, tabConfig.tertiaryIconPaths[subTabIndex])
                        end
                        if tabConfig.tertiaryIconSizeListHD and tabConfig.tertiaryIconSizeListHD[subTabIndex] then
                            table.insert(tertiaryIconSizeListHD, tabConfig.tertiaryIconSizeListHD[subTabIndex])
                        end
                    end
                    local tertiaryKey = string.format("CollectionUnlock_Props_MainTab%s_SubTab%s", mainTabInfo.mainTabId, mainTabInfo.mainTabId*100+subTabInfo.subTabId)
                    table.insert(tertiaryReddotTrieList, {
                        uiNavId = UIName2ID.TopBar,
                        reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Collection, key = tertiaryKey}}
                    }) 
                end
                table.insert(tertiaryTabGroup, {
                    tabTxtList = tertiaryTabTxt,
                    imgPathList = tertiaryIconPath,
                    imgSizeList = tertiaryIconSizeListHD,
                    fCallbackIns = SafeCallBack(self._OnTertiaryTabIndexChanged, self),
                    defalutIdx = 1,
                    bTriggerCallback = true,
                    bReInitChildWidgets = true,
                    bNewReddotTrie = true,
                    reddotTrieRegItemList = tertiaryReddotTrieList,
                    
                }) 
                if not IsHD() and mainTabInfo.mainTabId == 8 then
                    tertiaryTabGroup[#tertiaryTabGroup].tabSingleItemSize = FVector2D(230, 88)
                end
            else
                table.insert(tertiaryTabGroup, {})
            end
        end
    end
    Facade.UIManager:RegSwitchSubUI(self, uiNavIDList)
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {
        tabTxtList = tabTxt,
        imgPathList = IsHD() and {} or imagePath,
        fCallbackIns = SafeCallBack(self._OnTabIndexChanged, self),
        defalutIdx = targetTabIndex,
        bTriggerCallback = true,
        bReInitChildWidgets = true,
        bNewReddotTrie = true,
        reddotTrieRegItemList = reddotTrieList,
        tertiaryTabs = tertiaryTabGroup
    })
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function CollectionMainPanel:OnInitExtraData(mainTabId)
    local tabInfo = Module.Collection.Field:GetTabInfo()
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._targetTabIndex = -1
    if mainTabId then
        for mainTabIndex, mainTabInfo in ipairs(tabInfo) do
            if mainTabInfo.mainTabId == mainTabId then
                self._targetTabIndex = mainTabIndex
                break
            end
        end
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionMainPanel:OnOpen()
    Module.CommonBar:BindBackHandler(self._CloseMainPanel, self)
    self:AddListeners()
    LogAnalysisTool.ResetCollectionLog()
end




-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionMainPanel:OnClose()
    self._selectedSubUI = nil
    Facade.UIManager:ClearSubUIByParent(self, self._wtSubViewRoot)
    Facade.UIManager:UnRegSwitchSubUI(self)
    --Module.CDNIcon:ClearTexWithTag(Module.CDNIcon.Config.ECdnTagEnum.Collection)
end

function CollectionMainPanel:OnShow()
    if self._targetTabIndex > 0 then
        Module.CommonBar:CheckTopTabGroup(self._targetTabIndex, true, 2)
        self._targetTabIndex = -1
    end
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self._OnFetchCollectionData, self)
    if not self._buttonUpHandle then
        local gameInst = GetGameInstance()
        self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
    end
    if self._selectedSubUI ~= nil then
        self._selectedSubUI:UpdateBackground()
        self._selectedSubUI:ToggleControlAndListeners(true)
        local bNeedEnterSubStage = false
        local targetSubStage
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        if mainTabInfo then
            local maintabId = mainTabInfo.mainTabId
            local tabConfig = CollectionConfig.CollectionTabId2Info[maintabId]
            if tabConfig then
                if tabConfig.bUseDynamicSubStage and self._selectedSubUI ~= nil and self._selectedSubUI.GetTargetSubStage then
                    targetSubStage = self._selectedSubUI:GetTargetSubStage()
                elseif tabConfig.tertiarySubStages and tabConfig.tertiarySubStages[self._subTabIndex] then
                    targetSubStage = tabConfig.tertiarySubStages[self._subTabIndex]
                else
                    targetSubStage = tabConfig.subStage
                end
                bNeedEnterSubStage = Facade.GameFlowManager:GetCurrentSubStage() ~= targetSubStage 
            end
        end
        if bNeedEnterSubStage and targetSubStage then
            local fAllLevelFinishCallback = CreateCallBack(function()
                Facade.GameFlowManager:EnterSubStage(targetSubStage)
            end,self)
            Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(targetSubStage, true, nil, fAllLevelFinishCallback, false, 30)
        else
            self._selectedSubUI:OnRefreshModel()
        end
    else
        self:SetBackgroundImg(nil, false)
    end
    local CacheCollectionPropReachMaxNum = Facade.ConfigManager:GetUserArray(string.format("%s_CacheCollectionPropReachMaxNum", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" ), {})
    if CacheCollectionPropReachMaxNum ~= nil and #CacheCollectionPropReachMaxNum > 0 then
        self:_OnCollectionPropReachMaxNum(CacheCollectionPropReachMaxNum[1])
    end
end

function CollectionMainPanel:OnHide()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self._OnFetchCollectionData, self)
    if self._buttonUpHandle then
        local gameInst = GetGameInstance()
        UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
        self._buttonUpHandle = nil
    end
    if self._selectedSubUI ~= nil then
        self._selectedSubUI:ToggleControlAndListeners(false)
    end
end

---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionMainPanel:OnAnimFinished(anim)
end

function CollectionMainPanel:OnActivate()
    self._bFirstEnter = true
    self._bCanHandleMouseButtonEvent = true
    self:AddListeners()
    LogAnalysisTool.ResetCollectionLog(Server.AccountServer:GetPlayerId())
end


function CollectionMainPanel:OnDeactivate()
    if self._selectedSubUI ~= nil then
        self._selectedSubUI:ToggleControlAndListeners(false, true)
    end
    self:RemoveAllLuaEvent()
    Module.ItemDetail:CloseAllPopUI()
    CollectionLogic.ClearAllMysticalSkinFilterData()
    CollectionLogic.ClearAllMysticalPendantFilterData()
    CollectionLogic.ClearAllGunSkinFilterData()
    CollectionLogic.ClearAllHangingFilterData()
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._targetTabIndex = -1
    self._bFirstEnter = true
    self._bCanHandleMouseButtonEvent = false
    self._selectedSubUI = nil
    LogAnalysisTool.DoSendCollectionLog(Server.AccountServer:GetPlayerId())
end

function CollectionMainPanel:AddListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtPopUIChanged, self._OnPopUIChanged, self)
end

function CollectionMainPanel:OnCloseBtnClick()
    CollectionLogic.CloseMainPanelProcess()
end

function CollectionMainPanel:_OnTabIndexChanged(TabIndex, LastTabIndex)
    if self._targetTabIndex == TabIndex then
        self._targetTabIndex = -1
    end
    if self._mainTabIndex ~= TabIndex or self._bFirstEnter then
        self._mainTabIndex = TabIndex
        self._subTabIndex = -1
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        local maintabId = mainTabInfo.mainTabId
        local tabConfig = CollectionConfig.CollectionTabId2Info[maintabId]
        if (IsHD() or tabConfig and tabConfig.bAlwaysUseTertiaryTab) and #mainTabInfo.subTabList > 0 then
        else
            self:_SwitchTab(TabIndex, 1, false)
        end
    end
    CollectionConfig.Events.evtOnCollectionMainPanelTabIndexChanged:Invoke(TabIndex, LastTabIndex)
end


function CollectionMainPanel:_OnTertiaryTabIndexChanged(TabIndex, LastTabIndex)
    if self._subTabIndex ~= TabIndex then
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        local maintabId = mainTabInfo.mainTabId
        local tabConfig = CollectionConfig.CollectionTabId2Info[maintabId]
        self:_SwitchTab(self._mainTabIndex, TabIndex, true)
        if not tabConfig.tertiaryUiNavIDs and self._selectedSubUI and self._selectedSubUI.OnTertiaryTabIndexChanged then
            self._selectedSubUI:OnTertiaryTabIndexChanged(TabIndex, LastTabIndex)
        end
        CollectionConfig.Events.evtOnCollectionMainPanelTertiaryTabIndexChanged:Invoke(self._mainTabIndex, TabIndex, LastTabIndex)
    end
end


function CollectionMainPanel:_CloseMainPanel()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local bNewHall = Module.BattlefieldEntry:GetEnableNewHallModule()
    -- mp大厅
    if bNewHall and curGameFlow == EGameFlowStageType.Lobby then
       -- CollectionConfig.Events.evtOnCollectionMainPanelClosed:Invoke()
        if DFHD_LUA == 1 then
            Module.CommonBar:ForceInvokeBack()
        else
            Facade.UIManager:CloseUI(self)
        end
    else -- iris大厅
        if DFHD_LUA == 1 then
            Module.CommonBar:ForceInvokeBack()
        else
            Facade.UIManager:CloseUI(self)
        end
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollections, "StopCurrentSequence")
end


function CollectionMainPanel:_OnFetchCollectionData()
    local currentStackUI = Facade.UIManager:GetCurrentStackUI()
    if currentStackUI.UINavID == UIName2ID.CollectionMainPanel then
        Module.Collection.Field:SetSkinList()
        Module.Collection.Field:ClearMysticalSkinInstanceList()    
        Module.Collection.Field:SetSelectedPos(-1)
        Module.Collection.Field:SetSelectedPropId()
        if self._selectedSubUI ~= nil then
            self._selectedSubUI:RefreshView(self._mainTabIndex, true)
        end
    end
end


function CollectionMainPanel:_OnUpdateCollectionData(itemsChanged, addedOrRemovedItems)
    local alreadyProcessSkinInstance = {}
    for index, item in ipairs(itemsChanged) do
        if not alreadyProcessSkinInstance[item.id] and item.itemMainType == EItemType.WeaponSkin and item.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
            Module.Collection.Field:SetSkinList()
            if ItemHelperTool.IsMysticalSkin(item.id) then
                Module.Collection.Field:SetMysticalSkinInstanceList(item.id, nil)
                alreadyProcessSkinInstance[item.id] = true
            end
        end
    end
    if self._selectedSubUI ~= nil then
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        for index, item in ipairs(itemsChanged) do
            if table.contains(mainTabInfo.MainTabToMainTypes, item.itemMainType) and table.contains(mainTabInfo.MainTabToSubTypes, item.itemSubType) or #mainTabInfo.MainTabToMainTypes == 0 then
                local bAddOrRemove = false
                for index, addedOrRemovedItem in ipairs(addedOrRemovedItems) do
                    if table.contains(mainTabInfo.MainTabToMainTypes, addedOrRemovedItem.itemMainType) and table.contains(mainTabInfo.MainTabToSubTypes, addedOrRemovedItem.itemSubType) or #mainTabInfo.MainTabToMainTypes == 0 then
                        Module.Collection.Field:SetSelectedPos(-1)
                        Module.Collection.Field:SetSelectedPropId()
                        bAddOrRemove = true
                        break
                    end
                end
                self._selectedSubUI:RefreshView(self._mainTabIndex, bAddOrRemove, false)
                break
            end
        end
    end
end


function CollectionMainPanel:_SwitchTab(mainTabIndex, subTabIndex, bUseTertiaryTab)
    Module.ItemDetail:CloseAllPopUI()
    self._mainTabIndex = mainTabIndex
    self._subTabIndex = subTabIndex
    local tabInfo = Module.Collection.Field:GetTabInfo()
    local mainTabInfo = tabInfo[self._mainTabIndex]
    local maintabId = mainTabInfo.mainTabId
    local tabConfig = CollectionConfig.CollectionTabId2Info[maintabId]
    if tabConfig then
        local targetSubStage
        if tabConfig.tertiarySubStages and tabConfig.tertiarySubStages[self._subTabIndex] then
            targetSubStage = tabConfig.tertiarySubStages[self._subTabIndex]
        else
            targetSubStage = tabConfig.subStage
        end
        local fAllLevelFinishCallback = CreateCallBack(function()
            if self._selectedSubUI ~= nil and self._selectedSubUI.ToggleControlAndListeners then
                self._selectedSubUI:ToggleControlAndListeners(false, true)
            end
            Facade.GameFlowManager:EnterSubStage(targetSubStage)
            CollectionLogic.ClearAllMysticalSkinFilterData()
            CollectionLogic.ClearAllMysticalPendantFilterData()
            CollectionLogic.ClearAllGunSkinFilterData()
            CollectionLogic.ClearAllHangingFilterData()
            Module.Collection.Field:SetSelectedPos(-1)
            local pageIndex = 0
            if self._tabIndexToSubUIIndex[self._mainTabIndex] then
                if tabConfig.tertiaryUiNavIDs and #tabConfig.tertiaryUiNavIDs > 0 then
                    if self._tabIndexToSubUIIndex[self._mainTabIndex][self._subTabIndex] then
                        pageIndex = self._tabIndexToSubUIIndex[self._mainTabIndex][self._subTabIndex]
                    end
                else
                    if self._tabIndexToSubUIIndex[self._mainTabIndex][1] then
                        pageIndex = self._tabIndexToSubUIIndex[self._mainTabIndex][1]
                    end
                end
            end
            local weakUiIns = Facade.UIManager:SwitchSubUIByIndex(self, pageIndex, self._wtSubViewRoot)
            self._selectedSubUI = getfromweak(weakUiIns)
            if not hasdestroy(self._selectedSubUI) then
                if self._selectedSubUI.BindSetBackgourndCallback then
                    self._selectedSubUI:BindSetBackgourndCallback(self.SetBackgroundImg, self)
                end
                if self._selectedSubUI.BindShowDefaultBackgroundCallBack then 
                    self._selectedSubUI:BindShowDefaultBackgroundCallBack(self.ShowDefaultBackground,self)
                    self:ShowDefaultBackground()
                end
                if self._selectedSubUI.ToggleControlAndListeners then
                    self._selectedSubUI:ToggleControlAndListeners(true, true)
                end
                if self._selectedSubUI.RefreshView then
                    self._selectedSubUI:RefreshView(self._mainTabIndex, true, true, bUseTertiaryTab)
                end
            end
            if not self._bFirstEnter then
                LogAnalysisTool.AddCollectionTabEntryTime(tabConfig.tabType)
            end
            self._bFirstEnter = false
        end,self)
        Module.ItemDetail:CloseAllPopUI()
        local bNeedEnterSubStage = Facade.GameFlowManager:GetCurrentSubStage() ~= targetSubStage
        if bNeedEnterSubStage then
            Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(targetSubStage, true, nil, fAllLevelFinishCallback, false, 30)
        else
            fAllLevelFinishCallback()
        end 
    end
end

function CollectionMainPanel:ShowDefaultBackground()
    self._wtPropImg_1:SelfHitTestInvisible()
end

function CollectionMainPanel:SetBackgroundImg(propId, bShow)
    if bShow then
        self._wtImgPanel:SelfHitTestInvisible()
    else
        self._wtImgPanel:Collapsed()
    end
    Module.Collection:SetBackgroundImgByPropId({self._wtPropImg_1, self._wtPropImg_2, self._wtPropImg_3}, bShow, Module.CDNIcon.Config.ECdnTagEnum.Collection, nil, propId, self._wtPropImgBox)
end

function CollectionMainPanel:_OnPopUIChanged(curLastPopIdx)
    self._bCanHandleMouseButtonEvent = curLastPopIdx <= 1
end

function CollectionMainPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
    if self._selectedSubUI ~= nil then
        if self._selectedSubUI.ClosePopup then
                self._selectedSubUI:ClosePopup()
        end
        if self._bCanHandleMouseButtonEvent == true and self._selectedSubUI.OnHandleMouseButtonUpEvent then
            self._selectedSubUI:OnHandleMouseButtonUpEvent(mouseEvent)
        end
    end
end

function CollectionMainPanel:_OnInputTypeChanged(inputType)
    if self._selectedSubUI ~= nil then
        if IsHD() and inputType == EGPInputType.Gamepad then
            self:_EnableGamepadFeature()
        else
            self:_DisableGamepadFeature()
        end
        self._selectedSubUI:OnInputTypeChanged(inputType)
    end
end

function CollectionMainPanel:_EnableGamepadFeature()
    if self._selectedSubUI ~= nil then
        self._selectedSubUI:EnableGamepadFeature()
    end
end

function CollectionMainPanel:_DisableGamepadFeature()
    if self._selectedSubUI ~= nil then
        self._selectedSubUI:DisableGamepadFeature()
    end
end

return CollectionMainPanel
