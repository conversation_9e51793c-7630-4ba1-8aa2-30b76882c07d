----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionsMeleeSkinPagePanel = ui("CollectionsMeleeSkinPagePanel")
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
local ECheckButtonState = import"ECheckButtonState"
-- END MODIFICATION

function CollectionsMeleeSkinPagePanel:Ctor()
    self._wtMainPanel = self:Wnd("wtMainPanel", UIWidgetBase)
    self._wtSortDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown", self._OnSortOptionTabIndexChanged)
    self._wtWeaponSkinGridBox = UIUtil.WndScrollGridBox(self, "wtWeaponSkinGridBox", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase) 
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase) 
    self._wtApplyBtn = self:Wnd("wtApplyBtn", DFCommonButtonOnly)
    self._wtApplyBtn:Event("OnClicked", self._ApplyWeaponSkin, self)
    self._wtApplyBtn:Event("OnDeClicked", self._ApplyWeaponSkin, self)
    self._wtDetailBtn = self:Wnd("wtDetailBtn", DFCommonButtonOnly)
    self._wtDetailBtn:Event("OnClicked", self._ShowSkinDetailPage, self)
    self._wtDetailBtn_PC = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)
    self._wtDetailBtn_PC:Event("OnClicked", self._ShowSkinDetailPage, self)
    self._wtEmptyBg = self:Wnd("wtEmptyBg", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.NoItems)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtHideUIBtn = self:Wnd("wtHideUIBtn", UIButton)
    if not IsHD() then
        --self._wtHideUIBtn:Event("OnClicked", self._ToggleUI, self)
        --self._wtHideUIBtn:Event("OnPressed", self._OnHideUIBtnPressed, self)
        --self._wtHideUIBtn:Event("OnReleased", self._OnHideUIBtnReleased, self)
    end
    self._wtHideUIBtn:Collapsed()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    end
    if not hasdestroy(self._wtCommonDownload) then
        self._wtCommonDownload:Collapsed()
    end
    -- 截屏分享相关
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick,self)
    self._selectedPos = -1
    self._selectedCell = nil
    self._weaponSkinItems = {}
    self._dropDownDataList = {}
    self._animType = 1
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end


function CollectionsMeleeSkinPagePanel:OnInitExtraData()
    self._dropDownDataList = {}
    table.insert(self._dropDownDataList, CollectionConfig.Loc.QualitySort)
    table.insert(self._dropDownDataList, CollectionConfig.Loc.GainTimeSort)
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionsMeleeSkinPagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionsMeleeSkinPagePanel:OnClose()
    self._selectedCell = nil
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MeleeWeaponSkin)
end

function CollectionsMeleeSkinPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionsMeleeSkinPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionsMeleeSkinPagePanel:OnShow()
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtWeaponSkinGridBox, self)    
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionsMeleeSkinPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtNavGroupSkinList then
    	self._wtNavGroupSkinList = WidgetUtil.RegisterNavigationGroup(self._wtWeaponSkinGridBox, self, "Hittest")
        if self._wtNavGroupSkinList then
            self._wtNavGroupSkinList:AddNavWidgetToArray(self._wtWeaponSkinGridBox)
            self._wtNavGroupSkinList:SetScrollRecipient(self._wtWeaponSkinGridBox)
            self._wtNavGroupSkinList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    self._wtApplyBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    self._wtDetailBtn_PC:SetDisplayInputAction("GunSkin_ShowDetail", true, nil, true)    
    if not self._wtDetailBtn_PC_handle then
        self._wtDetailBtn_PC_handle = self:AddInputActionBinding("GunSkin_ShowDetail", EInputEvent.IE_Pressed, self._ShowSkinDetailPage, self, EDisplayInputActionPriority.UI_Stack)
    end
    self:_AddInputActionForApplyBtn()
    if self._wtSortDropDown then
        if not self._wtSortModeCheckButton then
            self._wtSortModeCheckButton = self._wtSortDropDown:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        if self._wtSortModeCheckButton then
            local keyIcon = self._wtSortModeCheckButton:Wnd("wtKeyIcon", HDKeyIconBox)
            if keyIcon then
                keyIcon:SetOnlyDisplayOnGamepad(true)
                keyIcon:InitByDisplayInputActionName("Collection_Sort_Gamepad", true, 0, false)   
            end
            self._wtSortModeCheckButton:Event("OnCheckButtonStateChanged",self._OnSortModeDropDownBoxOpenStateChanged,self)
        end
    end
    if not self._wtSortModeHandle then
        self._wtSortModeHandle = self:AddInputActionBinding("Collection_Sort_Gamepad", EInputEvent.IE_Pressed, self._OpenDropDown,self, EDisplayInputActionPriority.UI_Pop)
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionsMeleeSkinPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RemoveDropDownNavGroup()
    self:_RemoveDropDownShortcuts()
    self:_RemoveInputActionForApplyBtn()
    if self._wtSortModeHandle then
        self:RemoveInputActionBinding(self._wtSortModeHandle)
    end
    if self._wtSortModeCheckButton then
        self._wtSortModeCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    if self._wtDetailBtn_PC_handle then
        self:RemoveInputActionBinding(self._wtDetailBtn_PC_handle)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._wtSortModeHandle = nil
    self._wtApplyBtnHandle  = nil 
    self._wtDetailBtn_PC_handle = nil 
    self._NavConfigHandler = nil
    self._wtNavGroupSkinList = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionsMeleeSkinPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroupSkinList then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupSkinList)
    end
end

function CollectionsMeleeSkinPagePanel:_DisableGamepadA(FocusedNavGroup)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then 
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionsMeleeSkinPagePanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionsMeleeSkinPagePanel:_OnSortModeDropDownBoxOpenStateChanged(eCheckButtonState)
    if not IsHD() then
        return
    end
    if eCheckButtonState == ECheckButtonState.UncheckedPressed then
        self:_RegisterDropDownNavGroup()
        self:_InitDropDownShortcuts()
        self:_RemoveInputActionForApplyBtn()
    elseif eCheckButtonState == ECheckButtonState.Unchecked then
        self:_RemoveDropDownNavGroup()
        self:_RemoveDropDownShortcuts()
        self:_AddInputActionForApplyBtn()
    end
end

function CollectionsMeleeSkinPagePanel:_RegisterDropDownNavGroup()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return 
    end
    if not self._DropDownListNavGroup then
        if self._wtSortDropDown and self._wtSortDropDown.ScrollGridBox then
            self._DropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSortDropDown.ScrollGridBox, self._wtSortDropDown, "Hittest")
        end
 
        if self._DropDownListNavGroup then   
            self._DropDownListNavGroup:AddNavWidgetToArray(self._wtSortDropDown.ScrollGridBox)
            self._DropDownListNavGroup:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._DropDownListNavGroup)
        end
    end
end

function CollectionsMeleeSkinPagePanel:_RemoveDropDownNavGroup()
    if not IsHD() then
        return 
    end
    WidgetUtil.RemoveNavigationGroup(self._wtSortDropDown)
    self._DropDownListNavGroup = nil
end

function CollectionsMeleeSkinPagePanel:_InitDropDownShortcuts()
    if not self._closeDropDownHandler then
        self._closeDropDownHandler = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function CollectionsMeleeSkinPagePanel:_RemoveDropDownShortcuts()
    if self._closeDropDownHandler then
        self:RemoveInputActionBinding(self._closeDropDownHandler)
        self._closeDropDownHandler = nil
    end
end

function CollectionsMeleeSkinPagePanel:_OpenDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionsMeleeSkinPagePanel:_CloseDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionsMeleeSkinPagePanel:_AddInputActionForApplyBtn()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._bEnableApplyBtn and not self._wtApplyBtnHandle then
        self._wtApplyBtnHandle = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._ApplyWeaponSkin,self, EDisplayInputActionPriority.UI_Stack)
    end   
end

function CollectionsMeleeSkinPagePanel:_RemoveInputActionForApplyBtn()
    if  self._wtApplyBtnHandle then
        self:RemoveInputActionBinding(self._wtApplyBtnHandle)
        self._wtApplyBtnHandle = nil 
    end
end
-- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsMeleeSkinPagePanel:OnHide()
    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end
end

function CollectionsMeleeSkinPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_BlueprintPage_Cutter_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

function CollectionsMeleeSkinPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        if bFullReset == true then
            self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._OnWeaponSkinApplied, self)
            self:AddLuaEvent(Server.ArmedForceServer.Events.evtMPPresetPageItemChanged, self._OnWeaponSkinApplied, self)
        end
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        --self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        --self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self.OnShareFlowFinish, self)
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self:RemoveLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._OnWeaponSkinApplied, self)
            self:RemoveLuaEvent(Server.ArmedForceServer.Events.evtMPPresetPageItemChanged, self._OnWeaponSkinApplied, self)
            self._selectedCell = nil
            self._selectedPos = -1
        end
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        --self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
        --self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,self._OnDownloadStateChange,self)
        self:RemoveLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self.OnShareFlowFinish, self)
        self._bHideUI = nil
        local items = {}
        for index, skinItem in ipairs(self._weaponSkinItems) do
            if Server.CollectionServer:IsPropWithRedDot(skinItem.id) then
                table.insert(items, skinItem)
            end
        end
        if items and #items > 0 then
            CollectionLogic.RemoveRedDots(items)
        end 
    end
end


function CollectionsMeleeSkinPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    self._mainTabIndex = mainTabIndex or self._mainTabIndex
    if bResetList then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MeleeWeaponSkin)
    end
    if bResetTab then
        self._dropDownIndex = 1
        UIUtil.InitDropDownBox(self._wtSortDropDown, self._dropDownDataList, {}, 0)
        self:_ShowUI()
    else
        self._dropDownIndex = self._dropDownIndex > 0 and self._dropDownIndex or 1   
    end
    self:_OnRefreshWeaponSkinItems(bResetList)
end

function CollectionsMeleeSkinPagePanel:_OnGetItemsCount()
    return #self._weaponSkinItems
end

function CollectionsMeleeSkinPagePanel:_OnProcessItemWidget(position, itemWidget)
    local index = position + 1
    local item = self._weaponSkinItems[index]
    local sizeBox = itemWidget:Wnd("SizeBox_0", UIWidgetBase)
    sizeBox:SetCppValue("WidthOverride",548) 
    sizeBox:SetCppValue("HeightOverride",254)
    itemWidget:Visible()
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetButtonEnable(not IsHD() or not WidgetUtil.IsGamepad())
    if item then
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            return Server.CollectionServer:IsPropWithRedDot(item.id, item.gid)
        end,self)
        if isvalid(self._redDotInsMap[itemWidget]) and self._redDotInsMap[itemWidget]:GetIsValid() then
            Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MeleeWeaponSkin, self._redDotInsMap[itemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.MeleeWeaponSkin, fCheckFunc, nil ,itemWidget, {EReddotType.Normal})
        end
        local fClickCb = CreateCallBack(self._OnWeaponSkinItemClick, self,itemWidget, position)
        itemWidget:BindClickCallback(fClickCb)
        itemWidget:InitCollectionWeaponSkinItem(item, 
        CollectionLogic.GetBaseWeaponIdFromSkinId(item.id),  
        Server.CollectionServer:IsOwnedMeleeSkin(item.id, item.gid),
        CollectionLogic.CheckIfMeleeWeaponEquiped(item.id),
        nil,    
        Server.CollectionServer:GetRightsType(item.id, item.gid))
    end
    itemWidget:SetSelected(nil, self._selectedPos == position)
    if self._selectedPos == position then
        self._selectedCell = itemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end


function CollectionsMeleeSkinPagePanel:_OnRefreshWeaponSkinItems(bResetList)
    self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._wtSortDropDown:BP_SetMainTabText(self._dropDownDataList[self._dropDownIndex] or "")
    self._weaponSkinItems = CollectionLogic.GetCollectionItems(self._mainTabIndex, 1)
    if self._dropDownIndex == 1 then
        table.sort(self._weaponSkinItems, CollectionLogic.WeaponSkinQualitySortDecend)
    elseif self._dropDownIndex == 2 then
        table.sort(self._weaponSkinItems, CollectionLogic.WeaponSkinGainTimeSortDecend)
    end
    if bResetList then
        self._bApplied = false
        self._bUpdating = false
        Timer.CancelDelay(self._applyDelayHandle)
        self._selectedCell = nil
        self._selectedPos = -1
        if #self._weaponSkinItems > 0 then
            self._selectedPos = 0
            local item = self._weaponSkinItems[self._selectedPos+1]
            if item then
                LogAnalysisTool.AddSkinIDs(item.id)
                if Server.CollectionServer:IsPropWithRedDot(item.id) then
                    Timer.DelayCall(1, function ()
                        CollectionLogic.RemoveRedDots({item})
                    end)
                end
            end
        end
        self._wtWeaponSkinGridBox:RefreshAllItems()
    else
        if #self._weaponSkinItems == 0 then
            self._wtWeaponSkinGridBox:RefreshAllItems()
        else
            self._wtWeaponSkinGridBox:RefreshVisibleItems()
        end
    end
    if #self._weaponSkinItems == 0 then
        self._wtEmptyBg:SelfHitTestInvisible()
        self._wtEmptySlot:SelfHitTestInvisible()
    else
        self._wtEmptySlot:Collapsed()
        self._wtEmptyBg:Collapsed()
    end
    self:_RefreshItemUI(bResetList)
end


function CollectionsMeleeSkinPagePanel:OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.CollectionKnife then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "ResetDisplayItem")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "ResetWeapon")
        local item = self._weaponSkinItems[self._selectedPos + 1]
        if isvalid(item) then
            local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionKnife, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, true)
        end 
    end
end


function CollectionsMeleeSkinPagePanel:_OnWeaponSkinItemClick(itemCell, position)
    if self._selectedPos ~= position then
        self._bApplied = false
        Timer.CancelDelay(self._applyDelayHandle)
        local item = self._weaponSkinItems[position+1]
        if self._selectedCell then
            self._selectedCell:SetSelected(nil, false)
            self._selectedCell:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
        end
        if item then
            LogAnalysisTool.AddSkinIDs(item.id)
            if Server.CollectionServer:IsPropWithRedDot(item.id) then
                if self._selectedPos == -1 then
                    Timer.DelayCall(1, function ()
                        CollectionLogic.RemoveRedDots({item})
                    end)
                else
                    CollectionLogic.RemoveRedDots({item})
                end
            end
        end
        self._selectedCell = itemCell
        self._selectedCell:SetSelected(nil, true)
        self._selectedCell:SetCppValue("bHandleClick", false)
        self._selectedPos = position
        self:_RefreshItemUI(true)
    end
end



function CollectionsMeleeSkinPagePanel:_RefreshItemUI(bReset)
    local item = self._weaponSkinItems[self._selectedPos+1]
    if bReset or #self._weaponSkinItems == 0 then
        self._wtDownloadBtn:Collapsed()
        self._wtInfoPanel:Collapsed()
        self._wtEmptyHint:Collapsed()
        self._wtDetailBtn:Collapsed()
        self._wtDetailBtn_PC:Collapsed()
        self._wtShareBtn:Collapsed()
        self._wtHideUIBtn:Collapsed()
        self._wtApplyBtn:Collapsed()
        self._bEnableApplyBtn = false
        self._wtAlertHintBox:Collapsed()
        self._shotcutList = {}
        if isvalid(item) then
            self._wtApplyBtn:SetIsEnabled(true)
            self._wtApplyBtn:SetIsEnabledStyle(true)
            if IsHD() then
                if self._bHideUI then
                    table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false}) 
                end
                --table.insert(self._shotcutList, {actionName = "GunSkin_ShowDetail",func = self._ShowSkinDetailPage, caller = self ,bUIOnly = false, bHideIcon = false})
                self._wtDetailBtn_PC:Visible()
                self._bEnableApplyBtn = true
                self:_DownLoadResources()
            else
                self._wtDetailBtn:Visible()
                --self._wtHideUIBtn:Visible()
            end
            if Server.CollectionServer:IsOwnedMeleeSkin(item.id) == true then
                if not IsHD() then
                    Module.Share:FuncPointUnLock(item.quality >= ItemConfig.EWeaponSkinQualityType.Purple and SwitchSystemID.SubShareAdvancedGunSkin or SwitchSystemID.SubShareSObtainLowGunSkin, self._wtShareBtn)
                end
                if CollectionLogic.CheckIfMeleeWeaponEquiped(item.id) then
                    self._wtApplyBtn:SetIsEnabled(false)
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.Equipd)
                    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                    if IsHD() then
                        self:_RemoveInputActionForApplyBtn()
                    end
                    -- END MODIFICATION
                else
                    self._wtApplyBtn:SetIsEnabled(true)
                    self._bEnableApplyBtn = true
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAndEquip)
                end
            else
                local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
                local bHasConfig = false
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                if itemConfigRow then
                    if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                        if itemConfigRow.ButtonDes ~= nil and itemConfigRow.ButtonDes ~= "" then
                            self._wtAlertHintTxt:SetText(itemConfigRow.ButtonDes)
                            self._wtAlertHintBox:SelfHitTestInvisible()
                        end
                        local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID, {bNoShowLoadWindow=true})
                        self._wtApplyBtn:SetIsEnabledStyle(bCanJump)
                        self._bEnableApplyBtn = true
                        bHasConfig = true
                    elseif itemConfigRow.EndButtonDes ~= nil and itemConfigRow.EndButtonDes ~= "" then
                        self._wtAlertHintTxt:SetText(itemConfigRow.EndButtonDes)
                        self._wtAlertHintBox:SelfHitTestInvisible()
                        self._wtApplyBtn:SetIsEnabled(false)
                        bHasConfig = true
                    end
                end
                if not bHasConfig then
                    if CollectionLogic.IsItemInStore(item.id) == true then
                        local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                        self._wtApplyBtn:SetMainTitle(priceStr)
                        self._bEnableApplyBtn = true
                    else
                        self._wtApplyBtn:SetIsEnabled(false)
                        self:_RemoveInputActionForApplyBtn()
                    end
                end
            end
            self._wtApplyBtn:SelfHitTestInvisible()
        end
    end
    if isvalid(item) then
        self._wtItemDetailView:UpdateItem(item)
        self._wtItemDetailView:SetDetailBtnVisible(false)
        self._wtInfoPanel:SelfHitTestInvisible()
    end
    --self:_RefreshDownloadBtn()
    if self._bEnableApplyBtn then
        self:_AddInputActionForApplyBtn()
    else
        self:_RemoveInputActionForApplyBtn()
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, self._bHideUI)
    self:OnRefreshModel(ESubStage.CollectionKnife)
    self:UpdateBackground()
end


function CollectionsMeleeSkinPagePanel:_OnSortOptionTabIndexChanged(position, lastPosition)
    if self._dropDownIndex ~= position + 1 then
        self._dropDownIndex = position + 1
        self._selectedCell = nil
        self:_OnRefreshWeaponSkinItems(true)
    else
        if IsHD() and WidgetUtil.IsGamepad() then
            self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        end
    end
end

function CollectionsMeleeSkinPagePanel:_DownLoadResources()
    if not hasdestroy(self._wtCommonDownload) then
        local item = self._weaponSkinItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        self._wtCommonDownload:InitModuleKey(moduleKey)
        self._wtCommonDownload:Visible()
        logerror("[v_dzhanshen] CollectionsMeleeSkinPagePanel:_DownLoadResources moduleKey="..moduleKey)
    end
end

function CollectionsMeleeSkinPagePanel:_OnDownloadStateChange(moduleName, bDownloaded)
    if self._wtDownloadBtn then
        local item = self._weaponSkinItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        logerror("[v_dzhanshen] CollectionsMeleeSkinPagePanel:_OnDownloadStateChange moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if moduleName == moduleKey then
            if not bDownloaded then
                self._wtDownloadBtn:Visible()
            else
                self._wtDownloadBtn:Collapsed()
            end
        end
    end
end

function CollectionsMeleeSkinPagePanel:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local item = self._weaponSkinItems[self._selectedPos+1]
    local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
    logerror("[v_dzhanshen] CollectionsMeleeSkinPagePanel:_OnDownloadResult moduleKey="..moduleKey)
    if moduleName == moduleKey then
        self._wtWeaponSkinGridBox:RefreshVisibleItems()
        self:OnRefreshModel(ESubStage.CollectionKnife)
        self:_RefreshDownloadBtn()
    end
end

function CollectionsMeleeSkinPagePanel:_RefreshDownloadBtn()
    if self._wtDownloadBtn then
        local item = self._weaponSkinItems[self._selectedPos+1]
        local moduleKey = CollectionLogic.GetDownloadModuleKeyByItemId(item and item.id or nil)
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
        logerror("[v_dzhanshen] CollectionsMeleeSkinPagePanel:_RefreshDownloadBtn moduleKey="..moduleKey.." bDownloaded="..tostring(bDownloaded))
        if not bDownloaded and isvalid(item) then
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:InitModuleKey(moduleKey)
            end
            self._wtDownloadBtn:Visible()
        else
            self._wtDownloadBtn:Collapsed()
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

--[[
function CollectionsMeleeSkinPagePanel:_OnHideUIBtnPressed()
    if not self._bHideUI then
        self.bHandleLongPressTimerHandle = Timer:NewIns(CollectionConfig.LongPressDelta, 1)
        self.bHandleLongPressTimerHandle:AddListener(self._HideUI, self)
        self.bHandleLongPressTimerHandle:Start()
    end
end

function CollectionsMeleeSkinPagePanel:_OnHideUIBtnReleased()
    if self.bHandleLongPressTimerHandle then
        self.bHandleLongPressTimerHandle:Release()
        self.bHandleLongPressTimerHandle = nil
        if self._bHideUI == true then
            self:_ShowUI()
        end
    end
end
--]]

function CollectionsMeleeSkinPagePanel:_ToggleUI()
    if self._bHideUI == true then
        self:_ShowUI()
    else
        self:_HideUI()
    end
end

function CollectionsMeleeSkinPagePanel:_HideUI()
    self._bHideUI = true
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionsMeleeSkinPagePanel:_ShowUI()
    self._bHideUI = false
    self:HideUI(false)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
        --table.insert(self._shotcutList, {actionName = "GunSkin_ShowDetail",func = self._ShowSkinDetailPage, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        Module.CommonBar:SetTopBarVisible(true)
    end
end

function CollectionsMeleeSkinPagePanel:_ShowSkinDetailPage()
    local item = self._weaponSkinItems[self._selectedPos+1] 
    if isvalid(item) then
        Module.Collection:ShowWeaponSkinDetailPage(item)
    end
end

function CollectionsMeleeSkinPagePanel:_OnWeaponSkinApplied()
    --MP模式应用近战武器状态查询有延迟
    if not self._bApplied then
        if not self._bUpdating then
            self._bUpdating = true
            self._applyDelayHandle = Timer.DelayCall(0.05, function()
                local item = self._weaponSkinItems[self._selectedPos+1]
                if isvalid(item) then 
                    if CollectionLogic.CheckIfMeleeWeaponEquiped(item.id) then
                        self._bApplied = true
                        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.SuccessfullyAppliedSkin)
                    else
                        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.FailedToUse)
                    end
                end
                self._wtWeaponSkinGridBox:RefreshVisibleItems()
                self:_RefreshItemUI(true)
                self._bUpdating = false
            end)
        end 
    end
end

function CollectionsMeleeSkinPagePanel:_ApplyWeaponSkin()
    local item = self._weaponSkinItems[self._selectedPos+1] 
    if isvalid(item) then 
        if Server.CollectionServer:IsOwnedMeleeSkin(item.id, item.gid) == true then
            CollectionLogic.EquipMeleeWeapon(item.id)
        else
            self:_PurchaseWeaponSkin()
        end
    end
end


function CollectionsMeleeSkinPagePanel:_PurchaseWeaponSkin()
    local item = nil
    if self._selectedPos ~= #self._weaponSkinItems then
        item = self._weaponSkinItems[self._selectedPos+1]
        if item ~= nil then 
            local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
            local bHasConfig = false
            if itemConfigRow ~= nil then
                local bHasTipInConfig = itemConfigRow.UnlockTip ~= nil and itemConfigRow.UnlockTip ~= ""
                if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                    local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID)
                    if bCanJump then
                        Module.Jump:JumpByID(itemConfigRow.JumpID)
                    elseif bHasTipInConfig then
                        Module.CommonTips:ShowSimpleTip(itemConfigRow.UnlockTip)
                    else
                        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
                    end
                    bHasConfig = true
                end
            end
            if not bHasConfig then
                if CollectionLogic.IsItemInStore(item.id) == true then
                    Module.CommonTips:ShowConfirmWindow(
                        string.format(CollectionConfig.Loc.ConfirmPurchase, item.name),
                        function()
                            CollectionLogic.PurchaseItem(item.id)
                        end,
                        function()
            
                        end,
                        CollectionConfig.Loc.Cancel,
                        CollectionConfig.Loc.Confirm
                    )
                else
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
                end
            end
        end
    end
end


function CollectionsMeleeSkinPagePanel:ClosePopup()

end

function CollectionsMeleeSkinPagePanel:OnInputTypeChanged(inputType)
    self._wtWeaponSkinGridBox:RefreshVisibleItems()
end

function CollectionsMeleeSkinPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionsMeleeSkinPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

function CollectionsMeleeSkinPagePanel:PreScreenshotShare()
    Module.CommonBar:SetTopBarVisible(false)
    self._wtMainPanel:Hidden()
end

function CollectionsMeleeSkinPagePanel:AfterScreenshotShare()
    Module.CommonBar:SetTopBarVisible(true)
end

function CollectionsMeleeSkinPagePanel:OnShareFlowFinish()
    self._wtMainPanel:SelfHitTestInvisible()
end

function CollectionsMeleeSkinPagePanel:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)
    local item = self._weaponSkinItems[self._selectedPos+1] 
    if isvalid(item) then
        Module.Share:ReqShareWeapon(item.id, item, "CollectionWeaponSkinDetailPage", PreScreenshotShare, AfterScreenshotShare)
    end
end

return CollectionsMeleeSkinPagePanel
