----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemBaseTool  = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class ContainerMask : LuaUIBaseView
local ContainerMask = ui("ContainerMask")

local function log(...)
    loginfo("[ContainerMask]", ...)
end


-- 文本配置
ContainerMask.MapSlotType2Description = {
    [InventoryConfig.EContainerMaskType.JumpState] = InventoryConfig.Loc.VIPExtNormalTxt,
    [InventoryConfig.EContainerMaskType.NormalState] = InventoryConfig.Loc.VIPExtNormalTxt,
}

function ContainerMask:Ctor()
    -- self._wtBoxTitle = self:Wnd("wtBoxTitle", UITextBlock)

    -- 过期面板(type = 1)
    self._wtExpiredTxt = self:Wnd("wtExpiredTxt", UITextBlock)
    self._wtLeftBtn = self:Wnd("wtLeftBtn", DFCommonButtonOnly)
    self._wtLeftBtn:Event("OnClicked", self.LeftBtnClicked, self)
    if IsHD() then
        self._wtLeftBtn:Event("OnHovered", self.LeftBtnHovered, self)
        self._wtLeftBtn:Event("OnUnHovered", self.LeftBtnUnHovered, self)
    end
    self._wtRightBtn = self:Wnd("wtRightBtn", DFCommonButtonOnly)
    self._wtRightBtn:Event("OnClicked", self.RightBtnClicked, self)
    if IsHD() then
        self._wtRightBtn:Event("OnHovered", self.RightBtnHovered, self)
        self._wtRightBtn:Event("OnUnHovered", self.RightBtnUnHovered, self)
    end

    -- 未过期面板(type = 0)
    self._wtContainerName = self:Wnd("wtContainerName", UITextBlock)
    self._wtTipsBtn = self:Wnd("wtTipsBtn", DFCommonCheckButtonOnly)
    self._wtCutdownTxt = self:Wnd("wtCountDownTxt", UITextBlock)
    self._wtNormalTxt = self:Wnd("wtNormalTxt", UITextBlock)

    -- 跳转解锁面板(type = 2)
    self._wtUnLockPanelTxt = self:Wnd("wtUnLockPanelTxt", UITextBlock)
    self._wtTipsBtn_1 = self:Wnd("wtTipsBtn_1", UIWidgetBase)
    self._wtGotoUnlockBtn = self:Wnd("wtGotoUnlockBtn", DFCommonButtonOnly)
    self._wtGotoUnlockBtn:Event("OnClicked", self.UnLockBtnClicked, self)

    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._TipsOnHover, self._TipsOnUnhovered)
    self._wtDFTipsAnchor_1 = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor_1", self._TipsOnHover_1, self._TipsOnUnhovered_1)

    self._widgetType = nil
    self._titleTxt = nil
end

-----------------------------------------------------------------------
--region 生命周期

function ContainerMask:OnOpen()

end

function ContainerMask:OnClose()

end

function ContainerMask:OnShow()
    -- self:SetTitle()
    -- self:SetCppValue("Type", 2)
    -- self:SetType(2)
end

function ContainerMask:OnHide()
end

function ContainerMask:OnInitExtraData(item)
    self._item = item
end
--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public Function

function ContainerMask:HideLeftBtn()
    self._wtLeftBtn:Collapsed()
end

function ContainerMask:InitWidgetPanel(type, txt)
    self:SetWidgetType(type)
    self:SetWidgetTitle(txt)
end

-- 未来很有可能作为通用控件，所以需要通用化处理
-- 1. 外部接口设置控件类型
function ContainerMask:SetWidgetType(type)
    self._widgetType = type
    self:SetType(self._widgetType)
    self:SetType(type)
    self.hoverDescription = ContainerMask.MapSlotType2Description[type]
end
--2.外部接口设置控件标题
function ContainerMask:SetWidgetTitle(txt)
    self._titleTxt = txt and txt or ""
    if self._widgetType == InventoryConfig.EContainerMaskType.NormalState then
        self._wtNormalTxt:SetText(self._titleTxt)
    elseif self._widgetType == InventoryConfig.EContainerMaskType.ExpiredStatus then
        self._wtExpiredTxt:SetText(self._titleTxt)
    elseif self._widgetType == InventoryConfig.EContainerMaskType.JumpState then
        self._wtUnLockPanelTxt:SetText(self._titleTxt)
    end
end

function ContainerMask:SetExpiredTime(timeStr)
    self._wtCutdownTxt:SetText(timeStr)
end

-- 更改按钮标题
function ContainerMask:SetLeftBtnTitle(txt)
    self._wtLeftBtn:SetMainTitle(txt)
end

function ContainerMask:SetRightBtnTitle(txt)
    self._wtRightBtn:SetMainTitle(txt)
end

function ContainerMask:SetGotoUnlockBtnTitle(txt)
    self._wtGotoUnlockBtn:SetMainTitle(txt)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private Function

function ContainerMask:_TipsOnHover()
    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = self.hoverDescription}
    }}, self._wtDFTipsAnchor)
end

function ContainerMask:_TipsOnUnhovered()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function ContainerMask:_TipsOnHover_1()
    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor_1)
        self.hoverHandle = nil
    end
    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = self.hoverDescription}
    }}, self._wtDFTipsAnchor_1)
end

function ContainerMask:_TipsOnUnhovered_1()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor_1)
        self.hoverHandle = nil
    end
end


--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 按钮事件

function ContainerMask:LeftBtnClicked()
    if self._leftBtnClickedCallBack then
        self._leftBtnClickedCallBack()
    end
end

function ContainerMask:LeftBtnHovered()
    if IsHD() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        local summaryList = {}
        table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList)
    end
    if self._leftBtnHoveredCallBack then
        self._leftBtnHoveredCallBack()
    end
end

function ContainerMask:LeftBtnUnHovered()
    if IsHD() then
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
    if self._leftBtnUnHoveredCallBack then
        self._leftBtnUnHoveredCallBack()
    end
end
function ContainerMask:RightBtnClicked()
    if self._rightBtnClickedCallBack then
        self._rightBtnClickedCallBack()
    end
end

function ContainerMask:UnLockBtnClicked()
    if self._unLockBtnClickedCallBack then
        self._unLockBtnClickedCallBack()
    end
end

function ContainerMask:RightBtnHovered()
    if IsHD() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        local summaryList = {}
        table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList)
    end
    if self._rightBtnHoveredCallBack then
        self._rightBtnHoveredCallBack()
    end
end

function ContainerMask:RightBtnUnHovered()
    if IsHD() then
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
    if self._rightBtnUnHoveredCallBack then
        self._rightBtnUnHoveredCallBack()
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 绑定按钮事件

function ContainerMask:BindLeftBtn(callback)
    self._leftBtnClickedCallBack = callback
end

function ContainerMask:BindLeftBtnHovered(callback)
    self._leftBtnHoveredCallBack = callback
end

function ContainerMask:BindRightBtn(callback)
    self._rightBtnClickedCallBack = callback
end

function ContainerMask:BindRightBtnHovered(callback)
    self._rightBtnHoveredCallBack = callback
end

function ContainerMask:BindUnLockBtn(callback)
    self._unLockBtnClickedCallBack = callback
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 仓库安全箱钥匙包过期相关

function ContainerMask:InitWidget(item)
    self._item = item
    self:SetTitle(item)
    self:SetExpiredState()
end

function ContainerMask:SetExpiredState()
    if not self._item then
        return
    end
    local type = 0
    local equipFeature = self._item:GetFeature(EFeatureType.Equipment)
    local itemCanUse = equipFeature and equipFeature:PermissionCanUse()
    -- if ItemBaseTool.GetItemExpiredState(self._item) then
    if not itemCanUse then
        self:SetCppValue("Type", 1)
        type = 1
    else
        self:SetCppValue("Type", 0)
        type = 0
    end
    self:SetType(type)
end

function ContainerMask:SetTitle(item)
    self._item = setdefault(item, self._item)
    if not self._item then
        return
    end
    -- 钥匙包和富尔顿回收命名分开
    local equipFeature = self._item:GetFeature(EFeatureType.Equipment)
    if equipFeature and equipFeature:IsKeyChain() then
        self._wtExpiredTxt:SetText(string.format(Module.Inventory.Config.Loc.ExpiredMaskTips, self._item.name))
    elseif equipFeature and equipFeature:IsSafeBox() then
        self._wtExpiredTxt:SetText(Module.Inventory.Config.Loc.FultonRecycleExpiredTxt)
    end
end

--endregion
-----------------------------------------------------------------------

return ContainerMask