----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

local RoleInfoAchivenmentListItem = ui("RoleInfoAchivenmentListItem")
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local LAI = import "LAI"
local EUITweenEaseType = import "EUITweenEaseType"

local TOTAL_UPDATE_TIME = 0.3
local MIN_DESIRED_HEIGHT = 400

function RoleInfoAchivenmentListItem:Ctor()
    self._wtIcon = self:Wnd("DFImage_Icon", UIImage)
    self._wtBg = self:Wnd("DFImage_407", UIImage)
    self._wtName = self:Wnd("DFTextBlock_78", UITextBlock)
    self._wtProgress = self:Wnd("DFTextBlock", UITextBlock)
    self._wtIndexTxt = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtBgImg = self:Wnd("DFImage_0", UIImage)
    self._wtCommonHoverBg = self:Wnd("WBP_CommonHoverBg_V2", UIWidgetBase)
    self._wtWrapBoxMore = self:Wnd("DFWrapBox_102", UIWidgetBase)
    self._wtBtn = self:Wnd("DFButton_0", UIWidgetBase)
    self._wtBtn:Event("OnClicked", self.OnClickBtn, self)

    self._wtIcon:Event("OnClicked", self.OnClickBigReward, self)

    self._wtProgressBar = self:Wnd("DFRadialImage_68", UIProgressBar)
    self._wtIconParent = self:Wnd("DFCanvasPosReContainer_2", UIWidgetBase)
    self._wtIconSelect = self:Wnd("WBP_SlotCompSelected_1", UIWidgetBase)
    self._wtCircleProcessImg = self:Wnd("DFImage_162", UIWidgetBase)
    self._wtContainer1 = self:Wnd("DFCanvasPosReContainer_1", UIWidgetBase)

    self._itmeInsIds = {}
    self.acheveDataMap = {}
    self._index = -1
    self._SeriesID = -1
    self.bFold = false
    self._achievemendIds = {}
    self._bigRewardId = 0
    self._seriesType = -1
end

function RoleInfoAchivenmentListItem:OnOpen()
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtAchievementSelectChange, self.RefreshSelect, self)
end

function RoleInfoAchivenmentListItem:OnClose()
    self:RemoveAllLuaEvent()
end

function RoleInfoAchivenmentListItem:RefreshSelect()
    if #self._itmeInsIds > 0 then
        for _, insId in pairs(self._itmeInsIds) do
            local wtItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.AchievementBadgeItem, insId)
            local wtItem = getfromweak(wtItemWeak)
            if wtItem then
                wtItem:RefreshSelect()
                wtItem:RefreshProgress()
            end
        end
    end
    self:RefreshBigRewardSelect()

end

function RoleInfoAchivenmentListItem:RefreshBigRewardSelect()
    if self._bigRewardId == Module.RoleInfo.Field:GetAcheievementId() then
        self._wtIconSelect:Visible()
    else
        self._wtIconSelect:collapsed()
    end
end

function RoleInfoAchivenmentListItem:CreateAchievementBadgeItem(ids)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtWrapBoxMore)

    self._itmeInsIds = {}
    for _, id in pairs(ids) do
        local info = HeroHelperTool.GetHZAchievementDataTableRow(id)
        if not info then
            logerror("RoleInfoAchivenmentListItem:CreateAchievementBadgeItem info is nil", id)
            return
        end

        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.AchievementBadgeItem, self._wtWrapBoxMore
            , nil, id, self.acheveDataMap[id], self._index, self._seriesType)
        local widget = getfromweak(weakUIIns)
        if widget then
            table.insert(self._itmeInsIds, instanceId)
        end
    end
end

function RoleInfoAchivenmentListItem:ShowSelect(bShow)
    local RoleInfoField = Module.RoleInfo.Field
    if bShow then
        if RoleInfoField:GetAcheievementId() ~= self._achievemendIds[1] then
            RoleInfoField:SetAcheievementId(self._achievemendIds[1])
        end

        if #self._itmeInsIds == 0 then
            self:CreateAchievementBadgeItem(self._achievemendIds)
        end

        if IsHD() then
            self:PlayAnimation(self.WBP_Achievement_SeriesAchievements_open, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        else
            self:PlayAnimation(self.WBP_Achievement_SeriesAchievements_open_mobile, 0, 1, EUMGSequencePlayMode.Forward, 1
                , false)
        end

        self:SetType(1)
    else
        RoleInfoField:SetAcheievementId(-1)
        self:SetType(0)

        if IsHD() then
            self:PlayAnimation(self.WBP_Achievement_SeriesAchievements_close, 0, 1, EUMGSequencePlayMode.Forward, 1,
                false)
        else
            self:PlayAnimation(self.WBP_Achievement_SeriesAchievements_close_mobile, 0, 1, EUMGSequencePlayMode.Forward,
                1, false)
        end

    end

    local openList = Module.RoleInfo.Field:GetOpenAchievementList()
    openList[self._index] = bShow and 1 or 0
end

function RoleInfoAchivenmentListItem:AutoSetPadding()
    Timer.DelayCall(0.1, function()
        if #self._itmeInsIds == 0 then
            return
        end

        local Geometry = self._wtContainer1:GetCachedGeometry()
        if Geometry then

            local itemWidth = IsHD() and 232 or 172
            local padding = IsHD() and 20 or 16
            local boxWidth = Geometry:GetLocalSize().X

            -- 计算每行最大子项数（考虑间距）
            local maxPerRow = math.floor((boxWidth + padding) / (itemWidth + padding))
            maxPerRow = math.max(1, maxPerRow)

            -- 计算剩余空间（包含间距）
            local totalUsedWidth = maxPerRow * itemWidth + (maxPerRow - 1) * padding
            local spaceWidth = boxWidth - totalUsedWidth

            -- 平均分配到每个子项
            local extraPerItem = spaceWidth / maxPerRow
            local newWidth = math.floor(itemWidth + extraPerItem)

            -- 应用新宽度
            for k, insId in pairs(self._itmeInsIds) do
                local wtItem = Facade.UIManager:GetSubUI(self, UIName2ID.AchievementBadgeItem, insId)
                if wtItem then
                    wtItem:SetSize(newWidth, newWidth)
                end
            end
        end
    end)
end

function RoleInfoAchivenmentListItem:OnClickBtn()
    self.bFold = not self.bFold
    self:ShowSelect(self.bFold)
    Module.RoleInfo.Field:SetAcheievementIndex(self._index)

    self:AutoSetPadding()
end

function RoleInfoAchivenmentListItem:OnClickBigReward()
    Module.RoleInfo.Field:SetAcheievementId(self._bigRewardId)
end

function RoleInfoAchivenmentListItem:SetUltimateImg(RewarditemID, progressValue)
    local heroBadgeData = Facade.TableManager:GetTable("HeroBadgeData")
    if RewarditemID and RewarditemID > 0 then
        local heroBadgeInfo = heroBadgeData[RewarditemID]
        self._wtIcon:AsyncSetImagePath(heroBadgeInfo.BadgeImage)
        self._wtIcon:Visible()
        self:SetSeriesRewards(false)
    else
        self._wtIcon:Collapsed()
        self:SetSeriesRewards(true)
    end

    self._bigRewardId = RewarditemID
end

function RoleInfoAchivenmentListItem:GetCurAndAllCount(serverData)
    local curTotal = 0
    local allTotal = 0
    for acheiveId, value in pairs(serverData) do
        if value.bCareer then
            for key1, info1 in pairs(value.progressInfo) do
                local bComplete = true

                for key2, info2 in pairs(info1) do
                    if info2.progress < 1 then bComplete = false end
                end
                if bComplete then curTotal = curTotal + 1 end
                allTotal = allTotal + 1
            end
        else
            local progressInfo = self._seriesType == 1 and value.solProgressInfo or value.mpProgressInfo
            for key, info in pairs(progressInfo) do
                curTotal = (info.progress >= 1 and 1 or 0) + curTotal
                allTotal = allTotal + 1
            end
        end
    end

    return curTotal, allTotal
end

function RoleInfoAchivenmentListItem:CalRealyAchievementID(ids)
    self._achievemendIds = {}

    for _, id in pairs(ids) do
        local isShow = false
        local info = HeroHelperTool.GetHZAchievementDataTableRow(id)
        if not info then
            logerror("RoleInfoAchivenmentListItem:CreateAchievementBadgeItem info is nil", id)
            return
        end

        if info.Hide == -1 then -- 解锁才显示
            local curAccessoryData = Server.HeroServer:GetSelectedAccessory(Module.Hero:GetCurShowHeroId(),
                info.BadgeId[1])
            if curAccessoryData and curAccessoryData.is_unlock then
                isShow = true
            end
        else
            isShow = true
        end

        if isShow then
            table.insert(self._achievemendIds, id)
        end
    end
end

function RoleInfoAchivenmentListItem:_AddTickTimer()
    if self._timerForHeightAnim then
        self._timerForHeightAnim:Start()
        return
    end
    self._timerForHeightAnim = Timer:NewIns(0.01, 0)
    self._timerForHeightAnim:AddListener(self.UpdateHeight, self)
    self._timerForHeightAnim:Start()
end

function RoleInfoAchivenmentListItem:_PauseTickTimer()
    if self._timerForHeightAnim then
        self._timerForHeightAnim:Stop()
    end
end

function RoleInfoAchivenmentListItem:_RemoveTickTimer()
    --log("_RemoveTickTimer", self._timerForHeightAnim)
    if self._timerForHeightAnim then
        self._timerForHeightAnim:Release()
        self._timerForHeightAnim = nil
    end
end

function RoleInfoAchivenmentListItem:UpdateHeight()
	if self._oldHeight == self._newHeight then
		self:_PauseTickTimer()
		return
	end

	self._curPercent = (TimeUtil.GetCurrentTimeMillis() - self._beginUpdateTime) / TOTAL_UPDATE_TIME

	local y = FTweenEaseEquations.ComputeFloatEasing(EUITweenEaseType.EaseOutQuint, self._curPercent, 0, 1, 1)
	if self._curPercent > 1 then
		self._curPercent = 1
	end

	self._wtItemDetailPanel:SetMaxDesiredHeight(y * (self._newHeight - self._oldHeight) + self._oldHeight)
	if self._curPercent == 1 then
		self._oldHeight = self._newHeight
	end
end

function RoleInfoAchivenmentListItem:Refresh(index, dataTable, serverData, seriesType)
    if not dataTable then
        logerror("RoleInfoAchivenmentListItem:Show data is nil", index)
        return
    end

    self._index = index
    -- 0 sol , 1 mp
    -- if seriesType == 0 then -- 0 全部时用大厅的模式
    --     self._seriesType = RoleInfoLogic:IsInMp() and 2 or 1
    -- else
    self._seriesType = seriesType
    -- end

    self._wtIndexTxt:SetText('0 0 ' .. index)

    local openList = Module.RoleInfo.Field:GetOpenAchievementList()
    if openList[self._index] == 1 then
        self:SetType(1)
        self.bFold = true
    else
        self:SetType(0)
        self.bFold = false
    end

    if dataTable.SeriesName == "" then
        self._wtName:SetText(dataTable.SeriesID)
    else
        self._wtName:SetText(dataTable.SeriesName)
    end

    local progressValue = 0

    if serverData then
        self.acheveDataMap = serverData
        local curTotal, allTotal = self:GetCurAndAllCount(serverData)

        if curTotal == 0 then
            progressValue = 0
        else
            progressValue = curTotal / allTotal
        end
        self._wtProgressBar:SetRenderOpacity(1)
        self._wtProgressBar:SetRenderScale(FVector2D(1, 1))
        self._wtProgressBar:SetPercent(math.clamp(progressValue, 0, 1)) --todo 有的成就没有进度值 ！
        local percentText = string.format("%d%%", math.floor(progressValue * 100))
        self._wtProgress:SetText(percentText)

        if progressValue >= 1 then
            self._wtCommonHoverBg:Collapsed()
        else
            self._wtCommonHoverBg:SelfHitTestInvisible()
        end
    else
        local percentText = string.format("%d%%", 0)
        self._wtProgress:SetText(percentText)
    end

    self:SetUltimateImg(dataTable.RewarditemID, progressValue)

    self:CalRealyAchievementID(dataTable.AchievementID)


    self._wtCircleProcessImg:SetRenderScale(FVector2D(1, 1))
    self._wtCircleProcessImg:SetRenderOpacity(1)

    if self._SeriesID ~= dataTable.SeriesID then
        if self.bFold then
            self:CreateAchievementBadgeItem(self._achievemendIds)
        else
            self._itmeInsIds = {}
        end

        if dataTable.SeriesPic ~= "" then
            local DynamicMatIns = self._wtBg:GetDynamicMaterial()
            if DynamicMatIns then
                local fOnLoadFinishedCallback = function(self, imageAsset)
                    local classname = LAI.GetObjectClassName(imageAsset)
                    if not string.find(classname, "Texture") then
                        return
                    end
                    if DynamicMatIns.SetTextureParameterValue then
                        DynamicMatIns:SetTextureParameterValue("Texture", imageAsset)
                    end
                end
                ResImageUtil.AsyncLoadImgObjByPath(dataTable.SeriesPic, true, fOnLoadFinishedCallback, self)
            end
        end
        self._SeriesID = dataTable.SeriesID
    else
        if #self._itmeInsIds == 0 then -- 没有展开过
            return
        end

        local ids = self._achievemendIds
        if #ids ~= #self._itmeInsIds then -- 有新解锁
            self:CreateAchievementBadgeItem(ids)
            return
        end

        for k, insId in pairs(self._itmeInsIds) do
            local wtItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.AchievementBadgeItem, insId)
            local wtItem = getfromweak(wtItemWeak)
            if wtItem then
                wtItem:Refresh(ids[k], self.acheveDataMap[ids[k]], self._index, self._seriesType)
            end
        end
    end

    self:RefreshBigRewardSelect()

end

return RoleInfoAchivenmentListItem
