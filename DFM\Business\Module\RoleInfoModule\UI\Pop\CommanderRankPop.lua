----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class CommanderRankPop : LuaUIBaseView
local CommanderRankPop = ui("CommanderRankPop")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local Config = Module.RoleInfo.Config
--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

function CommanderRankPop:Ctor()
    self._wtRootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)

    local dfCommonButtons = self._wtRootWindow:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
        { btnText = Module.ArmedForce.Config.Loc.Confirm, fClickCallback = self._OnConfirmBtnClicked, caller = self }
    }, true)

    self._wtConfirmBtn = dfCommonButtons[CommonPopWindows.EHandleBtnType.Confirm]
    self._wtConfirmBtn:SelfHitTestInvisible()

    self._wtIcon1 = self:Wnd("WBP_RoleInfo_RankDisplayIcon_1", UIWidgetBase)
    self._wtIcon2 = self:Wnd("WBP_RoleInfo_RankDisplayIcon_2", UIWidgetBase)

    self:InitData()
end

function CommanderRankPop:InitData()
    self.score1 = 0
    self.score2 = 0

    self.commanderRankMode = 0
    local basicInfo = Server.RoleInfoServer:GetPlayerBasicInfo()
    if basicInfo then
        self.commanderRankMode = basicInfo.show_commander_rank_points
    end
end

function CommanderRankPop:OnOpen()
    local Events = Server.RoleInfoServer.Events
    self:AddLuaEvent(Events.evtCommanderRankModeSelect, self.OnEventRankModeSelect, self)

    local fCallbackIns = CreateCallBack(self._OnClosePopUI, self)
    self._wtRootWindow:BindCloseCallBack(fCallbackIns)

    self:RefreshUI()
end

function CommanderRankPop:OnClose()
    self:RemoveAllLuaEvent()
end

function CommanderRankPop:OnInitExtraData(score)
    self.select = self.commanderRankMode
    self.score1 = score
    self.score2 = Server.TournamentServer:GetCommanderMaxRankScore() -- 胜者为王积分

    self:RefreshRed()
end

function CommanderRankPop:RefreshRed()
    local TipsRecordServer = Server.TipsRecordServer
    if TipsRecordServer:GetNumber(TipsRecordServer.keys.FirstVictoryUniteRed) ~= 1 then
        TipsRecordServer:SetNumber(TipsRecordServer.keys.FirstVictoryUniteRed, 1)
        Server.RoleInfoServer.Events.evtSocialAvatarChange:Invoke()
    end
end

function CommanderRankPop:_OnConfirmBtnClicked()
    if self.select == self.commanderRankMode then
        return
    end

    Server.RoleInfoServer:ChangeCommanderRankMode(self.select)
end

function CommanderRankPop:OnEventRankModeSelect(select)
    self.select = select
    self:RefreshUI()
end

function CommanderRankPop:RefreshUI()
    self._wtIcon1:Refresh(0, self.score1, self.select) -- 晋升之路 
    self._wtIcon2:Refresh(1, self.score2, self.select) -- 胜者为王
end

function CommanderRankPop:_OnClosePopUI()
    Facade.UIManager:CloseUI(self)
end

function CommanderRankPop:OnNavBack()
    return false
end

--- BEGIN MODIFICATION @ VIRTUOS
function CommanderRankPop:OnShowBegin()
    if IsHD() then
        self:_InitGamepadInputs()
    end
end

function CommanderRankPop:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end

--- END MODIFICATION

--- BEGIN MODIFICATION @ VIRTUOS
function CommanderRankPop:_InitGamepadInputs()
    -- if not self:IsVisible() then
    --     return
    -- end

    -- if not self._navGroup then
    --     -- Replace common pop window nav group
    --     self._wtRootWindow:RemoveNavGroup()

    --     self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
    --     if self._navGroup then
    --         self._navGroup:AddNavWidgetToArray(self._wtItemScrollBox)
    --         self._navGroup:SetScrollRecipient(self._wtItemScrollBox)
    --         self._navGroup:MarkIsStackControlGroup()
    --     end

    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    -- end
end

function CommanderRankPop:_DisableGamepadInputs()
    -- if self._navGroup then
    --     WidgetUtil.RemoveNavigationGroup(self)
    --     self._navGroup = nil
    -- end
end

--- END MODIFICATION

return CommanderRankPop
