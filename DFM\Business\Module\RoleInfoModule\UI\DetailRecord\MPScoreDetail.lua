
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

local MPScoreDetail = ui("MPScoreDetail")

local mapConfigTable = Facade.TableManager:GetTable("MapConfig")
local tournamentTierTable = Facade.TableManager:GetTable("TournamentTier")
local tournamentParameterTable = Facade.TableManager:GetTable("TournamentParameter")
local tournamentParameterWorldTable = Facade.TableManager:GetTable("TournamentWordParameter")
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPUINavigationUtils = import("GPUINavigationUtils")
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"
-- END MODIFICATION
local tdmResult2Text = {
    [TDMResult.Match_Loss] = Module.RoleInfo.Config.Loc.ArenaResultTxt[ArenaResult.AR_Loss],
    [TDMResult.Match_Win] = Module.RoleInfo.Config.Loc.ArenaResultTxt[ArenaResult.AR_Win],
    [TDMResult.Match_Tie] = Module.RoleInfo.Config.Loc.ArenaResultTxt[ArenaResult.AR_Draw]
}

function MPScoreDetail:Ctor()
    self._wtMapTB = self:Wnd("wtMapTB", UITextBlock)
    self._wtResultTB = self:Wnd("wtResultTB", UITextBlock)

    self._wtNextStepWB = self:Wnd("wtNextStepWB", CommonSkipOverBg)
    self._wtNextStepWB:Collapsed()
    -- self._wtNextStepWB:BindClickEvent(self._OnNextStepBtnClicked, self)
    -- self._wtNextStepWB:SetKeepSelfOnClick(true)

    self._wtBattleTimeTB = self:Wnd("wtBattleTimeTB", UITextBlock)
    self._wtScoreTB = self:Wnd("wtScoreTB", UITextBlock)
    self._wtScorePerMinuteTB = self:Wnd("wtScorePerMinuteTB", UITextBlock)
    self._wtDurationCoefficientTB = self:Wnd("wtDurationCoefficientTB", UITextBlock)
    self._wtDurationCoefficientTipWB = self:Wnd("wtDurationCoefficientTipWB", DFCheckBoxOnly)
    self._wtDurationCoefficientTipTA = UIUtil.WndTipsAnchor(self, "wtDurationCoefficientTipTA", self._OnShowDurationCoefficientTip, self._OnHideDurationCoefficientTip)
    -- self._wtRankingTitleTB = self:Wnd("wtRankingTitleTB", UITextBlock)
    -- self._wtRankingTB = self:Wnd("wtRankingTB", UITextBlock)

    self._wtSpecialTipTA = UIUtil.WndTipsAnchor(self, "wtSpecialTipTA", self._OnShowSpecialTip, self._OnHideSepcialTip)

    self._wtRankingScoreTB = self:Wnd("wtRankingScoreTB", UITextBlock)
    self._wtLeaveTipWB = self:Wnd("wtLeaveTipWB", DFCheckBoxOnly)
    self._wtLeaveTipTA = UIUtil.WndTipsAnchor(self, "wtLeaveTipTA", self._OnShowLeaveTip, self._OnHideLeaveTip)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self.wtLeaveTipWB_1 = self:Wnd("wtLeaveTipWB_1", DFCheckBoxOnly)
    end
    --- END MODIFICATION

    self._wtPerformanceScoreVB = self:Wnd("wtPerformanceScoreVB", UIWidgetBase)
    self._wtPerformanceScoreTB = self:Wnd("wtPerformanceScoreTB", UITextBlock)
    self._wtClimbingAttackVictoryCP = self:Wnd("wtClimbingAttackVictoryCP", UIWidgetBase)
    self._wtMapWithCampBattleResultTB = self:Wnd("wtMapWithCampBattleResultTB", UITextBlock)
    self._wtClimbingAttackVictoryTB = self:Wnd("wtClimbingAttackVictoryTB", UITextBlock)
    self._wtMoralVictoryCP = self:Wnd("wtMoralVictoryCP", UIWidgetBase)
    self._wtMoralVictoryTB = self:Wnd("wtMoralVictoryTB", UITextBlock)
    self._wtSignificantDiffCP = self:Wnd("wtSignificantDiffCP", UIWidgetBase)
    self._wtSignificantDiffTB = self:Wnd("wtSignificantDiffTB", UITextBlock)

    self._wtSpecialScoreTB = self:Wnd("wtSpecialScoreTB", UITextBlock)
    self._wtHalfLeaveScoreTB = self:Wnd("wtHalfLeaveScoreTB", UITextBlock)
    self._wtHalfJoinScoreTB = self:Wnd("wtHalfJoinScoreTB", UITextBlock)

    self._wtMultipleScoreCP = self:Wnd("wtMultipleScoreCP", UIWidgetBase)
    self._wtRankScoreOneText = self:Wnd("wtRankScoreOneText", UITextBlock)

    self._wtMultipleScorePos = self:Wnd("wtMultipleScorePos", UIWidgetBase)
    self._wtRankTwoText = self:Wnd("wtRankTwoText", UITextBlock)
    self._wtRankScoreTwoText = self:Wnd("wtRankScoreTwoText", UITextBlock)
    self._wtDFImageBg3 = self:Wnd("DFImage_Bg_3", UIWidgetBase)

    self._wtProtectImgCP = self:Wnd("wtProtectImgCP", UIWidgetBase)
    self._wtRankProtectTB = self:Wnd("wtRankProtectTB", UITextBlock)

    self._wtCurScoreDeltaTB = self:Wnd("wtCurScoreDeltaTB", UITextBlock)
    self._wtCurStarScorePBTB = self:Wnd("wtCurStarScorePBTB", UITextBlock)
    self._wtCurStarScorePBImg = self:Wnd("wtCurStarScorePBImg", UIImage)
    self._wtCurStarScorePBLine = self:Wnd("wtCurStarScorePBLine", UIImage)

    self._wtRankIconWB = self:Wnd("wtRankIconWB", UIWidgetBase)
    self._wtRankNameTB = self:Wnd("wtRankNameTB", UITextBlock)
    self._wtRankStarWB = self:Wnd("wtRankStarWB", UIWidgetBase)

    -- azhengzheng:信誉非凡每日首胜
    self._wtReputationAwardCP = self:Wnd("wtReputationAwardCP", UIWidgetBase)
    self._wtReputationAwardTB = self:Wnd("wtReputationAwardTB", UITextBlock)

    self._wtTitle = self:Wnd("DFTextBlock_77", UIWidgetBase)
    self._wtTitle:Collapsed()

    if IsHD() then
        -- self._jumpOverHandle = self:AddInputActionBinding("JumpOver", EInputEvent.IE_Pressed, self._OnNextStepBtnClicked, self, EDisplayInputActionPriority.UI_Pop)
        --- BEGIN MODIFICATION @ VIRTUOS
        self.TipsNeedToHide = {}
        self._DCTipToShowCount = 1
        --- END MODIFICATION
    end

    if IsHD() then
        local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
        Module.CommonBar:RegStackUITopBarStyle(
            self,
            ETopBarStyleFlag.DefaultSecondary & ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends | ETopBarStyleFlag.Currency)
        )
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end

    Module.CommonBar:RegStackUITopBarTitle(self, Module.RoleInfo.Config.Loc.ScoreDetailTitle)

    self._isAnimFinish = nil
    self._length = IsHD() and 1180 or 898

    self._doubleRankScoreRate = 1

    -- self._wtAbilityScoreVB = self:Wnd("DFVerticalBox_1", UIWidgetBase)

    self._playerId = 0
    self._matchInfo = nil
end

function MPScoreDetail:OnInitExtraData(key, matchInfo, playerId)
    self._playerId = playerId
    self._matchInfo = matchInfo
end

function MPScoreDetail:OnOpen()
    -- azhengzheng:策划要求暂时屏蔽表现得分
    -- self:_DoSpecialLogic()

    -- Module.Settlement.Config.Events.evtMpRankSettlementShow:Invoke()

    self:_InitTournamentTable()

    -- if self._fakeBeginScore then
    --     self:_BuildFakeData()
    -- else
    -- end
    
    self:_SetTDMSettlementInfo()
    -- self:_DelayCreateRankScoreAnimTimer()
    self:_RefreshFinalRankScoreView()

    -- self:_DelayPlayUIAudioEvent()

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 输入绑定
    if IsHD() then
        if not self._scorePromptHandle then
            self._scorePromptHandle = self:AddInputActionBinding(
                "ScorePrompt", 
                EInputEvent.IE_Pressed, 
                self._OnToggleDurationCoefficientTip, 
                self, EDisplayInputActionPriority.UI_Pop
            )
        end
         
        -- if self._wtNextStepWB then
        --     if self._scorePromptHandle then
        --         self._wtNextStepWB:RemoveSummaries()
        --         self._wtNextStepWB:AddSummaries({"JumpOver","ScorePrompt"})
        --     end
        -- end
    end
    --- END MODIFICATION

end

function MPScoreDetail:OnShowBegin()

    -- if self._doubleRankScoreRate > 1 then
    --     if IsHD() then
    --         self:PlayAnimation(self.WBP_SettlementBattle_Integral_PC_S_double, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    --     else
    --         self:PlayAnimation(self.WBP_SettlementBattle_Integral_double, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    --     end
    -- else
    --     self:PlayAnimation(self.WBP_SettlementBattle_Integral_in_manual, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    -- end
end

function MPScoreDetail:OnShow()
    loginfo("MPScoreDetail:OnShow")
    Module.Settlement.Config.Events.evtMpRankSettlementShow:Invoke()

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() and not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        self._navGroup:MarkIsStackControlGroup()

        --禁用D-pad键导航和默认A键
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
    end
    --- END MODIFICATION
end

function MPScoreDetail:OnHideBegin()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        if self._jumpOverHandle then
            self:RemoveInputActionBinding(self._jumpOverHandle)
            self._jumpOverHandle = nil
        end
        if self._scorePromptHandle then
            self:RemoveInputActionBinding(self._scorePromptHandle)
            self._scorePromptHandle = nil
        end

        -- if self._wtNextStepWB then
        --     self._wtNextStepWB:RemoveSummaries()
        -- end
    end
    --- END MODIFICATION
    self:PlayAnimation(self.WBP_SettlementBattle_Integral_out, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
end

function MPScoreDetail:OnHide()
    loginfo("MPScoreDetail:OnHideBegin")
    Module.Settlement.Config.Events.evtMpRankSettlementClose:Invoke()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() and self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
        
        -- 还原导航配置
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
    --- END MODIFICATION
end

function MPScoreDetail:OnAnimFinished(anim)
    if anim == self.WBP_SettlementBattle_Integral_in_manual then
        self._isAnimFinish = true
        Module.Settlement.Config.Events.evtMpRankSettlementInAnimFin:Invoke()
    end
end



function MPScoreDetail:OnClose()
    Module.Settlement.Config.Events.evtMpRankSettlementClose:Invoke()

    self:_CloseAllTip()
    self:_StopAllUIAudio()

    Module.Settlement:OpenSettlementUI(self.UINavID)
end

-- function MPScoreDetail:_BuildFakeData()
--     self._curRankScore = self._fakeBeginScore
--     self._finalRankScoreDelta = self._fakeScoreDelta
--     self._finalRankScore = self._curRankScore + self._finalRankScoreDelta
-- end

-- function MPScoreDetail:_DoSpecialLogic()
--     self._wtAbilityScoreVB:Collapsed()
-- end

function MPScoreDetail:_InitTournamentTable()
    if not tournamentTierTable or not tournamentParameterTable then
        return
    end

    local curSerial = Server.TournamentServer:GetCurSerial()

    if not curSerial then
        return
    end

    self._tournamentTable = {}

    for _, value in pairs(tournamentTierTable) do
        if table.contains(value.SeasonID or {}, curSerial) then
            table.insert(self._tournamentTable, {name = value.Name, tierTypeID = value.TierTypeID, minPoint = value.MinPoint, starsDivided = value.StarsDivided, badgeMinIcon = value.BadgeMinIcon})
        end
    end

    table.sort(
        self._tournamentTable,
        function(a, b)
            return a.minPoint < b.minPoint
        end
    )

    for key, value in ipairs(self._tournamentTable) do
        if value.starsDivided == -1 then
            value.maxPoint = math.maxinteger
            value.singleStarScore = tournamentParameterTable["StartValue"] and tonumber(tournamentParameterTable["StartValue"]) or 50
        else
            value.maxPoint = self._tournamentTable[key + 1].minPoint
            value.singleStarScore = (value.maxPoint - value.minPoint) / value.starsDivided
        end
    end
end

function MPScoreDetail:_SetTDMSettlementInfo()
    -- local tdmSettlementInfo = Server.SettlementServer:GetMPSettlementInfo()

    -- if not tdmSettlementInfo then
    --     return
    -- end

    self:_SetMatchInfo(self._matchInfo)
    self:_SetTDMData()
end

function MPScoreDetail:_SetMatchInfo(matchInfo)
    if not matchInfo.match_mode then
        return
    end

    self:_SetMapTBByMapID(matchInfo.match_mode.map_id)
end

function MPScoreDetail:_SetMapTBByMapID(mapID)
    if not mapID or not mapConfigTable then
        return
    end

    for _, value in pairs(mapConfigTable) do
        if mapID == value.MapID then
            self._mapDisplayName = value.DisplayName
            self._wtMapTB:SetText(value.DisplayName)
            return
        end
    end

    self._wtMapTB:SetText("--")
end

function MPScoreDetail:_SetTDMData(tdmData)

    self:_FindMySelfInfo()

    local tdmData = self._matchInfo.tournament_data

    -- if not self._leave and self._isJoinAtMiddle then
    --     self._wtRankingTB:SetText(tdmData.self_ranking or "-")
    -- end

    self._wtDurationCoefficientTB:SetText(string.format(Module.Settlement.Config.Loc.PercentNum, math.round(100 * (tdmData.time_factor or 0))))

    if self._leave or (not tdmData.rank_score_spm_score) then
        self._wtRankingScoreTB:SetText(0)
    else
        if tdmData.rank_score_spm_score < 1 then
            self._wtRankingScoreTB:SetText(tdmData.rank_score_spm_score)
        else
            self._wtRankingScoreTB:SetText(string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.rank_score_spm_score))
        end
    end

    if Module.Reputation:ModuleFuncIsUnlock() and tdmData.rank_score_reputation_extra_score and tdmData.rank_score_reputation_extra_score > 0 then
        self._wtReputationAwardCP:Visible()
        self._wtReputationAwardTB:SetText(string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.rank_score_reputation_extra_score))
    else
        self._wtReputationAwardCP:Collapsed()
    end

    -- if tdmData.ranked_score_shoot_delta == 0 and tdmData.ranked_score_tactics_delta == 0 and tdmData.ranked_score_vehicle_delta == 0 then
    --     self._wtRankScoreVB:Collapsed()
    --     self._wtRankScoreDeltaTB:SetText(0)
    -- else
    --     local rankScoreDetla = tdmData.ranked_score_shoot_delta + tdmData.ranked_score_tactics_delta + tdmData.ranked_score_vehicle_delta
    --     self._wtRankScoreDeltaTB:SetText(rankScoreDetla < 1 and rankScoreDetla or string.format(Module.Settlement.Config.Loc.plusSignText, rankScoreDetla))
    --     self._wtShootScoreTB:SetText(tdmData.ranked_score_shoot_delta < 1 and tdmData.ranked_score_shoot_delta or string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.ranked_score_shoot_delta))
    --     self._wtVehicleScoreTB:SetText(tdmData.ranked_score_vehicle_delta < 1 and tdmData.ranked_score_vehicle_delta or string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.ranked_score_vehicle_delta))
    --     self._wtTacticsScoreTB:SetText(tdmData.ranked_score_tactics_delta < 1 and tdmData.ranked_score_tactics_delta or string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.ranked_score_tactics_delta))
    -- end

    local rankScoreResultScore = tdmData.rank_score_result_score or 0
    local rankScoreDefeatedExtraScore = tdmData.rank_score_defeated_extra_score or 0
    
    local rankScoreCampsGapExtraScore = tdmData.rank_score_camps_gap_extra_score or 0
    if rankScoreResultScore == 0 and rankScoreDefeatedExtraScore == 0 and rankScoreCampsGapExtraScore == 0 then
        self._wtPerformanceScoreTB:SetText(0)
        self._wtPerformanceScoreVB:Collapsed()
    else
        self._performanceTipList = {}
        self._performanceScore = rankScoreResultScore + rankScoreDefeatedExtraScore + rankScoreCampsGapExtraScore
        self._wtPerformanceScoreTB:SetText(self._performanceScore < 1 and self._performanceScore or string.format(Module.Settlement.Config.Loc.plusSignText, self._performanceScore))

        if rankScoreResultScore ~= 0 then
            self._wtClimbingAttackVictoryTB:SetText(rankScoreResultScore < 1 and rankScoreResultScore or string.format(Module.Settlement.Config.Loc.plusSignText, rankScoreResultScore))
            self._wtMapWithCampBattleResultTB:SetText(
                StringUtil.Key2StrFormat(
                    Module.Settlement.Config.Loc.MapWithCampBattleResult,
                    {
                        ["map"] = self._mapDisplayName or "--",
                        ["camp"] = self._isAttacker and Module.Settlement.Config.Loc.Attacker or Module.Settlement.Config.Loc.Defender,
                        ["result"] = self._isWinner == true and tdmResult2Text[TDMResult.Match_Win] or tdmResult2Text[TDMResult.Match_Loss]
                    }
                )
            )
            self._wtClimbingAttackVictoryCP:Visible()
            table.insert(
                self._performanceTipList,
                {
                    textContent = StringUtil.Key2StrFormat(
                        Module.Settlement.Config.Loc.MapWithCampBattleResultTip,
                        {
                            ["map"] = self._mapDisplayName or "--",
                            ["camp"] = self._isAttacker and Module.Settlement.Config.Loc.Attacker or Module.Settlement.Config.Loc.Defender,
                            ["result"] = self._isWinner == true and tdmResult2Text[TDMResult.Match_Win] or tdmResult2Text[TDMResult.Match_Loss]
                        }
                    ),
                    styleRowId = "C000"
                }
            )
        else
            self._wtClimbingAttackVictoryCP:Collapsed()
        end

        if rankScoreDefeatedExtraScore ~= 0 then
            self._wtMoralVictoryTB:SetText(rankScoreDefeatedExtraScore < 1 and rankScoreDefeatedExtraScore or string.format(Module.Settlement.Config.Loc.plusSignText, rankScoreDefeatedExtraScore))
            self._wtMoralVictoryCP:Visible()
            table.insert(self._performanceTipList, {textContent = Module.Settlement.Config.Loc.MoralVictoryTip, styleRowId = "C000"})
        else
            self._wtMoralVictoryCP:Collapsed()
        end

        if rankScoreCampsGapExtraScore ~= 0 then
            self._wtSignificantDiffTB:SetText(rankScoreCampsGapExtraScore < 1 and rankScoreCampsGapExtraScore or string.format(Module.Settlement.Config.Loc.plusSignText, rankScoreCampsGapExtraScore))
            self._wtSignificantDiffCP:Visible()
            table.insert(self._performanceTipList, {textContent = Module.Settlement.Config.Loc.SignificantDiffTip, styleRowId = "C000"})
        else
            self._wtSignificantDiffCP:Collapsed()
        end

        self._wtPerformanceScoreVB:Visible()
    end

    local specialScore = tdmData.ranked_score_leave_penalty + (self._leave and 0 or (tdmData.ranked_score_half_join_score or 0)) + (tdmData.rank_score_reputation_extra_score or 0)
    self._wtSpecialScoreTB:SetText(specialScore < 1 and specialScore or string.format(Module.Settlement.Config.Loc.plusSignText, specialScore))
    self._wtHalfLeaveScoreTB:SetText(tdmData.ranked_score_leave_penalty)
    self._wtHalfJoinScoreTB:SetText(self._leave and 0 or (tdmData.ranked_score_half_join_score and tdmData.ranked_score_half_join_score ~= 0 and string.format(Module.Settlement.Config.Loc.plusSignText, tdmData.ranked_score_half_join_score) or 0))
    TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtCurScoreDeltaTB, "C006")
    if tdmData.rank_shields and #tdmData.rank_shields ~= 0 then
        self._isProtect = true
        self._wtProtectImgCP:Visible()
        self._wtRankProtectTB:Visible()
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtCurScoreDeltaTB, "C005")

        local textStr = ""
        local shiledStr = ""
        for index, value in ipairs(tdmData.rank_shields) do
            if value.shield_type == 4 then
                if tournamentParameterWorldTable and tournamentParameterWorldTable["SettlementScoreProtectCard"] then
                    textStr = tournamentParameterWorldTable["SettlementScoreProtectCard"].Value
                    shiledStr = string.format(textStr, value.left_shield)
                else
                    textStr = Module.Settlement.Config.Loc.MPRankProtectCard
                    shiledStr = string.format("%s(%s)", textStr, value.left_shield)
                end
            end
            if value.shield_type == 2 then
                if tournamentParameterWorldTable and tournamentParameterWorldTable["SettlementScoreProtect"] then
                    if value.total_shied - value.used_shied > 100000 then
                        shiledStr = tournamentParameterWorldTable["SettlementScoreProtectInfinitely"].Value
                    else
                        shiledStr = string.format(tournamentParameterWorldTable["SettlementScoreProtect"].Value, value.total_shied - value.used_shied)
                    end
                else
                    textStr = Module.Settlement.Config.Loc.MPRankProtectScore
                    shiledStr = string.format("%s(%s)", textStr, value.total_shied - value.used_shied)
                end
            end
            if value.shield_type <= 1 then
                if tournamentParameterWorldTable and tournamentParameterWorldTable["SettlementTierProtect"] then
                    if value.total_shied - value.used_shied > 100000 then
                        shiledStr = tournamentParameterWorldTable["SettlementTierProtectInfinitely"].Value
                    else
                        shiledStr = string.format(tournamentParameterWorldTable["SettlementTierProtect"].Value, value.total_shied - value.used_shied)
                    end
                else
                    textStr = Module.Settlement.Config.Loc.MPRankScoreProtect
                    shiledStr = string.format("%s(%s)", textStr, value.total_shied - value.used_shied)
                end
            end
            if value.shield_type == 3 then
                if tournamentParameterWorldTable and tournamentParameterWorldTable["SettlementScoreProtectActivity"] then
                    if value.total_shied - value.used_shied > 100000 then
                        shiledStr = tournamentParameterWorldTable["SettlementScoreProtectActivityInfinitely"].Value
                    else
                        shiledStr = string.format(tournamentParameterWorldTable["SettlementScoreProtectActivity"].Value, value.total_shied - value.used_shied)
                    end
                else
                    textStr = Module.Settlement.Config.Loc.MPRankProtectAct
                    shiledStr = string.format("%s(%s)", textStr, value.total_shied - value.used_shied)
                end
                break
            end
        end
        self._wtRankProtectTB:SetText(shiledStr)
    end

    self._curRankScore = tdmData.rank_match_score
    self._finalRankScoreDelta = tdmData.real_total_ranked_score or 0
    self._finalRankScore = self._curRankScore + self._finalRankScoreDelta

    local rankBaseScore = tdmData.rank_score_spm_score
    if self._doubleRankScoreRate > 1 and rankBaseScore > 0 then
        rankBaseScore = math.modf(tdmData.rank_score_spm_score / self._doubleRankScoreRate)
    end
    self._wtRankScoreOneText:SetText(rankBaseScore > 0 and string.format(Module.Settlement.Config.Loc.plusSignText, rankBaseScore) or rankBaseScore)

    if self._doubleRankScoreRate > 1 and self._finalRankScoreDelta > -1 then
        self._wtMultipleScoreCP:Visible()
        self._wtMultipleScorePos:SelfHitTestInvisible()
        local doubleScore = tdmData.rank_score_spm_score - rankBaseScore
        self._wtRankScoreTwoText:SetText(doubleScore > 0 and string.format(Module.Settlement.Config.Loc.plusSignText, doubleScore) or doubleScore)
        self._wtDFImageBg3:SelfHitTestInvisible()
    else
        self._wtMultipleScorePos:Collapsed()
        self._wtMultipleScoreCP:Collapsed()
        self._wtDFImageBg3:Collapsed()
    end
end

function MPScoreDetail:_DelayCreateRankScoreAnimTimer()
    self._curRankScoreDelta = 0
    self._curRank = nil
    self:_RefreshRankScoreView()

    self._delayCreateRankScoreAnimTimerHandle = Timer:NewIns(0.1, 0)
    self._delayCreateRankScoreAnimTimerHandle:AddListener(self._CreateRankScoreAnimTimer, self)
    self._delayCreateRankScoreAnimTimerHandle:Start()
end

function MPScoreDetail:_ReleaseDelayCreateRankScoreAnimTimer()
    if not self._delayCreateRankScoreAnimTimerHandle then
        return 
    end

    self._delayCreateRankScoreAnimTimerHandle:Release()
    self._delayCreateRankScoreAnimTimerHandle = nil
end

function MPScoreDetail:_CreateRankScoreAnimTimer()
    self:_ReleaseDelayCreateRankScoreAnimTimer()

    if not self._tournamentTable then
        return
    end
 
    if self._finalRankScoreDelta == 0 then
        self:_RefreshFinalRankScoreView()
        return
    end

    self._singleRankScoreDelta = self._finalRankScoreDelta < 0 and -1 * math.ceil(math.abs(self._finalRankScoreDelta) / 120) or math.ceil(self._finalRankScoreDelta / 120)

    self._rankScoreAnimTimerHandle = Timer:NewIns(0.025, 0)
    self._rankScoreAnimTimerHandle:AddListener(self._RefreshRankScoreViewByTimer, self)
    self._rankScoreAnimTimerHandle:Start()

    Facade.SoundManager:PlayUIAudioEvent(self._finalRankScoreDelta < 0 and DFMAudioRes.UISettlementBarDownLoop or DFMAudioRes.UISettlementBarLoop)
end

function MPScoreDetail:_RefreshRankScoreViewByTimer()
    self._curRankScoreDelta = self._curRankScoreDelta + self._singleRankScoreDelta
    self._curRankScore = self._curRankScore + self._singleRankScoreDelta

    if self._finalRankScoreDelta < 0 then
        if self._curRankScoreDelta <= self._finalRankScoreDelta then
            self:_ReleaseRankScoreAnimTimer()
            return
        end
    else
        if self._finalRankScoreDelta <= self._curRankScoreDelta then
            self:_ReleaseRankScoreAnimTimer()
            return
        end
    end

    self:_RefreshRankScoreView()
end

function MPScoreDetail:_RefreshFinalRankScoreView()
    self._curRankScoreDelta = self._finalRankScoreDelta
    self._curRankScore = self._finalRankScore

    self:_RefreshRankScoreView()
    self:_StopAllUIAudio()
end

function MPScoreDetail:_RefreshRankScoreView()
    if not self._curRank or self._curRankScore < self._tournamentTable[self._curRank].minPoint or self._tournamentTable[self._curRank].maxPoint <= self._curRankScore then
        self:_CalculateRankByScore()
        self._wtRankIconWB:SetTournamentIconByScore(self._curRankScore)
        if self._curRank and self._tournamentTable[self._curRank] then
            self._wtRankNameTB:SetText(self._tournamentTable[self._curRank].name)
            self._wtRankStarWB:SetRankStarNum(self._tournamentTable[self._curRank].starsDivided, true)
        end
    
        if self._preRank then
            self._wtRankStarWB:SetRankStarState(0)
            self:_CalculateStarScore()
            self:_CalculateRankChengeType()
            self._preRank = self._curRank
            return
        end
        self._preRank = self._curRank
    end
    if self._curRank and self._tournamentTable[self._curRank] then
        self:_CalculateStarScore()
    end
end

function MPScoreDetail:_CalculateStarScore()
    local curRankStarActiveNum = self:_CalculateRankStarInfo()

    if self._curRankStarActiveNum ~= curRankStarActiveNum then
        if self._curRankStarActiveNum then
            Facade.SoundManager:PlayUIAudioEvent(self._finalRankScoreDelta < 0 and DFMAudioRes.UISeasonDown or DFMAudioRes.UISeasonUp)
        end

        self._curRankStarActiveNum = curRankStarActiveNum
        self._wtRankStarWB:SetRankStarState(self._curRankStarActiveNum)
    end

    self._wtCurScoreDeltaTB:SetText(self._curRankScoreDelta < 1 and self._curRankScoreDelta or string.format(Module.Settlement.Config.Loc.plusSignText, self._curRankScoreDelta))
    self._wtCurStarScorePBTB:SetText(string.format(Module.Settlement.Config.Loc.CurLevelExpSchedule, self._curStarScore, self._tournamentTable[self._curRank].singleStarScore))
    local curStarScorePB = self._curStarScore / self._tournamentTable[self._curRank].singleStarScore
    self._wtCurStarScorePBImg:SetPercent(curStarScorePB)
    self._wtCurStarScorePBLine:SetPosition(FVector2D(self._length * curStarScorePB, 0))
end

function MPScoreDetail:_CalculateRankChengeType()
    if self._tournamentTable[self._preRank] and self._tournamentTable[self._preRank].tierTypeID == self._tournamentTable[self._curRank].tierTypeID then
        if self._finalRankScoreDelta < 0 then
            self:_SmallRankDown()
        else
            self:_SmallRankUp()
        end
    else
        if self._finalRankScoreDelta < 0 then
            self:_BigRankDown()
        else
            self:_BigRankUp()
        end
    end
end

function MPScoreDetail:_BigRankUp()
    Facade.UIManager:AsyncShowUI(UIName2ID.MPBigRankUp, nil, self, self._tournamentTable[self._curRank].tierTypeID, self._tournamentTable[self._curRank].badgeMinIcon)

    self:_ReleaseRankScoreAnimTimer()
    self:_CloseAllTip()
end

function MPScoreDetail:_SmallRankUp()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallUp)
end

function MPScoreDetail:_BigRankDown()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallDown)
end

function MPScoreDetail:_SmallRankDown()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISeasonSmallDown)
end

function MPScoreDetail:_CalculateRankStarInfo()
    local curRankScore = self._curRankScore - self._tournamentTable[self._curRank].minPoint
    local curRankStarActiveNum = math.ceil((curRankScore + 1) / self._tournamentTable[self._curRank].singleStarScore)
    self._curStarScore = curRankScore - self._tournamentTable[self._curRank].singleStarScore * (curRankStarActiveNum - 1)
    return curRankStarActiveNum
end

function MPScoreDetail:_CalculateRankByScore()
    for key, value in ipairs(self._tournamentTable) do
        if value.minPoint <= self._curRankScore and self._curRankScore < value.maxPoint then
            self._curRank = key
            loginfo("MPScoreDetail:_CalculateRankByScore curRank = ", self._curRank)
            return
        end
    end

    self._finalRankScore = self._tournamentTable[1].minPoint
    self:_ReleaseRankScoreAnimTimer()
end

function MPScoreDetail:_ReleaseRankScoreAnimTimer()
    if not self._rankScoreAnimTimerHandle then
        return
    end

    self._rankScoreAnimTimerHandle:Release()
    self._rankScoreAnimTimerHandle = nil

    self:_RefreshFinalRankScoreView()
end

function MPScoreDetail:_FindMySelfInfo(tdmData)
    -- if not campList then
    --     return
    -- end

    -- local playerId = self._playerId  --Server.AccountServer:GetPlayerId()

    self:_SetTDMPlayerInfo()

    -- self._isWinner = camp.is_winner   
    -- self._isAttacker = camp.attacker
    -- self:_SetTDMPlayerInfo(player, camp.is_winner)

    -- if not playerId then
    --     return
    -- end

    -- for _, camp in pairs(campList) do
    --     for _, team in pairs(camp.team_list) do
    --         for _, player in pairs(team.player_list) do
    --             if playerId == player.player_id then
    --                 self._isWinner = camp.is_winner
    --                 self._isAttacker = camp.attacker
    --                 self:_SetTDMPlayerInfo(player, camp.is_winner)
    --                 return
    --             end
    --         end
    --     end
    -- end
end

function MPScoreDetail:_SetTDMPlayerInfo()

    local data = self._matchInfo.tournament_data
    local mpInfo = self._matchInfo.mp

    self._wtBattleTimeTB:SetText(TimeUtil.GetSecondsFormatHHMMSSString(data.play_time))
    self._wtScoreTB:SetText(MathUtil.GetNumberFormatStr(mpInfo.score or 0))
    self._wtScorePerMinuteTB:SetText(MathUtil.GetNumberFormatStr(math.ceil(mpInfo.score / data.play_time * 60)))

    self._isWinner = mpInfo.is_winner
    self._isAttacker = mpInfo.attacker

    if mpInfo.leave then
        self._leave = true
        self._wtResultTB:SetText(Module.RoleInfo.Config.Loc.RoleQuitTxt)
    else
        if self._isWinner then
            self._wtResultTB:SetText(Module.RoleInfo.Config.Loc.RoleWinTxt)
        else
            self._wtResultTB:SetText(Module.RoleInfo.Config.Loc.RoleLoseTxt)
        end
    end
    self._doubleRankScoreRate = 1

    if not self._leave and data.double_rank_info then
        self._doubleRankScoreRate = data.double_rank_info.double_rate
        local textStr = ""
        if data.double_rank_info.reason == 1 then
            if tournamentParameterWorldTable then
                if data.double_rank_info.total_num - data.double_rank_info.used_num > 100000 then
                    textStr = tournamentParameterWorldTable["SettlementDoubleActivityInfinitely"].Value
                else
                    textStr = string.format(tournamentParameterWorldTable["SettlementDoubleActivity"].Value, data.double_rank_info.total_num - data.double_rank_info.used_num)
                end
            else
                textStr = Module.Settlement.Config.Loc.DoubleRankActScore
            end
        end

        if data.double_rank_info.reason == 2 then
            if tournamentParameterWorldTable and tournamentParameterWorldTable["SettlementDoubleCard"] then
                textStr = string.format(tournamentParameterWorldTable["SettlementDoubleCard"].Value, data.double_rank_info.left_num)
            else
                textStr = string.format("%s(%s)", Module.Settlement.Config.Loc.DoubleRankScore, data.double_rank_info.left_num)
            end
        end
        self._wtRankTwoText:SetText(textStr)
    end
end

function MPScoreDetail:_OnShowDurationCoefficientTip()
    self._durationCoefficientTipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = Module.Settlement.Config.Loc.DurationCoefficientTip, styleRowId = "C000"}}, self._wtDurationCoefficientTipTA)
end

function MPScoreDetail:_OnHideDurationCoefficientTip()
    if not self._durationCoefficientTipHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._durationCoefficientTipHandle, self._wtDurationCoefficientTipTA)
    self._durationCoefficientTipHandle = nil
end

function MPScoreDetail:_OnShowSpecialTip()
    if self._specialTipHandle or not self._performanceTipList or #self._performanceTipList == 0 then
        return
    end

    self._specialTipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(self._performanceTipList, self._wtSpecialTipTA)
end

function MPScoreDetail:_OnHideSepcialTip()
    if not self._specialTipHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._specialTipHandle, self._wtSpecialTipTA)
    self._specialTipHandle = nil
end

function MPScoreDetail:_OnShowLeaveTip()
    if self._leaveTipHandle then
        return
    end

    self._leaveTipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = Module.Settlement.Config.Loc.PerformanceScoreTip, styleRowId = "C000"}}, self._wtLeaveTipTA)
end

function MPScoreDetail:_OnHideLeaveTip()
    if not self._leaveTipHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._leaveTipHandle, self._wtLeaveTipTA)
    self._leaveTipHandle = nil
end

function MPScoreDetail:_CloseAllTip()
    self:_OnHideLeaveTip()
    self:_OnHideSepcialTip()
    self:_OnHideDurationCoefficientTip()
end

function MPScoreDetail:_StopAllUIAudio()
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarDownLoop)
end

function MPScoreDetail:_OnNextStepBtnClicked()
    -- if Module.Settlement.Field:GetIsSpecialPopOpen() then
    --     return
    -- end

    -- if not self._isAnimFinish then
    --     self:_CancelAllDelay()
    --     self:StopAnimation(self.WBP_SettlementBattle_Integral_in_manual)
    -- end

    if self._delayCreateRankScoreAnimTimerHandle then
        self:_CreateRankScoreAnimTimer()
        return
    end

    if self._rankScoreAnimTimerHandle then
        self:_ReleaseRankScoreAnimTimer()
        return
    end

    Facade.UIManager:CloseUI(self)
end

function MPScoreDetail:OnNavBack()
    return true
end

function MPScoreDetail:_DelayPlayUIAudioEvent()
    Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Settlement_Title")

    local timerList = {}
    self._timerHandleList = {}
    table.insert(timerList, {delayTime = 1.6, func = function ()
        Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Settlement_ScoreDetail")
    end, caller = self})
    table.insert(timerList, {delayTime = 2.93, func = function ()
        Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Settlement_ScoreDetail")
    end, caller = self})
    table.insert(timerList, {delayTime = 3.77, func = function ()
        Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Settlement_Badge")
    end, caller = self})

    if self._isProtect then
        table.insert(timerList, {delayTime = 3.8, func = function ()
            Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Protection")
        end, caller = self})
    end

    if self._performanceScore then
        table.insert(timerList, {delayTime = 2.3, func = function ()
            Facade.SoundManager:PlayUIAudioEvent("UI_Tournament_Settlement_ScoreDetail")
        end, caller = self})
    end

    for _, value in pairs(timerList) do
        local timerHandle = Timer.DelayCall(value.delayTime, value.func, value.caller)
        table.insert(self._timerHandleList, timerHandle)
    end
end

function MPScoreDetail:_CancelAllDelay()
    if not self._timerHandleList or #self._timerHandleList == 0 then
        return
    end

    for _, value in pairs(self._timerHandleList) do
        Timer.CancelDelay(value)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function MPScoreDetail:_OnToggleDurationCoefficientTip()
    if not IsHD() then
        return
    end

    -- 将提示按钮和对应函数存入表中
    local ShownTiBtns = {
        self._wtDurationCoefficientTipWB,
        self._wtLeaveTipWB,
        self.wtLeaveTipWB_1
    }

    -- 对应于ShownTiBtns的Show函数
    local ShowFunctions = {
        self._OnShowDurationCoefficientTip, 
        self._OnShowLeaveTip,
        self._OnShowSpecialTip,                            
    }

    -- 对应于ShownTiBtns的Hide函数
    local HideFunctions = {
        self._OnHideDurationCoefficientTip,
        self._OnHideLeaveTip,
        self._OnHideSepcialTip           
    }
    
    if self._DCTipToShowCount == #HideFunctions + 1 then
        self._DCTipToShowCount = 1
    end

    for _, btn in ipairs(ShownTiBtns) do
        if btn and btn:IsVisible() then
            ShowFunctions[self._DCTipToShowCount](self)
            for _, func in ipairs(HideFunctions) do
                if func ~= HideFunctions[self._DCTipToShowCount] then
                    func(self)
                end
            end
            self._DCTipToShowCount = self._DCTipToShowCount + 1
            return
        end
        self._DCTipToShowCount = self._DCTipToShowCount + 1
    end
end
--- END MODIFICATION
return MPScoreDetail