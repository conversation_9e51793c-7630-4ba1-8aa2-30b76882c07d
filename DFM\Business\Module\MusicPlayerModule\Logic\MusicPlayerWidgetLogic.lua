----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LogMusicPlayer)
----- LOG FUNCTION AUTO GENERATE END -----------
---
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local UGPAudioStatics = import"GPAudioStatics"

---@class MusicPlayerWidgetLogic
local MusicPlayerWidgetLogic = {}


function MusicPlayerWidgetLogic.InitMusicList()
    local MusicDataTable = Facade.TableManager:GetTable("MusicPlayerWidgetData")
    if not MusicDataTable then
        logerror("[MusicPlayerWidgetLogic.InitMusicList] MusicDataTable is nil")
        return
    end

    local lastInfo = Module.MusicPlayer.Field:GetMusicPlayerWidgetInfo()
    local lastCurEvtName
    if lastInfo then
        lastCurEvtName = lastInfo.musicList and lastInfo.musicList[lastInfo.curIndex] and lastInfo.musicList[lastInfo.curIndex].AudioEventName
    end
    local MusicPlayerWidgetInfo = {}
    MusicPlayerWidgetInfo.musicList = {}
    MusicPlayerWidgetInfo.musicPlayPosRecords = lastInfo and lastInfo.musicPlayPosRecords or {}
    MusicPlayerWidgetInfo.curIndex = lastInfo and lastInfo.curIndex or 1
    MusicPlayerWidgetInfo.palyMode = lastInfo and lastInfo.palyMode or 1 --(1:循环, 2:顺序 , 3:随机)
    MusicPlayerWidgetInfo.isPlaying = lastInfo and lastInfo.isPlaying or Module.MusicPlayer:ReadIsPlaying()
    local musicList = {}
    for _, dataRow in pairs(MusicDataTable) do
        local musicData = {
            Name = dataRow.Name,
            Desc = dataRow.Desc,
            Icon = dataRow.Icon,
            AudioEventName = dataRow.AudioEventName,
            SortOrder = dataRow.SortOrder,
            bLimited = dataRow.bLimited,
            LimitedHeroFashionId = dataRow.LimitedHeroFashionId,
        }
        table.insert(musicList, musicData)
    end

    MusicPlayerWidgetInfo.musicList = musicList
    table.sort(MusicPlayerWidgetInfo.musicList, function(a, b)

        local isUnlockedA = MusicPlayerWidgetLogic.IsUnlockedMusic(a.AudioEventName) and 1 or 0
        local isUnlockedB = MusicPlayerWidgetLogic.IsUnlockedMusic(b.AudioEventName) and 1 or 0
        if isUnlockedA == isUnlockedB then
            return a.SortOrder < b.SortOrder
        end
        return isUnlockedA > isUnlockedB
    end)
    Module.MusicPlayer.Field:SetMusicPlayerWidgetInfo(MusicPlayerWidgetInfo)

    if lastCurEvtName and MusicPlayerWidgetLogic.IsUnlockedMusic(lastCurEvtName) then
        for i, musicData in ipairs(MusicPlayerWidgetInfo.musicList) do
            if lastCurEvtName == musicData.AudioEventName then
                MusicPlayerWidgetLogic.SetCurMusicIndex(i)
                break
            end
        end
    else
        for i, musicData in ipairs(MusicPlayerWidgetInfo.musicList) do
            if MusicPlayerWidgetLogic.IsUnlockedMusic(musicData.AudioEventName) then
                MusicPlayerWidgetLogic.SetCurMusicIndex(i)
                break
            end
        end
    end
end

function MusicPlayerWidgetLogic.GetMusicList()
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local musicList = MusicPlayerWidgetInfo.musicList
    return musicList
end

function MusicPlayerWidgetLogic.SetPlayAndPauseIcon(inWidget, evtName)
    local isDownloading = MusicPlayerWidgetLogic.IsMusicDownloading(evtName)
    if isDownloading then
        if inWidget then
            inWidget:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre['Pause'])
        end
        return Module.MusicPlayer.Config.MusicPlayerIconSoucre['Pause']
    end

    local isDownloaded = MusicPlayerWidgetLogic.IsMusicDownloaded(evtName)
    local hasDownloadPercent = (MusicPlayerWidgetLogic.GetMusicDownloadProgress(evtName) ~= 0)

    if not isDownloaded and hasDownloadPercent then --下载被暂停的状态
        if inWidget then
            inWidget:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre['Continue'])
        end
        return Module.MusicPlayer.Config.MusicPlayerIconSoucre['Continue']
    end

    if not isDownloaded then
        if inWidget then
            inWidget:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre['DownLoad'])
        end
        return Module.MusicPlayer.Config.MusicPlayerIconSoucre['DownLoad']
    end

    local isPlaying =  MusicPlayerWidgetLogic.IsPlayingInMusicList(evtName)
    if isPlaying then
        if inWidget then
            inWidget:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre['Pause'])
        end
        return Module.MusicPlayer.Config.MusicPlayerIconSoucre['Pause']
    else
        if inWidget then
            inWidget:AsyncSetImagePath(Module.MusicPlayer.Config.MusicPlayerIconSoucre['Play'])
        end
        return Module.MusicPlayer.Config.MusicPlayerIconSoucre['Play']
    end

end
function MusicPlayerWidgetLogic.IsUnlockedMusic(evtName)
    local MusicDataTable = Facade.TableManager:GetTable("MusicPlayerWidgetData")
    local dataRow = MusicDataTable and MusicDataTable[evtName]
    if not dataRow then
        logerror("[MusicPlayerWidgetLogic.IsUnlockedMusic] dataRow is nil")
        return false
    end
    local isLimited = dataRow.bLimited
    local isUnlocked = false
    local unlockWay = nil
    if isLimited then
        if dataRow.LimitedHeroFashionId then
            local fashionId = dataRow.LimitedHeroFashionId
            local isFashionUnlocked = Server.HeroServer:IsHeroFashionUnlocked(fashionId)
        
            if isFashionUnlocked then
                isUnlocked = true
            else
                local fashionData = HeroHelperTool.GetFashionDataRow(fashionId)
                if not fashionData then
                    logerror("[MusicPlayerWidgetLogic.IsUnlockedMusic] fashionData is nil")
                    unlockWay = "None"
                    return false
                else
                    unlockWay = Module.MusicPlayer.Config.Loc.ConditionPlayWithHeroFashion
                    local fashionName = fashionData.Name
                    unlockWay = StringUtil.Key2StrFormat(unlockWay,{["FashonName"] = fashionName})
                end
                isUnlocked = false
               
            end
        end
    else
        isUnlocked = true
    end
   return isUnlocked , unlockWay
end

function MusicPlayerWidgetLogic.NextMusic()
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    if not musicList or #musicList == 0 then
        logerror("[MusicPlayerWidgetLogic.NextMusic] musicList is nil")
        return
    end
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    local nextIndex = (curIndex % #musicList) + 1
    MusicPlayerWidgetLogic.PauseMusic(MusicPlayerWidgetLogic.GetCurMusicEvtName())

    local whileCount = 0
    while true do
        if  MusicPlayerWidgetLogic.IsUnlockedMusic(musicList[nextIndex].AudioEventName)  then
            break
        end
        nextIndex = (nextIndex % #musicList) + 1
        whileCount = whileCount + 1
        if whileCount > 100 then
            logerror("[MusicPlayerWidgetLogic.NextMusic] whileCount is too large")
            return
        end
    end

    local musicData = MusicPlayerWidgetLogic.GetCurMusicDataByIndex(nextIndex)
    if not musicData then
        logerror("[MusicPlayerWidgetLogic.NextMusic] musicData is nil")
        return
    end

    local musicName = musicData.AudioEventName
    MusicPlayerWidgetLogic.PlayMusic(musicName)
end

function MusicPlayerWidgetLogic.PreMusic()
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    if not musicList or #musicList == 0 then
        logerror("[MusicPlayerWidgetLogic.NextMusic] musicList is nil")
        return
    end
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    local nextIndex = ((#musicList+curIndex-2) % #musicList) + 1
    MusicPlayerWidgetLogic.PauseMusic(MusicPlayerWidgetLogic.GetCurMusicEvtName())
    local whileCount = 0
    while true do
        if  MusicPlayerWidgetLogic.IsUnlockedMusic(musicList[nextIndex].AudioEventName)  then
            break
        end
        nextIndex = ((#musicList+nextIndex-2) % #musicList) + 1
        whileCount = whileCount + 1
        if whileCount > 100 then
            logerror("[MusicPlayerWidgetLogic.PreMusic] whileCount is too large")
            return
        end
    end
   
    local musicData = MusicPlayerWidgetLogic.GetCurMusicDataByIndex(nextIndex)
    if not musicData then
        logerror("[MusicPlayerWidgetLogic.NextMusic] musicData is nil")
        return
    end

    local musicName = musicData.AudioEventName
    MusicPlayerWidgetLogic.PlayMusic(musicName)
end

function MusicPlayerWidgetLogic.RestartMusic(evtName)
    loginfo("[MusicPlayerWidgetLogic.RestartMusic] evtName = " .. evtName)
    local curIndex = MusicPlayerWidgetLogic.FindIndexByMusicName(evtName)
    if not curIndex then
        logerror("[MusicPlayerWidgetLogic.RestartMusic] curIndex is nil")
        return
    end
    MusicPlayerWidgetLogic.UpdateMusicPosRecord(evtName, 0)
    MusicPlayerWidgetLogic.PlayMusic(evtName)
end

function MusicPlayerWidgetLogic.RandomPlayMusic()
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    if not musicList or #musicList == 0 then
        logerror("[MusicPlayerWidgetLogic.RandomPlayMusic] musicList is nil")
        return
    end
    MusicPlayerWidgetLogic.PauseMusic(MusicPlayerWidgetLogic.GetCurMusicEvtName())
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    local randomIndex = math.random(1, #musicList)

    local whileCount = 0
    while true do
        if randomIndex ~= curIndex and MusicPlayerWidgetLogic.IsUnlockedMusic(musicList[randomIndex].AudioEventName)  then
            break
        end
        randomIndex = math.random(1, #musicList)
        whileCount = whileCount + 1
        if whileCount > 100 then
            logerror("[MusicPlayerWidgetLogic.RandomPlayMusic] whileCount is too large")
            return
        end
    end


    local musicData = MusicPlayerWidgetLogic.GetCurMusicDataByIndex(randomIndex)
    if not musicData then
        logerror("[MusicPlayerWidgetLogic.RandomPlayMusic] musicData is nil")
        return
    end

    local musicName = musicData.AudioEventName
    MusicPlayerWidgetLogic.PlayMusic(musicName)
end

function MusicPlayerWidgetLogic.PlayMusic(evtName)
    loginfo("[MusicPlayerWidgetLogic.PlayMusic] evtName = " .. evtName)
    local isDownloaded = MusicPlayerWidgetLogic.IsMusicDownloaded(evtName)
    if not isDownloaded then
        logerror("[MusicPlayerWidgetLogic.PlayMusic] evtName is not downloaded")
        return
    end

    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if not MusicPlayerWidgetInfo then
        logerror("[MusicPlayerWidgetLogic.PlayMusic] MusicPlayerWidgetInfo is nil")
        return
    end
    local index = MusicPlayerWidgetLogic.FindIndexByMusicName(evtName)
    if not index then
        logerror("[MusicPlayerWidgetLogic.PlayMusic] index is nil")
        return
    end
    MusicPlayerWidgetLogic.SetCurMusicIndex(index)
    local curPos = MusicPlayerWidgetLogic.GetMusicRecordPos(evtName)

    local playState = 1

    ---目前临时方案停掉其他大厅音乐，等新增停止播放器音乐，但不打开大厅音乐的Event
    --Facade.SoundManager:StopAllMusicForce(0) 
    Facade.SoundManager:StopMusicByName(curMusicEvtName,0)
    -- local real_isPlaying = Facade.SoundManager:IsMusicPlaying(evtName)
    -- if not real_isPlaying then 
    -- end
    Module.MusicPlayer:SetMuiscMode_2D()
    Facade.SoundManager:PlayLobbyMusicByName(evtName)
    if curPos ~= 0 then
        if curPos >0.99 then
            Module.MusicPlayer:OnMusicPlayerPlayCompleted(evtName)
            return
        end
        Facade.SoundManager:SeekMusicToPercent(evtName,curPos)
    end
    MusicPlayerWidgetInfo.isPlaying = true
    loginfo("[MusicPlayerWidgetLogic.PlayMusic] play music from pos = %s " .. tostring(curPos))
    Module.MusicPlayer:EnableUpdateCurMuiscPercent(true)
    Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange:Invoke(evtName, playState)
end

function MusicPlayerWidgetLogic.SeekMusicByPercent(evtName, percent)
    loginfo("[MusicPlayerWidgetLogic.SeekMusicByPercent] evtName = " .. evtName .. ", percent = " .. percent)
    if not evtName or not percent then
        logerror("[MusicPlayerWidgetLogic.SeekMusicByPercent] evtName or percent is nil")
        return
    end
    local isPlaying = Facade.SoundManager:IsMusicPlaying(evtName)
    if not isPlaying then
        loginfo("[MusicPlayerWidgetLogic.SeekMusicByPercent] evtName is not playing")
        return
    end
    MusicPlayerWidgetLogic.UpdateMusicPosRecord(evtName, percent)
    MusicPlayerWidgetLogic.PlayMusic(evtName)
end

function MusicPlayerWidgetLogic.PauseMusic(evtName,isChangePlayingState)
    loginfo("[MusicPlayerWidgetLogic.PauseMusic] evtName = " .. evtName .. ", isChangePlayingState = " .. tostring(isChangePlayingState) )
    if not evtName then
        logerror("[MusicPlayerWidgetLogic.PauseMusic] evtName is nil")
        return
    end
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    local bIsCurMusic = (evtName == curMusicEvtName)
    local playState = 2
    if bIsCurMusic then
        Module.MusicPlayer:EnableUpdateCurMuiscPercent(false)
    end

    if isChangePlayingState and bIsCurMusic then
        MusicPlayerWidgetInfo.isPlaying = false
    end

    MusicPlayerWidgetLogic.OpenDefaultLobbyBGMAndStopMusics()

    Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange:Invoke(evtName, playState)
end


function MusicPlayerWidgetLogic.SwitchCurMusicPalyState()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if not curMusicEvtName then
        logerror("[MusicPlayerWidgetLogic.SwitchCurMusicPalyState] curMusicEvtName is nil")
        return
    end

    local isPlaying = MusicPlayerWidgetLogic.IsPlayingInMusicList(curMusicEvtName)
    if isPlaying then
        MusicPlayerWidgetLogic.PauseMusic(curMusicEvtName,true)
    else
        MusicPlayerWidgetLogic.PlayMusic(curMusicEvtName)
    end
end

function MusicPlayerWidgetLogic.GetCurMusicEvtName()
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    if not musicList[curIndex] then
        loginfo("[MusicPlayerWidgetLogic.GetCurMusicEvtName] music data is nil , curIndex = " .. curIndex)
        return
    end
    return musicList[curIndex].AudioEventName
end

---打卡音乐播放详情面板
function MusicPlayerWidgetLogic.ShowMusicDetailPanel(fOnCreatedPanel,...)
    Facade.UIManager:AsyncShowUI(UIName2ID.MusicPlayerListPop, fOnCreatedPanel, nil, ...)
end

function MusicPlayerWidgetLogic.SetCurMusicIndex(index)
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    local oldIndex = MusicPlayerWidgetInfo.curIndex
    if not musicList or #musicList == 0 then
        logerror("[MusicPlayerWidgetLogic.SetCurMusicIndex] musicList is nil")
        return
    end
    if not index or index < 1 or index > #musicList then
        logerror("[MusicPlayerWidgetLogic.SetCurMusicIndex] index is invalid")
        return
    end

    if oldIndex ~= index then
        MusicPlayerWidgetLogic.PauseMusic(MusicPlayerWidgetLogic.GetCurMusicEvtName())
        MusicPlayerWidgetLogic.UpdateMusicPosRecord(musicList[index].AudioEventName, 0)
    end
    MusicPlayerWidgetInfo.curIndex = index
end

function MusicPlayerWidgetLogic.GetCurMusicIndex()
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    return MusicPlayerWidgetInfo.curIndex
end

function MusicPlayerWidgetLogic.GetMusicPlayerInfo()
   return Module.MusicPlayer.Field:GetMusicPlayerWidgetInfo()
end

function MusicPlayerWidgetLogic.GetCurMusicData()
    local curIndex = MusicPlayerWidgetLogic.GetCurMusicIndex()
    return MusicPlayerWidgetLogic.GetCurMusicDataByIndex(curIndex)
end

function MusicPlayerWidgetLogic.GetCurMusicDataByIndex(index)
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    if not index or index < 1 or index > #musicList then
        logerror("[MusicPlayerWidgetLogic.GetCurMusicDataByIndex] index is invalid")
        return
    end
    return musicList[index]
end

function MusicPlayerWidgetLogic.UpdateMusicPosRecord(inEvtName, inPos)
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    MusicPlayerWidgetInfo.musicPlayPosRecords[inEvtName] = inPos
    loginfo("[MusicPlayerWidgetLogic.UpdateMusicRecord] inEvtName = " .. inEvtName .. ", inPos = " .. inPos)
end

---@return number
function MusicPlayerWidgetLogic.GetMusicRecordPos(inEvtName) 
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    if not MusicPlayerWidgetInfo then
        logerror("[MusicPlayerWidgetLogic.GetMusicRecordPos] MusicPlayerWidgetInfo is nil")
        return 0
    end
    return MusicPlayerWidgetInfo.musicPlayPosRecords[inEvtName] or 0
end

function MusicPlayerWidgetLogic.FindIndexByMusicName(evtName)
    if not evtName or evtName == "" then
        logerror("[MusicPlayerWidgetLogic.FindIndexByMusicName] inMusicName is invalid")
        return
    end
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    for i, musicData in ipairs(musicList) do
        if musicData.AudioEventName == evtName then
            return i
        end
    end 
end

function MusicPlayerWidgetLogic.StopAllMusic()
    loginfo("[MusicPlayerWidgetLogic.StopAllMusic]")
    local musicList = MusicPlayerWidgetLogic.GetMusicList()
    if not musicList or #musicList == 0 then
        logerror("[MusicPlayerWidgetLogic.StopAllMusic] musicList is nil")
        return
    end
    for i, musicData in ipairs(musicList) do
        if musicData.AudioEventName then
            MusicPlayerWidgetLogic.PauseMusic(musicData.AudioEventName)
        end
    end
end

function MusicPlayerWidgetLogic.SwitchPlayMode(inMode)
    loginfo("[MusicPlayerWidgetLogic.SwitchPlayMode] inMode = " .. inMode)
    if not inMode or inMode < 1 or inMode > 3 then
        logerror("[MusicPlayerWidgetLogic.SwitchPlayMode] inMode is invalid")
        return
    end
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    MusicPlayerWidgetInfo.palyMode = inMode
end

function MusicPlayerWidgetLogic.ClickedControlBtn_Implement(inEvtName)
    if not inEvtName then
        logerror("[MusicPlayerWidgetLogic:ClickedControlBtn_Implement] inEvtName is nil")
        return
    end
    local isDownloading = MusicPlayerWidgetLogic.IsMusicDownloading(inEvtName)
    local isDownLoaded = MusicPlayerWidgetLogic.IsMusicDownloaded(inEvtName)
    if isDownloading  then
        MusicPlayerWidgetLogic.StopDownloadMusic(inEvtName)
        return
    end
    if not isDownLoaded  then
        MusicPlayerWidgetLogic.StartDownloadMusic(inEvtName)
        return
    end

    local isUnlocked = MusicPlayerWidgetLogic.IsUnlockedMusic(inEvtName)
    if isUnlocked then
        local isPlaying = MusicPlayerWidgetLogic.IsPlayingInMusicList(inEvtName)
        if isPlaying then
            MusicPlayerWidgetLogic.PauseMusic(inEvtName,true)
        else
            MusicPlayerWidgetLogic.PlayMusic(inEvtName)
        end
    end
end

function MusicPlayerWidgetLogic.GetPlayMode()
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    return MusicPlayerWidgetInfo.palyMode
end

function MusicPlayerWidgetLogic.IsPlayingInMusicList(musicName)
    local MusicPlayerWidgetInfo = MusicPlayerWidgetLogic.GetMusicPlayerInfo()
    local curMusicEvtName = MusicPlayerWidgetLogic.GetCurMusicEvtName()
    if  curMusicEvtName == musicName and MusicPlayerWidgetInfo.isPlaying then
       return true
    end
    return false
end

function MusicPlayerWidgetLogic.IsMusicDownloaded(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
   local isDownLoaded =  LiteDownloadManager:IsDownloadedByModuleName(musicModuleName)
   return isDownLoaded
end

function MusicPlayerWidgetLogic.IsMusicDownloading(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(musicModuleName)
    return isDownloading
end

function MusicPlayerWidgetLogic.StartDownloadMusic(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    LiteDownloadManager:DownloadByModuleName(musicModuleName)
end

function MusicPlayerWidgetLogic.StopDownloadMusic(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    LiteDownloadManager:CancelByModuleName(musicModuleName)
end

function MusicPlayerWidgetLogic.GetMusicDownloadProgress(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    local progress = LiteDownloadManager:GetDownloadProgressInfoByModuleName(musicModuleName)
    return progress and progress.PercentValue or 0 
end

function MusicPlayerWidgetLogic.GetMusicPackageSize(musicName)
    local musicModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MusicPlayer)
    local packageSize = LiteDownloadManager:GetTotalSizeByModuleName(musicModuleName)
    packageSize = packageSize / 1024 / 1024 -- 转换为MB
    packageSize = math.floor(packageSize * 100) / 100 -- 保留两位小数
    return packageSize
end

function MusicPlayerWidgetLogic.StopDefaultLobbyBGM()
    Facade.SoundManager:PlayLobbyMusicByName("Music_Lobby_Player_Playing") --该event关闭默认大厅音乐
end

function MusicPlayerWidgetLogic.OpenDefaultLobbyBGMAndStopMusics()
    Facade.SoundManager:PlayLobbyMusicByName("Music_Lobby_Player_Stop") --该event打开默认大厅音乐，并且关闭歌单音乐
end

function MusicPlayerWidgetLogic.SetPlayTypeByRTPC(InValue)
    if not InValue or InValue < 0 or InValue > 1 then
        logerror("[MusicPlayerWidgetLogic.SetPlayTypeByRTPC] InValue is invalid")
        return
    end

    loginfo("[MusicPlayerWidgetLogic.SetPlayTypeByRTPC] InValue = " .. InValue)
    local RTPCName = "RTPC_MusicPlayer_PlayType"
    UGPAudioStatics.SetGlobalRTPCByName(RTPCName, InValue ,0)
end

return MusicPlayerWidgetLogic