----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

local InventoryServerSkinCSQueue = require "DFM.Business.ServerCenter.InventoryServerSkinPacker.InventoryServerSkinCSQueue"

local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"

---@class CollectionServer : ServerBase
local CollectionServer = class("CollectionServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local bothInventory = 0
local IrisInventory = 1
local MpInventory = 2
local maxNumAllowed = 99999999

local function log(...)
    print("lzw", "[CollectionServer]", ...)
end

function CollectionServer:Ctor()
    loginfo("CollectionServer:Ctor")
    self.Events = {
        evtFetchCollectionData = LuaEvent:NewIns("CollectionServer.evtFetchCollectionData"),
        evtUpdateCollectionData = LuaEvent:NewIns("CollectionServer.evtUpdateCollectionData"),
        evtCollectionUsePropSucess = LuaEvent:NewIns("CollectionServer.evtCollectionUsePropSucess"),
        evtCollectionOpenBoxSuccess = LuaEvent:NewIns("CollectionServer.evtCollectionOpenBoxSuccess"),
        evtCollectionPropReachMaxNum = LuaEvent:NewIns("CollectionServer.evtCollectionPropReachMaxNum"),
        evtCollectionRedDotUpdate = LuaEvent:NewIns("CollectionServer.evtCollectionRedDotUpdate"),
        evtCollectionHallRedDotUpdate = LuaEvent:NewIns("CollectionServer.evtCollectionHallRedDotUpdate"),
        evtWeaponSkinRenamed = LuaEvent:NewIns("CollectionServer.evtWeaponSkinRenamed"),
        evtNickNameAvailable = LuaEvent:NewIns("CollectionServer.evtNickNameAvailable"),
        evtReNickNameSuccess = LuaEvent:NewIns("CollectionServer.evtReNickNameSuccess"),
        evtReceivedCollectionProps = LuaEvent:NewIns("CollectionServer.evtReceivedCollectionProps"),
        evtPropsChangedByMarket = LuaEvent:NewIns("CollectionServer.evtPropsChangedByMarket"),
        evtCollectionRetrieveWeaponSkin = LuaEvent:NewIns("CollectionServer.evtCollectionRetrieveWeaponSkin"),
        evtCollectionRetrieveAllWeaponSkinsOfPattern = LuaEvent:NewIns("CollectionServer.evtCollectionRetrieveAllWeaponSkinsOfPattern"),
        evtCollectionActivateSkinPattern = LuaEvent:NewIns("CollectionServer.evtCollectionActivateSkinPattern"),
        evtCollectionAutoDistributionReqSucess = LuaEvent:NewIns("CollectionServer.evtCollectionAutoDistributionReqSucess"),
        evtCollectionRandomSkinPoolUpdate = LuaEvent:NewIns("CollectionServer.evtCollectionRandomSkinPoolUpdate"),
        evtCollectionRandomSkinPoolToggled = LuaEvent:NewIns("CollectionServer.evtCollectionRandomSkinPoolToggled"),
        evtFetchPropExpireInfo = LuaEvent:NewIns("CollectionServer.evtFetchPropExpireInfo"),
        evtCollectionUseMissionFileExpCard = LuaEvent:NewIns("CollectionServer.evtCollectionUseMissionFileExpCard"),
        evtCollectionUseMissionFileActivateCard = LuaEvent:NewIns("CollectionServer.evtCollectionUseMissionFileActivateCard"),
        evtFetchCamouflageStatusInfo = LuaEvent:NewIns("CollectionServer.evtFetchCamouflageStatusInfo"),
        evtEquipSafeBoxSkin = LuaEvent:NewIns("CollectionServer.evtEquipSafeBoxSkin"),
    }
    self._collectionItemMap = {}
    self._collectionPropMap = {}
    self._collectionItemMapWithMode = {}
    self._collectionItemMapWithMode[bothInventory] = {}
    self._collectionItemMapWithMode[IrisInventory] = {}
    self._collectionItemMapWithMode[MpInventory] = {}
    self._ownedWeaponSkins = {}  
    self._allWeaponSkins = {}                  
    self._ownedMeleeSkins = {}    
    self._allMeleeSkins = {}    
    self._ownedMandelBricks = {}       
    self._allMandelBricks = {}                   
    self._brandInfo = {}
    self._visibleProps = {}
    self._collectionPropRedDots = {}
    self._collectionTaskRedDots = {}
    self._collectionNewArrivedTaskRedDots = {}
    self._battleConsumeChangeMap = {}
    self._rightsInfoMap = {}
    self._rightsProps = {}
    self._randomPoolSkins = {}
    self._randomSkinPoolsEnabled = {}
    self._propExpireInfoList = {}
    self._lowRentalNextDistributeTs = 0 -- 低级物资券下次自动发放时间戳
    self.suit_infos = {}
    self._newUnlockPendantReddotList = {}
    self._newGatherPendantReddotList = {}
    self._newUnlockPendantAnimaList = {}

end

function CollectionServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self._ownedWeaponSkins = nil
    self._allWeaponSkins = nil  
    self._ownedMeleeSkins = nil  
    self._allMeleeSkins = nil      
    self._ownedMandelBricks = nil     
    self._allMandelBricks = nil        
    self._brandInfo = nil     
    self._visibleProps = nil
    self._collectionPropRedDots = nil
    self._battleConsumeChangeMap = nil
    self._rightsInfoMap = nil
end

function CollectionServer:OnInitServer()
    self:_InitData()
    self:_BindPbListener()
end

function CollectionServer:FetchServerData(bUpdateRightsProp)
    logerror("CollectionServer:FetchServerData")
    self:FetchCollectionData(bUpdateRightsProp)
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
function CollectionServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function CollectionServer:OnLoadingGame2Frontend(gameFlowType)
    self:FetchServerData()
end

function CollectionServer:OnLoadingFrontend2Game(gameFlowType)
    self._collectionItemMap = {}
    self._collectionPropMap = {}
    if self._weaponSkinsCarring then
        for index, skinPropInfo in ipairs(self._weaponSkinsCarring) do
            declare_if_nil(self._collectionPropMap, skinPropInfo.id, {})
            declare_if_nil(self._collectionPropMap[skinPropInfo.id], skinPropInfo.gid, skinPropInfo)
        end
    end
    self._collectionItemMapWithMode = {}
    self._collectionItemMapWithMode[bothInventory] = {}
    self._collectionItemMapWithMode[IrisInventory] = {}
    self._collectionItemMapWithMode[MpInventory] = {}
    self._ownedWeaponSkins = {}   
    self._allWeaponSkins = {}  
    self._ownedPentdants = {}    
    self._allPentdants = {}       
    self._ownedMeleeSkins = {}   
    self._allMeleeSkins = {} 
    self._ownedMandelBricks = {}       
    self._allMandelBricks = {}           
    self._brandInfo = {}
    self._visibleProps = {}
    self._collectionPropRedDots = {}
    self._collectionTaskRedDots = {}
    self._collectionNewArrivedTaskRedDots = {}
    self._battleConsumeChangeMap = {}
    self._rightsInfoMap = {}
    self._rightsProps = {}
    self._randomPoolSkins = {}
    self._randomSkinPoolsEnabled = {}
    self._propExpireInfoList = {}
    self.suit_infos = {}
    self._newUnlockPendantReddotList = {}
    self._newGatherPendantReddotList = {}
    self._newUnlockPendantAnimaList = {}
end

function CollectionServer:_InitData()
end

function CollectionServer:_BindPbListener()
    -- 藏品仓库发生变化时
    Facade.ProtoManager:AddNtfListener("CSCollectionPropChangeNtf", self._OnCSCollectionPropChangeNtf, self)

    Facade.ProtoManager:AddNtfListener("CSCollectionBattleConsumeChangeNtf", self._OnCollectionBattleConsumeChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSCollectionMysticalPendantSuitNtf", self._OnCSCollectionMysticalPendantSuitNtf, self)
    -- 断线重连
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)

    Facade.ProtoManager:AddNtfListener("CSGetNetbarPrivNtf", self._OnFetchAccountProfile,self)
    Facade.ProtoManager:AddNtfListener("CSCollectionRandSkinPoolChangeNtf", self._OnCSCollectionRandSkinPoolChangeNtf, self)
end


function CollectionServer:_OnRelayConnected()
    self:FetchServerData()
end

function CollectionServer:_OnFetchAccountProfile()
    self:FetchServerData(true)
end

--- 拉取所有藏品仓库的道具
function CollectionServer:FetchCollectionData(bUpdateRightsProp)
    local weaponSkinDataTable = WeaponHelperTool.GetWeaponSkinDataTable()
    local meleeSkinDataTable = WeaponHelperTool.GetMeleeWeaponSkinDataTable()
    local pendantDataTable= WeaponHelperTool.GetPendantTable()
    local mandelBrickDataTable = Facade.TableManager:GetTable("MandelBrickDataTable")
    local itemInfo = nil
    local req = pb.CSCollectionLoadPropsReq:New()

    local fCSCollectionLoadMysticalSkinPropsRes
    local fCSCollectionLoadMysticalPendantSkinPropsRes

    local loadMysticalSkins = function(pageIndex)
        local req = pb.CSCollectionLoadMysticalSkinPropsReq:New()
        req.page = pageIndex
        req:Request(fCSCollectionLoadMysticalSkinPropsRes, {bEnableHighFrequency = true})
    end

    local loadMysticalPendantSkins = function(pageIndex)
        local req = pb.CSCollectionLoadMysticalPendantPropsReq:New()
        req.page = pageIndex
        req:Request(fCSCollectionLoadMysticalPendantSkinPropsRes,{bEnableHighFrequency = true})
    end

    fCSCollectionLoadMysticalPendantSkinPropsRes = function(res)
        if res.result ~= 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.PullInvDataFailed, 2)
            self:FilterCollectionItemsWithMode()
            self.Events.evtFetchCollectionData:Invoke()
            Server.InventoryServer:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
            self:FetchCamouflageStatusInfo()
            return
        end
        if res.mystical_pendant_props and #res.mystical_pendant_props > 0 then
            for index, propInfo in ipairs(res.mystical_pendant_props) do
                propInfo.gid = setdefault(propInfo.gid, 0)
                declare_if_nil(self._allPentdants, propInfo.id, {})
                self._allPentdants[propInfo.id][propInfo.gid] = true
                declare_if_nil(self._ownedPentdants, propInfo.id, {})
                self._ownedPentdants[propInfo.id][propInfo.gid] = true
                declare_if_nil(self._collectionPropMap, propInfo.id, {})
                declare_if_nil(self._collectionPropMap[propInfo.id], propInfo.gid, propInfo)
                self:CreateCollectionItem(propInfo)

                local bOpenCollection = WeaponHelperTool.GetPendantOpenCollection(propInfo.id)
                local bDisplayResources = WeaponHelperTool.GetPendantDisplayResources(propInfo.id)
                if bOpenCollection and bDisplayResources then
                    declare_if_nil(self._visibleProps, propInfo.id, true)
                end
            end
        end

        if res.redpoint_list and #res.redpoint_list > 0 then
            for index, propStruct in ipairs(res.redpoint_list) do
                if Server.CollectionServer:IsVisibleProp(propStruct.prop_id) then
                    itemInfo = ItemConfigTool.GetItemConfigById(propStruct.prop_id)
                    if itemInfo and not itemInfo.NoRedPoint then
                        declare_if_nil(self._collectionPropRedDots, propStruct.prop_id, {})
                        propStruct.prop_gid = setdefault(propStruct.prop_gid, 0)
                        declare_if_nil(self._collectionPropRedDots[propStruct.prop_id], propStruct.prop_gid, true)
                    end
                end
            end
        end

        if res.suit_infos then
            self.suit_infos = res.suit_infos
            self:RefWaitActivePendant()
        end

        if res.mystical_pendant_suit_redpoints then
            self.mystical_pendant_suit_redpoints = res.mystical_pendant_suit_redpoints
        end

        if res.cur_page <  res.sum_page-1 then
            loadMysticalPendantSkins(res.cur_page+1)
        else
            self:FilterCollectionItemsWithMode()
            self.Events.evtFetchCollectionData:Invoke()
            Server.InventoryServer:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
            self:FetchCamouflageStatusInfo()
        end
    end
    fCSCollectionLoadMysticalSkinPropsRes = function(res)
        if res.result ~= 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.PullInvDataFailed, 2)
            loadMysticalPendantSkins(0)
            return
        end
        if res.mystical_skin_props and #res.mystical_skin_props > 0 then
            local skinDataRow
            for index, propInfo in ipairs(res.mystical_skin_props) do
                skinDataRow = weaponSkinDataTable[propInfo.id]
                if skinDataRow ~= nil then
                    propInfo.gid = setdefault(propInfo.gid, 0)
                    declare_if_nil(self._allWeaponSkins, propInfo.id, {})
                    self._allWeaponSkins[propInfo.id][0] = nil
                    self._allWeaponSkins[propInfo.id][propInfo.gid] = true
                    declare_if_nil(self._ownedWeaponSkins, propInfo.id, {})
                    self._ownedWeaponSkins[propInfo.id][propInfo.gid] = true
                    declare_if_nil(self._collectionPropMap, propInfo.id, {})
                    declare_if_nil(self._collectionPropMap[propInfo.id], propInfo.gid, propInfo)
                    self:CreateCollectionItem(propInfo)
                    if skinDataRow.OpenCollection == true then
                        declare_if_nil(self._visibleProps, skinDataRow.SkinId, true)
                    end
                end
            end
        end
        if res.redpoint_list and #res.redpoint_list > 0 then
            for index, propStruct in ipairs(res.redpoint_list) do
                if Server.CollectionServer:IsVisibleProp(propStruct.prop_id) then
                    itemInfo = ItemConfigTool.GetItemConfigById(propStruct.prop_id)
                    if itemInfo and not itemInfo.NoRedPoint then
                        declare_if_nil(self._collectionPropRedDots, propStruct.prop_id, {})
                        propStruct.prop_gid = setdefault(propStruct.prop_gid, 0)
                        declare_if_nil(self._collectionPropRedDots[propStruct.prop_id], propStruct.prop_gid, true)
                    end
                end
            end
        end

        if res.cur_page <  res.sum_page-1 then
            loadMysticalSkins(res.cur_page+1)
        else
            loadMysticalPendantSkins(0)
        end
    end


    local fOnCSCollectionLoadPropsRes = function(res)
        local weaponSkinsAll = {}
        local meleeSkinsAll = {}
        local pentdantAll = {}
        local mandelBricksAll = {}
        if res.weapon_skin_props and #res.weapon_skin_props > 0 then
            local skinDataRow
            for index, propInfo in ipairs(res.weapon_skin_props) do
                skinDataRow = weaponSkinDataTable[propInfo.id]
                if skinDataRow ~= nil then
                    propInfo.gid = setdefault(propInfo.gid, 0)
                    declare_if_nil(self._allWeaponSkins, propInfo.id, {})
                    self._allWeaponSkins[propInfo.id][propInfo.gid] = true
                    declare_if_nil(self._ownedWeaponSkins, propInfo.id, {})
                    self._ownedWeaponSkins[propInfo.id][propInfo.gid] = true
                    table.insert(weaponSkinsAll, propInfo)
                    if skinDataRow.OpenCollection == true then
                        declare_if_nil(self._visibleProps, skinDataRow.SkinId, true)
                    end
                else
                    skinDataRow = meleeSkinDataTable[propInfo.id]
                    if skinDataRow ~= nil then
                        propInfo.gid = setdefault(propInfo.gid, 0)
                        declare_if_nil(self._allMeleeSkins, propInfo.id, {})
                        self._allMeleeSkins[propInfo.id][propInfo.gid] = true
                        declare_if_nil(self._ownedMeleeSkins, propInfo.id, {})
                        self._ownedMeleeSkins[propInfo.id][propInfo.gid] = true
                        table.insert(meleeSkinsAll, propInfo)
                        if skinDataRow.OpenCollection == true then
                            declare_if_nil(self._visibleProps, skinDataRow.MeleeSkinID, true)
                        end
                    end
                end
                if propInfo.rights_id and propInfo.rights_id > 0 then
                    declare_if_nil(self._rightsProps, propInfo.id, {})
                    self._rightsProps[propInfo.id][propInfo.gid] = propInfo.rights_id
                end
            end
        end

        if res.weapon_pendant_props and #res.weapon_pendant_props > 0 then
            for index, propInfo in ipairs(res.weapon_pendant_props) do
                propInfo.gid = setdefault(propInfo.gid, 0)
                declare_if_nil(self._allPentdants, propInfo.id, {})
                self._allPentdants[propInfo.id][propInfo.gid] = true
                declare_if_nil(self._ownedPentdants, propInfo.id, {})
                self._ownedPentdants[propInfo.id][propInfo.gid] = true
                table.insert(pentdantAll, propInfo)
                local bOpenCollection = WeaponHelperTool.GetPendantOpenCollection(propInfo.id)
                if bOpenCollection then
                    declare_if_nil(self._visibleProps, propInfo.id, true)
                end
            end
        end

        if res.common_props and #res.common_props > 0 then
            local mandelBrickDataRow
            for _, propInfo in ipairs(res.common_props) do
                if ItemHelperTool.GetMainTypeById(propInfo.id) == EItemType.Gift and ItemHelperTool.GetSubTypeById(propInfo.id) == ECollectableType.LotteryBox then
                    mandelBrickDataRow = mandelBrickDataTable[propInfo.id]
                    if mandelBrickDataRow ~= nil then
                        declare_if_nil(self._allMandelBricks, propInfo.id, {})
                        self._allMandelBricks[propInfo.id][0] = true
                        declare_if_nil(self._ownedMandelBricks, propInfo.id, {})
                        self._ownedMandelBricks[propInfo.id][0] = true
                        table.insert(mandelBricksAll, propInfo)
                        if mandelBrickDataRow.OpenCollection == true then
                            declare_if_nil(self._visibleProps, mandelBrickDataRow.MandelBrickID, true)
                        end
                    end
                end
            end
        end

        for key, skinDataRow in pairs(weaponSkinDataTable) do
            if ItemHelperTool.GetMainTypeById(skinDataRow.SkinId) == EItemType.WeaponSkin then
                if self._allWeaponSkins[skinDataRow.SkinId] == nil then
                    declare_if_nil(self._allWeaponSkins, skinDataRow.SkinId, {})
                    self._allWeaponSkins[skinDataRow.SkinId][0] = true
                    table.insert(weaponSkinsAll, {id=skinDataRow.SkinId, gid=0, num=1})
                    if skinDataRow.OpenCollection == true and skinDataRow.ResourceType == 2 then
                        declare_if_nil(self._visibleProps, skinDataRow.SkinId, true)
                    end
                end
            end
        end

        for key, pentdantDataRow in pairs(pendantDataTable) do
            local pentdantId = pentdantDataRow.PendantId
            if self._allPentdants[pentdantId] == nil then
                declare_if_nil(self._allPentdants, pentdantId, {})
                self._allPentdants[pentdantId][0] = true
                table.insert(pentdantAll, {id=pentdantId, gid=0, num=1})
                local bOpenCollection = pentdantDataRow.OpenCollection
                local bDisplayResources = pentdantDataRow.DisplayResources
                if bOpenCollection and bDisplayResources then
                    declare_if_nil(self._visibleProps, pentdantId, true)
                    -- if bDisplayResources or self._ownedPentdants[pentdantId] then 
                    --     declare_if_nil(self._visibleProps, pentdantId, true)
                    -- end
                end
            end
        end

        for key, skinDataRow in pairs(meleeSkinDataTable) do
            if ItemHelperTool.GetMainTypeById(skinDataRow.MeleeSkinID) == EItemType.WeaponSkin then
                if self._allMeleeSkins[skinDataRow.MeleeSkinID] == nil then
                    declare_if_nil(self._allMeleeSkins, skinDataRow.MeleeSkinID, {})
                    self._allMeleeSkins[skinDataRow.MeleeSkinID][0] = true
                    table.insert(meleeSkinsAll, {id=skinDataRow.MeleeSkinID, gid=0, num=1})
                    if skinDataRow.OpenCollection == true and skinDataRow.ResourceType == 2 then
                        declare_if_nil(self._visibleProps, skinDataRow.MeleeSkinID, true)
                    end
                end
            end
        end  

        for key, mandelBrickDataRow in pairs(mandelBrickDataTable) do
            if ItemHelperTool.GetMainTypeById(mandelBrickDataRow.MandelBrickID) == EItemType.Gift and ItemHelperTool.GetSubTypeById(mandelBrickDataRow.MandelBrickID) == ECollectableType.LotteryBox then
                if self._allMandelBricks[mandelBrickDataRow.MandelBrickID] == nil then
                    declare_if_nil(self._allMandelBricks, mandelBrickDataRow.MandelBrickID, {})
                    self._allMandelBricks[mandelBrickDataRow.MandelBrickID][0] = true
                    table.insert(mandelBricksAll, {id=mandelBrickDataRow.MandelBrickID, gid=0, num=0})
                    if mandelBrickDataRow.OpenCollection == true then
                        declare_if_nil(self._visibleProps, mandelBrickDataRow.MandelBrickID, true)
                    end
                end
            end
        end

        if res.common_props and #res.common_props > 0 then
            for _, prop in ipairs(res.common_props) do
                if ItemHelperTool.GetMainTypeById(prop.id) ~= EItemType.Gift or ItemHelperTool.GetSubTypeById(prop.id) ~= ECollectableType.LotteryBox then
                    prop.gid = setdefault(prop.gid, 0)
                    declare_if_nil(self._visibleProps,prop.id,true)
                    declare_if_nil(self._collectionPropMap, prop.id, {})
                    declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                    self:CreateCollectionItem(prop)
                end
            end
        end

        if weaponSkinsAll and #weaponSkinsAll > 0 then
            for _, prop in ipairs(weaponSkinsAll) do
                prop.gid = setdefault(prop.gid, 0)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end

        if meleeSkinsAll and #meleeSkinsAll > 0 then
            for _, prop in ipairs(meleeSkinsAll) do
                prop.gid = setdefault(prop.gid, 0)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end

        if mandelBricksAll and #mandelBricksAll > 0 then
            for _, prop in ipairs(mandelBricksAll) do
                prop.gid = setdefault(prop.gid, 0)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end

        -- 载具皮肤进行拆分
        if res.carrier_skin_props and #res.carrier_skin_props > 0 then
            for _, prop in ipairs(res.carrier_skin_props) do
                prop.gid = setdefault(prop.gid, 0)
                declare_if_nil(self._visibleProps,prop.id,true)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end

        -- 武器挂饰进行拆分
        if pentdantAll and #pentdantAll > 0 then
            for _, prop in ipairs(pentdantAll) do
                prop.gid = setdefault(prop.gid, 0)
                -- declare_if_nil(self._visibleProps,prop.id,true)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end

        if res.redpoint_list and #res.redpoint_list > 0 then
            for index, propStruct in ipairs(res.redpoint_list) do
                if propStruct.prop_id > 0 and Server.CollectionServer:IsVisibleProp(propStruct.prop_id) then
                    itemInfo = ItemConfigTool.GetItemConfigById(propStruct.prop_id)
                    if itemInfo and not itemInfo.NoRedPoint then
                        declare_if_nil(self._collectionPropRedDots, propStruct.prop_id, {})
                        propStruct.prop_gid = setdefault(propStruct.prop_gid, 0)
                        declare_if_nil(self._collectionPropRedDots[propStruct.prop_id], propStruct.prop_gid, true)
                    end
                    if propStruct.gun_skin_pattern_id > 0 then
                        declare_if_nil(self._collectionNewArrivedTaskRedDots, propStruct.gun_skin_pattern_id, true)
                    end
                end
            end
        end

        self:ProcessBattleConsume(res.battle_consume)
        self:ProcessRightsInfo(res.rights_info)
        self:ProcessRandSkinPool(res.rand_weapon_skin_list)
        self:ProcessPropExpireInfo(res.prop_expire_record)
        self:ProcessSafeBoxSkinInfo(res.safebox_skin_unlocked_list)
        self:ProcessSafeBoxEquiped(res.safebox_skin_equiped)


        if bUpdateRightsProp == true then
            self:FilterCollectionItemsWithMode()
            self.Events.evtFetchCollectionData:Invoke()
            Server.InventoryServer:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
            self:FetchCamouflageStatusInfo()
        else
            loadMysticalSkins(0)
        end
    end

    Server.InventoryServer:ProcessSkinDependsCSReq(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
    if bUpdateRightsProp ~= true then
        self._collectionPropMap = {}
        self._collectionItemMap = {}
        self._ownedWeaponSkins = {}
        self._allWeaponSkins = {}
        self._ownedMeleeSkins = {}
        self._allMeleeSkins = {}
        self._ownedMandelBricks = {}       
        self._allMandelBricks = {} 
        self._ownedPentdants = {}
        self._allPentdants = {}
        self._brandInfo = {}
        self._visibleProps = {}
        self._collectionPropRedDots = {}
        self._collectionTaskRedDots = {}
        self._collectionNewArrivedTaskRedDots = {}
        self._battleConsumeChangeMap = {}
        self._rightsInfoMap = {}
        self._rightsProps = {}
        self._randomPoolSkins = {}
        self._randomSkinPoolsEnabled = {}
        self._propExpireInfoList = {}
        self.suit_infos = {}
        self._newUnlockPendantReddotList = {}
        self._newGatherPendantReddotList = {}
        self._newUnlockPendantAnimaList = {}
    end
    req:Request(fOnCSCollectionLoadPropsRes)

    self:CSCollectionAutoDistributionReq(true)
end

--- 拆分藏品道具
function CollectionServer:CreateCollectionItem(prop)
    self._collectionItemMap = setdefault(self._collectionItemMap, {})
    prop.gid = setdefault(prop.gid, 0)
    if prop ~= nil then
        local itemInfo = ItemConfigTool.GetItemConfigById(prop.id)
        if itemInfo then
            local newItem = ItemHelperTool.CreateItemByPropInfo(prop)
            if newItem then
                newItem:SetCollectionInfo(itemInfo)
                declare_if_nil(self._collectionItemMap, prop.id, {})
                self._collectionItemMap[prop.id][prop.gid] = newItem
                self:_SplitCollectionSpecialItem(newItem)
            end
        else
            if self._ownedMeleeSkins[prop.id] and self._ownedMeleeSkins[prop.id][prop.gid] ~= nil then
                self._ownedMeleeSkins[prop.id][prop.gid] = nil
            elseif self._ownedWeaponSkins[prop.id] and self._ownedWeaponSkins[prop.id][prop.gid] ~= nil then
                self._ownedWeaponSkins[prop.id][prop.gid] = nil
            elseif self._ownedPentdants[prop.id] and self._ownedPentdants[prop.id][prop.gid] ~= nil then
                self._ownedPentdants[prop.id][prop.gid] = nil
            elseif self._ownedMandelBricks[prop.id] and self._ownedMandelBricks[prop.id][prop.gid] ~= nil then
                self._ownedMandelBricks[prop.id][prop.gid] = nil
            end
            if self._allMeleeSkins[prop.id] and self._allMeleeSkins[prop.id][prop.gid] ~= nil then
                self._allMeleeSkins[prop.id][prop.gid] = nil
            elseif self._allWeaponSkins[prop.id] and self._allWeaponSkins[prop.id][prop.gid] ~= nil then
                self._allWeaponSkins[prop.id][prop.gid] = nil
            elseif self._allPentdants[prop.id] and self._allPentdants[prop.id][prop.gid] ~= nil then
                self._allPentdants[prop.id][prop.gid] = nil
            elseif self._allMandelBricks[prop.id] and self._allMandelBricks[prop.id][prop.gid] ~= nil then
                self._allMandelBricks[prop.id][prop.gid] = nil
            end
        end
    end
end

function CollectionServer:_SplitCollectionSpecialItem(item)
    if item then
        if item.itemMainType == EItemType.WeaponSkin then
            if item.itemSubType == ItemConfig.EWeaponItemType.Melee then
                if self._ownedMeleeSkins[item.id] and self._ownedMeleeSkins[item.id][item.gid] then
                    self._ownedMeleeSkins[item.id][item.gid] = item
                end
                if self._allMeleeSkins[item.id] and self._allMeleeSkins[item.id][item.gid] then
                    self._allMeleeSkins[item.id][item.gid] = item
                end 
            else
                if self._ownedWeaponSkins[item.id] and self._ownedWeaponSkins[item.id][item.gid] then
                    self._ownedWeaponSkins[item.id][item.gid] = item
                end
                if self._allWeaponSkins[item.id] and self._allWeaponSkins[item.id][item.gid] then
                    self._allWeaponSkins[item.id][item.gid] = item
                end 
            end
        elseif item.itemMainType == EItemType.Adapter and item.itemSubType == ItemConfig.EAdapterItemType.Pendant then
            if self._ownedPentdants[item.id] and self._ownedPentdants[item.id][item.gid] then
                self._ownedPentdants[item.id][item.gid] = item
            end
            if self._allPentdants[item.id] and self._allPentdants[item.id][item.gid] then
                self._allPentdants[item.id][item.gid] = item
            end 
        elseif item.itemMainType == EItemType.Gift and item.itemSubType == ECollectableType.LotteryBox then
            if self._ownedMandelBricks[item.id] and self._ownedMandelBricks[item.id][item.gid] then
                self._ownedMandelBricks[item.id][item.gid] = item
            end
            if self._allMandelBricks[item.id] and self._allMandelBricks[item.id][item.gid] then
                self._allMandelBricks[item.id][item.gid] = item
            end
        end
    end
end

function CollectionServer:RemoveCollectionItem(prop)
    self._collectionItemMap = setdefault(self._collectionItemMap, {})
    prop.gid = setdefault(prop.gid, 0)
    if self._collectionItemMap[prop.id] then
        self._collectionItemMap[prop.id][prop.gid] = nil
    end
    self:_RemoveOwnedSpecialItem(prop.id, prop.gid)
end

function CollectionServer:_RemoveOwnedSpecialItem(id, gid)
    if id == nil then
        return
    end
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    if self._ownedWeaponSkins[id] then
        self._ownedWeaponSkins[id][gid] = nil
    end
    if self._ownedMeleeSkins[id] then
        self._ownedMeleeSkins[id][gid] = nil
    end
    if self._allWeaponSkins[id] then
        self._allWeaponSkins[id][gid] = nil
    end
    if self._ownedPentdants[id] then
        self._ownedPentdants[id][gid] = nil
    end
    if gid > 0 and self._allPentdants[id] then
        self._allPentdants[id][gid] = nil
    end
    if self._ownedMandelBricks[id] then
        self._ownedMandelBricks[id][gid] = nil
    end
    if self._allMandelBricks[id] and self._allMandelBricks[id][gid] then
        self._allMandelBricks[id][gid].num = 0
    end
end
--- 筛选藏品道具
function CollectionServer:FilterCollectionItemsWithMode()
    -- 分类
    self._collectionItemMapWithMode = {}
    self._collectionItemMapWithMode[bothInventory] = {}
    self._collectionItemMapWithMode[IrisInventory] = {}
    self._collectionItemMapWithMode[MpInventory] = {}
    for id, collection in pairs(self._collectionItemMap) do
        local bNeedIgnoreMysticalSkinBase = table.nums(collection) > 1
        for gid, item in pairs(collection) do
            if bNeedIgnoreMysticalSkinBase == false or bNeedIgnoreMysticalSkinBase == true and gid ~= 0 then
                self:_AddItemToFilterMap(item)
            end
        end
    end
end



function CollectionServer:_AddItemToFilterMap(item) 
    declare_if_nil(self._collectionItemMapWithMode, item.inventory, {})
    -- 二级：所在一级目录
    declare_if_nil(self._collectionItemMapWithMode[item.inventory], item.itemMainType, {})
    -- 三级：所在二级目录
    declare_if_nil(self._collectionItemMapWithMode[item.inventory][item.itemMainType], item.itemSubType, {})
    table.insert(self._collectionItemMapWithMode[item.inventory][item.itemMainType][item.itemSubType], item)
end

--- 通过道具id获得藏品道具
function CollectionServer:GetCollectionItemById(id, gid, bCheckExpired)
    if not id then
        logerror("CollectionServer:GetCollectionItemsById:propId is nil !!!")
        return nil
    end
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    local collectionGroup = (self._collectionItemMap and self._collectionItemMap[id]) and self._collectionItemMap[id] or {}
    local item = collectionGroup[gid]
    if item and bCheckExpired then
        local rawExpireInfo = self:GetPropExpireInfo(item.id)
        if rawExpireInfo and #rawExpireInfo > 0 then
            local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
            local bPropExpired = true
            for index, info in ipairs(rawExpireInfo) do
                if info.expireTime > currentTimestamp then
                    bPropExpired = false
                    break
                end
            end
            if bPropExpired == true then
                item = nil
            end
        end
    end
    return item
end

--- 通过道具id获得藏品道具prop(未拆分)
function CollectionServer:GetCollectionPropById(id, gid, bCheckExpired)
    if not id then
        logerror("CollectionServer:GetCollectionPropById:propId is nil !!!")
        return nil
    end
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    local collectionGroup = (self._collectionPropMap and self._collectionPropMap[id]) and self._collectionPropMap[id] or {}
    local prop = collectionGroup[gid]
    if prop and bCheckExpired then
        local rawExpireInfo = self:GetPropExpireInfo(prop.id)
        if rawExpireInfo and #rawExpireInfo > 0 then
            local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
            local bPropExpired = true
            for index, info in ipairs(rawExpireInfo) do
                if info.expireTime > currentTimestamp then
                    bPropExpired = false
                    break
                end
            end
            if bPropExpired == true then
                prop = nil
            end
        end
    end
    return prop
end

--- 通过道具GameItemType获得藏品道具list
function CollectionServer:GetCollectionItemsByGameItemType(gameItemType)
    if not gameItemType then
        logerror("CollectionServer:GetCollectionItemsByGameItemType:propId is nil !!!")
        return {}
    end
    local collectionItems = {}
    for id, collection in pairs(self._collectionItemMap) do
        local itemInfo = ItemConfigTool.GetItemConfigById(id)
        if itemInfo and itemInfo.GameItemType == gameItemType and collection ~= nil then
            for gid, item in pairs(collection) do
                table.insert(collectionItems, item)
            end
        end
    end
    return collectionItems
end

--- 通过道具主类型附类型获得藏品道具list
function CollectionServer:GetCollectionItemsByMainTypeOrSubType(mainType, subType)
    if self._collectionItemMapWithMode then
        local collectionItems = {}
        if mainType ~= nil then
            for inventoryType, inventoryCollection in pairs(self._collectionItemMapWithMode) do
                if inventoryCollection and inventoryCollection[mainType] then
                    if subType ~= nil then
                        if inventoryCollection[mainType][subType] then
                            table.append(collectionItems, inventoryCollection[mainType][subType])  
                        end
                    else
                        for subType, subTypeCollection in pairs(inventoryCollection[mainType]) do
                            table.append(collectionItems, subTypeCollection)     
                        end 
                    end
                end 
            end
        elseif subType ~= nil then
            for inventoryType, inventoryCollection in pairs(self._collectionItemMapWithMode) do
                for mainType, mainTypeCollection in pairs(inventoryCollection) do
                    if mainTypeCollection[subType] then
                        table.append(collectionItems, mainTypeCollection[subType])  
                    end
                end 
            end 
        end
        -- 每次获取物资券数据时都检查下次获取物资券的信息
        if ((mainType and mainType == EItemType.CollectionProp) and (subType and subType == ECollectableType.RentalVoucher)) then
            self:CSCollectionAutoDistributionReq()
        end
        local validCollectionItems = {}
        for index, item in ipairs(collectionItems) do
            local rawExpireInfo = Server.CollectionServer:GetPropExpireInfo(item.id)
            if rawExpireInfo and #rawExpireInfo > 0 then
                local currentTimestamp = Facade.ClockManager:GetServerTimestamp()
                local bPropExpired = true
                for index, info in ipairs(rawExpireInfo) do
                    if info.expireTime > currentTimestamp then
                        bPropExpired = false
                        break
                    end
                end
                if bPropExpired == true then
                    self:RemoveCollectionItem(item:GetRawPropInfo())
                else
                    table.insert(validCollectionItems, item)     
                end
            else
                table.insert(validCollectionItems, item)   
            end
        end
        if #collectionItems > #validCollectionItems then
            self:FilterCollectionItemsWithMode()
        end
        return validCollectionItems
    end
    return {}   
end

--- 通过藏品id获取当前此藏品数量
function CollectionServer:GetCollectionItemsNumById(id)
    if not id then
        return 0
    end
    local num = 0
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    local itemSubType = ItemHelperTool.GetSubTypeById(id)
    local collectionItems = Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(itemMainType, itemSubType)
    table.walk(collectionItems, 
        function (v, k)
            if id == v.id then
                num = num + v.num
            end
        end)
    return num
end


--- 筛选藏品道具
function CollectionServer:GetCollectionItemsMapWithMode()
    return self._collectionItemMapWithMode
end

--获取所有已拥有的蓝图皮肤
---@return table<number, ItemBase>
function CollectionServer:GetOwnedWeaponSkins()
    return self._ownedWeaponSkins
end

function CollectionServer:GetOwnedWeaponSkinNum(bFilterMysticalSkin)
    local counter = 0
    for skinId, group in pairs(self._ownedWeaponSkins) do
        if self:IsVisibleProp(skinId) and (not bFilterMysticalSkin or bFilterMysticalSkin and ItemHelperTool.IsMysticalSkin(skinId)) then
            counter = counter + table.nums(group)
        end
    end
    return counter
end

--获取某个已拥有的蓝图皮肤
---@param id number
---@return ItemBase
function CollectionServer:GetWeaponSkinIfOwned(id, gid)
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    return self._ownedWeaponSkins[id] ~= nil and self._ownedWeaponSkins[id][gid] or nil
end

function CollectionServer:GetContainedWeaponSkinFromOwned(id)
    id = setdefault(id, 0)
    local contained = not table.isempty(self._ownedWeaponSkins[id])
    return contained
end

function CollectionServer:GetWeaponMysticalSkinInfosFromSkinID(id)
    local bMysticalSkin = ItemHelperTool.IsMysticalSkin(id)
    if not bMysticalSkin then
        return nil
    end
    local map = self._ownedWeaponSkins[id]
    return map
end

function CollectionServer:GetWeaponMysticalPendantInfosFromPendantID(id)
    local bMysticalPendant = ItemHelperTool.IsMysticalPendant(id)
    if not bMysticalPendant then
        return nil
    end
    local map = self._ownedPentdants[id]
    return map
end

function CollectionServer:GetWeaponSkinInstanceNum(id)
    id = tonumber(id)
    return self._ownedWeaponSkins[id] ~= nil and table.nums(self._ownedWeaponSkins[id]) or 0
end

--某个蓝图皮肤是否已拥有
---@param id number
---@return boolean s
function CollectionServer:IsOwnedWeaponSkin(id, gid)
    local id = tonumber(id)
    if gid then
        gid = tonumber(gid)
        return not table.isempty(self._ownedWeaponSkins[id]) and self._ownedWeaponSkins[id][gid] ~= nil
    else
        return not table.isempty(self._ownedWeaponSkins[id])
    end
end


--获取所有蓝图皮肤
function CollectionServer:GetAllWeaponSkins()
    return self._allWeaponSkins
end


--获取所有蓝图皮肤中的某个皮肤
function CollectionServer:GetWeaponSkinIfExists(id, gid)
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    return not table.isempty(self._allWeaponSkins[id]) and self._allWeaponSkins[id][gid] or nil
end

--获取所有已拥有的近战武器皮肤
---@return table<number, ItemBase>
function CollectionServer:GetOwnedMeleeSkins()
    return self._ownedMeleeSkins
end

function CollectionServer:GetOwnedMeleeSkinNum()
    local counter = 0
    for skinId, group in pairs(self._ownedMeleeSkins) do
        if self:IsVisibleProp(skinId) then
            counter = counter + table.nums(group)
        end
    end
    return counter
end

--获得某个已拥有的近战武器皮肤
---@param id number
---@return ItemBase
function CollectionServer:GetMeleeSkinIfOwned(id, gid)
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    return not table.isempty(self._ownedMeleeSkins[id]) and self._ownedMeleeSkins[id][gid] or nil
end

--是否拥有某个近战武器皮肤
---@param id number
---@return boolean
function CollectionServer:IsOwnedMeleeSkin(id, gid)
    local id = tonumber(id)
    if gid then
        local gid = tonumber(setdefault(gid, 0))
        return not table.isempty(self._ownedMeleeSkins[id]) and self._ownedMeleeSkins[id][gid] ~= nil
    else
        return not table.isempty(self._ownedMeleeSkins[id])
    end
end

--获得所有近战武器皮肤
function CollectionServer:GetAllMeleeSkins()
    return self._allMeleeSkins
end


--获取所有近战武器皮肤中的某个皮肤
function CollectionServer:GetMeleeSkinIfExists(id, gid)
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    return not table.isempty(self._allMeleeSkins[id]) and self._allMeleeSkins[id][gid] or nil
end

--判断某个道具是否设为可见
function CollectionServer:IsVisibleProp(id)
    local id = tonumber(id)
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    local itemSubType = ItemHelperTool.GetSubTypeById(id)
    if itemMainType == EItemType.WeaponSkin then
        local skinDataRow
        if itemSubType == ItemConfig.EWeaponItemType.Melee then
            if self:IsOwnedMeleeSkin(id) then
                return self._visibleProps[id] == true
            end
            skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(id))
        else
            if self:IsOwnedWeaponSkin(id) then
                return self._visibleProps[id] == true
            end
            skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(id))
        end
        if skinDataRow and skinDataRow.ResourceType == 3 then
            for index, startTimeString in ipairs(skinDataRow.StartTime) do
                if startTimeString ~= "" 
                and skinDataRow.EndTime 
                and skinDataRow.EndTime[index] 
                and skinDataRow.EndTime[index] ~= "" 
                and self:TimeStringToTimeStamp(startTimeString) < Facade.ClockManager:GetServerTimestamp()
                and Facade.ClockManager:GetServerTimestamp() < self:TimeStringToTimeStamp(skinDataRow.EndTime[index])
                then
                    self._visibleProps[id] = true
                    break
                end
            end
        end
    end
    return self._visibleProps[id] == true
end

function CollectionServer:IsRightsProp(id, gid)
    id = tonumber(id)
    gid = tonumber(setdefault(gid, 0))
    return not table.isempty(self._rightsProps[id]) and self._rightsProps[id][gid] ~= nil
end

function CollectionServer:GetRightsType(id, gid)
    id = tonumber(id)
    gid = tonumber(setdefault(gid, 0))
    if not table.isempty(self._rightsProps[id]) then
        return self._rightsProps[id][gid]
    end
    return ERightsType.None
end

function CollectionServer:IsPropWithRedDot(id, gid)
    local id = tonumber(id)
    local gid = tonumber(setdefault(gid, 0))
    if gid == nil or gid == 0 then
        return self._collectionPropRedDots[id] ~= nil and table.nums(self._collectionPropRedDots[id]) > 0
    else
        return self._collectionPropRedDots[id] ~= nil and self._collectionPropRedDots[id][gid] == true
    end
end

function CollectionServer:IsTaskWithRedDot(taskId, bNewTask)
    local taskId = tonumber(taskId)
    if bNewTask then
        return self._collectionNewArrivedTaskRedDots[taskId]
    else
        return self._collectionNewArrivedTaskRedDots[taskId] ~= nil or self._collectionTaskRedDots[taskId] ~= nil
    end
end

function CollectionServer:GetPropRedDots()
    return self._collectionPropRedDots or {}
end

function CollectionServer:GetTaskRedDots()
    return self._collectionTaskRedDots or {}
end

function CollectionServer:GetNewArrivedTaskRedDots()
    return self._collectionNewArrivedTaskRedDots or {}
end

function CollectionServer:GetBattleConsumeChangeByFunctionId(id)
    local id = tonumber(id)
    return self._battleConsumeChangeMap[id]
end

function CollectionServer:GetRightsInfoByType(type)
    if self._rightsInfoMap and self._rightsInfoMap[type] then
        return self._rightsInfoMap[type]
    end
end

--获取所有挂饰
function CollectionServer:GetAllHangings()
    return self._allPentdants
end

--获取拥有的挂饰
function CollectionServer:GetOwnedHangings()
    return self._ownedPentdants
end

--是否拥有某个挂饰
---@param id number
---@return boolean
function CollectionServer:IsOwnedHanging(id)
    local id = tonumber(id)
    return not table.isempty(self._ownedPentdants[id])
end

--获得某个已拥有的挂饰
---@param id number
---@return ItemBase
function CollectionServer:GetHangingIfOwned(id, gid)
    id = tonumber(id)
    gid = tonumber(setdefault(gid, 0))
    return not table.isempty(self._ownedPentdants[id]) and self._ownedPentdants[id][gid] or nil
end

function CollectionServer:GetHangingInstanceNum(id)
    id = tonumber(id)
    return self._ownedPentdants[id] ~= nil and table.nums(self._ownedPentdants[id]) or 0
end


--获取所有曼德尔砖
function CollectionServer:GetAllMandelBricks()
    return self._allMandelBricks
end

--获取拥有的曼德尔砖
function CollectionServer:GetOwnedMandelBricks()
    return self._ownedMandelBricks
end

--是否拥有某个曼德尔砖
---@param id number
---@return boolean
function CollectionServer:IsOwnedMandelBrick(id)
    local id = tonumber(id)
    return not table.isempty(self._ownedMandelBricks[id])
end

--获得某个已拥有的曼德尔砖
---@param id number
---@return ItemBase
function CollectionServer:GetMandelBrickIfOwned(id)
    id = tonumber(id)
    return not table.isempty(self._ownedMandelBricks[id]) and self._ownedMandelBricks[id][0] or nil
end


function CollectionServer:TimeStringToTimeStamp(timeString)
    local year, month, day, hour, minute, second = string.match(timeString, "(%d+)-(%d+)-(%d+) (%d+):(%d+):(%d+)")
    local timeStamp = os.time({year = year, month = month, day = day, hour = hour, min = minute, sec = second})
    return timeStamp
end


--- 使用道具
---@param items ItemBase[]
---@param nums number[]
---@param targetIds number[]
---@param targetGids number[]
---@param fCustomCallback function
function CollectionServer:DoUseMultiItems(items, nums, targetIds, targetGids, fCustomCallback)
    ensure(#items == #nums, "CollectionServer:DoUseMultiItems 道具和数量不匹配。")
    if #items == 0 then
        return false
    end
    loginfo("CollectionServer : DoUseMultiItems req")
    local fOnCSCollectionUsePropRes = function(res)
        ---@field public result number
        ---@field public data_change pb_CollectionPropChange[]
        ---@field public related_change pb_DataChange
        if res.result == 0 then
            self:ProcessRightsInfoChange(res.right_change)
            self:ProcessBattleConsumeChange(res.battle_consume_change)
            self:ProcessPbDataChange(res.data_change)

            loginfo("CollectionServer : evtCollectionUsePropSucess Invoke res.result", res.result)
            self.Events.evtCollectionUsePropSucess:Invoke(res, items)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end

    ---@field public cmds pb_UsePropCmd[]
    local req = pb.CSCollectionUsePropReq:New()
    req.cmds = {}
    for i, item in ipairs(items) do
        local cmd = pb.UsePropCmd:New()
        cmd.prop_id = item.id
        cmd.prop_gid = item.gid
        cmd.num = nums[i]
        cmd.target_id = targetIds[i]    -- could be nil
        cmd.target_gid = targetGids[i]  -- could be nil
        table.insert(req.cmds, cmd)
    end
    req:Request(fOnCSCollectionUsePropRes)

    return true
end

function CollectionServer:DoUseMissionFileExpCard(item, num, fCustomCallback)
    if item == nil then
        return false
    end
    num = setdefault(num, 1)
    local fOnCSBattlePassUseExprCardRes = function(res)
        if res.result == 0 then
            Server.BattlePassServer:SaveBPServerInfo(res.info)
            self.Events.evtCollectionUseMissionFileExpCard:Invoke(res)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSBattlePassUseExprCardReq:New()
    req.card_id = item.id
    req.num = num
    req:Request(fOnCSBattlePassUseExprCardRes)
    return true
end


function CollectionServer:DoUseMissionFileActivateCard(item)
    if item == nil then
        return false
    end
    local fOnCSBattlePassUseUnlockCardRes = function(res)
        if res.result == 0 then
            self.Events.evtCollectionUseMissionFileActivateCard:Invoke(res)
        end
    end
    local req = pb.CSBattlePassUseUnlockCardReq:New()
    req.card_id = item.id
    req:Request(fOnCSBattlePassUseUnlockCardRes)
    return true
end


function CollectionServer:UpdateRandomSkinPool(weaponId, skinIds, skinGids, bAppend, bUpdateFullList,fCustomCallback)
    if skinIds == nil then
        return
    end
    local fOnCSCollectionUpdateRandSkinPoolRes = function(res)
        if res.result == 0 then
            self:ProcessRandSkinPool(res.rand_weapon_skin_list)
            self.Events.evtCollectionRandomSkinPoolUpdate:Invoke()
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionUpdateRandSkinPoolReq:New()
    if bUpdateFullList then
        req.weapon_id = weaponId
        req.new_skin_pool_ids = {}
        for index, skinId in ipairs(skinIds) do
            if skinGids[index] == 0 then
                table.insert(req.new_skin_pool_ids, skinId)
            end
        end
        for index, skinGid in ipairs(skinGids) do
            if skinGid ~= 0 then
                table.insert(req.new_skin_pool_ids, skinGid)
            end
        end
    else
        req.skin_id = skinIds[1]
        req.skin_gid = skinGids[1]
        req.is_append = bAppend or false
    end
    req:Request(fOnCSCollectionUpdateRandSkinPoolRes, {bEnableHighFrequency = true})
    return true
end

function CollectionServer:EnableRandomSkin(weaponId, bEnable, fCustomCallback)
    if weaponId == nil then
        return
    end
    local fOnCSCollectionRandSkinSwitchRes = function(res)
        if res.result == 0 then
            self:ProcessRandSkinPool(res.rand_weapon_skin_list)
            self.Events.evtCollectionRandomSkinPoolToggled:Invoke()
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionRandSkinSwitchReq:New()
    req.weapon_id = weaponId
    req.is_open = bEnable or false
    req:Request(fOnCSCollectionRandSkinSwitchRes, {bEnableHighFrequency = true})
    return true
end

function CollectionServer:_OnCSCollectionRandSkinPoolChangeNtf(ntf)
    self:ProcessRandSkinPool(ntf.rand_weapon_skin_list)
    self.Events.evtCollectionRandomSkinPoolUpdate:Invoke()
end


function CollectionServer:RenameMysticalSkin(skinId, skinGid, newName, currencyType, price, secondaryCurrencyType, secondaryPrice, fCustomCallback)
    if skinId == nil or skinGid == nil or newName == nil then
        return
    end
    local fOnCSCollectionMysticalSkinRenameRes = function(res)
        ---@field public result number
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionMysticalSkinRenameReq:New()
    req.prop_id = skinId
    req.prop_gid = skinGid
    req.skin_name = newName
    req.currency_type = currencyType
    req.price = price
    req.currency_type_substitute = secondaryCurrencyType
    req.price_substitute = secondaryPrice
    req:Request(fOnCSCollectionMysticalSkinRenameRes)
    return true
end


function CollectionServer:RenamePendant(item, newName, fCustomCallback)
    if item == nil or newName == nil then
        return
    end
    local fOnCSCollectionMysticalSkinRenameRes = function(res)
        ---@field public result number
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionMysticalPendantRenameReq:New()
    req.prop_id = item.id
    req.prop_gid = item.gid
    req.pendant_name = newName
    req:Request(fOnCSCollectionMysticalSkinRenameRes)
    return true
end


function CollectionServer:RecombineMysticalSkins(items, bIsBatchProcess, fCustomCallback)
    if items == nil or #items == 0 then
        return
    end
    local fOnCSCollectionMysticalSkinCombineRes = function(res)
        ---@field public result number
        if res.result == 0 then
            self:ProcessPbDataChange(res.data_change)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionMysticalSkinCombineReq:New()
    req.cmds = {}
    req.batch = bIsBatchProcess or false
    for i, item in ipairs(items) do
        local cmd = pb.UsePropCmd:New()
        cmd.prop_id = item.id
        cmd.prop_gid = item.gid
        cmd.num = 1
        cmd.target_id = nil    -- could be nil
        cmd.target_gid = nil  -- could be nil
        table.insert(req.cmds, cmd)
    end
    req:Request(fOnCSCollectionMysticalSkinCombineRes)
    return true
end

function CollectionServer:ExchangeMysticalPendants(items, bIsBatchProcess, fCustomCallback)
    if items == nil or #items == 0 then
        return
    end
    local fOnCSCollectionMysticalPendantCombineRes = function(res)
        ---@field public result number
        if res.result == 0 then
            self:ProcessPbDataChange(res.data_change)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    local req = pb.CSCollectionMysticalPendantCombineReq:New()
    req.cmds = {}
    req.batch = bIsBatchProcess or false
    for i, item in ipairs(items) do
        local cmd = pb.UsePropCmd:New()
        cmd.prop_id = item.id
        cmd.prop_gid = item.gid
        cmd.num = 1
        cmd.target_id = nil    -- could be nil
        cmd.target_gid = nil  -- could be nil
        table.insert(req.cmds, cmd)
    end
    req:Request(fOnCSCollectionMysticalPendantCombineRes)
    return true
end


function CollectionServer:LoadWeaponSkinProgress(skinId, fCustomCallback)
    if skinId == nil then
        return
    end
    local fOnCSCollectionGunSkinRewardsRes = function(res)
        local skinTaskInfo = {
            MP_TaskInfo = {
                bUnlocked = false,
                bFinished = false,
                pre_Tasks = {},
                tasks = {},
                totalTaskNum = 0,
                finishedTaskNum = 0,
            },
            SOL_TaskInfo = {
                bUnlocked = false,
                bFinished = false,
                pre_Tasks = {},
                tasks = {},
                totalTaskNum = 0,
                finishedTaskNum = 0,
            }
        }
        if res.result == 0 then
            skinTaskInfo.MP_TaskInfo.bUnlocked = true
            for index, goalData in ipairs(res.bf_precondition_goals) do
                skinTaskInfo.MP_TaskInfo.totalTaskNum = skinTaskInfo.MP_TaskInfo.totalTaskNum + 1
                if goalData.cur_value < goalData.max_value then
                    skinTaskInfo.MP_TaskInfo.bUnlocked = false
                else
                    skinTaskInfo.MP_TaskInfo.finishedTaskNum = skinTaskInfo.MP_TaskInfo.finishedTaskNum + 1
                end
                table.insert(skinTaskInfo.MP_TaskInfo.pre_Tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value, bIsPreTask=true})
            end
            skinTaskInfo.MP_TaskInfo.bFinished = skinTaskInfo.MP_TaskInfo.bUnlocked
            for index, goalData in ipairs(res.bf_goals) do
                skinTaskInfo.MP_TaskInfo.totalTaskNum = skinTaskInfo.MP_TaskInfo.totalTaskNum + 1
                if goalData.cur_value < goalData.max_value then
                    skinTaskInfo.MP_TaskInfo.bFinished = false
                else
                    skinTaskInfo.MP_TaskInfo.finishedTaskNum = skinTaskInfo.MP_TaskInfo.finishedTaskNum + 1
                end
                table.insert(skinTaskInfo.MP_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
            end
            skinTaskInfo.SOL_TaskInfo.bUnlocked = true
            for index, goalData in ipairs(res.sol_precondition_goals) do
                skinTaskInfo.SOL_TaskInfo.totalTaskNum = skinTaskInfo.SOL_TaskInfo.totalTaskNum + 1
                if goalData.cur_value < goalData.max_value then
                    skinTaskInfo.SOL_TaskInfo.bUnlocked = false
                else
                    skinTaskInfo.SOL_TaskInfo.finishedTaskNum = skinTaskInfo.SOL_TaskInfo.finishedTaskNum + 1
                end
                table.insert(skinTaskInfo.SOL_TaskInfo.pre_Tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value, bIsPreTask=true})
            end
            skinTaskInfo.SOL_TaskInfo.bFinished = skinTaskInfo.SOL_TaskInfo.bUnlocked
            for index, goalData in ipairs(res.sol_goals) do
                skinTaskInfo.SOL_TaskInfo.totalTaskNum = skinTaskInfo.SOL_TaskInfo.totalTaskNum + 1
                if goalData.cur_value < goalData.max_value then
                    skinTaskInfo.SOL_TaskInfo.bFinished = false
                else
                    skinTaskInfo.SOL_TaskInfo.finishedTaskNum = skinTaskInfo.SOL_TaskInfo.finishedTaskNum + 1
                end
                table.insert(skinTaskInfo.SOL_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
            end
        end
        if fCustomCallback then
            fCustomCallback(res, skinId, skinTaskInfo)
        end
    end
    local req = pb.CSCollectionGunSkinRewardsReq:New()
    req.skin_id = skinId
    req:Request(fOnCSCollectionGunSkinRewardsRes, {bEnableHighFrequency = true})
    return true
end


function CollectionServer:RetrieveWeaponSkin(skinId)
    if skinId == nil then
        return false
    end
    local fOnCSCollectionUnlockGunSkinRewardsRes = function(res)
        if res.result == 0 then
            if res.data_change then
                for i, propChange in ipairs(res.data_change) do
                    local newSkinItem = self:GetWeaponSkinIfExists(propChange.prop.id)
                    if newSkinItem then
                        self.Events.evtCollectionRetrieveWeaponSkin:Invoke(newSkinItem)
                        break
                    end
                end
                self:ProcessPbDataChange(res.data_change)  
            end
        end
    end
    local req = pb.CSCollectionUnlockGunSkinRewardsReq:New()
    req.skin_id = skinId
    req:Request(fOnCSCollectionUnlockGunSkinRewardsRes, {bEnableHighFrequency = true})
    return true
end

function CollectionServer:RetrieveWeaponSkinsOfPattern(patternId)
    if patternId == nil then
        return false
    end
    local fOnCSCollectionUnlockMasterGunSkinRes = function(res)
        if res.result == 0 then
            local newSkinItems = {}
            local newSkinItem
            if res.data_change then
                for i, propChange in ipairs(res.data_change) do
                    newSkinItem = self:GetWeaponSkinIfExists(propChange.prop.id)
                    if newSkinItem then
                        table.insert(newSkinItems, newSkinItem)
                    end
                end
                self.Events.evtCollectionRetrieveAllWeaponSkinsOfPattern:Invoke(newSkinItems)
                self:ProcessPbDataChange(res.data_change)  
            end
        end
    end
    local req = pb.CSCollectionUnlockMasterGunSkinReq:New()
    req.pattern_id = patternId
    req:Request(fOnCSCollectionUnlockMasterGunSkinRes, {bEnableHighFrequency = true})
    return true
end

function CollectionServer:ActivateSkinPattern(patternId)
    if patternId == nil then
        return false
    end
    local fOnCSCollectionChangeActivatedGunSkinPatternRes = function(res)
        if res.result == 0 then
            if self._camouflageStatusInfo then
                if ItemHelperTool.IsMasterSkinPattern(patternId) then
                    self._camouflageStatusInfo.activatedMasterCamouflageId = patternId
                else
                    self._camouflageStatusInfo.activatedCamouflageId = patternId
                end
            end
            self.Events.evtCollectionActivateSkinPattern:Invoke(patternId)
        end
    end
    local req = pb.CSCollectionChangeActivatedGunSkinPatternReq:New()
    req.pattern_id = patternId
    req:Request(fOnCSCollectionChangeActivatedGunSkinPatternRes, {bEnableHighFrequency = true})
    return true
end

function CollectionServer:FetchCamouflageStatusInfo(fCustomCallback)
    local req = pb.CSCollectionLoadGunSkinRewardsStatusReq:New()
    local fOnCSCollectionLoadGunSkinRewardsStatusRes = function(res)
        if res.result == 0 then
            local newTaskRedDots = {}
            self._skinTaskInfoMap = {}
            self._camouflageStatusInfo = {
                camouflages = {},
                masterCamouflages = {},
                activatedCamouflageId = res.pattern_id_activated,
                activatedMasterCamouflageId = res.master_pattern_id_activated,
            }
            local camouflageDataRow
            if res.gun_skin_rewards and #res.gun_skin_rewards > 0 then
                for index, rawCamouflageInfo in ipairs(res.gun_skin_rewards) do
                    declare_if_nil(self._camouflageStatusInfo.camouflages, rawCamouflageInfo.pattern_id, {})
                    camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", rawCamouflageInfo.pattern_id)
                    self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].seasonId = camouflageDataRow and camouflageDataRow.SeasonID or 0
                    self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].patternId = rawCamouflageInfo.pattern_id
                    self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].skinItems = {}
                    self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].skinTasks = {}
                    self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].unlockedNum = 0
                    if rawCamouflageInfo.gun_skin_rewards_progress then
                        local skinItem
                        for index, rawTaskInfo in ipairs(rawCamouflageInfo.gun_skin_rewards_progress) do
                            declare_if_nil(self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].skinTasks, rawTaskInfo.skin_id, {})
                            local skinTaskInfo = self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].skinTasks[rawTaskInfo.skin_id]
                            self._skinTaskInfoMap[rawTaskInfo.skin_id] = skinTaskInfo
                            skinTaskInfo.patternId = rawCamouflageInfo.pattern_id
                            skinTaskInfo.MP_TaskInfo = {}
                            skinTaskInfo.MP_TaskInfo.bUnlocked = true
                            skinTaskInfo.MP_TaskInfo.pre_Tasks = {}
                            skinTaskInfo.MP_TaskInfo.tasks = {}
                            skinTaskInfo.MP_TaskInfo.totalTaskNum = 0
                            skinTaskInfo.MP_TaskInfo.finishedTaskNum = 0
                            skinTaskInfo.MP_TaskInfo.progress = 0
                            skinTaskInfo.MP_TaskInfo.preProgress = 0
                            for index, goalData in ipairs(rawTaskInfo.bf_precondition_goals) do
                                table.insert(skinTaskInfo.MP_TaskInfo.pre_Tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value, bIsPreTask=true})
                                skinTaskInfo.MP_TaskInfo.totalTaskNum = skinTaskInfo.MP_TaskInfo.totalTaskNum + 1
                                if goalData.cur_value < goalData.max_value then
                                    skinTaskInfo.MP_TaskInfo.bUnlocked = false
                                else
                                    skinTaskInfo.MP_TaskInfo.preProgress = skinTaskInfo.MP_TaskInfo.preProgress + 1
                                    skinTaskInfo.MP_TaskInfo.finishedTaskNum = skinTaskInfo.MP_TaskInfo.finishedTaskNum + 1
                                end
                            end
                            skinTaskInfo.MP_TaskInfo.bFinished = skinTaskInfo.MP_TaskInfo.bUnlocked
                            for index, goalData in ipairs(rawTaskInfo.bf_goals) do
                                table.insert(skinTaskInfo.MP_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
                                skinTaskInfo.MP_TaskInfo.totalTaskNum = skinTaskInfo.MP_TaskInfo.totalTaskNum + 1
                                if goalData.cur_value < goalData.max_value then
                                    skinTaskInfo.MP_TaskInfo.bFinished = false
                                else
                                    skinTaskInfo.MP_TaskInfo.progress = skinTaskInfo.MP_TaskInfo.progress + 1
                                    skinTaskInfo.MP_TaskInfo.finishedTaskNum = skinTaskInfo.MP_TaskInfo.finishedTaskNum + 1
                                end
                            end
                            skinTaskInfo.MP_TaskInfo.preProgress = skinTaskInfo.MP_TaskInfo.preProgress/#skinTaskInfo.MP_TaskInfo.pre_Tasks
                            skinTaskInfo.MP_TaskInfo.progress = skinTaskInfo.MP_TaskInfo.preProgress/#skinTaskInfo.MP_TaskInfo.tasks
                            skinTaskInfo.SOL_TaskInfo = {}
                            skinTaskInfo.SOL_TaskInfo.bUnlocked = true
                            skinTaskInfo.SOL_TaskInfo.pre_Tasks = {}
                            skinTaskInfo.SOL_TaskInfo.tasks = {}
                            skinTaskInfo.SOL_TaskInfo.totalTaskNum = 0
                            skinTaskInfo.SOL_TaskInfo.finishedTaskNum = 0
                            skinTaskInfo.SOL_TaskInfo.progress = 0
                            skinTaskInfo.SOL_TaskInfo.preProgress = 0
                            for index, goalData in ipairs(rawTaskInfo.sol_precondition_goals) do
                                table.insert(skinTaskInfo.SOL_TaskInfo.pre_Tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value, bIsPreTask=true})
                                skinTaskInfo.SOL_TaskInfo.totalTaskNum = skinTaskInfo.SOL_TaskInfo.totalTaskNum + 1
                                if goalData.cur_value < goalData.max_value then
                                    skinTaskInfo.MP_TaskInfo.bUnlocked = false
                                else
                                    skinTaskInfo.SOL_TaskInfo.preProgress = skinTaskInfo.SOL_TaskInfo.preProgress + 1
                                    skinTaskInfo.SOL_TaskInfo.finishedTaskNum = skinTaskInfo.SOL_TaskInfo.finishedTaskNum + 1
                                end
                            end
                            skinTaskInfo.SOL_TaskInfo.bFinished = skinTaskInfo.SOL_TaskInfo.bUnlocked
                            for index, goalData in ipairs(rawTaskInfo.sol_goals) do
                                table.insert(skinTaskInfo.SOL_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
                                skinTaskInfo.SOL_TaskInfo.totalTaskNum = skinTaskInfo.SOL_TaskInfo.totalTaskNum + 1
                                if goalData.cur_value < goalData.max_value then
                                    skinTaskInfo.SOL_TaskInfo.bFinished = false
                                else
                                    skinTaskInfo.MP_TaskInfo.progress = skinTaskInfo.MP_TaskInfo.progress + 1
                                    skinTaskInfo.SOL_TaskInfo.finishedTaskNum = skinTaskInfo.SOL_TaskInfo.finishedTaskNum + 1
                                end
                            end
                            skinTaskInfo.SOL_TaskInfo.preProgress = skinTaskInfo.SOL_TaskInfo.preProgress/#skinTaskInfo.SOL_TaskInfo.pre_Tasks
                            skinTaskInfo.SOL_TaskInfo.progress = skinTaskInfo.SOL_TaskInfo.progress/#skinTaskInfo.SOL_TaskInfo.tasks
                            if self:IsVisibleProp(rawTaskInfo.skin_id) then
                                skinItem = self:GetWeaponSkinIfExists(rawTaskInfo.skin_id)
                                if skinItem then
                                    table.insert(self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].skinItems, skinItem)
                                    if skinTaskInfo.SOL_TaskInfo.bFinished or skinTaskInfo.MP_TaskInfo.bFinished then
                                        self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].unlockedNum = self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].unlockedNum + 1
                                        if not Server.CollectionServer:IsOwnedWeaponSkin(rawTaskInfo.skin_id) then
                                            declare_if_nil(newTaskRedDots, rawCamouflageInfo.pattern_id, true)
                                        end
                                    elseif Server.CollectionServer:IsOwnedWeaponSkin(rawTaskInfo.skin_id) then
                                        self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].unlockedNum = self._camouflageStatusInfo.camouflages[rawCamouflageInfo.pattern_id].unlockedNum + 1
                                    end
                                end
                            end
                        end
                    end
                end
            end
            if res.master_gun_skin_rewards and #res.master_gun_skin_rewards > 0 then
                for index, rawCamouflageInfo in ipairs(res.master_gun_skin_rewards) do
                    declare_if_nil(self._camouflageStatusInfo.masterCamouflages, rawCamouflageInfo.pattern_id, {})
                    local camouflageTaskInfo = self._camouflageStatusInfo.masterCamouflages[rawCamouflageInfo.pattern_id]
                    camouflageDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/SkinPattern", rawCamouflageInfo.pattern_id)
                    camouflageTaskInfo.bIsMaster = true
                    camouflageTaskInfo.seasonId = camouflageDataRow and camouflageDataRow.SeasonID or 0
                    camouflageTaskInfo.patternId = rawCamouflageInfo.pattern_id
                    camouflageTaskInfo.skinItems = {}
                    camouflageTaskInfo.MP_TaskInfo = {}
                    camouflageTaskInfo.MP_TaskInfo.finishedTaskNum = 0
                    camouflageTaskInfo.MP_TaskInfo.totalTaskNum = rawCamouflageInfo.bf_goals_num
                    rawCamouflageInfo.pre_condition_skin_num_cur_bf = math.min(rawCamouflageInfo.pre_condition_skin_num_cur_bf, rawCamouflageInfo.pre_condition_skin_num_max_bf)
                    camouflageTaskInfo.MP_TaskInfo.pre_CamouflageTask = {patternId=rawCamouflageInfo.pre_condition_skin_pattern_id_bf, curValue=rawCamouflageInfo.pre_condition_skin_num_cur_bf, maxValue=rawCamouflageInfo.pre_condition_skin_num_max_bf}
                    camouflageTaskInfo.MP_TaskInfo.tasks = {}
                    camouflageTaskInfo.MP_TaskInfo.bUnlocked = rawCamouflageInfo.pre_condition_skin_num_cur_bf >= rawCamouflageInfo.pre_condition_skin_num_max_bf
                    for index, goalData in ipairs(rawCamouflageInfo.bf_goals) do
                        table.insert(camouflageTaskInfo.MP_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
                        if goalData.cur_value >= goalData.max_value then
                            camouflageTaskInfo.MP_TaskInfo.finishedTaskNum = camouflageTaskInfo.MP_TaskInfo.finishedTaskNum + 1
                        end
                    end
                    camouflageTaskInfo.MP_TaskInfo.finishedTaskNum = math.min(camouflageTaskInfo.MP_TaskInfo.finishedTaskNum, camouflageTaskInfo.MP_TaskInfo.totalTaskNum)
                    camouflageTaskInfo.MP_TaskInfo.bFinished = camouflageTaskInfo.MP_TaskInfo.bUnlocked and (camouflageTaskInfo.MP_TaskInfo.finishedTaskNum >= camouflageTaskInfo.MP_TaskInfo.totalTaskNum)
                    camouflageTaskInfo.SOL_TaskInfo = {}
                    camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum = 0
                    camouflageTaskInfo.SOL_TaskInfo.totalTaskNum = rawCamouflageInfo.sol_goals_num
                    rawCamouflageInfo.pre_condition_skin_num_cur_sol = math.min(rawCamouflageInfo.pre_condition_skin_num_cur_sol, rawCamouflageInfo.pre_condition_skin_num_max_sol)
                    camouflageTaskInfo.SOL_TaskInfo.pre_CamouflageTask = {patternId=rawCamouflageInfo.pre_condition_skin_pattern_id_sol, curValue=rawCamouflageInfo.pre_condition_skin_num_cur_sol, maxValue=rawCamouflageInfo.pre_condition_skin_num_max_sol}
                    camouflageTaskInfo.SOL_TaskInfo.tasks = {}
                    camouflageTaskInfo.SOL_TaskInfo.bUnlocked = rawCamouflageInfo.pre_condition_skin_num_cur_sol >= rawCamouflageInfo.pre_condition_skin_num_max_sol
                    for index, goalData in ipairs(rawCamouflageInfo.sol_goals) do
                        table.insert(camouflageTaskInfo.SOL_TaskInfo.tasks, {goalId=goalData.goal_id, curValue=goalData.cur_value, maxValue=goalData.max_value})
                        if goalData.cur_value >= goalData.max_value then
                            camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum = camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum + 1
                        end
                    end
                    camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum = math.min(camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum, camouflageTaskInfo.SOL_TaskInfo.totalTaskNum)
                    camouflageTaskInfo.SOL_TaskInfo.bFinished = camouflageTaskInfo.SOL_TaskInfo.bUnlocked and (camouflageTaskInfo.SOL_TaskInfo.finishedTaskNum >= camouflageTaskInfo.SOL_TaskInfo.totalTaskNum)
                    if rawCamouflageInfo.reward_skin_ids and #rawCamouflageInfo.reward_skin_ids > 0 then
                        local skinItem
                        for index, skinId in ipairs(rawCamouflageInfo.reward_skin_ids) do
                            if self:IsVisibleProp(skinId) then
                                skinItem = self:GetWeaponSkinIfExists(skinId)
                                if skinItem then
                                    table.insert(camouflageTaskInfo.skinItems, skinItem)
                                    if not rawCamouflageInfo.is_received and (camouflageTaskInfo.MP_TaskInfo.bFinished or camouflageTaskInfo.SOL_TaskInfo.bFinished) then
                                        declare_if_nil(newTaskRedDots, rawCamouflageInfo.pattern_id, true)
                                    end
                                end
                            end
                            self._skinTaskInfoMap[skinId] = camouflageTaskInfo
                        end
                    end
                end
            end
            local bNeedUpdateRedDot = false
            for patternId, value in pairs(newTaskRedDots) do
                if not self._collectionTaskRedDots[patternId] then
                    bNeedUpdateRedDot = true
                end
            end
            if not bNeedUpdateRedDot then
                for patternId, value in pairs(self._collectionTaskRedDots) do
                    if not newTaskRedDots[patternId] then
                        bNeedUpdateRedDot = true
                    end
                end
            end
            self._collectionTaskRedDots = newTaskRedDots
            if bNeedUpdateRedDot then
                self.Events.evtCollectionRedDotUpdate:Invoke()
            end
            self.Events.evtFetchCamouflageStatusInfo:Invoke(self._camouflageStatusInfo)
        end
        if fCustomCallback then
            fCustomCallback(res)
        end
    end
    req:Request(fOnCSCollectionLoadGunSkinRewardsStatusRes, {bEnableHighFrequency = true})
end

function CollectionServer:GetAllCamouflageStatusInfo()
    return self._camouflageStatusInfo or {}
end

function CollectionServer:GetSkinTaskInfoBySkinId(skinId)
    return self._skinTaskInfoMap and self._skinTaskInfoMap[skinId] or nil 
end

function CollectionServer:CheckSkinTaskIsFinishedBySkinId(skinId)
    if self._skinTaskInfoMap and self._skinTaskInfoMap[skinId] then
        return self._skinTaskInfoMap[skinId].MP_TaskInfo.bFinished or self._skinTaskInfoMap[skinId].SOL_TaskInfo.bFinished
    end
    return false
end

function CollectionServer:GetActivatedCamouflageId(bIsMaster)
    if self._camouflageStatusInfo then
        return bIsMaster and self._camouflageStatusInfo.activatedMasterCamouflageId or self._camouflageStatusInfo.activatedCamouflageId
    end
    return nil 
end

function CollectionServer:CheckIsActivatedCamouflageById(patternId)
    if self._camouflageStatusInfo then
        return patternId == self._camouflageStatusInfo.activatedMasterCamouflageId or patternId == self._camouflageStatusInfo.activatedCamouflageId
    end
    return false
end

function CollectionServer:GetActivatedCamouflageInfo(bIsMaster)
    if self._camouflageStatusInfo then
        if bIsMaster then
            return self._camouflageStatusInfo.masterCamouflages[self._camouflageStatusInfo.activatedMasterCamouflageId]
        else
            return self._camouflageStatusInfo.camouflages[self._camouflageStatusInfo.activatedCamouflageId]
        end
    end
    return nil 
end

--- 打开宝箱
---@param items ItemBase[]
---@param nums number[]
---@param fCustomCallback function
function CollectionServer:OpenBoxs(items, nums, group_ids, prop_ids, bAssemble,fCustomCallback)
    ensure(#items == #nums, "CollectionServer:OpenBoxs 道具和数量不匹配。")
    if #items == 0 then
        return false
    end
    local fOnCSOpenBoxRes = function(res)
        ---@field public change pb_DataChange
        if fCustomCallback then
            fCustomCallback(res)
        end
        if res.result == 0 then
            if res.send_by_mail == true then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryIsFull)
            end
            self.Events.evtCollectionOpenBoxSuccess:Invoke(res)
        end
    end

    ---@field public box_list pb_LotteryBoxData[]
    local req = pb.CSOpenBoxReq:New()
    ---@class pb_LotteryBoxData : ProtoBase
    ---@field public box_id number
    ---@field public num number
    req.box_list = {}
    for i, item in ipairs(items) do
        local boxId = 0
        local itemInfo = ItemConfigTool.GetItemConfigById(item.id)
        if itemInfo then
            boxId = itemInfo.ConnectedPool or 0
        end
        local lotteryBoxData = pb.LotteryBoxData:New()
        lotteryBoxData.box_id = boxId
        lotteryBoxData.num = nums[i] or 1
        lotteryBoxData.group_id = group_ids[i]
        lotteryBoxData.prop_id = prop_ids[i]
        lotteryBoxData.opened_prop_id = item.id
        lotteryBoxData.opened_prop_gid = item.gid
        lotteryBoxData.opened_prop_num = nums[i] or 1
        table.insert(req.box_list, lotteryBoxData)

        Server.StoreServer:ClearBoxInfoCache(boxId) --清除商城的箱子缓存信息
    end
    req.instant_assemble = bAssemble or false
    req:Request(fOnCSOpenBoxRes, {bEnableHighFrequency = true})
    return true
end

--- 打开单个宝箱
---@param item ItemBase
function CollectionServer:OpenBox(item, num, fCustomCallback)
    num = setdefault(num, item.num)
    return self:OpenBoxs({item}, {num},  fCustomCallback)
end



---更新红点状态
---@param blueprint_id number
---@param num number
---@param fCustomCallback function
function CollectionServer:UpdateRedDot(propInfoList, patternIds)
    if (propInfoList == nil or #propInfoList == 0) and (patternIds == nil or #patternIds == 0) then
        return false
    end
    local fOnCSCollectionUpdateRedPointRes = function(res)
        ---@field public result number
        if res.result == 0 then
            local bNeedUpdate = false
            if propInfoList then
                for index, propInfo in ipairs(propInfoList) do
                    if self._collectionPropRedDots[propInfo.id] then
                        bNeedUpdate = self._collectionPropRedDots[propInfo.id][propInfo.gid] == true
                        self._collectionPropRedDots[propInfo.id][propInfo.gid] = nil
                        if table.nums(self._collectionPropRedDots[propInfo.id]) == 0 then
                            self._collectionPropRedDots[propInfo.id] = nil
                        end
                    end
                end
            end
            if patternIds then
                for index, patternId in ipairs(patternIds) do
                    bNeedUpdate = self._collectionNewArrivedTaskRedDots[patternId] == true
                    self._collectionNewArrivedTaskRedDots[patternId] = nil  
                end
            end
            if bNeedUpdate == true then
                self.Events.evtCollectionRedDotUpdate:Invoke()
            end
        end
    end
    ---@field public prop_ids 道具id列表
    local req = pb.CSCollectionUpdateRedPointReq:New()
    req.props = propInfoList
    req.gun_skin_pattern_id = patternIds
    req:Request(fOnCSCollectionUpdateRedPointRes, {bEnableHighFrequency = true})
    return true
end

--大战场消耗性道具权益数据
function CollectionServer:_OnCollectionBattleConsumeChangeNtf(ntf)
    self:ProcessBattleConsumeChange(ntf.data_change)
end

function CollectionServer:_OnCSCollectionMysticalPendantSuitNtf(ntf)
    if ntf.suit_infos then
        self:DiffNewPendantNum(ntf.suit_infos)
        self.suit_infos = ntf.suit_infos
        self:RefWaitActivePendant()
        self:CollectionHallRedDotUpdate()
    end
end

-- 获取套装挂饰数量
function CollectionServer:GetSuitPendantNum(pendantId)
    for _, data in ipairs(self.suit_infos) do
        for _, pendant_data in pairs(data.pendant_list) do
            if pendant_data.pendant_id == pendantId then
                return pendant_data.cnt
            end
        end
    end
    -- 服务器只下发有数量的，没有下发即为0
    return 0
end

-- 获取套装挂饰是否激活
function CollectionServer:GetSuitPendantActived(pendantId)
    for _, data in ipairs(self.suit_infos) do
        for _, pendant_data in pairs(data.pendant_list) do
            if pendant_data.pendant_id == pendantId then
                return data.actived
            end
        end
    end
    -- 服务器只下发有数量的，没有下发即为false
    return false
end

-------------------- 典藏挂饰收藏馆红点相关 start --------------------
-------------------- 典藏挂饰收藏馆红点相关 start --------------------
-------------------- 典藏挂饰收藏馆红点相关 start --------------------
-- diff是否有新增的典藏挂饰
function CollectionServer:DiffNewPendantNum(new_suit_infos)
    if self.suit_infos then
        for _, data in ipairs(new_suit_infos) do
            for _, pendant_data in pairs(data.pendant_list) do
                if (pendant_data.cnt > 0) and (self:GetSuitPendantNum(pendant_data.pendant_id) == 0) then
                    self:SetNewUnlockPendantOnRed(data.suit_id, true)
                    self._newUnlockPendantAnimaList[pendant_data.pendant_id] = true
                    break
                end
            end
        end
    end
end

-- 是否有待激活的典藏挂饰
function CollectionServer:RefWaitActivePendant()
    if self.suit_infos then
        for _, data in ipairs(self.suit_infos) do
            self:SetNewGatherPendantOnRed(data.suit_id, false)
            if data.gathered then
                self:SetNewGatherPendantOnRed(data.suit_id, not data.actived)
            end
        end
    end
end

-- 设置新增的典藏挂饰
function CollectionServer:SetNewUnlockPendantOnRed(suitID, bool)
    self._newUnlockPendantReddotList[suitID] = bool
    if not bool then
        if self.suit_infos then
            for _, data in ipairs(self.suit_infos) do
                for _, pendant_data in pairs(data.pendant_list) do
                    if data.suit_id == suitID then
                        if self._newUnlockPendantAnimaList[pendant_data.pendant_id] then
                            self._newUnlockPendantAnimaList[pendant_data.pendant_id] = false
                        end
                    end
                end
            end
        end
    end
end

-- 获取新增的典藏挂饰动画状态
function CollectionServer:GetNewUnlockPendantOnAnimaRed(pendant_id)
    return self._newUnlockPendantAnimaList[pendant_id] and true or false
end

-- 设置待激活的典藏挂饰
function CollectionServer:SetNewGatherPendantOnRed(suitID, bool)
    self._newGatherPendantReddotList[suitID] = bool
end

-- 获取是否有新增或待激活的挂饰
function CollectionServer:GetNewCollectionPendantFlag()
    for suitID, bool in pairs(self._newUnlockPendantReddotList) do
        if bool then
            return true
        end
    end
    for suitID, bool in pairs(self._newGatherPendantReddotList) do
        if bool then
            return true
        end
    end
    return false
end

-- 获取是否有新增或待激活的指定套装挂饰
function CollectionServer:GetNewCollectionPendantFlagBySuitID(suitID)
    if self._newUnlockPendantReddotList[suitID] then
        return true
    end
    if self._newGatherPendantReddotList[suitID] then
        return true
    end
    return false
end

-- 获取是否有待激活的指定套装挂饰
function CollectionServer:GetNewPendantSuitActive(suitID)
    if self._newGatherPendantReddotList[suitID] then
        return true
    end
    return false
end

-- 获取是否有新增的指定套装挂饰
function CollectionServer:GetNewUnlockCollectionPendantFlagBySuitID(suitID)
    return self._newUnlockPendantReddotList[suitID]
end

-- 红点更新
function CollectionServer:CollectionHallRedDotUpdate()
   self.Events.evtCollectionHallRedDotUpdate:Invoke()
end

-------------------- 典藏挂饰收藏馆红点相关 end --------------------
-------------------- 典藏挂饰收藏馆红点相关 end --------------------
-------------------- 典藏挂饰收藏馆红点相关 end --------------------

function CollectionServer:ProcessBattleConsume(consumeDataLoaded)
    if consumeDataLoaded then
        for index, consumeData in ipairs(consumeDataLoaded) do
            if consumeData.functional_type_id then
                self._battleConsumeChangeMap[consumeData.functional_type_id] = {}
                self._battleConsumeChangeMap[consumeData.functional_type_id].functionTypeId = consumeData.functional_type_id
                self._battleConsumeChangeMap[consumeData.functional_type_id].newCurCount = consumeData.cur_cnt
                self._battleConsumeChangeMap[consumeData.functional_type_id].newMaxCount = consumeData.max_cnt
            end
        end
    end
end

function CollectionServer:ProcessBattleConsumeChange(consumeDataChange)
    if consumeDataChange then
        for index, consumeData in ipairs(consumeDataChange) do
            if consumeData.functional_type_id then
                self._battleConsumeChangeMap[consumeData.functional_type_id] = {}
                self._battleConsumeChangeMap[consumeData.functional_type_id].functionTypeId = consumeData.functional_type_id
                self._battleConsumeChangeMap[consumeData.functional_type_id].oldCurCount = consumeData.old_cur_cnt
                self._battleConsumeChangeMap[consumeData.functional_type_id].oldMaxCount = consumeData.old_max_cnt
                self._battleConsumeChangeMap[consumeData.functional_type_id].newCurCount = consumeData.new_cur_cnt
                self._battleConsumeChangeMap[consumeData.functional_type_id].newMaxCount = consumeData.new_max_cnt
                self._battleConsumeChangeMap[consumeData.functional_type_id].propId = consumeData.prop_id
                self._battleConsumeChangeMap[consumeData.functional_type_id].propChangeNum = consumeData.prop_change_num
            end
        end
    end
end

function CollectionServer:ProcessRightsInfo(rightsInfoLoaded)
    if rightsInfoLoaded then
        self._rightsInfoMap = {}
        for index, rightInfo in ipairs(rightsInfoLoaded) do
            if rightInfo.type then
                self._rightsInfoMap[rightInfo.type] = {}
                self._rightsInfoMap[rightInfo.type].type = rightInfo.type
                self._rightsInfoMap[rightInfo.type].expireTime = rightInfo.expire_time
            end
        end
    end
end

function CollectionServer:ProcessRightsInfoChange(rightsInfoChange)
    if rightsInfoChange then
        for index, rightInfo in ipairs(rightsInfoChange) do
            if rightInfo.right_type then
                self._rightsInfoMap[rightInfo.right_type] = {}
                self._rightsInfoMap[rightInfo.right_type].type = rightInfo.right_type
                self._rightsInfoMap[rightInfo.right_type].oldExpireTime = rightInfo.old_expire_time
                self._rightsInfoMap[rightInfo.right_type].expireTime = rightInfo.new_expire_time
                self._rightsInfoMap[rightInfo.right_type].effectTime = rightInfo.effect_time
                self._rightsInfoMap[rightInfo.right_type].propId = rightInfo.prop_id
                self._rightsInfoMap[rightInfo.right_type].propChangeNum = rightInfo.prop_change_num
            end
        end
    end
end


function CollectionServer:CheckIsRightsEffectingByCardId(cardId)
    local activatingTable = Facade.TableManager:GetTable("RightsCardActivatingConfig")
    local rightsType = nil
    for key, activatingData in pairs(activatingTable) do
        if tonumber(activatingData.TheActivating) == cardId then
            rightsType = activatingData.TheActivated
            break
        end
    end
    local rightsInfo = self:GetRightsInfoByType(rightsType)
    local bIsEffecting = false
    if rightsInfo then
        bIsEffecting = (rightsInfo.expireTime - Facade.ClockManager:GetServerTimestamp()) > 0
    end
    return bIsEffecting
end

function CollectionServer:CheckIsMysticalSkinInRandomPool(weaponId, skinId)
    local map = self:GetWeaponMysticalSkinInfosFromSkinID(skinId)
    if map == nil then
        return false
    end
    for guid, itemBase in pairs(map) do
        local bRandom = self:CheckIsSkinInRandomPool(weaponId, skinId, guid)
        if bRandom then
            return true
        end
    end
    return false
end

function CollectionServer:CheckIsSkinInRandomPool(weaponId, skinId, skinGid)
    if self._randomPoolSkins[weaponId] then
        if self._randomPoolSkins[weaponId][skinId] and self._randomPoolSkins[weaponId][skinId][0] then
            return true
        end
        if self._randomPoolSkins[weaponId][0] and self._randomPoolSkins[weaponId][0][skinGid] then
            return true
        end
    end
    return false
end

function CollectionServer:CheckAreAnySkinsInRandomPool(weaponId)
    if self._randomPoolSkins[weaponId] then
        return table.nums(self._randomPoolSkins[weaponId]) > 0
    end
    return false
end

function CollectionServer:CheckIsRandomPoolEnabled(weaponId)
    if self._randomSkinPoolsEnabled[weaponId] then
        return true
    end
    return false
end

function CollectionServer:GetPropExpireInfoList()
    return self._propExpireInfoList
end

function CollectionServer:GetPropExpireInfo(propId)
    return self._propExpireInfoList[propId]
end

-- 藏品仓库发生变化时
function CollectionServer:_OnCSCollectionPropChangeNtf(ntf)
    ---@field public data_change pb_CollectionPropChange[]
    log("OnCSCollectionPropChangeNtf")
    if ntf.expire_time_records and #ntf.expire_time_records > 0 then
        self:ProcessPropExpireInfo(ntf.expire_time_records)
    end
    if ntf.data_change and #ntf.data_change > 0 then
        self:ProcessPbDataChange(ntf.data_change)
    end
end


function CollectionServer:ProcessPbDataChange(dataChange)
    local reachMaxNumPropId = nil
    local itemsNeedRemoveReddot = {}
    local propsReceived = {}
    local bPropsChangedByMarket = false
    local addedOrRemovedItems = {}
    local itemsChanged = {}
    local bNeedRefetchCamouflageStatusInfo = false
    for i, propChange in ipairs(dataChange) do
        local itemInfo = ItemConfigTool.GetItemConfigById(propChange.prop.id)
        if itemInfo then
            if propChange.prop.num ~= nil and propChange.prop.num >= maxNumAllowed then
                reachMaxNumPropId = propChange.prop.id
            end
            local itemMainType = ItemHelperTool.GetMainTypeById(propChange.prop.id)
            local itemSubType = ItemHelperTool.GetSubTypeById(propChange.prop.id)
            propChange.prop.gid = setdefault(propChange.prop.gid, 0)
            logerror("[v_dzhanshen] CollectionServer:ProcessPbDataChange changeType="..tostring(propChange.change_type).." reason="..tostring(propChange.reason))
            if propChange.change_type == PropChangeType.Add then
                if propChange.reason == ePropChangeReason.CollectionSkinPreUnlock 
                    and Server.CollectionServer:IsVisibleProp(propChange.prop.id) == true 
                    and itemInfo.NoRedPoint == false then
                    declare_if_nil(self._collectionPropRedDots, propChange.prop.id, {})
                    self._collectionPropRedDots[propChange.prop.id][propChange.prop.gid] = true
                    local skinTaskInfo = self:GetSkinTaskInfoBySkinId(propChange.prop.id) 
                    if skinTaskInfo and skinTaskInfo.patternId then
                        declare_if_nil(self._collectionTaskRedDots, skinTaskInfo.patternId, true)
                    end
                else
                    if itemMainType ~= EItemType.WeaponSkin then
                        self._visibleProps[propChange.prop.id] = true
                    else
                        local skinDataRow
                        if itemSubType == ItemConfig.EWeaponItemType.Melee then
                            skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(propChange.prop.id))
                        else
                            skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(propChange.prop.id))
                        end
                        if skinDataRow and skinDataRow.OpenCollection == true then
                            self._visibleProps[propChange.prop.id] = true
                        end
                    end
                    self:ProcessPbPropChange_Add(propChange)
                    local skinItem = self:GetWeaponSkinIfOwned(propChange.prop.id, propChange.prop.gid) or self:GetMeleeSkinIfOwned(propChange.prop.id, propChange.prop.gid)
                    if Server.CollectionServer:IsVisibleProp(propChange.prop.id) == true
                        and itemInfo.NoRedPoint == false then
                        declare_if_nil(self._collectionPropRedDots, propChange.prop.id, {}) 
                        if itemMainType == EItemType.WeaponSkin and self._collectionPropRedDots[propChange.prop.id][propChange.prop.gid] == true then
                            table.insert(itemsNeedRemoveReddot, skinItem)
                        else
                            self._collectionPropRedDots[propChange.prop.id][propChange.prop.gid] = true
                        end
                    end
                    if skinItem ~= nil then
                        skinItem.exceedPercent = propChange.exceed_percent
                    end 
                    if propChange.reason == ePropChangeReason.GM then
                        table.insert(propsReceived, self:GetCollectionItemById(propChange.prop.id, propChange.prop.gid))
                        local skinTaskInfo = self:GetSkinTaskInfoBySkinId(propChange.prop.id) 
                        if skinTaskInfo then
                            bNeedRefetchCamouflageStatusInfo = true
                        end
                    end
                end
                table.insert(itemsChanged, self:GetCollectionItemById(propChange.prop.id, propChange.prop.gid))
                table.insert(addedOrRemovedItems, itemsChanged[#itemsChanged])
            elseif propChange.change_type == PropChangeType.Modify then
                local oldPropNum = 0
                if self._collectionPropMap[propChange.prop.id] ~= nil and self._collectionPropMap[propChange.prop.id][propChange.prop.gid] ~= nil then
                    oldPropNum = self._collectionPropMap[propChange.prop.id][propChange.prop.gid].num or 0
                    if self._collectionPropMap[propChange.prop.id][propChange.prop.gid].mystical_skin_data and propChange.prop.mystical_skin_data then
                        deepcopy(self._collectionPropMap[propChange.prop.id][propChange.prop.gid].mystical_skin_data, propChange.prop.mystical_skin_data)
                        propChange.prop.mystical_skin_data = self._collectionPropMap[propChange.prop.id][propChange.prop.gid].mystical_skin_data
                    end
                end
                if (propChange.prop.num or 0) > oldPropNum then
                    if Server.CollectionServer:IsVisibleProp(propChange.prop.id) == true 
                        and itemInfo.NoRedPoint == false then
                        declare_if_nil(self._collectionPropRedDots, propChange.prop.id, {})
                        self._collectionPropRedDots[propChange.prop.id][propChange.prop.gid] = true
                    end
                end
                --只显示一次 超过后不重复显示
                if oldPropNum >= maxNumAllowed then
                    reachMaxNumPropId = nil
                end
                self:ProcessPbPropChange_Modify(propChange)
                if propChange.reason == ePropChangeReason.GM then
                    local item = ItemBase:NewIns(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                    item:SetRawPropInfo(propChange.prop)
                    item.num = propChange.delta_num or propChange.prop.num
                    table.insert(propsReceived, item)
                elseif propChange.reason == ePropChangeReason.CollectionSkinRename then
                    self.Events.evtWeaponSkinRenamed:Invoke(self:GetCollectionItemById(propChange.prop.id, propChange.prop.gid))
                elseif propChange.reason == ePropChangeReason.CollectionSkinPreUnlock and itemMainType == EItemType.WeaponSkin then
                    bNeedRefetchCamouflageStatusInfo = true
                end
                table.insert(itemsChanged, self:GetCollectionItemById(propChange.prop.id, propChange.prop.gid))
            elseif propChange.change_type == PropChangeType.Del then
                if self._collectionPropRedDots[propChange.prop.id] 
                    and self._collectionPropRedDots[propChange.prop.id][propChange.prop.gid] == true 
                    and self._collectionItemMap[propChange.prop.id] ~= nil 
                    and self._collectionItemMap[propChange.prop.id][propChange.prop.gid] ~= nil then
                    table.insert(itemsNeedRemoveReddot, self._collectionItemMap[propChange.prop.id][propChange.prop.gid])
                end
                table.insert(itemsChanged, self:GetCollectionItemById(propChange.prop.id, propChange.prop.gid))
                if ItemHelperTool.IsMysticalSkin(propChange.prop.id) then
                    if self:GetWeaponSkinInstanceNum(propChange.prop.id) <= 1 then
                        table.insert(addedOrRemovedItems, itemsChanged[#itemsChanged])
                    end
                else
                    table.insert(addedOrRemovedItems, itemsChanged[#itemsChanged])
                end
                self:ProcessPbPropChange_Delete(propChange)
                if itemMainType == EItemType.WeaponSkin and itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                    local skinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(propChange.prop.id))
                    if skinDataRow and skinDataRow.OpenCollection == true and skinDataRow.ResourceType == 1 then
                        self._visibleProps[propChange.prop.id] = self:GetWeaponSkinInstanceNum(propChange.prop.id) > 0
                    end
                end
            end
            if propChange.reason == ePropChangeReason.FromMarketSell or propChange.reason == ePropChangeReason.FromMarketBuy
            or propChange.reason == ePropChangeReason.FromMarketPreBuy then
                bPropsChangedByMarket = true
            end
        end
    end
    -- 筛选藏品
    self:FilterCollectionItemsWithMode()
    self.Events.evtUpdateCollectionData:Invoke(itemsChanged, addedOrRemovedItems)
    if reachMaxNumPropId ~= nil then
        self.Events.evtCollectionPropReachMaxNum:Invoke(reachMaxNumPropId)
        Facade.ConfigManager:SetUserArray(string.format("%s_CacheCollectionPropReachMaxNum", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" ), {tonumber(reachMaxNumPropId)})
    end
    self.Events.evtCollectionRedDotUpdate:Invoke()
    if #itemsNeedRemoveReddot > 0 then
        self:UpdateRedDot(itemsNeedRemoveReddot)
    end
    if #propsReceived > 0 then
        self.Events.evtReceivedCollectionProps:Invoke(propsReceived)
    end
    if bPropsChangedByMarket == true then
        self.Events.evtPropsChangedByMarket:Invoke(dataChange)
    end
    if bNeedRefetchCamouflageStatusInfo then
        self:FetchCamouflageStatusInfo()
    end
end

function CollectionServer:ProcessPbPropChange_Add(propChange)
    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
    local propInfo = propChange.prop
    local weaponSkinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(propInfo.id))
    local meleeWeaponSkinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(propInfo.id))
    if weaponSkinDataRow then
        declare_if_nil(self._allWeaponSkins, propInfo.id, {})
        self._allWeaponSkins[propInfo.id][0] = nil
        self._allWeaponSkins[propInfo.id][propInfo.gid] = true
        declare_if_nil(self._ownedWeaponSkins, propInfo.id, {})
        self._ownedWeaponSkins[propInfo.id][propInfo.gid] = true
    elseif meleeWeaponSkinDataRow then
        declare_if_nil(self._allMeleeSkins, propInfo.id, {})
        self._allMeleeSkins[propInfo.id][propInfo.gid] = true
        declare_if_nil(self._ownedMeleeSkins, propInfo.id, {})
        self._ownedMeleeSkins[propInfo.id][propInfo.gid] = true
    end
    local itemMainType = ItemHelperTool.GetMainTypeById(propChange.prop.id)
    local itemSubType = ItemHelperTool.GetSubTypeById(propChange.prop.id)
    if itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant then
        declare_if_nil(self._ownedPentdants, propInfo.id, {})
        self._ownedPentdants[propInfo.id][propInfo.gid] = true
        declare_if_nil(self._allPentdants, propInfo.id, {})
        self._allPentdants[propInfo.id][propInfo.gid] = true
    end
    declare_if_nil(self._collectionPropMap, propInfo.id, {})
    self._collectionPropMap[propInfo.id][propInfo.gid] = propInfo
    self:CreateCollectionItem(propInfo)  
end

function CollectionServer:ProcessPbPropChange_Modify(propChange)
    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
    local propInfo = propChange.prop
    declare_if_nil(self._collectionPropMap, propInfo.id, {})
    self._collectionPropMap[propInfo.id][propInfo.gid] = propInfo
    self:CreateCollectionItem(propInfo)
end

function CollectionServer:ProcessPbPropChange_Delete(propChange)
    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
    local propInfo = propChange.prop
    local weaponSkinDataRow = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(propInfo.id))
    if weaponSkinDataRow and self:GetWeaponSkinInstanceNum(id) <= 0 then
        declare_if_nil(self._allWeaponSkins, weaponSkinDataRow.SkinId, {})
        self._allWeaponSkins[weaponSkinDataRow.SkinId][0] = true
        self:CreateCollectionItem({id=weaponSkinDataRow.SkinId, gid=0, num=1})
    end
    if self._collectionPropMap and self._collectionPropMap[propInfo.id] then
        self._collectionPropMap[propInfo.id][propInfo.gid] = nil
    end
    self:RemoveCollectionItem(propInfo, self._collectionItemMap)
end

function CollectionServer:IsHaveBlueprint(weaponID)
    local dataTable = WeaponHelperTool.GetWeaponSkinDataTable()
    local datas = {}
    for key, value in pairs(dataTable) do
        local id = tonumber(value.BaseWeaponId)
        if id == weaponID then
            if self:IsOwnedWeaponSkin(key) then
                table.insert(datas, key)
            end
        end
    end
    local bContains = #datas > 0
    return bContains, datas
end





--检验昵称是否可用
function CollectionServer:SendNickNameAvailable(newName)
    if tostring(newName) == "" then
        return
    end
    local onCSAccountValidateNickRes = function(res)
        if res and res.result == 0 then
            self.Events.evtNickNameAvailable:Invoke(newName)
        else
            self.Events.evtNickNameAvailable:Invoke()
        end
    end
    local req = pb.CSAccountValidateNickReq:New()
    req.nick = tostring(newName)
    req:Request(onCSAccountValidateNickRes)
end

--请求改名
function CollectionServer:RequestReName(newName)
    if tostring(newName) == "" then
        return
    end
    local onCSAccountUpdateProfileRes = function(res)
        if res and res.result == 0 then
            self.used_rename_card = true
            self.rename_cd_time = res.rename_cd_time
            loginfo("rename success return value rename_cd_time = ", res.rename_cd_time)
            self.Events.evtReNickNameSuccess:Invoke()
            Server.TeamServer:SendChangeEquipNtf()
        end
    end
    local req = pb.CSAccountUpdateProfileReq:New()
    req.nickname = tostring(newName)
    req:Request(onCSAccountUpdateProfileRes)
end

--获取改名卡时间
function CollectionServer:GetReNameCardInfo()
    if self.used_rename_card ~= nil and self.rename_cd_time ~= nil then
        local list = {
            used_rename_card = self.used_rename_card,
            rename_cd_time = self.rename_cd_time,
        }
        return list
    end
    return Server.RoleInfoServer:GetReNameCardInfo()
end

--校验低级物资券数据
function CollectionServer:CSCollectionAutoDistributionReq(bForce)
    bForce = setdefault(bForce, false)
    local onCollectionAutoDistributionRes = function(res)
        if res and res.result == 0 then
            self._lowRentalNextDistributeTs = res.next_distribute_ts -- 下次自动发放时间戳
            self.Events.evtCollectionAutoDistributionReqSucess:Invoke()
        end
    end
    -- 强行拉取或者下一次获取时间戳小于当前服务器时间戳
    if bForce or self._lowRentalNextDistributeTs < Facade.ClockManager:GetLocalTimestamp() then
        local req = pb.CSCollectionAutoDistributionReq:New()
        req:Request(onCollectionAutoDistributionRes)
    end
end

--获取低级物资券下次自动发放时间戳
function CollectionServer:GetLowRentalNextDistributeTs()
    return self._lowRentalNextDistributeTs or 0
end

--设置入局需要保留的数据的皮肤
function CollectionServer:SetWeaponSkinsCarring(weaponSkinsCarring)
    self._weaponSkinsCarring = weaponSkinsCarring
end


function CollectionServer:_ToggleLocalRandomSkinData(weaponId, skinId, skinGid, bAdd)
    if bAdd == true then
        declare_if_nil(self._randomPoolSkins, weaponId, {})
        declare_if_nil(self._randomPoolSkins[weaponId], skinId, {})
        declare_if_nil(self._randomPoolSkins[weaponId], skinId or 0, {})
        declare_if_nil(self._randomPoolSkins[weaponId][skinId or 0], skinGid or 0, true)
    elseif self._randomPoolSkins[weaponId] then
        if self._randomPoolSkins[weaponId][skinId] then
            self._randomPoolSkins[weaponId][skinId][skinGid] = nil
        end
        if table.nums(self._randomPoolSkins[weaponId][skinId]) == 0 then
            self._randomPoolSkins[weaponId][skinId] = nil
        end
        if table.nums(self._randomPoolSkins[weaponId]) == 0 then
            self._randomPoolSkins[weaponId] = nil
        end
    end
end

function CollectionServer:ProcessRandSkinPool(randSkinList)
    if randSkinList then
        self._randomPoolSkins = {}
        self._randomSkinPoolsEnabled = {}
        for index, randSkinInfo in ipairs(randSkinList) do
            for index, skinId in ipairs(randSkinInfo.skin_ids) do
                self:_ToggleLocalRandomSkinData(randSkinInfo.weapon_id, skinId, 0, true) 
            end
            for index, skinGid in ipairs(randSkinInfo.skin_gids) do
                self:_ToggleLocalRandomSkinData(randSkinInfo.weapon_id, 0, skinGid, true) 
            end
            self._randomSkinPoolsEnabled[randSkinInfo.weapon_id] = randSkinInfo.enabled_rand
        end
    end
end

function CollectionServer:ProcessPropExpireInfo(expireInfoList)
    if expireInfoList then
        self._propExpireInfoList = {}
        for index, expireInfo in ipairs(expireInfoList) do
            declare_if_nil(self._propExpireInfoList, expireInfo.prop_id, {})
            for index, timeInfo in ipairs(expireInfo.items) do
                table.insert(self._propExpireInfoList[expireInfo.prop_id], {expireTime=timeInfo.expire_time, num=timeInfo.num})
            end
            table.sort(self._propExpireInfoList[expireInfo.prop_id], function(expireInfoA, expireInfoB)
                return expireInfoA.expireTime < expireInfoB.expireTime
            end)
        end
    end
end

function CollectionServer:ProcessSafeBoxSkinInfo(safeboxList)
    if safeboxList then
        self._propSafeBoxList = {}
        for _, safeBoxID in ipairs(safeboxList) do
            table.insert(self._propSafeBoxList, safeBoxID)
        end
    end
end

function CollectionServer:ProcessSafeBoxEquiped(safeboxInfoEquiped)
    self.safeboxInfoEquiped = safeboxInfoEquiped
end


function CollectionServer:FetchPropExpireInfo()
    local req = pb.CSCollectionLoadPropsReq:New()
    local fOnCSCollectionLoadPropsRes = function(res)
        if res.common_props and #res.common_props > 0 then
            for _, prop in ipairs(res.common_props) do
                prop.gid = setdefault(prop.gid, 0)
                declare_if_nil(self._visibleProps,prop.id,true)
                declare_if_nil(self._collectionPropMap, prop.id, {})
                declare_if_nil(self._collectionPropMap[prop.id], prop.gid, prop)
                self:CreateCollectionItem(prop)
            end
        end
        self:ProcessPropExpireInfo(res.prop_expire_record)
        self:FilterCollectionItemsWithMode()
        self.Events.evtFetchPropExpireInfo:Invoke()
        Server.InventoryServer:ProcessSkinDependsCSRes(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
    end
    self._propExpireInfoList = {}
    Server.InventoryServer:ProcessSkinDependsCSReq(InventoryServerSkinCSQueue.IDs.CSCollectionLoadProps)
    req:Request(fOnCSCollectionLoadPropsRes)
end


function CollectionServer:RemoveExpiredPropLocally(item, bNeedEvent)
    self:RemoveCollectionItem(item:GetRawPropInfo())
    self:FilterCollectionItemsWithMode()
    self.Events.evtUpdateCollectionData:Invoke({item}, {item})
end

-- 安装3X3安全箱外观
function CollectionServer:EquipSafeBoxSkin(skinId)
    local req = pb.CSCollectionSafeboxSkinEquipReq:New()
    local fOnCollectionSafeboxSkinEquipRes = function(res)
        if res.result == 0 then
            self.safeboxInfoEquiped = skinId
            self.Events.evtEquipSafeBoxSkin:Invoke()
        end
    end
    req.safebox_skin_id = skinId
    req:Request(fOnCollectionSafeboxSkinEquipRes)
end

return CollectionServer
