----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local WarehouseEquipSlotView = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView"
local InvSlotView            = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local ItemOperaTool          = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local CommonWidgetConfig     = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp                  = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos             = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

---@class WareHouseContainerBox : LuaUIBaseView
local WareHouseContainerBox = ui("WareHouseContainerBox")

local function log(...)
    loginfo("[WareHouseContainerBox]", ...)
end

function WareHouseContainerBox:Ctor()
    self._wtBoxCapacity = self:Wnd("wtBoxCapacity", UITextBlock)
    self._wtTipsCheckBox = self:Wnd("wtTipsCheckBox", DFCommonCheckButtonOnly)
    self._wtEquipSlotView = self:Wnd("wtEquipSlotView", WarehouseEquipSlotView)
    self._wtBoxSubSlotView = self:Wnd("wtBoxSubSlotView", InvSlotView)
    self._wtBoxTitle = self:Wnd("wtBoxTitle", UITextBlock)
    self._wtCutDownPanel = self:Wnd("Slot_Warehouse_WarehouseMask", UINamedSlot)

    self._wtHoverBtn = self:Wnd("wtTipsCheckBox", DFCheckBoxOnly)
    self._wtHoverBtn:SetPreciseTap()
    -- self._wtHoverBtn:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._OnShowDescriptionTip, self._OnHideDescriptionTip)
end

-----------------------------------------------------------------------
--region 生命周期

function WareHouseContainerBox:OnOpen()

end

function WareHouseContainerBox:OnClose()

end

function WareHouseContainerBox:OnShow()
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:_CreateRefreshTimer()
    self:RefreshView()
end

function WareHouseContainerBox:OnHide()
    self:ReleaseTimer()
end

function WareHouseContainerBox:OnInitExtraData()

end
--endregion
-----------------------------------------------------------------------

-- 公有刷新容量方法
function WareHouseContainerBox:RefreshCapacity()
    self:_RefreshCapacity()
end

function WareHouseContainerBox:_RefreshCapacity()
    if self._wtBoxSubSlotView then
        local capacity = self._wtBoxSubSlotView.itemSlot:GetUsedCapacity()
        local totalCapacity = self._wtBoxSubSlotView.itemSlot:GetTotalCapacity()

        if totalCapacity > 0 then
            self._wtBoxCapacity:SetText(string.format("%d/%d", capacity, totalCapacity))
        end
    end
end

function WareHouseContainerBox:GetEquipSlotView()
    return self._wtEquipSlotView
end

function WareHouseContainerBox:GetContainerSlotView()
    return self._wtBoxSubSlotView
end

function WareHouseContainerBox:SetBoxCapacity(curNum, maxNum)
    self._wtBoxCapacity:SetText(string.format("%d/%d", curNum, maxNum))
end

function WareHouseContainerBox:SetBoxTitle(title)
    self._wtBoxTitle:SetText(title)
end

function WareHouseContainerBox:SetEquipSlotViewType(type)
    self._wtEquipSlotView:InitSlotType(type)
end

function WareHouseContainerBox:RefreshView()
    local equipSlotViewPanel = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtEquipSlotView)
    local coefficient = 1.4
    equipSlotViewPanel:SetSize(FVector2D(coefficient * ItemConfig.DefaultItemViewSize, coefficient * ItemConfig.DefaultItemViewSize))
end

function WareHouseContainerBox:_OnShowDescriptionTip()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle)
        self.hoverHandle = nil
    end

    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = Module.Inventory.Config.Loc.HoverSafeSpaceDes}
    }},self._wtDFTipsAnchor)
    self._wtHoverBtn:SetIsChecked(true)
end

function WareHouseContainerBox:_OnHideDescriptionTip()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle,self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
    self._wtHoverBtn:SetIsChecked(false)
end

function WareHouseContainerBox:_OnCheckStateChanged(bChecked)
    if bChecked then
        self:_OnShowDescriptionTip()
    else
        self:_OnHideDescriptionTip()
    end
end

-----------------------------------------------------------------------
--region 安全箱商业化相关

function WareHouseContainerBox:_CreateRefreshTimer()
    -- local equipItem = self.bindEquipSlot and self.bindEquipSlot:GetEquipItem()
    -- if not equipItem then
    --     return
    -- end
    -- local equipItemFeature = equipItem:GetFeature(EFeatureType.Equipment)
    -- -- 永久和已过期或者无权限的道具不需要定时刷新
    -- if not equipItemFeature:GetExpiredStatus() and equipItemFeature._expireTimes and equipItemFeature._expireTimes > 0 then
        self._timer = Timer:NewIns(1, 0)
        self._timer:AddListener(self._UpdateStatus, self)
        self._timer:Start()
    -- end
end

function WareHouseContainerBox:_UpdateStatus()
	local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox)
	local equipItem = safeBoxSlot and safeBoxSlot:GetEquipItem()
    -- local equipItem = self.bindEquipSlot and self.bindEquipSlot:GetEquipItem()
    local equipItemFeature = equipItem and equipItem:GetFeature(EFeatureType.Equipment)
    local equipSlotView = self._wtEquipSlotView:GetMainItemView()
    if equipItemFeature and not equipItemFeature:GetExpiredStatus() and equipItemFeature._expireTimes and equipItemFeature._expireTimes > 0 then
        local cutDownComponent = equipSlotView and equipSlotView:GetComponent(EComp.BottomRightIconText)
        if cutDownComponent then
            cutDownComponent:RefreshComponent()
        end
    else
        self:SetSafeBoxState()
        self:ReleaseTimer()
    end
end

function WareHouseContainerBox:ReleaseTimer()
    if self._timer then
        self._timer:Release()
        self._timer = nil
    end
end

function WareHouseContainerBox:SetSafeBoxState()
	-- 获取当前槽位的装备
	local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox)
	local equipItem = safeBoxSlot and safeBoxSlot:GetEquipItem()
	local equipmentFeature = equipItem and equipItem:GetFeature()
	-- local bExpire = equipmentFeature and equipmentFeature._expireTimes and equipmentFeature:GetExpiredStatus()
    local bExpire = equipmentFeature and not equipmentFeature:PermissionCanUse()
	self:_ShowExpiredState(bExpire)
	self:_ShowExpiredGridMask(bExpire, equipItem)
end

function WareHouseContainerBox:_ShowExpiredState(bExpire)
    local wtMainItem = self._wtEquipSlotView:GetMainItemView()
    if not wtMainItem then
        return
    end
    if bExpire then
        wtMainItem:FindOrAdd(EComp.ExpireMask, UIName2ID.IVContainerExpiredComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
        wtMainItem:EnableComponent(EComp.ExpireMask, true)
        wtMainItem:RefreshView()
    else
        wtMainItem:EnableComponent(EComp.ExpireMask, false)
    end
end

function WareHouseContainerBox:_ShowExpiredGridMask(bExpire, item)
    if bExpire then
        self._wtCutDownPanel:Visible()
        if not self._safeBoxMask then
            local weakIns = Facade.UIManager:AddSubUI(self, UIName2ID.ContainerMask, self._wtCutDownPanel, nil, item)
            self._safeBoxMask = getfromweak(weakIns)
            self._safeBoxMask:BindLeftBtn(SafeCallBack(function()
                local safeBoxSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBox)
                local curItem = safeBoxSlot and safeBoxSlot:GetEquipItem()
                if not curItem then
                    loginfo("[WareHouseContainerBox_HD]: SlotItem is nil")
                    return
                end
                Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxAndKeyChainMain, nil, nil, curItem.InSlot.SlotType, curItem)
            end))
            self._safeBoxMask:BindRightBtn(SafeCallBack(function()
                    -- 转移全部按钮绑定
                    ItemOperaTool.TransferSlotItems(ESlotType.SafeBoxContainer)
                end))
        end
        if ItemOperaTool.bInSettlement then
            self._safeBoxMask:HideLeftBtn()
        end
        self._wtBoxSubSlotView:ShowGreyMask(true)
        self._safeBoxMask:Visible()
        self._safeBoxMask:InitWidget(item)
    else
        if self._safeBoxMask then
            self._wtCutDownPanel:Collapsed()
        end
        self._wtBoxSubSlotView:ShowGreyMask(false)
    end
end

function WareHouseContainerBox:_OnItemMove(moveItemInfo)
    local newLoc = moveItemInfo.NewLoc
    local oldLoc = moveItemInfo.OldLoc
    if newLoc and (newLoc.ItemSlot == Server.InventoryServer:GetSlot(ESlotType.SafeBox)) then
        self:SetSafeBoxState()
        -- 更换权益道具后，定时器重置
        self:ReleaseTimer()
        self:_CreateRefreshTimer()
        self:RefreshCapacity()
    elseif newLoc and newLoc.ItemSlot and (newLoc.ItemSlot.SlotType == ESlotType.SafeBoxContainer)
    or oldLoc and oldLoc.ItemSlot and (oldLoc.ItemSlot.SlotType == ESlotType.SafeBoxContainer) then
        self:RefreshCapacity()
    end
end

--endregion
-----------------------------------------------------------------------

return WareHouseContainerBox