----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GPModularWeaponDescLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithCPPLogic.GPModularWeaponDescLogic"
local GunsmithSkinData = require "DFM.Business.Module.GunsmithModule.Data.Skin.GunsmithSkinData"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local GunsmithTLogLogic = require "DFM.Business.Module.GunsmithModule.Logic.TLog.GunsmithTLogLogic"
local EGunsmithUIState = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithUIState"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"

local FItemInfoContext = import "ItemInfoContext"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"

---@type GunsmithUIContextProcessor : LuaObject
local GunsmithUIContextProcessor = class('GunsmithUIContextProcessor', LuaObject)

function GunsmithUIContextProcessor:Ctor()
end

function GunsmithUIContextProcessor:OverrideContext()
end

function GunsmithUIContextProcessor:OverrideContextFromServerPartUpdated()
end

function GunsmithUIContextProcessor:OverrideContextFromServerSkinUpdated()
    local propinfo = self:InternalOverridePropinfo()

    local skinInfoParam = WeaponAssemblyTool.GetNewPbWeaponSkinInfoParam()
    skinInfoParam = WeaponAssemblyTool.SetPropInfo2PbWeaponSkinInfoParam(propinfo, skinInfoParam)

    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription, skinInfoParam)

    self:OnOverrideContextFromServerSkinUpdated(skinInfoParam)
end

function GunsmithUIContextProcessor:OverrideContextBackupFromDescription(weaponDescription, bOverrideSkinInfo, bOverridePendantInfo)
end

function GunsmithUIContextProcessor:_InternalOverrideContextBackupFromDescription(syncWeaponDescription, inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)
    GunsmithUIContextLogic.OverrideDescSolutionFromDescription(syncWeaponDescription, inWeaponDescription, bOverrideSkinInfo, bOverridePendantInfo)

    GPModularWeaponDescLogic.RefreshSocketNodeFlag(syncWeaponDescription)

    self:OnFrontendUpdated(syncWeaponDescription)
end

function GunsmithUIContextProcessor:ProcessAddPartNode(itemID, itemGUID, socketGUID)
    local weaponDescription4Backup = GunsmithUIContextLogic.GetWeaponDescription4Backup()
    local weaponDescription4Frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    -- revert
    GPModularWeaponDescLogic.BuildFromModularWeaponDesc(weaponDescription4Frontend, weaponDescription4Backup)
    local bSuccess, result = self:InternalAddPartNode(itemID, itemGUID, socketGUID, weaponDescription4Frontend)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor.ProcessAddPartNode, fail:", result, itemID, itemGUID, socketGUID)
    end

    GunsmithUIContextLogic.SetFastEquipResult(result)

    self:OnFrontendUpdated()
end

function GunsmithUIContextProcessor:ProcessSetSkinID(skinID, skinGUID)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    if isinvalid(weaponDescription) then
        return
    end

    skinGUID = setdefault(skinGUID, 0)

    local skinInfoParam = weaponDescription:GetPbWeaponSkinInfoParam()
    skinInfoParam.SkinId = skinID 
    skinInfoParam.SkinGid = skinGUID 
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription, skinInfoParam)
end

---@param skinInfoParam FPbWeaponSkinInfoParam
function GunsmithUIContextProcessor:ProcessSetSkinInfoParam(skinInfoParam)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription, skinInfoParam)
end

function GunsmithUIContextProcessor:ProcessSetSkinLockAllAppearance(bLockAllAppearance)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    if isinvalid(weaponDescription) then
        return
    end

    local skinInfoParam = weaponDescription:GetPbWeaponSkinInfoParam()
    skinInfoParam.bLockApperance = bLockAllAppearance
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription, skinInfoParam)
end

function GunsmithUIContextProcessor:ProcessSetPendantInfo(pendantInfo)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GPModularWeaponDescLogic.SetSkinFromPendantInfo(weaponDescription, pendantInfo)
end

function GunsmithUIContextProcessor:ProcessSetDescription(weaponDescription)
    if isinvalid(weaponDescription) then
        return
    end
    self:OnFrontendUpdated(weaponDescription)
end

function GunsmithUIContextProcessor:SyncAddPartNode(itemID, itemGUID, socketGUID, bUnlock)
end

function GunsmithUIContextProcessor:SyncRemovePartNode(socketGUID, bUnlock)
end

function GunsmithUIContextProcessor:SyncPart2Server(fastEquipResult, operationItemID, operationSocketGUID)
    local propinfo4Sync, propinfo4Local = self:InteralFormatParam4SyncContext()
    local pass = tostring(GunsmithUIContextLogic.GetUIState())

    local function fcallback(res)
        if not res then
            return
        end
        self:InternalProcessFastEquipResult(fastEquipResult, operationItemID, operationSocketGUID)
    end

    self:CSWAssemblyDepositPropUpdateReq(propinfo4Sync, fcallback, propinfo4Local)
end

function GunsmithUIContextProcessor:PreProcessSyncTuneNodeValue()
end

function GunsmithUIContextProcessor:SyncTuneNodeValue(socketGUID)
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local backup = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    backup:SeteTuneNodeValueFromDesc(socketGUID, frontend)

    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    sync:SeteTuneNodeValueFromDesc(socketGUID, frontend)

    self:OnSyncTuneNodeValue(socketGUID)
end

function GunsmithUIContextProcessor:SyncPart2ServerFromFrontend()
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    GPModularWeaponDescLogic.BuildFromModularWeaponDesc(sync, frontend)
    self:InternalRelinkWeaponDescriptionNodeGUID(sync)
    -- 空机匣额外处理根节点武器
    self:InternalRelinkWeaponDescriptionRootNodeGUID(sync)

    self:SyncPart2Server()
end

function GunsmithUIContextProcessor:OnFrontendUpdated(inWeaponDescription)
    local frontend = GunsmithUIContextLogic.GetFrontend()
    if frontend == nil then
        return
    end

    local frontendWeaponDescription = frontend:GetWeaponDescription()
    if isvalid(inWeaponDescription) then
        GPModularWeaponDescLogic.BuildFromModularWeaponDesc(frontendWeaponDescription, inWeaponDescription)
    end

    local focusSocketGUID = GunsmithUIContextLogic.GetFocusSocketGUID()

    frontend:SetWeaponSocketSnapshot()

    local socketsSnapshot = GunsmithUIContextLogic.GetWeaponSocketSnapshot4FrontEnd()
    local focusSocket = socketsSnapshot:GetSocketFromGUID(focusSocketGUID)
    GunsmithUIContextLogic.SetFocusSocket(focusSocket)

    self:OnPostFrontendUpdated()
end

function GunsmithUIContextProcessor:OnPostFrontendUpdated()
    GunsmithUIContextLogic.UpdateMissionEquippedAndUnequippedPartList()
end

function GunsmithUIContextProcessor:OnBackupUpdated(inWeaponDescription)
    local backup = GunsmithUIContextLogic.GetBackup()
    if backup == nil then
        return
    end

    local backupWeaponDescription = backup:GetWeaponDescription()
    if isvalid(inWeaponDescription) then
        GPModularWeaponDescLogic.BuildFromModularWeaponDesc(backupWeaponDescription, inWeaponDescription)
    end

    GPModularWeaponDescLogic.RefreshSocketNodeFlag(backupWeaponDescription)
    backup:SetWeaponSocketSnapshot()
    local snapshot = GunsmithUIContextLogic.GetWeaponSocketSnapshot4Backup()
    local uiState = snapshot:GetUIState()

    GunsmithUIContextLogic.SetUIState(uiState, true)
end

function GunsmithUIContextProcessor:ProcessUIStateUpdated(state, bFromDataUpdated)
    self:OnUIStateUpdatedTLog(state, bFromDataUpdated)
    self:OnUIStateUpdated()
end

function GunsmithUIContextProcessor:OnUIStateUpdatedTLog(state, bFromDataUpdated)
    local bIsSimulate = EGunsmithUIState.IsSimulateState(state)
    if bIsSimulate then
        GunsmithTLogLogic.TLogEnterSimulate()
        return
    end

    if not bFromDataUpdated then
        return
    end
    GunsmithTLogLogic.TLogExitSimulate()
end

function GunsmithUIContextProcessor:OnUIStateUpdated()
end

---@param skinInfoParam FPbWeaponSkinInfoParam
function GunsmithUIContextProcessor:OnOverrideContextFromServerSkinUpdated(skinInfoParam)
end

function GunsmithUIContextProcessor:OnSyncTuneNodeValue(socketGUID)
end

function GunsmithUIContextProcessor:SetTuneNodeValue(socketGUID, inTuneID, inValue)
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    if isinvalid(frontend) then
        return
    end
    frontend:SeteTuneNodeValue(socketGUID, inTuneID, inValue)
end

function GunsmithUIContextProcessor:InteralFormatParam4SyncContext()
end

-- 空机匣写入武器GUID
function GunsmithUIContextProcessor:InternalRelinkWeaponDescriptionRootNodeGUID(weaponDescription)
    -- 是否空机匣进入
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    if not bIsFromUserPropInfo then
        return
    end
    -- 武器在仓库是否存在
    local weaponGUID = GunsmithUIContextLogic.GetWeaponItemGUIDFormUser()
    local itemBase = GunsmithUIContextLogic.GetItemByGid(weaponGUID)
    if isinvalid(itemBase) then
        return
    end

    -- 将新获得武器GUID写入到根节点
    GPModularWeaponDescLogic.SetPartNodeGUID(weaponDescription, 0, weaponGUID)

    -- 将新获得武器默认皮肤写入
    self:_InternalSetSyncSkinInfoParam(weaponDescription, itemBase)
    -- 处理挂饰
    self:_InternalSetSyncPendantInfo(weaponDescription, itemBase)
end

function GunsmithUIContextProcessor:_InternalSetSyncSkinInfoParam(weaponDescription, itemBase)
    if isinvalid(itemBase) then
        return
    end

    local skinIDFrondend = GPModularWeaponDescLogic.GetSkinID(weaponDescription)
    local bIsValid = skinIDFrondend and skinIDFrondend == 0
    if bIsValid then
        return
    end
    local skinID = itemBase:Get_PropInfo_Weapon_SkinID()
    bIsValid = skinID and skinID ~= 0
    if bIsValid then
        return
    end
    local weaponDescription4Server = itemBase:GetRawDescObj()
    local skinInfoParam = GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromWeaponDescription(weaponDescription4Server)
    GPModularWeaponDescLogic.SetSkinFromPbWeaponSkinInfoParam(weaponDescription, skinInfoParam)
end

function GunsmithUIContextProcessor:_InternalSetSyncPendantInfo(weaponDescription, itemBase)
    if isinvalid(itemBase) then
        return
    end

    local propinfo = itemBase:GetRawPropInfo()
    local pendantInfo = WeaponAssemblyTool.GetNewPendantInfo()
    pendantInfo = WeaponAssemblyTool.SetPropInfo2FPendantInfo(propinfo, pendantInfo)
    GPModularWeaponDescLogic.SetSkinFromPendantInfo(weaponDescription, pendantInfo)
end

function GunsmithUIContextProcessor:InternalRelinkWeaponDescriptionNodeGUID(weaponDescription, bContainsFromInventory, bOverrideSkinInfo)
    bOverrideSkinInfo = setdefault(bOverrideSkinInfo, false)
    local unlockedSocketGUID2GUIDs, locked, unlocked4Inventory = GunsmithUIContextLogic.GetIDsContainedInventoryFromWeaponDescription(weaponDescription, bOverrideSkinInfo)

    bContainsFromInventory = setdefault(bContainsFromInventory, true)
    local unLockedCount = table.getkeynum(unlockedSocketGUID2GUIDs)
    local lockedCount = table.getkeynum(locked)
    local unLlocked4InventoryCount = table.getkeynum(unlocked4Inventory)

    local bFilterInventory = (not bContainsFromInventory) and lockedCount > 0 and unLlocked4InventoryCount > 0
    --logerror("GunsmithUIContextProcessor::InternalRelinkWeaponDescriptionNodeGUID:", unLockedCount, lockedCount)
    for key, value in pairs(unlockedSocketGUID2GUIDs) do
        --logerror("linked:", key, value)
        local bCanSet = not (bFilterInventory and unlocked4Inventory[key] and unlocked4Inventory[key] == value)
        if bCanSet then
            GPModularWeaponDescLogic.SetPartNodeGUID(weaponDescription, key, value)
        end
    end

    for key, value in pairs(locked) do
        logerror("locked:", key, value)
    end
    return unlockedSocketGUID2GUIDs, locked
end

---@param itemID number
---@param itemGUID number
---@param socketGUID number
---@param weaponDescription UModularWeaponDesc
---@param itemInfoContext FItemInfoContext
function GunsmithUIContextProcessor:InternalAddPartNode(itemID, itemGUID, socketGUID, weaponDescription, itemInfoContext)
    if isinvalid(weaponDescription) then
        logerror("GunsmithUIContextProcessor.InternalAddPartNode, weaponDescription is null.")
        return
    end

    if isinvalid(itemInfoContext) then
        itemInfoContext = GPModularWeaponDescLogic.GetPartsDefaultItemInfoFromDesc(itemID, itemGUID, socketGUID, weaponDescription)
    end
    
    local result = UAssembleWeaponDataLibrary.AddNodeToDescFromSocketGUID(weaponDescription, itemID, itemGUID, itemInfoContext, socketGUID, false)

    weaponDescription:RelinkSocketGUID()

    local bSuccess = self:InternalCheckyFastEquipResult(result)
    return bSuccess, result
end

function GunsmithUIContextProcessor:InternalRemovePartNode(socketGUID, weaponDescription)
    if isinvalid(weaponDescription) then
        logerror("GunsmithUIContextProcessor.InternalRemovePartNode, weaponDescription is null.")
        return
    end

    local result = UAssembleWeaponDataLibrary.AutoRemoveNodeFromDescWithSocketGUID(weaponDescription, socketGUID, false)
    weaponDescription:RelinkSocketGUID()

    local bSuccess = self:InternalCheckyFastEquipResult(result)
    return bSuccess, result
end

---@param result FFastEquipResult
function GunsmithUIContextProcessor:InternalCheckyFastEquipResult(result)
    if isinvalid(result) then
        return false
    end
    local bSuccess = result.bSuccess
    return bSuccess
end

---@param result FFastEquipResult
function GunsmithUIContextProcessor:InternalProcessFastEquipResult(result, opreationItemID, opreationSocketGUID)
    local bSuccess = self:InternalCheckyFastEquipResult(result)
    if not bSuccess then
        return
    end

    local socektIDS = {}
    for key, value in pairs(result.RecyclePartSocketIds) do
        socektIDS[key] = value
    end

    local opreationSocketID = GPModularWeaponDescLogic.DecodeSocketIDFromSocketGUID(opreationSocketGUID)
    local nameList = {}
    local function fcollect(index, itemID)
        if itemID == opreationItemID then
            return
        end
        
        local socketID = socektIDS[index]
        if socketID == opreationSocketID then
            return
        end

        local bIsVirtualPart = UAssembleWeaponDataLibrary.IsVirutalPartItem(itemID)
        if bIsVirtualPart then
            return
        end

        local name = ItemConfigTool.GetItemName(itemID)
        table.insert(nameList, tostring(name))
    end

    for index, itemID in pairs(result.RecyclePartItemIds) do
        fcollect(index, itemID)
    end
    if #nameList > 0 then
        local names = table.concat(nameList, tostring(Module.FastEquip.Config.Loc.PartItemRemoveSplit))
        Module.CommonTips:ShowSimpleTip(string.format(tostring(Module.FastEquip.Config.Loc.PartItemRemove), names))
    end
end

function GunsmithUIContextProcessor:InternalOverridePropinfo()
    local bUser, userPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    local propinfo = nil
    if bUser then
        propinfo = userPropInfo
    else
        local guid = GunsmithUIContextLogic.GetGUID()
        local groupID = GunsmithUIContextLogic.GetGroupID()
        propinfo = Server.InventoryServer:GetWeaponPropInfo(guid, groupID, nil)
    end

    GunsmithUIContextLogic.SetPropInfo(propinfo)
    return propinfo
end

function GunsmithUIContextProcessor:ProcessRemoveAllPart()
    local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    local weaponItemGUID = GunsmithUIContextLogic.GetWeaponItemGUIDFormUser()
    local bIsLockInventory = (weaponItemGUID == nil or weaponItemGUID == 0)
    if bIsFromUserPropInfo and bIsLockInventory then
        -- todo 直接本地从标准预设刷新数据
        GunsmithUIContextLogic.ProcessContextFromDefaultProcessor()
        Module.Gunsmith.Config.Events.evtGunsmithShortcutPropUpdate:Invoke()
        return
    end

    self:InternalRemoveAllPart()
end

function GunsmithUIContextProcessor:InternalRemoveAllPart()
    -- 仓库里的数据将配件全部卸下
    -- 直接以标准预设设置
    local weaponID = GunsmithUIContextLogic.GetItemID()
    -- 设置机匣GUID
    local weaponGUID = GunsmithUIContextLogic.GetGUID()
    -- 设置皮肤
    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    local skinInfoParam = GunsmithUIContextLogic.GetPbWeaponSkinInfoParamFromWeaponDescription(sync)

    local propInfo = GunsmithUIContextLogic.GetPresetPropinfo(weaponID, weaponGUID, skinInfoParam)
    self:SyncPropInfo2Server(propInfo)
end

function GunsmithUIContextProcessor:SyncPropInfo2Server(propinfo, fCallBack)
    local propinfo4Sync = self:InteralFormatParam4SyncContext()

    self:CSWAssemblyDepositPropUpdateReq(propinfo, fCallBack, propinfo4Sync)
end

function GunsmithUIContextProcessor:CSWAssemblyDepositPropUpdateReq(newPropInfo, fCallBack, local_prop)
    local curUIState = GunsmithUIContextLogic.GetUIState()
    local bCanSendReq = self:_InternalCanSendCSWAssemblyDepositPropUpdateReq()
    if not bCanSendReq then
        local bIsShipping = VersionUtil.IsShipping()
        if not bIsShipping then
            logerror("GunsmithUIContextProcessor:CSWAssemblyDepositPropUpdateReq -- UIState: ", curUIState," can not CSWAssemblyDepositPropUpdateReq!!!")
            logerror("GunsmithUIContextProcessor:CSWAssemblyDepositPropUpdateReq -- ", debug.traceback())
            logerror("GunsmithUIContextProcessor:CSWAssemblyDepositPropUpdateReq -- newPropInfo: ", newPropInfo, ", local_prop: ", local_prop)
            LuaGlobalEvents.evtServerShowTip:Invoke(Module.Gunsmith.Config.Loc.GunsmithErrorOperationTips)
        end
        return
    end
    local pass = tostring(curUIState)
    GunsmithUIContextLogic.CSWAssemblyDepositPropUpdateReq(newPropInfo, fCallBack, local_prop, pass)
end

function GunsmithUIContextProcessor:_InternalCanSendCSWAssemblyDepositPropUpdateReq(curUIState)
    if curUIState == nil then
        curUIState = GunsmithUIContextLogic.GetUIState()
    end
    return not (curUIState == EGunsmithUIState.Range)
end

function GunsmithUIContextProcessor:ProcessRemovePartNodeFromweaponDescription(socketGUID, weaponDescription)
    local bSuccess, result = self:InternalRemovePartNode(socketGUID, weaponDescription)
    if not bSuccess then
        logerror("GunsmithUIContextProcessor:ProcessRemovePartNodeFromweaponDescription, fail:", socketGUID)
        return
    end
end

function GunsmithUIContextProcessor:ProcessLinkWeaponDescriptionNodeGUID(weaponDescription, bContainsFromInventory, bOverrideSkinInfo)
    local unlockedSocketGUID2GUIDs, locked = self:InternalRelinkWeaponDescriptionNodeGUID(weaponDescription, bContainsFromInventory, bOverrideSkinInfo)
    -- 空机匣额外处理根节点武器
    self:InternalRelinkWeaponDescriptionRootNodeGUID(weaponDescription)
    return unlockedSocketGUID2GUIDs, locked
end

return GunsmithUIContextProcessor