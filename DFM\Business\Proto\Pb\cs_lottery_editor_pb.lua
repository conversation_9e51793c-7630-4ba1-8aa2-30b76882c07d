--cs_lottery.protoencode&decode functions.
function pb.pb_CSLotteryPropDrawReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSLotteryPropDrawReq) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __prop_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __prop_gid ~= 0 then tb.prop_gid = __prop_gid end
    local __num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_CSLotteryPropDrawReqEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.prop_gid) then    encoder:addu64(2, tb.prop_gid)    end
    if(tb.num) then    encoder:addi32(3, tb.num)    end
end

function pb.pb_CSLotteryPropDrawResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSLotteryPropDrawRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSLotteryPropDrawResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
end

function pb.pb_CSLotteryBlindBoxDrawReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSLotteryBlindBoxDrawReq) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __prop_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __prop_gid ~= 0 then tb.prop_gid = __prop_gid end
    local __num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __condition_id = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __condition_id ~= 0 then tb.condition_id = __condition_id end
    tb.box_list = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.box_list[k] = pb.pb_LotteryBoxDataDecode(v)
    end
    return tb
end

function pb.pb_CSLotteryBlindBoxDrawReqEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.prop_gid) then    encoder:addu64(2, tb.prop_gid)    end
    if(tb.num) then    encoder:addi32(3, tb.num)    end
    if(tb.condition_id) then    encoder:addi32(4, tb.condition_id)    end
    if(tb.box_list) then
        for i=1,#(tb.box_list) do
            pb.pb_LotteryBoxDataEncode(tb.box_list[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_CSLotteryBlindBoxDrawResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSLotteryBlindBoxDrawRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSLotteryBlindBoxDrawResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
end

function pb.pb_LotteryResultInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryResultInfo) or {} 
    local __lottery_process = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __lottery_process ~= "" then tb.lottery_process = __lottery_process end
    local __box_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __box_id ~= 0 then tb.box_id = __box_id end
    local __num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_LotteryResultInfoEncode(tb, encoder)
    if(tb.lottery_process) then    encoder:addstr(1, tb.lottery_process)    end
    if(tb.box_id) then    encoder:addu64(2, tb.box_id)    end
    if(tb.num) then    encoder:addi64(3, tb.num)    end
end

function pb.pb_LotteryBoxPropConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxPropConfig) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __active_flag = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __active_flag ~= false then tb.active_flag = __active_flag end
    local __indep_prob = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __indep_prob ~= 0 then tb.indep_prob = __indep_prob end
    local __prob = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __prob ~= 0 then tb.prob = __prob end
    local __num = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __restore_flag = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __restore_flag ~= false then tb.restore_flag = __restore_flag end
    tb.inventory_type_list = decoder:getu32ary(6)
    local __pos = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __pos ~= 0 then tb.pos = __pos end
    local __send_by_mail = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __send_by_mail ~= false then tb.send_by_mail = __send_by_mail end
    tb.result = pb.pb_LotteryResultInfoDecode(decoder:getsubmsg(10))
    local __estimated_probability = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __estimated_probability ~= "" then tb.estimated_probability = __estimated_probability end
    local __prob_showed = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __prob_showed ~= 0 then tb.prob_showed = __prob_showed end
    local __acquisition_guaranteed = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __acquisition_guaranteed ~= 0 then tb.acquisition_guaranteed = __acquisition_guaranteed end
    local __real_prob = decoder:getfloat(14)
    if not PB_USE_DEFAULT_TABLE or __real_prob ~= 0 then tb.real_prob = __real_prob end
    local __group_id = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= 0 then tb.group_id = __group_id end
    local __num_id = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __num_id ~= 0 then tb.num_id = __num_id end
    local __position = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    local __bound_flag = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __bound_flag ~= 0 then tb.bound_flag = __bound_flag end
    local __choose_up = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __choose_up ~= 0 then tb.choose_up = __choose_up end
    local __un_choose_up = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __un_choose_up ~= 0 then tb.un_choose_up = __un_choose_up end
    local __begin_time = decoder:geti64(21)
    if not PB_USE_DEFAULT_TABLE or __begin_time ~= 0 then tb.begin_time = __begin_time end
    local __end_time = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    return tb
end

function pb.pb_LotteryBoxPropConfigEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.active_flag) then    encoder:addbool(2, tb.active_flag)    end
    if(tb.indep_prob) then    encoder:addi32(3, tb.indep_prob)    end
    if(tb.prob) then    encoder:addi32(4, tb.prob)    end
    if(tb.num) then    encoder:addi32(5, tb.num)    end
    if(tb.restore_flag) then    encoder:addbool(7, tb.restore_flag)    end
    if(tb.inventory_type_list) then    encoder:addu32(6, tb.inventory_type_list)    end
    if(tb.pos) then    encoder:addi32(8, tb.pos)    end
    if(tb.send_by_mail) then    encoder:addbool(9, tb.send_by_mail)    end
    if(tb.result) then    pb.pb_LotteryResultInfoEncode(tb.result, encoder:addsubmsg(10))    end
    if(tb.estimated_probability) then    encoder:addstr(11, tb.estimated_probability)    end
    if(tb.prob_showed) then    encoder:addfloat(12, tb.prob_showed)    end
    if(tb.acquisition_guaranteed) then    encoder:addi32(13, tb.acquisition_guaranteed)    end
    if(tb.real_prob) then    encoder:addfloat(14, tb.real_prob)    end
    if(tb.group_id) then    encoder:addu32(15, tb.group_id)    end
    if(tb.num_id) then    encoder:addu32(16, tb.num_id)    end
    if(tb.position) then    encoder:addi32(17, tb.position)    end
    if(tb.bound_flag) then    encoder:addi32(18, tb.bound_flag)    end
    if(tb.choose_up) then    encoder:addi64(19, tb.choose_up)    end
    if(tb.un_choose_up) then    encoder:addi64(20, tb.un_choose_up)    end
    if(tb.begin_time) then    encoder:addi64(21, tb.begin_time)    end
    if(tb.end_time) then    encoder:addi64(22, tb.end_time)    end
end

function pb.pb_LotteryBoxGroupConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxGroupConfig) or {} 
    local __group_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= 0 then tb.group_id = __group_id end
    local __active_flag = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __active_flag ~= false then tb.active_flag = __active_flag end
    local __indep_prob = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __indep_prob ~= 0 then tb.indep_prob = __indep_prob end
    local __prob = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __prob ~= 0 then tb.prob = __prob end
    local __restore_flag = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __restore_flag ~= false then tb.restore_flag = __restore_flag end
    local __change_start = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __change_start ~= 0 then tb.change_start = __change_start end
    local __change_rate = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __change_rate ~= 0 then tb.change_rate = __change_rate end
    local __time_assured = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __time_assured ~= 0 then tb.time_assured = __time_assured end
    local __core_flag = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __core_flag ~= false then tb.core_flag = __core_flag end
    tb.prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.prop_list[k] = pb.pb_LotteryBoxPropConfigDecode(v)
    end
    local __real_prob = decoder:getfloat(11)
    if not PB_USE_DEFAULT_TABLE or __real_prob ~= 0 then tb.real_prob = __real_prob end
    local __begin_time = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __begin_time ~= 0 then tb.begin_time = __begin_time end
    local __end_time = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    return tb
end

function pb.pb_LotteryBoxGroupConfigEncode(tb, encoder)
    if(tb.group_id) then    encoder:addu32(1, tb.group_id)    end
    if(tb.active_flag) then    encoder:addbool(2, tb.active_flag)    end
    if(tb.indep_prob) then    encoder:addi32(3, tb.indep_prob)    end
    if(tb.prob) then    encoder:addi32(4, tb.prob)    end
    if(tb.restore_flag) then    encoder:addbool(6, tb.restore_flag)    end
    if(tb.change_start) then    encoder:addi64(7, tb.change_start)    end
    if(tb.change_rate) then    encoder:addfloat(8, tb.change_rate)    end
    if(tb.time_assured) then    encoder:addi64(9, tb.time_assured)    end
    if(tb.core_flag) then    encoder:addbool(10, tb.core_flag)    end
    if(tb.prop_list) then
        for i=1,#(tb.prop_list) do
            pb.pb_LotteryBoxPropConfigEncode(tb.prop_list[i], encoder:addsubmsg(5))
        end
    end
    if(tb.real_prob) then    encoder:addfloat(11, tb.real_prob)    end
    if(tb.begin_time) then    encoder:addi64(12, tb.begin_time)    end
    if(tb.end_time) then    encoder:addi64(13, tb.end_time)    end
end

function pb.pb_LotteryBoxConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxConfig) or {} 
    local __box_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __box_id ~= 0 then tb.box_id = __box_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.group_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.group_list[k] = pb.pb_LotteryBoxGroupConfigDecode(v)
    end
    tb.sol_prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.sol_prop_list[k] = pb.pb_LotteryBoxPropConfigDecode(v)
    end
    local __show_id1 = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __show_id1 ~= 0 then tb.show_id1 = __show_id1 end
    local __show_id2 = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __show_id2 ~= 0 then tb.show_id2 = __show_id2 end
    tb.show_id_list = decoder:getu64ary(7)
    return tb
end

function pb.pb_LotteryBoxConfigEncode(tb, encoder)
    if(tb.box_id) then    encoder:addu64(1, tb.box_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.group_list) then
        for i=1,#(tb.group_list) do
            pb.pb_LotteryBoxGroupConfigEncode(tb.group_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.sol_prop_list) then
        for i=1,#(tb.sol_prop_list) do
            pb.pb_LotteryBoxPropConfigEncode(tb.sol_prop_list[i], encoder:addsubmsg(4))
        end
    end
    if(tb.show_id1) then    encoder:addu64(5, tb.show_id1)    end
    if(tb.show_id2) then    encoder:addu64(6, tb.show_id2)    end
    if(tb.show_id_list) then    encoder:addu64(7, tb.show_id_list)    end
end

function pb.pb_CSOpenBoxReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSOpenBoxReq) or {} 
    tb.box_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.box_list[k] = pb.pb_LotteryBoxDataDecode(v)
    end
    local __instant_assemble = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __instant_assemble ~= false then tb.instant_assemble = __instant_assemble end
    return tb
end

function pb.pb_CSOpenBoxReqEncode(tb, encoder)
    if(tb.box_list) then
        for i=1,#(tb.box_list) do
            pb.pb_LotteryBoxDataEncode(tb.box_list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.instant_assemble) then    encoder:addbool(2, tb.instant_assemble)    end
end

function pb.pb_CSOpenBoxResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSOpenBoxRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    local __send_by_mail = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __send_by_mail ~= false then tb.send_by_mail = __send_by_mail end
    return tb
end

function pb.pb_CSOpenBoxResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.change) then    pb.pb_DataChangeEncode(tb.change, encoder:addsubmsg(2))    end
    if(tb.send_by_mail) then    encoder:addbool(3, tb.send_by_mail)    end
end

function pb.pb_LotteryBoxPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxPropInfo) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __active_flag = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __active_flag ~= false then tb.active_flag = __active_flag end
    local __indep_prob = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __indep_prob ~= 0 then tb.indep_prob = __indep_prob end
    local __prob = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __prob ~= 0 then tb.prob = __prob end
    local __num = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __restore_flag = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __restore_flag ~= false then tb.restore_flag = __restore_flag end
    local __hit_sum = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __hit_sum ~= 0 then tb.hit_sum = __hit_sum end
    local __pos = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __pos ~= 0 then tb.pos = __pos end
    local __estimated_probability = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __estimated_probability ~= "" then tb.estimated_probability = __estimated_probability end
    local __prob_showed = decoder:getfloat(11)
    if not PB_USE_DEFAULT_TABLE or __prob_showed ~= 0 then tb.prob_showed = __prob_showed end
    local __real_prob = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __real_prob ~= 0 then tb.real_prob = __real_prob end
    local __acquisition_guaranteed = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __acquisition_guaranteed ~= 0 then tb.acquisition_guaranteed = __acquisition_guaranteed end
    tb.prop_info = pb.pb_PropInfoDecode(decoder:getsubmsg(14))
    local __bound_flag = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __bound_flag ~= 0 then tb.bound_flag = __bound_flag end
    local __is_chosen = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_chosen ~= false then tb.is_chosen = __is_chosen end
    local __num_id = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __num_id ~= 0 then tb.num_id = __num_id end
    local __begin_time = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __begin_time ~= 0 then tb.begin_time = __begin_time end
    local __end_time = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    return tb
end

function pb.pb_LotteryBoxPropInfoEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.active_flag) then    encoder:addbool(2, tb.active_flag)    end
    if(tb.indep_prob) then    encoder:addi32(3, tb.indep_prob)    end
    if(tb.prob) then    encoder:addi32(4, tb.prob)    end
    if(tb.num) then    encoder:addi32(5, tb.num)    end
    if(tb.restore_flag) then    encoder:addbool(7, tb.restore_flag)    end
    if(tb.hit_sum) then    encoder:addi32(8, tb.hit_sum)    end
    if(tb.pos) then    encoder:addi32(9, tb.pos)    end
    if(tb.estimated_probability) then    encoder:addstr(10, tb.estimated_probability)    end
    if(tb.prob_showed) then    encoder:addfloat(11, tb.prob_showed)    end
    if(tb.real_prob) then    encoder:addfloat(12, tb.real_prob)    end
    if(tb.acquisition_guaranteed) then    encoder:addi32(13, tb.acquisition_guaranteed)    end
    if(tb.prop_info) then    pb.pb_PropInfoEncode(tb.prop_info, encoder:addsubmsg(14))    end
    if(tb.bound_flag) then    encoder:addi32(15, tb.bound_flag)    end
    if(tb.is_chosen) then    encoder:addbool(16, tb.is_chosen)    end
    if(tb.num_id) then    encoder:addu32(17, tb.num_id)    end
    if(tb.begin_time) then    encoder:addi64(18, tb.begin_time)    end
    if(tb.end_time) then    encoder:addi64(19, tb.end_time)    end
end

function pb.pb_LotteryBoxGroupInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxGroupInfo) or {} 
    local __group_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= 0 then tb.group_id = __group_id end
    local __active_flag = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __active_flag ~= false then tb.active_flag = __active_flag end
    local __indep_prob = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __indep_prob ~= 0 then tb.indep_prob = __indep_prob end
    local __prob = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __prob ~= 0 then tb.prob = __prob end
    local __restore_flag = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __restore_flag ~= false then tb.restore_flag = __restore_flag end
    local __hit_sum = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __hit_sum ~= 0 then tb.hit_sum = __hit_sum end
    local __time_assured = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __time_assured ~= 0 then tb.time_assured = __time_assured end
    local __core_flag = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __core_flag ~= false then tb.core_flag = __core_flag end
    tb.prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.prop_list[k] = pb.pb_LotteryBoxPropInfoDecode(v)
    end
    local __has_chosen_up = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __has_chosen_up ~= false then tb.has_chosen_up = __has_chosen_up end
    local __begin_time = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __begin_time ~= 0 then tb.begin_time = __begin_time end
    local __end_time = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    return tb
end

function pb.pb_LotteryBoxGroupInfoEncode(tb, encoder)
    if(tb.group_id) then    encoder:addu32(1, tb.group_id)    end
    if(tb.active_flag) then    encoder:addbool(2, tb.active_flag)    end
    if(tb.indep_prob) then    encoder:addi32(3, tb.indep_prob)    end
    if(tb.prob) then    encoder:addi32(4, tb.prob)    end
    if(tb.restore_flag) then    encoder:addbool(5, tb.restore_flag)    end
    if(tb.hit_sum) then    encoder:addi32(6, tb.hit_sum)    end
    if(tb.time_assured) then    encoder:addi64(9, tb.time_assured)    end
    if(tb.core_flag) then    encoder:addbool(11, tb.core_flag)    end
    if(tb.prop_list) then
        for i=1,#(tb.prop_list) do
            pb.pb_LotteryBoxPropInfoEncode(tb.prop_list[i], encoder:addsubmsg(7))
        end
    end
    if(tb.has_chosen_up) then    encoder:addbool(8, tb.has_chosen_up)    end
    if(tb.begin_time) then    encoder:addi64(12, tb.begin_time)    end
    if(tb.end_time) then    encoder:addi64(13, tb.end_time)    end
end

function pb.pb_MandelRewardInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MandelRewardInfo) or {} 
    local __approve_count = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __approve_count ~= 0 then tb.approve_count = __approve_count end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_MandelRewardInfoEncode(tb, encoder)
    if(tb.approve_count) then    encoder:addu64(1, tb.approve_count)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_PropInfoEncode(tb.props[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_LotteryBoxInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxInfo) or {} 
    local __box_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __box_id ~= 0 then tb.box_id = __box_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.group_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.group_list[k] = pb.pb_LotteryBoxGroupInfoDecode(v)
    end
    local __open_count = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __open_count ~= 0 then tb.open_count = __open_count end
    local __show_id1 = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __show_id1 ~= 0 then tb.show_id1 = __show_id1 end
    local __show_id2 = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __show_id2 ~= 0 then tb.show_id2 = __show_id2 end
    tb.collide_props = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.collide_props[k] = pb.pb_PropInfoDecode(v)
    end
    tb.info_list = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.info_list[k] = pb.pb_MandelRewardInfoDecode(v)
    end
    local __core_open_count = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __core_open_count ~= 0 then tb.core_open_count = __core_open_count end
    local __last_core_reward_approve_num = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __last_core_reward_approve_num ~= 0 then tb.last_core_reward_approve_num = __last_core_reward_approve_num end
    tb.show_id_list = decoder:getu64ary(11)
    return tb
end

function pb.pb_LotteryBoxInfoEncode(tb, encoder)
    if(tb.box_id) then    encoder:addu64(1, tb.box_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.group_list) then
        for i=1,#(tb.group_list) do
            pb.pb_LotteryBoxGroupInfoEncode(tb.group_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.open_count) then    encoder:addi64(4, tb.open_count)    end
    if(tb.show_id1) then    encoder:addu64(5, tb.show_id1)    end
    if(tb.show_id2) then    encoder:addu64(6, tb.show_id2)    end
    if(tb.collide_props) then
        for i=1,#(tb.collide_props) do
            pb.pb_PropInfoEncode(tb.collide_props[i], encoder:addsubmsg(7))
        end
    end
    if(tb.info_list) then
        for i=1,#(tb.info_list) do
            pb.pb_MandelRewardInfoEncode(tb.info_list[i], encoder:addsubmsg(8))
        end
    end
    if(tb.core_open_count) then    encoder:addi64(9, tb.core_open_count)    end
    if(tb.last_core_reward_approve_num) then    encoder:addi64(10, tb.last_core_reward_approve_num)    end
    if(tb.show_id_list) then    encoder:addu64(11, tb.show_id_list)    end
end

function pb.pb_CSGetBoxInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSGetBoxInfoReq) or {} 
    tb.id_list = decoder:getu64ary(1)
    local __source = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    return tb
end

function pb.pb_CSGetBoxInfoReqEncode(tb, encoder)
    if(tb.id_list) then    encoder:addu64(1, tb.id_list)    end
    if(tb.source) then    encoder:addi32(2, tb.source)    end
end

function pb.pb_CSGetBoxInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSGetBoxInfoRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.info_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.info_list[k] = pb.pb_LotteryBoxInfoDecode(v)
    end
    tb.open_lottery_records = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.open_lottery_records[k] = pb.pb_OpenLotteryItemRecordDecode(v)
    end
    return tb
end

function pb.pb_CSGetBoxInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.info_list) then
        for i=1,#(tb.info_list) do
            pb.pb_LotteryBoxInfoEncode(tb.info_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.open_lottery_records) then
        for i=1,#(tb.open_lottery_records) do
            pb.pb_OpenLotteryItemRecordEncode(tb.open_lottery_records[i], encoder:addsubmsg(3))
        end
    end
end

