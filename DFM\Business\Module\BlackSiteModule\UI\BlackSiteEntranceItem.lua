----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBlackSite)
----- LOG FUNCTION AUTO GENERATE END -----------



local BlackSiteEntranceItem = ui("BlackSiteEntranceItem")

local EEntranceType2EntranceName = {
    [BlackSiteDefine.EBlackSiteEntranceType.Construct] = Module.BlackSite.Config.Loc.BlackSiteConstructEntranceName,
    [BlackSiteDefine.EBlackSiteEntranceType.Produce] = Module.BlackSite.Config.Loc.BlackSiteProduceEntranceName,
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement] = Module.BlackSite.Config.Loc.BlackSiteCollectionRoomManagement,
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionCatalog] = Module.BlackSite.Config.Loc.BlackSiteCollectionCatalog
}

local EEntranceType2EntranceDescription = {
    [BlackSiteDefine.EBlackSiteEntranceType.Construct] = nil,
    [BlackSiteDefine.EBlackSiteEntranceType.Produce] = nil,
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement] =
        NSLOCTEXT("BlackSiteModule", "BlackSiteCollectionRoomManagementDescription", "可在收藏室中进行收藏品的上架、下架和交互操作"),
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionCatalog] =
        NSLOCTEXT("BlackSiteModule", "BlackSiteCollectionCatalogDescription", "可在收藏品中进行收藏品的解锁和升级操作")
}

local EEntranceType2EntranceIconPath = {
    [BlackSiteDefine.EBlackSiteEntranceType.Construct] = "/Game/UI/UIAtlas/System/SafeRoom3D/Sp/C4X4/SafeRoom3D_Sp_27.SafeRoom3D_Sp_27",
    [BlackSiteDefine.EBlackSiteEntranceType.Produce] = "/Game/UI/UIAtlas/System/SafeRoom3D/Sp/C4X4/SafeRoom3D_Sp_28.SafeRoom3D_Sp_28",
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement] = "Texture2D'/Game/UI/UIAtlas/System/SafeRoom3D/Sp/BG/SafeRoom3D_Sp_BG_001.SafeRoom3D_Sp_BG_001'",
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionCatalog] = "Texture2D'/Game/UI/UIAtlas/System/SafeRoom3D/Sp/BG/SafeRoom3D_Sp_BG_002.SafeRoom3D_Sp_BG_002'"
}

local EEntranceType2ReddotDataOBType = {
    [BlackSiteDefine.EBlackSiteEntranceType.Construct] = EReddotTrieObserverType.BlackSiteConstruct,
    [BlackSiteDefine.EBlackSiteEntranceType.Produce] = EReddotTrieObserverType.BlackSiteProduce,
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement] = EReddotTrieObserverType.CollectionRoom,
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionCatalog] = EReddotTrieObserverType.CollectionRoom
}

local EEntranceType2ReddotDataKey = {
    [BlackSiteDefine.EBlackSiteEntranceType.Construct] = "DeviceUnlock",
    [BlackSiteDefine.EBlackSiteEntranceType.Produce] = "ProductionCompleted",
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement] = "CollectionCanShelve",
    [BlackSiteDefine.EBlackSiteEntranceType.CollectionCatalog] = "CabinetCanLevel"
}

function BlackSiteEntranceItem:Ctor()
    self._wtEntranceBtn = self:Wnd("wtEntranceBtn", UIButton)
    self._wtEntranceBtn:Event("OnClicked", self._OnEntranceBtnClicked, self)

    -- azhengzheng:Gamepad Start
    if IsHD() then
        self._wtEntranceBtn:Event("OnHovered", self._OnEntranceBtnHovered, self)
        self._wtEntranceBtn:Event("OnUnHovered", self._OnEntranceBtnUnHovered, self)
    end
    -- azhengzheng:Gamepad End

    self._wtEntranceNameTB = self:Wnd("wtEntranceNameTB", UITextBlock)
    self._wtEntranceIconImg = self:Wnd("wtEntranceIconImg", UIImage)
    self._wtEntranceDescriptionTB = self:Wnd("wtEntranceDescriptionTB", UITextBlock)
end

function BlackSiteEntranceItem:OnOpen()
    self._wtEntranceNameTB:SetText(EEntranceType2EntranceName[self.entranceType] or "--")
    self._wtEntranceDescriptionTB:SetText(EEntranceType2EntranceDescription[self.entranceType] or "--")

    if EEntranceType2EntranceIconPath[self.entranceType] then
        self._wtEntranceIconImg:AsyncSetImagePath(EEntranceType2EntranceIconPath[self.entranceType], true)
    end
end

function BlackSiteEntranceItem:OnShowBegin()
    self:_RegisterStaticReddotDotBySelf()
end

function BlackSiteEntranceItem:OnHideBegin()
    self:_UnRegisterStaticReddotDotBySelf()
end

function BlackSiteEntranceItem:_RegisterStaticReddotDotBySelf()
    if self._staticReddotDot or not self.entranceType or not EEntranceType2ReddotDataOBType[self.entranceType] or not EEntranceType2ReddotDataKey[self.entranceType] then
        return
    end

    self._staticReddotDot = Module.ReddotTrie:RegisterStaticReddotDot
    (
        self._wtEntranceBtn,
        {
            {
                reddotData = Module.ReddotTrie:GetReddotData(EEntranceType2ReddotDataOBType[self.entranceType], EEntranceType2ReddotDataKey[self.entranceType]),
                reddotStyle =
                {
                    placeMode = EReddotPlaceMode.Custom,
                    fPlaceOperation = function(reddot)
                        reddot:SetAbsPos(FVector2D(0, 12))
                    end
                }
            }
        }
    )
end

function BlackSiteEntranceItem:_UnRegisterStaticReddotDotBySelf()
    if not self._staticReddotDot then
        return
    end

    Module.ReddotTrie:UnRegisterStaticReddotDot(self._staticReddotDot)
    self._staticReddotDot = nil
end

function BlackSiteEntranceItem:_OnEntranceBtnClicked()
    if self.entranceType <= 2 then
        Module.BlackSite:Jump(nil, self.entranceType == BlackSiteDefine.EBlackSiteEntranceType.Construct)
        return
    end

    -- azhengzheng:特勤处交互数据上报
    LogAnalysisTool.DoSendBlackSiteInteractiveData(self.entranceType == BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement and 81 or 82)

    if self.entranceType <= 4 then
        if not Module.BlackSite:CheckDeviceIsOpenByID(EBlackSiteDeviceName2Id.CollectionRoom, true) then
            return
        end

        local deviceData = Module.BlackSite:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)

        if not deviceData then
            return
        end

        if deviceData:GetLevel() == 0 then
            Module.CommonTips:ShowConfirmWindow(
                string.format(Module.BlackSite.Config.Loc.BlackSiteNotUnlockedYet, deviceData:GetName()),
                function()
                    Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
                end,
                nil,
                nil,
                Module.BlackSite.Config.Loc.BlackSiteGoToUpgrade
            )
            return
        end

        Module.CollectionRoom:ShowMainPanel(self.entranceType == BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement and 1 or 2, ECollectionRoomMainPanelEnterFrom.BlackSite)
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.BlackSiteShowCollectionRoomPanel, self.entranceType == BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement and 1 or 2)
        return
    end
end

-- azhengzheng:Gamepad Start
function BlackSiteEntranceItem:_OnEntranceBtnHovered()
    self:_InitShortcuts(true)
end

function BlackSiteEntranceItem:_OnEntranceBtnUnHovered()
    self:_InitShortcuts(false)
end

function BlackSiteEntranceItem:_InitShortcuts(isHovered)
    if self:IsInHideBeginState() or self:IsInHideState() or Module.BlackSite.Field:GetIsEnteringCollectionRoom() then
        return
    end

    local summaryList = {}
    table.insert(summaryList, {actionName = "Back_Gamepad", func = nil, caller = self, bUIOnly = false, bHideIcon = false})

    if isHovered then
        table.insert(summaryList, {actionName = "Select_Gamepad", func = self._OnEntranceBtnClicked, caller = self, bUIOnly = false, bHideIcon = false})
    end

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end
-- azhengzheng:Gamepad End

return BlackSiteEntranceItem