----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



local ReddotDataTrie = require "DFM.Business.Module.ReddotTrieModule.ReddotBase.ReddotDataTrie"
local ArmedForceManifest = require "DFM.Business.Module.ReddotTrieModule.DotUnit.ArmedForceDotUnit.Defination.ArmedForceManifest"
local ReddotTrieConfig = require "DFM.Business.Module.ReddotTrieModule.ReddotTrieConfig"
local ItemHelperTool     = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

local ArmedForceDataTrie = ReddotDataTrie:New(EReddotTrieObserverType.ArmedForce)

function ArmedForceDataTrie:InitTrie()
    self:GenerateSubTrie(ArmedForceManifest)
end

local Label2RentalConsumableID = {
    [1] = ***********,
    [2] = ***********,
    [3] = ***********,
    [4] = ***********,
    [5] = 0, -- 定制券
}
ArmedForceDataTrie.GetRentalConsumableNum = function(label)
    local rentalVoucherNum = 0
    local itemMainType = EItemType.CollectionProp
    local itemSubType = ECollectableType.RentalVoucher
    local consumableItems = Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(itemMainType, itemSubType)
    table.walk(consumableItems, 
        function (v, k)
            if label then
                if Label2RentalConsumableID[label] == v.id then
                    rentalVoucherNum = rentalVoucherNum + v.num
                elseif Label2RentalConsumableID[label] == 0 and not table.contains(Label2RentalConsumableID, v.id) then -- 定制券数量
                    rentalVoucherNum = rentalVoucherNum + v.num
                end
            else
                rentalVoucherNum = rentalVoucherNum + v.num
            end
        end)
    return rentalVoucherNum
end
--建议在函数中对id做判空,以防阻塞创建数据节点
ArmedForceDataTrie.CheckRentalNumberFun = function()
    local rentalConsumableNum = ArmedForceDataTrie.GetRentalConsumableNum()
    return rentalConsumableNum > 0, rentalConsumableNum
end

ArmedForceDataTrie.CheckRentalNumberByTab = function(id)
    if not id then
        return false , 0
    end
    local rentalConsumableNum = ArmedForceDataTrie.GetRentalConsumableNum(id)
    return rentalConsumableNum > 0, rentalConsumableNum
end

return ArmedForceDataTrie
