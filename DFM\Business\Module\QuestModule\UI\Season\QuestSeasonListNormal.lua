---@class QuestSeasonListNormal : LuaUIBaseView

local QuestSeasonListNormal = ui("QuestSeasonListNormal")

local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function QuestSeasonListNormal:Ctor()

    self._wtlinePanelBtn = self:Wnd("WBP_CommonButtonV2S1", DFCommonButtonOnly)
    self._wtlinePanelBtn:Event("OnClicked", self._OnClickLinePanelBtn, self)
    
    self._wtCustomWaterFallList = 
        UIUtil.WndWaterfallScrollBox(
        self,
        "DFWaterfallScrollView_93",
        self._OnGetQuestRromptCount,
        self._OnProcessQuestRromptWidget
    )

    self._wtDetailPanel = self:Wnd("WBP_TaskSystem_DetailPanel", UIWidgetBase)
    self._wtAssemblyMapNeed = self:Wnd("WBP_AssemblyMapNeed", UIWidgetBase)

    self._wtActionBtn = self:Wnd("wtCommonButtonV1S1_1", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnClickActionBtn, self)
    self._wtActionBtn:Event("OnDeClicked", self._OnClickActionBtn, self)

    self._wtRewardScrollBox = self:Wnd("wRewardWrapBox", UIScrollBox)

    self._lineInfo = nil
    self._stageID = nil
    self._sortedGroupIDs = nil
    self._groupUnfoldList = nil
    self._questInfo = nil
    self._groupID = nil
    self._selectedGroupItem = nil
    self._groupIDWithDeco = {}
    self._bIsFirstDisplay = true

    self._timerHandle = nil
end

function QuestSeasonListNormal:OnInitExtraData(lineInfo, stageID, questID)

    self:ClearData()
    
    self._lineInfo = lineInfo
    self._stageID = stageID
    self._sortedGroupIDs = {}
    local EQuestSeasonType = Module.Quest.Config.EQuestSeasonType
    table.insert(self._sortedGroupIDs, lineInfo:GetMainGroupIDByStageID(stageID))
    local HardGroups = lineInfo:GetSortedBranchGroupByStageIDAndType(stageID, EQuestSeasonType.Hard)
    for i = 1, #HardGroups do
        table.insert(self._sortedGroupIDs, HardGroups[i])        
    end
    local EasyGroups = lineInfo:GetSortedBranchGroupByStageIDAndType(stageID, EQuestSeasonType.Easy)
    for i = 1, #EasyGroups do
        table.insert(self._sortedGroupIDs, EasyGroups[i])        
    end

    self._groupUnfoldList = {}
    for i = 1, #self._sortedGroupIDs, 1 do
        self._groupUnfoldList[self._sortedGroupIDs[i]] = false
    end

    self._groupIDWithDeco = {}
    if #HardGroups > 0 then
        table.insert(self._groupIDWithDeco, lineInfo:GetMainGroupIDByStageID(stageID))
    end
    if #EasyGroups > 0 then
        table.insert(self._groupIDWithDeco, HardGroups[#HardGroups])
    end


    if questID then
        self._questInfo = Server.QuestServer:GetQuestInfoById(questID)
        self._groupID = lineInfo:GetGroupIDByQuestID(questID)
    else
        self._groupID = self._sortedGroupIDs[1]
    end
    self._wtCustomWaterFallList:RefreshAllItems()
end

function QuestSeasonListNormal:OnShowBegin()
    self:_AddEventListener()
    self:_EnableGamepad()
end

function QuestSeasonListNormal:OnShow()
    if IsHD () then  
        WidgetUtil.SetUserFocusToWidget(self._selectedGroupItem._selectedQuestWidget, true)
    end
end

function QuestSeasonListNormal:_AddEventListener()
    self:AddLuaEvent(Module.Quest.Config.evtQuestSeasonEntryClickedWithGroupInfo, self._OnQuestEntryClicked, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestSeasonHeadClicked, self._OnQuestHeadClicked, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)

    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateObjectiveState, self._UpdateQuestInfos, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._UpdateQuestInfos, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtQuestSubmitItemsSuccess, self._OnQuestSubmitItemsSuccess, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtShowNewQuestInfo, self._OnShowNewQuestInfo, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtPrepareJoinMatch, self.OnPrepareJoinMatch_Safe, self)

end

function QuestSeasonListNormal:OnHide()
    Facade.UIManager:ClearSubUIByParent(self, self._wtRewardScrollBox)
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self:RemoveAllLuaEvent()
    self:_DisableGamepad()
end

function QuestSeasonListNormal:OnClose()

end

function QuestSeasonListNormal:ClearData()
    self._lineInfo = nil
    self._stageID = nil
    self._sortedGroupIDs = nil
    self._groupUnfoldList = nil
    self._questInfo = nil
    self._groupID = nil
    self._selectedGroupItem = nil
    self._groupIDWithDeco = {}
    self._bIsFirstDisplay = true
end

function QuestSeasonListNormal:_OnGetQuestRromptCount()
    return #self._sortedGroupIDs
end

function QuestSeasonListNormal:_OnProcessQuestRromptWidget(position, itemWidget)
    local groupInfo = self._lineInfo:GetGroupInfo(self._sortedGroupIDs[position])
    if groupInfo then

        loginfo("JackieTest: ProcessGroup: ", groupInfo.name, " Widget: ", itemWidget._debugName)

        itemWidget:RefreshWithGroup(self._lineInfo, self._stageID, groupInfo, self._groupUnfoldList[groupInfo.groupID])
        itemWidget:ShowDecoLine(false)

        for key, value in pairs(self._groupIDWithDeco) do
            if groupInfo.groupID == value then
                itemWidget:ShowDecoLine(true)
                break
            end 
        end

        if self._groupID == groupInfo.groupID then
            itemWidget:SetIsSelected(true)
            self._selectedGroupItem = itemWidget
            self._groupID = groupInfo.groupID
            if self._bIsFirstDisplay then
                self._bIsFirstDisplay = false
                self._selectedGroupItem:SetIsUnfold(true)
                self._groupUnfoldList[groupInfo.groupID] = true
                if self._questInfo then
                    self._selectedGroupItem:SelectQuestByID(self._questInfo.id)
                else
                    self._questInfo = self._selectedGroupItem:SelectDefaultQuest()
                end
                self:_UpdateQuestInfos()
                loginfo("JackieTest: ProcessGroup: ", groupInfo.name)
            end
        else
            itemWidget:SetIsSelected(false)
        end
    end
end

function QuestSeasonListNormal:_OnClickLinePanelBtn()
    Module.Quest:OpenQuestSeasonLinePanel(self._lineInfo:GetStageInfoByID(self._stageID), self._questInfo)
end

function QuestSeasonListNormal:_OnClickActionBtn()
    if self._questInfo == nil then
        return
    end
    if self._questInfo.state == QuestState.Unaccepted then
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Completed then
        QuestLogic.DoGetQuestRewards(self._questInfo) --前往领取奖励物品
    elseif self._questInfo.state == QuestState.Failed then
        -- 提交道具
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Accepted and self._questInfo:IsFinishSubmitObjectives() == false then
        local itemStructList, submitDatas, zeroStoredItemList = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
        if table.isempty(itemStructList) then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.noItemsToSubmit) -- 暂时没有可提交的物品
        else
            -- 点击提交物品记录当前打开任务详情页，返回任务界面时候打开
            -- Module.Quest.Field:SetCurQuestDetailInfo(self._questInfo)
            Module.Quest.Field:SetSubmitQuestId(self._questInfo.id)
            Module.Inventory:OpenItemSubmitPanel(
                itemStructList,
                submitDatas,
                zeroStoredItemList,
                QuestLogic.ProcessItemSubmit
            )
        end
    end
end

function QuestSeasonListNormal:_OnQuestEntryClicked(questInfo, questWidget, groupId, groupWidget)
        
    loginfo("JackieTest: QuestSeasonListNormal _OnQuestEntryClicked", questInfo.name)

    if self._questInfo.id == questInfo.id then
        return
    end
    
    self._questInfo = questInfo
    self:_UpdateQuestInfos()
    questWidget:SetIsSelected(true)

    if self._groupID ~= groupId then
        self._selectedGroupItem:SetIsSelected(false)
        self._selectedGroupItem:ClearAllSelection()
        self._selectedGroupItem = groupWidget
        self._groupID = groupId
        self._selectedGroupItem:SetIsSelected(true)
    end

end

function QuestSeasonListNormal:_OnQuestHeadClicked(groupID, groupWidget)

    loginfo("JackieTest: QuestSeasonListNormal _OnQuestHeadClicked")

    groupWidget:ToggleFold()

    self._groupUnfoldList[groupID] = not self._groupUnfoldList[groupID]
    
    if groupID ~= self._groupID then 
        self._selectedGroupItem:SetIsSelected(false)
        self._selectedGroupItem:ClearAllSelection()
        groupWidget:SetIsSelected(true)

        if groupWidget._bIsUnfold then
            local questInfo = groupWidget:SelectDefaultQuest()
            if questInfo.id ~= self._questInfo.id then
                self._questInfo = questInfo
            end
            self:_UpdateQuestInfos()
        end
        self._groupID = groupID
        self._selectedGroupItem = groupWidget
    end

    self._wtCustomWaterFallList:RefreshAllItems()

end

function QuestSeasonListNormal:_UpdateQuestInfos()
    self._wtDetailPanel:Visible()
    self._wtDetailPanel:UpdateInfo(self._questInfo)
    self:_UpdateActionBtn()
    self:_UpdateQuestRewards()
end

function QuestSeasonListNormal:_UpdateQuestRewards()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardScrollBox)
    local allRewardList = self._questInfo:GetRewardList()
    local newList = {}
    for index, value in ipairs(allRewardList) do
        table.insert(newList, value)
    end

    if #newList > 0 then
        for _, rewardItem in ipairs(newList) do
            local weakUIIns, instanceId =
                Module.CommonWidget:CreateIVCommonItemTemplateBySize(self, self._wtRewardScrollBox, nil, 168, 168)
            local rewardItemView = getfromweak(weakUIIns)
            rewardItemView:InitItem(rewardItem)
            -- 调用IVCommonItemTemplate设置详情页上报数据的界面id
            rewardItemView:SetItemDetailFromUIID(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)
            if self._questInfo.state >= QuestState.Rewarded then
                rewardItemView:SetAlreadyGetState()
                rewardItemView:EnableComponent(EComp.GetMask, true)
            else
                rewardItemView:EnableComponent(EComp.GetMask, false)
            end
            rewardItemView:ShowBindingComp(rewardItem.bindType ~= PropBindingType.BindingNotBind)
            rewardItemView.Slot:SetPadding(FMargin(0, 0, 20, 0))
        end
    end
end

function QuestSeasonListNormal:_UpdateActionBtn()
    if self._questInfo == nil then
        return
    end

    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)

    self._wtActionBtn:SetIsEnabledStyle(true)
    if self._questInfo.state == QuestState.Unaccepted or self._questInfo.state == QuestState.Unread 
        or self._questInfo.state == QuestState.Locked then
        if Server.QuestServer:IsQuestAcceptable(self._questInfo) then
            self._wtActionBtn:SetIsEnabled(true)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.accept)
        else
            self._wtActionBtn:SetIsEnabled(false)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.NotUnLock)
            if self._questInfo:GetRemainToAcceptTime() > 0 then
                local desc = ""
                local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self._questInfo:GetRemainToAcceptTime())
                if day > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif hour > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif min > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif sec > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) ..
                        Module.Quest.Config.Loc.CanAccept
                end
                self._wtAssemblyMapNeed:SetType(0)
                self._wtAssemblyMapNeed:SetMainTitle(desc)
                self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Visible)

                self._timerHandle = Timer:NewIns(5, 0)
                self._timerHandle:AddListener(self._UpdateRemainCountTime, self)
                self._timerHandle:Start()
            end
        end
    elseif self._questInfo.state == QuestState.Failed then
        self._wtActionBtn:SetIsEnabled(true)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.restart)
    elseif self._questInfo.state == QuestState.Accepted then
        -- 没有道具目标或提交已完成
        if self._questInfo:IsFinishSubmitObjectives() then
            self._wtActionBtn:SetIsEnabled(false)
            if self._questInfo.bCgQuest then
                self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.watchCgGetReward)
            else
                self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.underWay)
            end
        else
            local itemStructList, submitDatas = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
            self._wtActionBtn:SetIsEnabled(true)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.submit)
        end
    elseif self._questInfo.state == QuestState.Completed then
        self._wtActionBtn:SetIsEnabled(true)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.receive)
    elseif self._questInfo.state == QuestState.Rewarded then
        self._wtActionBtn:SetIsEnabled(false)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.rewardReceived)
    end
end

function QuestSeasonListNormal:_UpdateRemainCountTime()
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self._questInfo:GetRemainToAcceptTime())
    local desc = ""
    if day > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif hour > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif min > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
            Module.Quest.Config.Loc.CanAccept
    else
        desc = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) .. Module.Quest.Config.Loc.CanAccept
        if sec <= 0 then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
            self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)
            return
        end
    end

    self._wtAssemblyMapNeed:SetType(0)
    self._wtAssemblyMapNeed:SetMainTitle(desc)
    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Visible)
end


function QuestSeasonListNormal:_OnQuestStateUpdate(questId)
    if self._questInfo then
        if questId == self._questInfo.id then
            self:_UpdateQuestInfos()
            if self._questInfo.state >= QuestState.Rewarded then
                local postQuests = self._questInfo:GetPostQuests()
                local multiPostQusets = self._questInfo:GetMultiPostQuests()
                local hasPostQuest = false
                if #postQuests > 0 then
                    for index, value in ipairs(postQuests) do
                        if value:IsPreQuestAllFinished() then
                            Module.Quest.Field:CachingNewlyOpenQuestItem(value)
                            hasPostQuest = true
                        end
                    end

                    if hasPostQuest then
                        Module.Reward:OpenQuestDetailNewPanel(Module.Quest.Config.Loc.NewQuestOpen, nil, postQuests)
                    end
                elseif #multiPostQusets > 0 then
                    for index, value in ipairs(multiPostQusets) do
                        if value:IsPreQuestAllFinished() then
                            Module.Quest.Field:CachingNewlyOpenQuestItem(value)
                            hasPostQuest = true
                        end
                    end

                    if hasPostQuest then
                        Module.Reward:OpenQuestDetailNewPanel(
                            Module.Quest.Config.Loc.NewQuestOpen,
                            nil,
                            multiPostQusets
                        )
                    end
                end

                if #postQuests <= 0 or hasPostQuest == false or #multiPostQusets <= 0 then
                    self:_OnShowNewQuestInfo()
                end
            end
        end
    end
end

function QuestSeasonListNormal:_OnShowNewQuestInfo()
    self._bIsFirstDisplay = true
    self._groupID = self._sortedGroupIDs[1]
    self._questInfo = nil
    self._wtCustomWaterFallList:RefreshAllItems()
end

function QuestSeasonListNormal:_OnQuestSubmitItemsSuccess(questId)
    if questId == self._questInfo.id then
        -- 提交物品成功时刷新提交界面
        local submitPanelHandle = Module.Inventory:GetItemSubmitPanel()
        if submitPanelHandle then
            submitPanelHandle:GetUIIns():ItemSubmitedSuccessUpdate(QuestLogic.Get3ItemSubmitInfos(self._questInfo))
        end
    end
end

function QuestSeasonListNormal:OnPrepareJoinMatch_Safe()
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.QuestDetailOpen)
end

-------- Gamepad ------------

function QuestSeasonListNormal:_EnableGamepad()
    if IsHD() then
        
        self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtCustomWaterFallList, self, "Hittest")
        if self._wtNavGroup1 then
            self._wtNavGroup1:AddNavWidgetToArray(self._wtCustomWaterFallList)
            self._wtNavGroup1:SetScrollRecipient(self._wtCustomWaterFallList)
            -- WidgetUtil.WrapNavBoundary(self._wtNavGroup1, {EUINavigation.Up, EUINavigation.Down})
            WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup1, self._WaterfallListBoundaryRule, self)
        end

        self._wtNavGroup3 = WidgetUtil.RegisterNavigationGroup(self._wtRewardScrollBox, self, "Hittest")
        if self._wtNavGroup3 then
            self._wtNavGroup3:AddNavWidgetToArray(self._wtRewardScrollBox)
        end
        self:_InitShortcuts()
    end
end

function QuestSeasonListNormal:_DisableGamepad()
    if IsHD() then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup1 = nil
        self._wtNavGroup3 = nil
        self:_RemoveShortcuts()
    end
end

function QuestSeasonListNormal:_InitShortcuts()
    -- 打开任务总览
    if self._PressAllQuestButton == nil then 
        self._PressAllQuestButton = self:AddInputActionBinding("QuestDetail_OpenAllQuest", EInputEvent.IE_Pressed, self._OnClickLinePanelBtn,self, EDisplayInputActionPriority.UI_Stack)
        self._wtlinePanelBtn:SetDisplayInputAction("QuestDetail_OpenAllQuest", true, nil, true)
    end

	-- 行为按钮（领取任务/奖励/提交/重新开始）
    if self._PressActionButton == nil then
        self._PressActionButton = self:AddInputActionBinding("QuestDetail_Action", EInputEvent.IE_Pressed, self._OnClickActionBtn,self, EDisplayInputActionPriority.UI_Stack)
        self._wtActionBtn:SetDisplayInputAction("QuestDetail_Action", true, nil, true)
    end

end

function QuestSeasonListNormal:_RemoveShortcuts()
    if self._PressAllQuestButton then
        self:RemoveInputActionBinding(self._PressAllQuestButton)
        self._PressAllQuestButton= nil
    end
	if self._PressActionButton then
        self:RemoveInputActionBinding(self._PressActionButton)
        self._PressActionButton= nil
    end
end

function QuestSeasonListNormal:_WaterfallListBoundaryRule(direction)
    if direction == EUINavigation.Up then
        local lastIdx = #self._sortedGroupIDs
        self._wtCustomWaterFallList:ScrollToIndex(lastIdx)
        local item = self._wtCustomWaterFallList:GetItemByIndex(lastIdx)
        if item._bIsUnfold then
            return item:GetLastItem()
        else
            return item            
        end
    elseif direction == EUINavigation.Down then
        self._wtCustomWaterFallList:ScrollToStart()
        local item = self._wtCustomWaterFallList:GetItemByIndex(1)
        loginfo("JackieTest: BoundaryRule: ", item._wtHead._debugName, "QuestInfo: ", item._groupInfo.name)
        return item._wtHead
    else
        return nil
    end
end


return QuestSeasonListNormal