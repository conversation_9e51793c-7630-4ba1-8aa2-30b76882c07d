local FeatureBase = require "DFM.Business.DataStruct.ItemStruct.FeatureBase"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"

---@class EquipmentFeature : FeatureBase
local EquipmentFeature = class("EquipmentFeature", FeatureBase)

local LOW_DURABILITY_PERCENT = 0.3
EquipmentFeature.MIN_CAN_REPAIR_DURABILITY = 10
function EquipmentFeature:Ctor()
    self._featureType = EFeatureType.Equipment

    self.originDurability = -1
    self.curDurability = 0.0
    self.maxDurability = 0.0
    self.maxDurabilityOriginal = 0
    self.totalCapacity = 0

    self.curArmorHealth = 0
    self.maxArmorHealth = 0
    self.materialText = ""
    self.maxArmorValue = 0
    self.ArmorLevel = 0
    self.ricochetText = ""
end

function EquipmentFeature:_DurabilityFormat()
    if self.curDurability and self.curDurability ~= 0 then
        self.curDurability = MathUtil.GetPreciseDecimal(self.curDurability, 1)
    end
    if self.maxDurability and self.maxDurability ~= 0 then
        self.maxDurability = MathUtil.GetPreciseDecimal(self.maxDurability, 1)
    end
end

function EquipmentFeature:InitFromItemId(id)
    FeatureBase.InitFromItemId(self, id)

    self:_InitFromEquipmentTable(id)
    self:_InitFromPermissionItemTable(self._id)
end

---@param propData pb_PropInfo
function EquipmentFeature:InitFromProp(propData)
    FeatureBase.InitFromProp(self, propData)
    self:_InitFromEquipmentTable(self._id)
    local durabilityGidMap = Server.SettlementServer:GetDurabilityGidMap()
    if durabilityGidMap then
        for _, v in pairs(durabilityGidMap) do
            if v.prop.gid == self._gid then
                self.originDurability = v.origin_health
            end
        end
    end
    self.curDurability = propData.health or self.curDurability
    self.maxDurability = propData.health_max or self.maxDurability
    self:UpdateArmorInfo()
    self.curArmorHealth = self.maxArmorValue --局外
    if self:IsKeyChain() or self:IsSafeBox() then
        self._expireTimes = propData.expire_timestamp
    end
    self:_DurabilityFormat()
end

function EquipmentFeature:InitFromCppInfo(cppInfo)
    FeatureBase.InitFromCppInfo(self, cppInfo)
    self:_InitFromEquipmentTable(self._id)
    if cppInfo then
        self.curDurability = cppInfo.ItemDurability or 0.0
        self.maxDurability = cppInfo.ItemDurabilityMax or 0.0
        self.curArmorHealth = math.floor(cppInfo.ItemArmorHealth) or 0
        self.maxArmorHealth = math.floor(cppInfo.ItemArmorHealthMax) or 0
    end
    self:UpdateArmorInfo()
    self:_DurabilityFormat()
end
function EquipmentFeature:IsDamaged()
    if self._itemSubType == EEquipmentType.BreastPlate then
        local bodyArmorInfo = ItemConfigTool.GetBodyArmorFunctionById(self._id)
        if bodyArmorInfo then
            if self.curDurability <= (bodyArmorInfo.MaxDurability * bodyArmorInfo.DamagedDurabilityRate) then
                return true
            end
        end
       
    elseif self._itemSubType == EEquipmentType.Helmet then
        local helmetArmorInfo = ItemConfigTool.GetHelmetArmorFunctionById(self._id)
        if helmetArmorInfo then
            if self.curDurability <= (helmetArmorInfo.MaxDurability * helmetArmorInfo.DamagedDurabilityRate) then
                return true
            end
        end
    end
    return false
end
function EquipmentFeature:IsCantRepair()
    if self._itemSubType == EEquipmentType.BreastPlate then
        local bodyArmorInfo = ItemConfigTool.GetBodyArmorFunctionById(self._id)
        if bodyArmorInfo then
            if self.maxDurability <= (bodyArmorInfo.MaxDurability * bodyArmorInfo.DamagedDurabilityRate) then
                return true
            end
        end
       
    elseif self._itemSubType == EEquipmentType.Helmet then
        local helmetArmorInfo = ItemConfigTool.GetHelmetArmorFunctionById(self._id)
        if helmetArmorInfo then
            if self.maxDurability <= (helmetArmorInfo.MaxDurability * helmetArmorInfo.DamagedDurabilityRate) then
                return true
            end
        end
    end
    return false
end
--耐久度变更后更新护甲值和防护等级信息
function EquipmentFeature:UpdateArmorInfo()
    if self._itemSubType == EEquipmentType.BreastPlate then
        local bodyArmorInfo = ItemConfigTool.GetBodyArmorFunctionById(self._id)
        if self:IsDamaged() and bodyArmorInfo then
            self.ArmorLevel = bodyArmorInfo.DamageArmorLevel
            self.maxArmorValue = bodyArmorInfo.DamageMaxArmorValue
           
            self.maxArmorHealth = self.maxArmorValue
        end
    elseif self._itemSubType == EEquipmentType.Helmet then
        local helmetArmorInfo = ItemConfigTool.GetHelmetArmorFunctionById(self._id)
        if self:IsDamaged() and helmetArmorInfo then
            self.ArmorLevel = helmetArmorInfo.DamageArmorLevel
			self.headDamagesReduction = helmetArmorInfo.DamageHeadDamagesReduction
        end
    end

    if self.curDurability == 0 then
        -- self.ArmorLevel  = 0
        self.headDamagesReduction  = 0
        self.curArmorHealth = 0 
        self.maxArmorHealth = 0
        self.maxArmorValue = 0
    end
    
end
function EquipmentFeature:_InitFromEquipmentTable(id)
    self.protectionBones = {} -- 待废弃
    self.protectionPartText = ""

    self.speedReduction = 0
    self.gainEquipPosLength = 0
    self.gainEquipPosWidth = 0
    self.totalCapacity = 0
    self.bagCapacityExtension = 0
    self.headDamagesReduction = 0

    -- 胸甲
    if self._itemSubType == EEquipmentType.BreastPlate then
        local bodyArmorInfo = ItemConfigTool.GetBodyArmorFunctionById(id)
        if bodyArmorInfo then
            self.maxArmorValue = bodyArmorInfo.MaxArmorValue
            self.curArmorHealth = self.maxArmorValue
            self.maxArmorHealth = self.maxArmorValue
            self.materialText = bodyArmorInfo.MaterialText
            self.curDurability = bodyArmorInfo.MaxDurability
            self.maxDurability = bodyArmorInfo.MaxDurability
            self.maxDurabilityOriginal = bodyArmorInfo.MaxDurability
            self.protectionPartText = bodyArmorInfo.ProtectionPartText
            self.ArmorLevel = bodyArmorInfo.ArmorLevel
            self.durabilityReduction = bodyArmorInfo.DurabilityReductionRepair
            self.repairPricePer = bodyArmorInfo.RepairPricePerDurability
            self.baseRepairPrice = bodyArmorInfo.BaseRepairPrice
            self.damagedDurability = bodyArmorInfo.DamagedDurabilityRate
        else
            self.maxArmorValue = 0
            self.curArmorHealth = 0
            self.maxArmorHealth = 0
            self.materialText = ""
            self.curDurability = 0.0
            self.maxDurability = 0.0
            self.maxDurabilityOriginal = 0
            self.protectionPartText = ""
            self.ArmorLevel = 0
            self.durabilityReduction = 0
            self.repairPricePer = 0
            self.baseRepairPrice = 0
            self.damagedDurability = 0
        end
        -- 头盔
    elseif self._itemSubType == EEquipmentType.Helmet then
        local helmetArmorInfo = ItemConfigTool.GetHelmetArmorFunctionById(id)
        if helmetArmorInfo then
            self.headDamagesReduction = helmetArmorInfo.HeadDamagesReduction
            self.ricochet = helmetArmorInfo.Ricochet
            self.ricochetDamage = helmetArmorInfo.RicochetDamage
            self.ricochetText = helmetArmorInfo.RicochetText
            self.soundReduction = helmetArmorInfo.SoundReduction
            self.soundReductionText = helmetArmorInfo.SoundReductionText
            self.materialText = helmetArmorInfo.MaterialText
            self.curDurability = helmetArmorInfo.MaxDurability
            self.maxDurability = helmetArmorInfo.MaxDurability
            self.maxDurabilityOriginal = helmetArmorInfo.MaxDurability
            self.protectionPartText = helmetArmorInfo.ProtectionPartText
            self.ArmorLevel = helmetArmorInfo.ArmorLevel
            self.durabilityReduction = helmetArmorInfo.DurabilityReductionRepair
            self.repairPricePer = helmetArmorInfo.RepairPricePerDurability
            self.baseRepairPrice = helmetArmorInfo.BaseRepairPrice
            self.damagedDurability = helmetArmorInfo.DamagedDurabilityRate
        else
            self.headDamagesReduction = 0
            self.ricochet = 0
            self.ricochetDamage = 0
            self.ricochetText = ""
            self.soundReduction = 0
            self.soundReductionText = ""
            self.materialText = ""
            self.curDurability = 0.0
            self.maxDurability = 0.0
            self.maxDurabilityOriginal = 0
            self.protectionPartText = ""
            self.ArmorLevel = 0
            self.durabilityReduction = 0
            self.repairPricePer = 0
            self.baseRepairPrice = 0
            self.damagedDurability = 0
        end
    else
        -- 装备信息
        -- local length, height, capacity = ItemHelperTool.GetContainerItemSizeCfg(id)
        -- self.gainEquipPosLength = length
        -- self.gainEquipPosWidth = height
        -- self.totalCapacity = capacity

        local spaceData = ItemBaseTool.GetSpaceDataByEquipmentId(id)
        if spaceData then
            self.gainEquipPosLength = spaceData.Length
            self.gainEquipPosWidth = spaceData.Height
            self.totalCapacity = spaceData.TotalCapacity
        end
        
        self.bagCapacityExtension = ItemHelperTool.GetBagCapacityExtendValue(id)
    end

    -- 维修信息
    -- self.bHasRepairConfig = false
    -- if self.bRepairable then
    --     self.repairInfo = ItemConfigTool.GetRepairInfoById(self._id)
    --     if self.repairInfo then
    --         self.healthMaxReducePerPoint = self.repairInfo.HealthMaxReducePerPoint
    --         self.repairCastCurrency = self.repairInfo.RepairCastCurrency -- 消耗的货币类型
    --         self.repairCast = self.repairInfo.RepairCast
    --         self.minRepairHealth = self.repairInfo.MinRepairHealth
    --         self.bHasRepairConfig = true

    --         self.consumeProps = {}
    --         local propIdKey, propNumKey, propId, propNum
    --         for i = 1, 3 do
    --             propIdKey = "ConsumeProp" .. i .. "ID"
    --             propNumKey = "ConsumeProp" .. i .. "Num"

    --             propId = self.repairInfo[propIdKey]
    --             propNum = self.repairInfo[propNumKey]

    --             if propId > 0 and propNum > 0 then
    --                 self.consumeProps[propId] = propNum
    --             end
    --         end
    --     else
    --         LogUtil.LogInfo("Item repair info not found, check id: "..self._id)
    --         self.healthMaxReducePerPoint = 0
    --         self.repairCastCurrency = 0
    --         self.repairCast = 0
    --         self.minRepairHealth = 0

    --         self.consumeProps = {}
    --     end
    -- end
    self:_DurabilityFormat()
end

function EquipmentFeature:_InitFromPermissionItemTable(id)
    -- 入局时不加载
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.LobbyToGame or curGameFlow == EGameFlowStageType.Game then
        return
    end
    if self._itemSubType == EEquipmentType.SafeBox or self._itemSubType == EEquipmentType.KeyChain then
        local permissionInfoCfg = ItemHelperTool.FindPermissonInfo(id)
        if permissionInfoCfg then
            self._permissionId = permissionInfoCfg.ID
            self._permissionName = permissionInfoCfg.Name
            self._type = permissionInfoCfg.Type
            self._slot = permissionInfoCfg.Slot
            self._isValid = permissionInfoCfg.IsValid
            self._horizontalLength = permissionInfoCfg.HorizontalLength
            self._vertialLength = permissionInfoCfg.VerticalLengt
            self._totalLength = permissionInfoCfg.TotalLength
            self._price = permissionInfoCfg.Price
            self._externalItemNumber = permissionInfoCfg.ExternalItemNumber
            self.currencyType = permissionInfoCfg.Currency
            self._isFree =  permissionInfoCfg.IsFree
            self._maxTime = permissionInfoCfg.MaxTime
            self._itemID = permissionInfoCfg.VirtualItemID
        end
    end
end

-----------------------------------------------------------------------
--region Public API
-- 头盔
function EquipmentFeature:IsHelmet()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.Helmet
end

-- 胸甲
function EquipmentFeature:IsBreastPlate()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.BreastPlate
end

function EquipmentFeature:IsBag()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.Bag
end

--- 是否为胸挂
function EquipmentFeature:IsChestHanging()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.ChestHanging
end

-- 钥匙包
function EquipmentFeature:IsKeyChain()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.KeyChain
end

function EquipmentFeature:IsSafeBox()
    return self._itemMainType == EItemType.Equipment and self._itemSubType == EEquipmentType.SafeBox
end

function EquipmentFeature:IsPermissionItem()
    return self:IsKeyChain() or self:IsSafeBox()
end

-- function EquipmentFeature:IsRepairableItem()
--     return self.bRepairable and self.maxDurability >= self.minRepairHealth
-- end

function EquipmentFeature:GetDurabilityAfterRepair()
    -- if not self:IsRepairableItem() then
    --     return -1
    -- end

    -- local reduction = self.healthMaxReducePerPoint * (self.maxDurability - self.curDurability)
    return math.floor(self.maxDurability - self.durabilityReduction)
end

function EquipmentFeature:GetDurabilityPercent()
    if self.maxDurability == 0 then
        return 0
    else
        return self.curDurability / self.maxDurability
    end
end

function EquipmentFeature:IsLowDurability()
    local percent = self:GetDurabilityPercent()
    if percent == 0 then
        return false
    else
        return percent <= LOW_DURABILITY_PERCENT
    end
end

function EquipmentFeature:IsDurabilityFull()
    return self.curDurability == self.maxDurability
end

-- function EquipmentFeature:GetRepairCost(repairValue)
--     return math.floor(self.repairCast * repairValue)
-- end

--- 该道具是否为容器类型
---@return boolean
function EquipmentFeature:IsContainerItem()
    return self._itemMainType == EItemType.Equipment
        and ((self._itemSubType >= EEquipmentType.ChestHanging and self._itemSubType <= EEquipmentType.SafeBox) or
            self._itemSubType == EEquipmentType.KeyChain)
end

function EquipmentFeature:IsPlayerEquipment()
    return self._itemMainType == EItemType.Equipment
end

function EquipmentFeature:IsDurabilityItem()
    return self.maxDurability > 0
end

-- 拿耐久的当前值和初始最大值
function EquipmentFeature:GetDurabilityValueOriginal()
    return self.curDurability, self.maxDurabilityOriginal
end

-- 拿耐久的当前值和最大值
function EquipmentFeature:GetDurabilityValue()
    return self.curDurability, self.maxDurability
end

-- 拿耐久的当前值
function EquipmentFeature:GetDurabilityCurValue()
    return self.curDurability
end

-- 拿耐久的最大值
function EquipmentFeature:GetDurabilityMaxValue()
    return self.maxDurability
end

function EquipmentFeature:GetDurabilityRatio()
    if self.maxDurability <= 0 then
        return 0
    end
    return math.clamp(self.curDurability / self.maxDurability, 0, 1)
end

--- 是否为扩容类道具
function EquipmentFeature:IsExtendItem()
    return self._itemMainType == EItemType.ExtendItem and self._itemSubType ~= 11
end

function EquipmentFeature:GetContainerSize()
    if self:IsContainerItem() or self:IsExtendItem() then
        return self.totalCapacity
    elseif self:IsExtendItem() then
        return self.gainEquipPosLength * self.gainEquipPosWidth
    else
        return 0
    end
end

-- 胸挂、背包是行数和每行固定格数
-- 其他容器还是自定义宽高
function EquipmentFeature:GetRealLengthAndHeight()
    if self._itemSubType == EEquipmentType.Bag or self._itemSubType == EEquipmentType.ChestHanging then
        return ItemConfig.DEFAULT_BAG_SPACE_WIDTH, self.bagCapacityExtension
    else
        return self.gainEquipPosLength, self.gainEquipPosWidth
    end
end

function EquipmentFeature:GetGainEquipCapacity()
    if self._itemSubType == EEquipmentType.Bag or self._itemSubType == EEquipmentType.ChestHanging then
        return ItemConfig.DEFAULT_BAG_SPACE_WIDTH * self.bagCapacityExtension
    else
        return self.gainEquipPosLength * self.gainEquipPosWidth
    end
end

function EquipmentFeature:GetMaxArmorValue()
    return self.maxArmorValue
end

function EquipmentFeature:GetHeadDamagesReduction()
    return self.headDamagesReduction
end

function EquipmentFeature:GetOriginDurability()
    if self.originDurability ~= -1 then
        return self.originDurability
    end
end

-- 获取权益道具过期时间
function EquipmentFeature:GetItemExpiredTime()
    return self._expireTimes
end

-- 获取权益道具过期状态
function EquipmentFeature:GetExpiredStatus()
    if self._expireTimes then
        return self._expireTimes > 0 and TimeUtil.GetServerRemainTime2Seconds(self._expireTimes) == 0
    end
end

-- 权益道具是否免费
function EquipmentFeature:PermissionItemIsFree()
    if self._expireTimes then
        return self._expireTimes < 0
    end
end

-- 权益道具是否可使用
function EquipmentFeature:PermissionCanUse()
    if not self._expireTimes then
        return false
    end
    return (self:PermissionItemIsFree() or not self:GetExpiredStatus()) and not self:CheckNetBarSafeBoxIsInvalid()
end

-- 道具是否未过期（包括永久道具）
function EquipmentFeature:PermissionNotExpired()
    return self._expireTimes == nil or not self:PermissionCanUse()
end

-- 可否续费(MS23临时处理)
function EquipmentFeature:CanRenewal()
    if self:PermissionItemIsFree() then
        return false
    end
    local canRenewal = tonumber(self.currencyType) == ECurrencyItemId.UnBindBankNote
    local remainTime = self._expireTimes and TimeUtil.GetServerRemainTime2Seconds(self._expireTimes) or 0
    local isMaxTime = self._maxTime * 24 * 3600 - remainTime > 604800
    return canRenewal and isMaxTime
end

-- 是否是网吧安全箱
function EquipmentFeature:IsNetBarSafeBox()
    return self._id == ***********
end

-- 网吧安全箱是否无效
function EquipmentFeature:CheckNetBarSafeBoxIsInvalid()
    if self:IsNetBarSafeBox() then
        return Server.RoleInfoServer:GetNetBarPrivilegeLevel() < 4
    end
end

--endregion
-----------------------------------------------------------------------

return EquipmentFeature
