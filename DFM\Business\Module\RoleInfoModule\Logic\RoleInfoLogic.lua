----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------


local RoleInfoField = require("DFM.Business.Module.RoleInfoModule.RoleInfoField")
local AdCodeTable   = Facade.TableManager:GetTable("RankAreaConfig")

local RoleInfoLogic = {}

RoleInfoLogic.CalcRate = function(molecular, denominator)
    local survivalRate = 0
    if denominator ~= 0 then --避免除0
        survivalRate = math.modf((molecular / denominator) * 100)
    end
    survivalRate = survivalRate .. "%"
    return survivalRate
end
RoleInfoLogic.InteralGetSingleRoleInfoQualityCardValue = function(cardId)
    local allCardsList = Module.RoleInfo.Field:GetSortedListByCardID(Server.RoleInfoServer.qualityCardList)
    local returnValue = -1
    if next(allCardsList) then
        for _, card in pairs(allCardsList) do
            if card.card_id == cardId then
                returnValue = card.quality_value
            end
        end
    else
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardValue allCardsList = nil!")
    end
    if returnValue == -1 then
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardValue returnValue = nil!")
    end
    return returnValue
end
RoleInfoLogic.InteralGetSingleRoleInfoQualityCardName = function(cardId)
    local name = ""
    local cardTable = Module.RoleInfo.Config.CardTable
    if cardTable then
        for i, row in pairs(cardTable) do
            if row.QualityID == cardId then
                name = row.Name
            end
        end
    else
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardName cardTable = nil!")
    end
    if name == "" then
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardName name = nil!")
    end
    return name
end
RoleInfoLogic.InteralGetSingleRoleInfoQualityCardImgPath = function(cardId)
    local path = ""
    local cardTable = Module.RoleInfo.Config.CardTable
    if cardTable then
        for i, row in pairs(cardTable) do
            if row.QualityID == cardId then
                path = row.ImgPath
            end
        end
    else
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardImgPath cardTable = nil!")
    end
    if path == "" then
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardImgPath path = nil!")
    end
    return path
end
RoleInfoLogic.InteralGetSingleRoleInfoQualityCardSpritePath = function(cardId)
    local path = ""
    local cardTable = Module.RoleInfo.Config.CardTable
    if cardTable then
        for i, row in pairs(cardTable) do
            if row.QualityID == cardId then
                path = row.SpritePath
            end
        end
    else
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardSpritePath cardTable = nil!")
    end
    if path == "" then
        logerror("[xxww] RoleInfoLogic.InteralGetSingleRoleInfoQualityCardSpritePath path = nil!")
    end
    return path
end

RoleInfoLogic.ShowAchevementTips = function(achievementList, tipsAnchor)
    local function loadFinCall(uiIns)
        if isvalid(tipsAnchor) then
            tipsAnchor:BindTipsWidget(uiIns)
        else
            UIUtil.AttachWidgetToMouse2(uiIns, nil, FVector2D(10, 10)) --临时处理
        end
    end

    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.AchievementListTips, loadFinCall, nil, achievementList)
    return handle
end

RoleInfoLogic.GetRoleInfoNumberStr = function(num)
    num = setdefault(num, 0)
    local flag = ""
    local numStr = tostring(num)
    if num >= 1000000000 then
        num = MathUtil.GetNumberFormatStr(num / 1000000000, 1)
        flag = 'B'
    elseif num >= 1000000 then
        num = MathUtil.GetNumberFormatStr(num / 1000000, 1)
        flag = 'M'
    elseif num >= 10000 then
        num = MathUtil.GetNumberFormatStr(num / 1000, 1)
        flag = 'K'
    else
        num = math.floor(num)
    end
    numStr = tostring(num) .. flag
    return numStr
end


RoleInfoLogic.IsInMp = function()
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local bNewHall = Module.BattlefieldEntry:GetEnableNewHallModule()
    -- mp大厅
    if bNewHall and curGameFlow == EGameFlowStageType.Lobby then
        return true
    end
    return false
end

RoleInfoLogic.SortAchievement = function(achevementList)
    local function SortList(info1, info2)
        local level1 = Module.Achievement:GetAchievementOrderByID(info1.id)
        local level2 = Module.Achievement:GetAchievementOrderByID(info2.id)
        return level1 < level2
    end

    table.sort(achevementList, SortList)
end

RoleInfoLogic.DamageNumber = function(num)
    return MathUtil.GetNumPrecisionOne(num)
end


function RoleInfoLogic.TranslateTitleName(avatarName, adCode, ranking)
    if not avatarName then
        logerror("GetRealTitleName avatarName or adCode is nil")
        return ""
    end

    local regionName = nil

    if adCode then
        if adCode == 0 then
            if ranking > 0 then -- ranking required
                regionName = Module.RoleInfo.Config.Loc.TitleRegionServer
            end
        else
            local adCodeInfo = AdCodeTable[adCode]
            if not adCodeInfo then
                logerror("CommonSocialTitleItem_Big adCodeInfo is nil, adCode:", adCode)
            else
                if adCodeInfo.OpenRankType == 1 then -- Province
                    regionName = adCodeInfo.Province
                elseif adCodeInfo.OpenRankType == 2 then -- City
                    regionName = adCodeInfo.City
                end
            end
        end
    end

    local titleName = ""
    if regionName == nil then
        logwarning("GetRealTitleName regionName is nil")
        titleName = avatarName
    else
        titleName = regionName .. avatarName
    end

    return titleName
end

RoleInfoLogic.InteralCheckPrisonMap = function(match_mode_id)
    local config = Module.RoleInfo.Config
    return match_mode_id == config.PrisonLowMapModeId or match_mode_id == config.PrisonMediumMapModeId or
        match_mode_id == config.PrisonHighMapModeId
end

RoleInfoLogic.GetResultStr = function(gameResult, matchModeId)
    if RoleInfoLogic.InteralCheckPrisonMap(matchModeId) then
        local Loc = Module.Settlement.Config.Loc

        if gameResult == EGspPlayerResultType.kGspPlayerResultEscaped then
            return Loc.EscapePrisonSuccessTXT
        elseif gameResult == EGspPlayerResultType.kGspPlayerResultKilled then
            return Loc.EscapePrisonFailTXT
        elseif gameResult == EGspPlayerResultType.kGspPlayerResultMissing then
            return Loc.EscapePrisonTimeOutTXT
        end
    end
    return Module.Friend.Config.SolResult[gameResult]
end

RoleInfoLogic.OpenTitleUnlockPop = function()
    local function IsHaveWeekUnlockTitle()
        local titleTable = Server.RoleInfoServer:GetTitleTbl()
        for _, value in ipairs(titleTable) do
            if (value.UnlockPopType ~= nil and value.UnlockPopType == 1) and value.Islock then
                return true
            end
        end
        return false
    end

    local bHave = IsHaveWeekUnlockTitle()
    logerror('RoleInfoLogic OpenTitleUnlockPop ' .. (bHave and 1 or 0))

    local function CallBack(last_weekly_reward_time)
        local weekTime = 7 * 24 * 3600
        local curTime = Facade.ClockManager:GetLocalTimestamp()
        if last_weekly_reward_time > 0 then
            if curTime - last_weekly_reward_time < weekTime then
                Facade.UIManager:AsyncShowUI(UIName2ID.TitleUnlockPop)
            end
        end
    end

    if bHave then
        Server.RoleInfoServer:GetRankCheckPopUpWindow(CallBack)
    end
end

return RoleInfoLogic
