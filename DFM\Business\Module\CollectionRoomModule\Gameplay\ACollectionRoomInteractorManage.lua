----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"

---@class ACollectionRoomInteractorManage : ACollectionRoomInteractorBase
local ACollectionRoomInteractorManage = class("ACollectionRoomInteractorManage", ACollectionRoomInteractorBase)

function ACollectionRoomInteractorManage:Ctor()
    self:log("Ctor")
    self.interactorType = Module.IrisSafeHouse.Config.EInteractorType.CollectionRoomManage
end

function ACollectionRoomInteractorManage:Imp_ReceiveBeginPlay()
    ACollectionRoomInteractorBase.Imp_ReceiveBeginPlay(self)
    Module.MusicPlayer:SetLobbyMusicPlayerPos(self:K2_GetActorLocation())
end

function ACollectionRoomInteractorManage:StartInteract()
    CollectionRoomLogic.ShowMainPanel(nil, ECollectionRoomMainPanelEnterFrom.Interactor)
end

function ACollectionRoomInteractorManage:StartSecondaryInteract()
    Facade.UIManager:AsyncShowUI(UIName2ID.MusicPlayerListPop)
end

return ACollectionRoomInteractorManage