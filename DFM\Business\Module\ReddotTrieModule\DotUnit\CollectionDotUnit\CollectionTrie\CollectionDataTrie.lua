----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



local ReddotDataTrie = require "DFM.Business.Module.ReddotTrieModule.ReddotBase.ReddotDataTrie"
local CollectionManifest = require "DFM.Business.Module.ReddotTrieModule.DotUnit.CollectionDotUnit.Defination.CollectionManifest"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

local CollectionDataTrie = ReddotDataTrie:New(EReddotTrieObserverType.Collection)

function CollectionDataTrie:InitTrie()
    self:GenerateSubTrie(CollectionManifest)
end
--建议在函数中对id做判空,以防阻塞创建数据节点
CollectionDataTrie.CheckCollectionUnlockList = function()
    return CollectionDataTrie.CheckCollectionUnlockListByMainTab()
end

CollectionDataTrie.CheckCollectionUnlockListByMainTab = function(reddotTreeMainId)
    local CollectionTabConfig = Facade.TableManager:GetTable("Collection/CollectionTab")
    local propRedDotsMap = Server.CollectionServer:GetPropRedDots()
    local taskRedDotsMap = Server.CollectionServer:GetTaskRedDots()
    local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
    if not reddotTreeMainId then
        for key, tabRow in pairs(CollectionTabConfig) do
            if tabRow.SubTabId == 0 and tabRow.bHide ~= 1 then
                for propid, gidsMap in pairs(propRedDotsMap) do
                    if table.nums(gidsMap) > 0 then
                        local itemMainType = ItemHelperTool.GetMainTypeById(propid)
                        local itemSubType = ItemHelperTool.GetSubTypeById(propid)
                        if table.contains(tabRow.MainTabToMainTypes, itemMainType) and table.contains(tabRow.MainTabToSubTypes, itemSubType) then
                            return true
                        end
                    end
                end
                if tabRow.MainTabId == 8 then
                    for taskId, value in pairs(taskRedDotsMap) do
                        if value == true then
                            return true
                        end
                    end
                    for taskId, value in pairs(newArrivedTaskRedDotsMap) do
                        if value == true then
                            return true
                        end
                    end
                end
            end
        end
    else
        local mainTabConfigRow
        for key, tabRow in pairs(CollectionTabConfig) do
            if tabRow.MainTabId == reddotTreeMainId and tabRow.bHide ~= 1 then
                if tabRow.SubTabId == 0 then
                    mainTabConfigRow = tabRow
                end
            end
        end
        if mainTabConfigRow then
            for propid, gidsMap in pairs(propRedDotsMap) do
                if mainTabConfigRow.MainTabId == 8 then
                    local skinTaskInfo = Server.CollectionServer:GetSkinTaskInfoBySkinId(propid)
                    if skinTaskInfo then
                        if skinTaskInfo.MP_TaskInfo.bFinished or skinTaskInfo.SOL_TaskInfo.bFinished then
                            return true
                        end
                    end
                elseif table.nums(gidsMap) > 0 then
                    local itemMainType = ItemHelperTool.GetMainTypeById(propid)
                    local itemSubType = ItemHelperTool.GetSubTypeById(propid)
                    if table.contains(mainTabConfigRow.MainTabToMainTypes, itemMainType) and table.contains(mainTabConfigRow.MainTabToSubTypes, itemSubType) then
                        return true
                    end
                end
            end
            if mainTabConfigRow.MainTabId == 8 then
                for taskId, value in pairs(taskRedDotsMap) do
                    if value == true then
                        return true
                    end
                end
                for taskId, value in pairs(newArrivedTaskRedDotsMap) do
                    if value == true then
                        return true
                    end
                end
            end
        end
    end
    return false
end

CollectionDataTrie.CheckCollectionUnlockListBySubTab = function(reddotTrieSubId)
    if reddotTrieSubId then
        local propRedDotsMap = Server.CollectionServer:GetPropRedDots()
        for propid, gidsMap in pairs(propRedDotsMap) do
            if reddotTrieSubId > 800 then
                local skinTaskInfo = Server.CollectionServer:GetSkinTaskInfoBySkinId(propid)
                if skinTaskInfo then
                    if reddotTrieSubId == 801 and not skinTaskInfo.bIsMaster then
                        return skinTaskInfo.MP_TaskInfo.bFinished or skinTaskInfo.SOL_TaskInfo.bFinished
                    elseif reddotTrieSubId == 802 and skinTaskInfo.bIsMaster then
                        return skinTaskInfo.MP_TaskInfo.bFinished or skinTaskInfo.SOL_TaskInfo.bFinished
                    end
                end
            end
        end
        if reddotTrieSubId > 800 then
            local taskRedDotsMap = Server.CollectionServer:GetTaskRedDots()
            for taskId, value in pairs(taskRedDotsMap) do
                if taskId < 30000 and value == true then
                    if reddotTrieSubId == 801 and taskId < 20000 then
                        return true
                    elseif reddotTrieSubId == 802 and taskId >= 20000 then
                        return true
                    end
                end
            end
            local newArrivedTaskRedDotsMap = Server.CollectionServer:GetNewArrivedTaskRedDots()
            for taskId, value in pairs(newArrivedTaskRedDotsMap) do
                if taskId < 30000 and value == true then
                    if reddotTrieSubId == 801 and taskId < 20000 then
                        return true
                    elseif reddotTrieSubId == 802 and taskId >= 20000 then
                        return true
                    end
                end
            end
        end
    end
    return false
end

CollectionDataTrie.CheckNewCollectionPendant = function ()
    return Server.CollectionServer:GetNewCollectionPendantFlag()
end

CollectionDataTrie.CheckNewCollectionPendantBySuitID = function (suitID)
    return Server.CollectionServer:GetNewCollectionPendantFlagBySuitID(suitID)
end

CollectionDataTrie.CheckNewPendantSuitActive = function (suitID)
    return Server.CollectionServer:GetNewPendantSuitActive(suitID)
end


return CollectionDataTrie
