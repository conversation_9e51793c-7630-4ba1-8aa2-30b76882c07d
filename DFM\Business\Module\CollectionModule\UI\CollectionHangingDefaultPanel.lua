----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

---@class CollectionHangingDefaultPanel : LuaUIBaseView
local CollectionHangingDefaultPanel = ui("CollectionHangingDefaultPanel")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local CollectionConfig = Module.Collection.Config
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local BattleFieldSelectionDataLogic = require "DFM.Business.Module.ArmedForceModule.Logic.BattleField.BattleFieldSelectionDataLogic"
local ItemMatchConditionTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemMatchConditionTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
ECheckButtonState = import"ECheckButtonState"
local SHOWGUNTYPENUM = 99
function CollectionHangingDefaultPanel:Ctor()
    self._wtCommonPopWin = self:Wnd("wtCommonPopWin", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._wtWeaponTypeDropDown = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self._OnWeaponTypeDropdownChanged)

    self._wtWeaponDropDown = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox_1", self._OnWeaponDropdownChanged)

    self._wtWeaponDropDownMaskBtn = self:Wnd("wtWeaponDropDownMaskBtn", UIButton)
    self._wtWeaponDropDownMaskBtn:Event("OnClicked", self._OnWeaponDropdownClicked, self)

    self._wtCheckBox = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtCheckBox:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)

    self._wtWeaponScrollView = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_46", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._gunTypes = {1, 2, 3, 4, 5, 6, 7}
    self._weaponType = {}
    self._weaponInfo = {}
    self._listWeapon = {}
    self._defaultWeaponMp = {}
    self._chooseWeapon = {}
    self._equipedWeapon = {}
    self.pendantId = 0
    self.pendantGid = 0
    self.selectWeaponId = 0
    self.selectWeaponTypeIndex = 1
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation

function CollectionHangingDefaultPanel:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function CollectionHangingDefaultPanel:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function CollectionHangingDefaultPanel:_EnableGamepadFeature()
    if not IsHD() then
        return
    end

    if not self._wtNavGroup then
        self.NavGroupPanle = self:Wnd("DFCanvasPanel_92", UIWidgetBase)
        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self.NavGroupPanle, self, "Hittest")
        if self._wtNavGroup then
            -- self._wtNavGroup:AddNavWidgetToArray(self._wtWeaponTypeDropDown)
            -- self._wtNavGroup:AddNavWidgetToArray(self._wtWeaponDropDown)
            -- self._wtNavGroup:AddNavWidgetToArray(self._wtCheckBox)
            self._wtNavGroup:AddNavWidgetToArray(self._wtWeaponScrollView)
            self._wtNavGroup:SetScrollRecipient(self._wtWeaponScrollView)
            self._wtNavGroup:MarkIsStackControlGroup();
        end
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
    end

    self._wtCommonPopWin:AddSummaries({"PendantSetDefaultAll","Select"})
    if not self._openWeaponTypeHandle then 
        self._openWeaponTypeHandle = self:AddInputActionBinding("PendantGunType_GamepadUsed", EInputEvent.IE_Pressed, self._OnWeaponTypeDropDownOpen, self, EDisplayInputActionPriority.UI_Pop)
    end
    self._SelectAll = self:AddInputActionBinding("PendantSetDefaultAll", EInputEvent.IE_Pressed, self._OnCheckStateChanged_Pad, self, EDisplayInputActionPriority.UI_Pop)
    -- self._wtCheckBoxIcon = self._wtCheckBox:Wnd("DFCheckBox_Icon", UIWidgetBase)
    -- self._wtCheckBoxIcon:InitByDisplayInputActionName("PendantSetDefaultAll", true, 0, true)
    local wtKeyIconBox = self._wtWeaponTypeDropDown:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
    if wtKeyIconBox then
        wtKeyIconBox:Visible()
        wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
        wtKeyIconBox:InitByDisplayInputActionName("PendantGunType_GamepadUsed", true, 0, true)
    end
    if  self._wtWeaponTypeDropDown.MenuAnchor then
        self._wtWeaponTypeDropDown:Event("PostOnMenuOpenChanged_GamepadUsed",self._OnWeaponTypeDropDownStateChanged,self)
    end
    if  self._wtWeaponDropDown.MenuAnchor then
        self._wtWeaponDropDown:Event("PostOnMenuOpenChanged_GamepadUsed",self._OnDropDownBoxOpenStateChanged,self)
    end
        self._wtCommonPopWin:OverrideGamepadSetting("GunSkin_FilterApply_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default)

end

function CollectionHangingDefaultPanel:_OnWeaponTypeDropDownOpen()
    if self._wtWeaponTypeDropDown:IsMenuShow() then 
        self._wtWeaponTypeDropDown:CloseMenu()
    else
        self._wtWeaponTypeDropDown:OpenMenu()
    end
end

function CollectionHangingDefaultPanel:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    if  self._wtWeaponDropDown.MenuAnchor then
        self._wtWeaponDropDown:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end
    if  self._wtWeaponTypeDropDown.MenuAnchor then
        self._wtWeaponTypeDropDown:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")
    end
    if self._wtNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
    end
    if self._openWeaponTypeHandle then 
        self:RemoveInputActionBinding(self._openWeaponTypeHandle)
        self._openWeaponTypeHandle = nil
    end
    if self._SelectAll then
        self:RemoveInputActionBinding(self._SelectAll)
        self._SelectAll= nil
    end
    self:_RemoveDropDownNavGroup()
    self:_RemoveWeaponTypeDropDownNavGroup()
    self._wtCommonPopWin:RemoveSummaries()
end

function CollectionHangingDefaultPanel:_OnWeaponTypeDropDownStateChanged(bOpen)
    if not IsHD() then
        return
    end
    if bOpen then
        self:_RegisterWeaponTypeDropDownNavGroup()
    else
        self:_RemoveWeaponTypeDropDownNavGroup()
    end
end

function CollectionHangingDefaultPanel:_RegisterWeaponTypeDropDownNavGroup()
    if not IsHD() then
        return
    end
    if not self._DropDownListNavGroup then
        if self._wtWeaponTypeDropDown and self._wtWeaponTypeDropDown.ScrollGridBox then
            self._DropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWeaponTypeDropDown.ScrollGridBox, self._wtWeaponTypeDropDown, "Hittest")
        end
 
        if self._DropDownListNavGroup then   
            self._DropDownListNavGroup:AddNavWidgetToArray(self._wtWeaponTypeDropDown.ScrollGridBox)
            self._DropDownListNavGroup:SetScrollRecipient(self._wtWeaponTypeDropDown.ScrollGridBox)
            self._DropDownListNavGroup:MarkIsStackControlGroup();
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._DropDownListNavGroup)
        end
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
end

function CollectionHangingDefaultPanel:_RemoveWeaponTypeDropDownNavGroup()
    if not IsHD() then
        return
    end
    if self._DropDownListNavGroup then
        WidgetUtil.RemoveNavigationGroup(self._wtWeaponTypeDropDown)
        self._DropDownListNavGroup = nil
    end     
end


function CollectionHangingDefaultPanel:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return
    end
    if bOpen then
        self:_RegisterDropDownNavGroup()
    else
        self:_RemoveDropDownNavGroup()
    end
end

function CollectionHangingDefaultPanel:_RegisterDropDownNavGroup()
    if not IsHD() then
        return
    end
    if not self._DropDownListNavGroup then
        if self._wtWeaponDropDown and self._wtWeaponDropDown.ScrollGridBox then
            self._DropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWeaponDropDown.ScrollGridBox, self._wtWeaponDropDown, "Hittest")
        end
 
        if self._DropDownListNavGroup then   
            self._DropDownListNavGroup:AddNavWidgetToArray(self._wtWeaponDropDown.ScrollGridBox)
            self._DropDownListNavGroup:MarkIsStackControlGroup();
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._DropDownListNavGroup)
        end
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
end

function CollectionHangingDefaultPanel:_RemoveDropDownNavGroup()
    if not IsHD() then
        return
    end
    if self._DropDownListNavGroup then
        WidgetUtil.RemoveNavigationGroup(self._wtWeaponDropDown)
        self._DropDownListNavGroup = nil
    end     
end
-- END MODIFICATION

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function CollectionHangingDefaultPanel:OnInitExtraData(id, gid)
    self.pendantId = id
    self.pendantGid = gid
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionHangingDefaultPanel:OnOpen()
    CollectionLogic.ResetGunSkinFilterData()
    self:AddListeners()
end

-- UI监听事件、协议
function CollectionHangingDefaultPanel:AddListeners()
    -- self:AddLuaEvent(CollectionConfig.Events.evtOnGunSkinFilterGunTypeChanged, self._OnGunSkinFilterGunTypeChanged, self)
    -- self:AddLuaEvent(CollectionConfig.Events.evtOnGunSkinFilterWeaponChoiceChanged, self._OnGunSkinFilterWeaponChoiceChanged, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionHangingDefaultPanel:OnClose()
    self:RemoveAllLuaEvent()
    if self._buttonUpHandle then
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
	end
    Facade.UIManager:ClearSubUIByParent(self, self._wtLabelGroupContainer)
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionHangingDefaultPanel:OnShow()
    local list = Server.InventoryServer:GetDefaultWeaponSkins()
    if list then 
        for k,v in pairs(list) do 
            self._defaultWeaponMp[v.weapon_id] = v
        end
    end
    if Facade.GameFlowManager:GetCurrentGameFlow()  == EGameFlowStageType.SafeHouse then 
        list = Server.InventoryServer:GetWeaponSkinSetUp()
        if list then 
            for k,v in pairs(list) do 
                self._defaultWeaponMp[v.weapon_id] = v
            end
        end
    end
    self:RefreshView()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionHangingDefaultPanel:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionHangingDefaultPanel:OnAnimFinished(anim)
end

function CollectionHangingDefaultPanel:RefreshView()
    local btns = self._wtCommonPopWin:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
        {btnText = CollectionConfig.Loc.SetDefault, fClickCallback = self._OnConfirmBtnClicked,fDeClickCallback = self._OnDeConfirmBtnClicked, caller = self, bNeedClose = false,bNeedDeClose = false}
    })
    self._wtConfirmBtn = btns[CommonPopWindows.EHandleBtnType.Confirm]
    self:_RefreshWeaponInfoList()
    self:_OnGunSkinFilterGunTypeChanged()
end

function CollectionHangingDefaultPanel:_OnGunSkinFilterGunTypeChanged()
    local optionText = {}
    local selectedIndex = 1
    if self.selectWeaponTypeIndex > 1 then
        for index, weaponInfo in ipairs(self._weaponInfo) do
            table.insert(optionText, weaponInfo.name)
            if self.selectWeaponId == weaponInfo.id then
                selectedIndex = index
            end
        end
        self._wtWeaponDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        self._wtWeaponDropDownMaskBtn:Collapsed()
    else
        table.insert(optionText, self._weaponInfo[1].name)
        self._wtWeaponDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Disabled)
        if #self._listWeapon >= SHOWGUNTYPENUM then
            self._wtWeaponDropDownMaskBtn:Visible()
        else
            self._wtWeaponDropDownMaskBtn:Collapsed()
        end
    end
    UIUtil.InitDropDownBox(self._wtWeaponDropDown, optionText, {}, selectedIndex-1)
    self.selectWeaponId = self._weaponInfo[selectedIndex].id
    -- CollectionLogic.SetGunSkinFilterWeaponChoice(self._weaponInfo[selectedIndex].id)

    local optionText = {}
    for k,v in ipairs(self._weaponType) do 
        table.insert(optionText, v.name)
    end
    UIUtil.InitDropDownBox(self._wtWeaponTypeDropDown, optionText, {}, self.selectWeaponTypeIndex-1)
end


function CollectionHangingDefaultPanel:_OnGunSkinFilterWeaponChoiceChanged()
    local weaponId = self.selectWeaponId
    for index, info in ipairs(self._weaponInfo) do
        if info.id == weaponId then
            self._wtWeaponDropDown:BP_SetMainTabText(info.name)
            break
        end
    end
end

function CollectionHangingDefaultPanel:_OnWeaponDropdownChanged(position, lastPosition)
    if self.selectWeaponId ~= self._weaponInfo[position+1].id then
        self.selectWeaponId  = self._weaponInfo[position+1].id
        self:_OnGunSkinFilterWeaponChoiceChanged()
        self:_ChooseWeapon(self.selectWeaponId)
    end
end

function CollectionHangingDefaultPanel:_OnWeaponTypeDropdownChanged(position, lastPosition)
    self.selectWeaponTypeIndex = position + 1
    self:_OnGunSkinFilterGunTypeChanged()
    self:_OnGunSkinFilterWeaponChoiceChanged()
    self:_RefreshWeaponList(self._weaponType[self.selectWeaponTypeIndex].id)
    -- if CollectionLogic.GetGunSkinFilterWeaponChoice() ~= self._weaponInfo[position+1].id then
    --     CollectionLogic.SetGunSkinFilterWeaponChoice(self._weaponInfo[position+1].id)
    -- else
    --     self:_OnGunSkinFilterWeaponChoiceChanged()
    -- end
end

function CollectionHangingDefaultPanel:_OnWeaponDropdownClicked()
    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.WeaponFilterHint)
end

function CollectionHangingDefaultPanel:_RefreshWeaponInfoList()
    self._chooseWeapon = {}
    self._equipedWeapon = {}
    self._weaponType = {}
    table.insert(self._weaponType, {
        id = 0,
        name = CollectionConfig.Loc.AnyType
    })
    for key, value in pairs(CollectionConfig.WeaponType2Name) do
        table.insert(self._weaponType, {
            id = key,
            name = value
        })
    end
    table.sort(self._weaponType,function (a,b)
        return a.id < b.id
    end)
    local weaponSeriesList = CollectionLogic.GetWeaponSeriesList()
    self._weaponInfo = {}
    table.insert(self._weaponInfo, {
        id = 0,
        name = CollectionConfig.Loc.AnyFirearm
    })
    for _, item in ipairs(self._listWeapon) do
        if self._weaponType[self.selectWeaponTypeIndex].id == item.itemSubType then
            table.insert(self._weaponInfo, {
                id = item.id,
                name = ItemConfigTool.GetItemName(item.id)
            })
            -- for weaponIndex, baseWeaponId in ipairs(weaponSeriesData.weapons) do
            
            --     logerror("[v_dzhanshen] baseWeaponId = "..tostring(baseWeaponId).." name="..tostring(ItemConfigTool.GetItemName(baseWeaponId)))
            -- end
        end
    end

    self:_RefreshWeaponList(ItemConfig.EWeaponItemType.Unknown,true)
end

function CollectionHangingDefaultPanel:compareTables(A, B)
    local addedFields = {}
    local removedFields = {}

    -- 找出在 B 中新增的字段
    for key, value in pairs(B) do
        if A[key] == nil then
            table.insert(addedFields, value)
        end
    end

    -- 找出在 A 中删除的字段
    for key, value in pairs(A) do
        if B[key] == nil then
            table.insert(removedFields, value)
        end
    end

    return addedFields, removedFields
end

function CollectionHangingDefaultPanel:_OnDeConfirmBtnClicked()
    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.SetDefaultPendant)
end


function CollectionHangingDefaultPanel:_OnConfirmBtnClicked()
    --设为默认
    local addWeapon,removeWeapon = self:compareTables(self._equipedWeapon,self._chooseWeapon)
    local addweaponItems = {}
    for _,item in pairs(addWeapon) do 
        addweaponItems[item.id] = item
    end
    if next(addweaponItems) then 
        CollectionLogic.EquipPendantAllModeByWeaponList(addweaponItems,self.pendantId,self.pendantGid)
        Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.EquipedDefault)
    end
    local removeweaponItems = {}
    for _,item in pairs(removeWeapon) do
        removeweaponItems[item.id] = item
    end
    if next(removeweaponItems) then 
        CollectionLogic.EquipPendantAllModeByWeaponList(removeweaponItems,0,0)
        if not next(addweaponItems) then 
            Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.CancelDefault)
        end
    end
    Facade.UIManager:CloseUI(self)
end

function CollectionHangingDefaultPanel:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function CollectionHangingDefaultPanel:OnNavBack()
    Facade.UIManager:CloseUI(self)
    return true
end

function CollectionHangingDefaultPanel:_OnGetItemsCount()
    return #self._listWeapon
end

function CollectionHangingDefaultPanel:_OnProcessItemWidget(position, itemWidget)
    -- InitCollectionMysticalWeaponSkinItem
    local index = position
    local fCheckBoxCb = CreateCallBack(self._OnWeaponItemToogleClick, self,itemWidget, position)
    local fWidgetCb = CreateCallBack(self._OnWeaponItemClick, self,itemWidget, position)
    itemWidget:BindCheckBoxCallBack(fCheckBoxCb)
    itemWidget:BindClickCallback(fWidgetCb)
    local item = self._listWeapon[index]
    -- if item ~= nil then
    --     local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
    --         return Server.CollectionServer:IsPropWithRedDot(item.id)
    --     end,self)
    --     if isvalid(self._redDotInsMap[itemWidget]) then
    --         Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[itemWidget], fCheckFunc, nil)
    --     else
    --         self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,itemWidget, {EReddotType.Normal})
    --     end
    -- end
    if item ~= nil then
        itemWidget:InitCollectionHangingWeaponItem(item)
        itemWidget:SetSelected(nil, false)
        -- BEGIN MODIFICATION @ VIRTUOS : checkbox needs to be navigated in this panel.
        if IsHD() and itemWidget._wtCheckBoxIcon ~= nil then
            itemWidget._wtCheckBoxIcon:SetIsFocusable(true)
        end
        -- END MODIFICATION
        if self._chooseWeapon[item.id]then 
            itemWidget:SetIsChecked(true)
        else
            itemWidget:SetIsChecked(false)
        end
    end
end

function CollectionHangingDefaultPanel:_RefreshWeaponList(subType,refreshAll)
    self._listWeapon = {}
    local notChooseWeapon = {}
    local equipedWeapon = {}
    if Facade.GameFlowManager:GetCurrentGameFlow()  == EGameFlowStageType.Lobby then 
        local slot = Server.InventoryServer:GetSlot(ESlotType.MP_MainWeapon, ESlotGroup.MPApply)
        for _, mpItem in Server.InventoryServer:GetItemsIterator(ESlotGroup.MPApply) do
            local item = ItemBase:NewIns(mpItem.id, mpItem.gid)
            local propInfo = {}
            deepcopy(propInfo,mpItem.rawPropInfo)
            propInfo.weapon.pendant_id = nil
            propInfo.weapon.pendant_gid = nil
            item:SetRawPropInfo(propInfo)
            local weaponFeature = item:GetFeature(EFeatureType.Weapon)
            local isWeapon = weaponFeature and weaponFeature:IsWeapon()
            local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
            item.EquipPendant = false
            if isWeapon then 
                if subType == 0 then 
                    if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then 
                        if mpItem.rawPropInfo.weapon.pendant_id == self.pendantId and mpItem.rawPropInfo.weapon.pendant_gid == self.pendantGid  then 
                            if refreshAll then 
                                self._chooseWeapon[item.id] = item
                                self._equipedWeapon[item.id] = item
                            end
                            equipedWeapon[item.id] = item
                        else
                            table.insert(notChooseWeapon,item)
                        end
                    end
                else
                    if itemSubType == subType then 
                        if mpItem.rawPropInfo.weapon.pendant_id == self.pendantId or  (self._defaultWeaponMp[item.id] and self._defaultWeaponMp[item.id].pendant_id == self.pendantId and self._defaultWeaponMp[item.id].pendant_gid == self.pendantGid) then 
                            if refreshAll then 
                                self._chooseWeapon[item.id] = item
                                self._equipedWeapon[item.id] = item
                            end
                            equipedWeapon[item.id] = item
                        else
                            table.insert(notChooseWeapon,item)
                        end
                    end
                end
                -- if self._chooseWeapon[item.id] then
                --     self._chooseWeapon[item.id] = item
                -- end

            end
        end
        local weaponId2UnLockInfoMap = Server.InventoryServer:GetMPWeaponLockedInfoMap()
        for weaponId, unlockInfo in pairs(weaponId2UnLockInfoMap) do
            local itemSubType = ItemHelperTool.GetSubTypeById(weaponId)
            if subType == 0 then 
                if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then 
                    local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(weaponId)
                    local item = ItemBase:NewIns(weaponId, 0, 0)
                    item.EquipPendant = false
                    item:SetRawPropInfo(WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId))
                    item:ClientSetNum(0)
                    if self._defaultWeaponMp[item.id] and self._defaultWeaponMp[item.id].pendant_id == self.pendantId and self._defaultWeaponMp[item.id].pendant_gid == self.pendantGid  then
                        if refreshAll then 
                            self._chooseWeapon[item.id] = item
                            self._equipedWeapon[item.id] = item
                        end
                        equipedWeapon[item.id] = item
                    else
                        table.insert(notChooseWeapon,item)
                    end
                end
            else
                if itemSubType == subType then 
                    local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(weaponId)
                    local item = ItemBase:NewIns(weaponId, 0, 0)
                    item.EquipPendant = false
                    item:SetRawPropInfo(WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId))
                    item:ClientSetNum(0)
                    if self._defaultWeaponMp[item.id] and self._defaultWeaponMp[item.id].pendant_id == self.pendantId and self._defaultWeaponMp[item.id].pendant_gid == self.pendantGid  then
                        if refreshAll then 
                            self._chooseWeapon[item.id] = item
                            self._equipedWeapon[item.id] = item
                        end
                        equipedWeapon[item.id] = item
                    else
                        table.insert(notChooseWeapon,item)
                    end
                end
            end
        end
    end
    if Facade.GameFlowManager:GetCurrentGameFlow()  == EGameFlowStageType.SafeHouse then 
        local slot = Server.InventoryServer:GetSlot(ESlotType.MP_MainWeapon, ESlotGroup.MPApply)
        for _, mpItem in Server.InventoryServer:GetItemsIterator(ESlotGroup.MPApply) do
            local pendantId = Server.InventoryServer:GetWeaponPendantIDFromBaseWeaponID(mpItem.id)
            local pendantGId = Server.InventoryServer:GetWeaponPendantGUIDFromBaseWeaponID(mpItem.id)
            local skinId = Server.InventoryServer:GetSOLWeaponSkinIDFromBaseWeaponID(mpItem.id)
            local gidId =Server.InventoryServer:GetSOLWeaponSkinGUIDFromBaseWeaponID(mpItem.id)
            local item
            if skinId ~= 0 then  
                item = ItemBase:New(mpItem.id)
                local weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(skinId)
                if isvalid(weaponDesc) then
                    if mpItem.gid > 0 then
                        local propInfo = CollectionLogic.getWeaponPropFromInventory(mpItem.id)
                        if propInfo then 
                            local newPropInfo = {}
                            deepcopy(newPropInfo,propInfo)
                            newPropInfo.weapon.pendant_id = nil
                            newPropInfo.weapon.pendant_gid = nil
                            WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, newPropInfo)
                        else
                            local a = 1
                        end
                    end
                    item:SetRawDescObj(weaponDesc)
                end
            else
                item = ItemBase:NewIns(mpItem.id, mpItem.gid)
                local propInfo = {}
                deepcopy(propInfo,mpItem.rawPropInfo)
                propInfo.weapon.pendant_id = nil
                propInfo.weapon.pendant_gid = nil
                item:SetRawPropInfo(propInfo)
            end
            local weaponFeature = mpItem:GetFeature(EFeatureType.Weapon)
            local isWeapon = weaponFeature and weaponFeature:IsWeapon()
            local itemSubType = ItemHelperTool.GetSubTypeById(mpItem.id)
            if isWeapon then 
                if subType == 0 then 
                    if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then 
                        if pendantId == self.pendantId and pendantGId == self.pendantGid then 
                            if refreshAll then 
                                self._chooseWeapon[mpItem.id] = item
                                self._equipedWeapon[mpItem.id] = item
                            end
                            equipedWeapon[mpItem.id] = item
                        else
                            table.insert(notChooseWeapon,item)
                        end
                    end
                else
                    if itemSubType == subType then
                        -- table.insert(self._listWeapon,item)
                        if pendantId == self.pendantId and pendantGId == self.pendantGid then 
                            if refreshAll then 
                                self._chooseWeapon[mpItem.id] = item
                                self._equipedWeapon[mpItem.id] = item
                            end
                            equipedWeapon[mpItem.id] = item
                        else
                            table.insert(notChooseWeapon,item)
                        end
                    end
                end
            end
        end
        local weaponId2UnLockInfoMap = Server.InventoryServer:GetMPWeaponLockedInfoMap()
        for weaponId, unlockInfo in pairs(weaponId2UnLockInfoMap) do
            local itemSubType = ItemHelperTool.GetSubTypeById(weaponId)
            if subType == 0 then 
                if itemSubType ~= ItemConfig.EWeaponItemType.Melee and itemSubType ~= ItemConfig.EWeaponItemType.Heavy then 
                    local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(weaponId)
                    local item = ItemBase:NewIns(weaponId, 0, 0)
                    item:SetRawPropInfo(WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId))
                    item:ClientSetNum(0)
                    if self._defaultWeaponMp[item.id] and self._defaultWeaponMp[item.id].pendant_id == self.pendantId and self._defaultWeaponMp[item.id].pendant_gid == self.pendantGid  then
                        if refreshAll then 
                            self._chooseWeapon[item.id] = item
                            self._equipedWeapon[item.id] = item
                        end
                        equipedWeapon[item.id] = item
                    else
                        table.insert(notChooseWeapon,item)
                    end
                end
            else
                if itemSubType == subType then 
                    local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(weaponId)
                    local item = ItemBase:NewIns(weaponId, 0, 0)
                    item:SetRawPropInfo(WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId))
                    item:ClientSetNum(0)
                    if self._defaultWeaponMp[item.id] and self._defaultWeaponMp[item.id].pendant_id == self.pendantId and self._defaultWeaponMp[item.id].pendant_gid == self.pendantGid  then
                        if refreshAll then 
                            self._chooseWeapon[item.id] = item
                            self._equipedWeapon[item.id] = item
                        end
                        equipedWeapon[item.id] = item
                    else
                        table.insert(notChooseWeapon,item)
                    end
                end
            end
        end
    end
        
    local compare = function(a,b)
        local aSubType = ItemHelperTool.GetSubTypeById(a.id)
        local bSubType = ItemHelperTool.GetSubTypeById(b.id)
        if aSubType == bSubType then 
            local aId = WeaponAssemblyTool.GetBaseWeaponIDFromWeaponSkinID(a.id)
            if aId == 0 then 
                aId = a.id
            end
            local bId = WeaponAssemblyTool.GetBaseWeaponIDFromWeaponSkinID(b.id)
            if bId == 0 then 
                bId = b.id
            end
            return aId < bId
        end
        return aSubType < bSubType
    end
    self._listWeapon = table.values(equipedWeapon)
    table.sort(self._listWeapon,compare)
    table.sort(notChooseWeapon,compare)
    for k,v in ipairs(notChooseWeapon) do 
        table.insert(self._listWeapon,v)
    end
    if #self._listWeapon >= SHOWGUNTYPENUM then 
        self._wtWeaponDropDown:SelfHitTestInvisible()
    else
        self._wtWeaponDropDown:Collapsed()
    end
    self._wtWeaponScrollView:RefreshAllItems()
    if refreshAll then 
        self:RefreshDefaultBtnState()
    end
end

function CollectionHangingDefaultPanel:_ChooseWeapon(id)
    if id == 0 then 
        self:_RefreshWeaponList(self._weaponType[self.selectWeaponTypeIndex].id)
    else
        local findItem = nil
        for _, item in pairs(self._listWeapon) do
            if item.id == id then 
                findItem = item
                break
            end
        end
        self._listWeapon = {}
        table.insert(self._listWeapon,findItem)
        self._wtWeaponScrollView:RefreshAllItems()
    end
end

function CollectionHangingDefaultPanel:_OnWeaponItemClick(itemWidget,position)
    local checked = itemWidget:GetIsChecked()
    itemWidget:SetIsChecked(not checked)
    self:_OnWeaponItemToogleClick(itemWidget,position)
end

function CollectionHangingDefaultPanel:_OnWeaponItemToogleClick(itemWidget,position)
    local checked = itemWidget:GetIsChecked()
    local item = self._listWeapon[position]
    if checked then 
        self._chooseWeapon[item.id] = item
    else
        self._chooseWeapon[item.id] = nil
    end
    self:RefreshDefaultBtnState()
end

function CollectionHangingDefaultPanel:_OnCheckStateChanged_Pad()
    local isChecked = self:GetIsChecked()
    self._wtCheckBox:SetIsChecked(not isChecked)
    if isChecked then 
        self._wtCommonPopWin:AddSummaries({"PendantSetDefaultAll","Select"})
    else
        self._wtCommonPopWin:AddSummaries({"PendantCancelDefaultAll","Select"})
    end
    self:_OnCheckStateChanged()
end

function CollectionHangingDefaultPanel:_OnCheckStateChanged()
    local isChecked = self:GetIsChecked()
    if isChecked then
        for _,item in pairs(self._listWeapon) do 
            if not self._chooseWeapon[item.id] then 
                self._chooseWeapon[item.id] = item
            end
        end
    else
        self._chooseWeapon = {}
    end
    self:RefreshDefaultBtnState()
    self._wtWeaponScrollView:RefreshVisibleItems()
end

function CollectionHangingDefaultPanel:SetIsChecked(bIsChecked)
    self._wtCheckBox:SetIsChecked(bIsChecked, false)
end

function CollectionHangingDefaultPanel:GetIsChecked()
    return self._wtCheckBox:GetIsChecked()
end

function CollectionHangingDefaultPanel:RefreshDefaultBtnState()
    if not next(self._equipedWeapon) and not next(self._chooseWeapon) then 
        self._wtConfirmBtn:SetIsEnabledStyle(false)
    else
        self._wtConfirmBtn:SetIsEnabledStyle(true)
    end
end

return CollectionHangingDefaultPanel
