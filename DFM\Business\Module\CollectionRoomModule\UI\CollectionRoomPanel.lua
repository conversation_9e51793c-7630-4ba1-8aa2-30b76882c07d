----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local CurrencyHelperTool = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local InteractionTip = require "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomInteractionTip"

local UHallCameraCtrlComponentBase = import "HallCameraCtrlComponentBase"
local AHallCollectionRoomDisplayCtrl = import "HallCollectionRoomDisplayCtrl"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

---@class CollectionRoomPanel : LuaUIBaseView
local CollectionRoomPanel = ui("CollectionRoomPanel")

function CollectionRoomPanel:Ctor()
    self._wtRootPanel = self:Wnd("DFCanvasPanel_27", UIWidgetBase)
    self._wtEnterCollectionRoomBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtEnterCollectionRoomBtn:Event("OnClicked", self._EnterCollectionRoomBtnClicked, self)
    self._wtLastMainCameraBtn = self:Wnd("DFButton_222", DFButtonOnly)
    self._wtLastMainCameraBtn:Event("OnClicked", self._OnLastMainCameraBtnClicked, self)
    self._wtNextMainCameraBtn = self:Wnd("DFButton_1", DFButtonOnly)
    self._wtNextMainCameraBtn:Event("OnClicked", self._OnNextMainCameraBtnClicked, self)
    self._wtCurrentLevel = self:Wnd("DFTextBlock", UITextBlock)
    self._wtTotalValue = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtCurrentShowAreaName = self:Wnd("DFTextBlock_359", UITextBlock)
    self._wtCommonCheckInstruction = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    if not IsHD() then
        self._wtCommonCheckInstruction:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)
    end
    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DescTipAnchor", self._ShowTips, self._HideTips)
    self._wtUpgradeBtn = self:Wnd("WBP_CommonIconButton", DFButtonOnly)
    self._wtUpgradeBtn:Event("OnClicked", self._OnUpgradeBtnClicked, self)
    self._wtInteractionTip = self:Wnd("WBP_CollectionRoom_InteractionTip", InteractionTip)
    self._wtDFCanvasPanel = self:Wnd("DFCanvasPanel_3", UIWidgetBase)
    -- self._wtTotalPriceTitle = self:Wnd("DFTextBlock_193", UITextBlock)
    -- self._wtTotalPriceTitle:SetText(CollectionRoomConfig.Loc.CollectionRoomValue)

    ---@type number DIY展柜数量
    self._diyCabinetNum = 0
    ---@type number 收藏室总价值
    self._totalValue = 0
    ---@type table<EShowCabinetType,number[]> 各收藏柜价值
    self._cabinetValueMap = {}
    self._tipsHandle = nil
    ---@type CollectionRoomMainPanel
    self._parent = nil
    ---@type boolean
    self._bIsFirstPlay = true
    self._displayInputHandles = {}
end

function CollectionRoomPanel:OnInitExtraData(parent, cameraId)
    self._parent = parent
    Module.CollectionRoom.Field.mainPanelCurrentCameraId = cameraId or 2
end

function CollectionRoomPanel:OnShowBegin()
    if self._parent and self._parent:GetCurrentSubUINavID() ~= self.UINavID then
        return
    end
    loginfo("CollectionRoomPanel:OnShowBegin")
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if deviceData and deviceData.level then
        self._wtCurrentLevel:SetText(string.format(CollectionRoomConfig.Loc.CurrentLevel, deviceData.level))
    else
        logerror("CollectionRoomPanel:OnShowBegin fail to get level")
    end
    self._diyCabinetNum = CollectionRoomLogic.GetDIYCabinetNum()
    local totalValue, upgradeValue, displayCabinetValue, specialCabinetValue, diyCabinetValues, seasonLimitedValue = CollectionRoomLogic.GetCollectionRoomTotalValue()
    self._totalValue = totalValue
    self._cabinetValueMap[0] = upgradeValue
    self._cabinetValueMap[EShowCabinetType.Display] = displayCabinetValue
    self._cabinetValueMap[EShowCabinetType.Special] = specialCabinetValue
    self._cabinetValueMap[EShowCabinetType.DIY] = diyCabinetValues
    self._cabinetValueMap[-1] = seasonLimitedValue
    self._wtTotalValue:SetText(CurrencyHelperTool.GetCurrencyNumFormatStr(self._totalValue))

    self._eventHandle = nil
    local Ctrl = UGameplayStatics.GetActorOfClass(GetWorld(), AHallCollectionRoomDisplayCtrl)
    if Ctrl then
        local cameraComp = Ctrl:GetComponentByClass(UHallCameraCtrlComponentBase)
        if cameraComp then
            self._eventHandle = cameraComp.HallCameraMovedDone:Add(CreateCPlusCallBack(self._OnHallCameraMovedDone, self))
        end
    end

    -- 注册红点
    self:_RegisterReddot()
    self:_SetViewTargetToMainCamera(true)

    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.CollectionRoomPanelOnShowBegin)
    if not IsHD() then
        local gameInst = GetGameInstance()
        if gameInst then
            UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
        end
    end

    -- 手柄适配
    if IsHD() then
        self:_EnableGamePadFeature()
    end
end

function CollectionRoomPanel:OnShow()
    loginfo("CollectionRoomPanel:OnShow")
    if IsHD() then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
    end
end

function CollectionRoomPanel:OnHide()
    loginfo("CollectionRoomPanel:OnHide")
    if not IsHD() then
        local gameInst = GetGameInstance()
        if gameInst then
            UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
        end
    end
    self:_HideTips()
    self._wtCommonCheckInstruction:SetIsChecked(false, false)
    if self._eventHandle then
        local Ctrl = UGameplayStatics.GetActorOfClass(GetWorld(), AHallCollectionRoomDisplayCtrl)
        if Ctrl then
            local cameraComp = Ctrl:GetComponentByClass(UHallCameraCtrlComponentBase)
            if cameraComp then
                cameraComp.HallCameraMovedDone:Remove(self._eventHandle)
            end
        end
        self._eventHandle = nil
    end
    self._wtDFCanvasPanel:SelfHitTestInvisible()

    if self._reddotIns then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._reddotIns)
    end
    self._bIsFirstPlay = true

    -- 手柄适配
    if IsHD() then
        self:_DisableGamePadFeature()
    end
end

function CollectionRoomPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
    local screenPos = mouseEvent:GetScreenSpacePosition()
    local geometry = self._wtCommonCheckInstruction:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, screenPos)
    if not isUnder then
        self:_HideTips()
        self._wtCommonCheckInstruction:SetIsChecked(false, false)
    end
end

function CollectionRoomPanel:_OnHallCameraMovedDone()
    self._wtDFCanvasPanel:SelfHitTestInvisible()
    if not self._bIsFirstPlay then
        self:PlayWidgetAnim(self.WBP_CollectionHome_Main_in_1)
    end
    self._bIsFirstPlay = false
end

function CollectionRoomPanel:_SetViewTargetToMainCamera(bFromOnShowBegin)
    bFromOnShowBegin = setdefault(bFromOnShowBegin, false)
    local currentCameraId = Module.CollectionRoom.Field.mainPanelCurrentCameraId
    if currentCameraId == 1 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.SpecialCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.Special, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(false)
        self._wtNextMainCameraBtn:SetIsEnabled(true)
        self:SetInteractionPosition(1)
    elseif currentCameraId == 2 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.DisplayCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.Display, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(true)
        self._wtNextMainCameraBtn:SetIsEnabled(true)
        self:SetInteractionPosition(0)
    elseif currentCameraId == 3 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.DIYCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.DIY, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(true)
        self._wtNextMainCameraBtn:SetIsEnabled(false)
        self:SetInteractionPosition(2)
    end
    local currentDisplayType = CollectionRoomLogic.CallCtrlFunction("GetDisplayType")
    local bImmediatelyFlag = currentDisplayType == "None"
    local targetDisplayType = CollectionRoomConfig.CameraConfig.Main[Module.CollectionRoom.Field.mainPanelCurrentCameraId]
    if self._eventHandle and not bImmediatelyFlag and targetDisplayType ~= currentDisplayType then
        self._wtDFCanvasPanel:Collapsed()
    else
        self._wtDFCanvasPanel:SelfHitTestInvisible()
        if not bFromOnShowBegin then
            self:PlayWidgetAnim(self.WBP_CollectionHome_Main_in_1)
        end
    end
    CollectionRoomLogic.SetViewTargetToNamedCamera(CollectionRoomConfig.CameraConfig.Main[Module.CollectionRoom.Field.mainPanelCurrentCameraId], true, bImmediatelyFlag)

    if isvalid(self._reddotIns) then
        Module.ReddotTrie:UpdateDynamicReddot(self._wtInteractionTip, EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._reddotIns, self._fCheckReddot, nil)
    end
end

function CollectionRoomPanel:_OnSubStageChangeEnter(curSubStageType)
    if curSubStageType == ESubStage.CollectionRoom then
        self:_SetViewTargetToMainCamera()
    end
end

function CollectionRoomPanel:_EnterCollectionRoomBtnClicked()
    local currentCameraId = Module.CollectionRoom.Field.mainPanelCurrentCameraId
    CollectionRoomLogic.EnterCollectionRoom(ECollectionRoomEnterFrom.MainPanel, {currentCameraId})
end

function CollectionRoomPanel:_OnLastMainCameraBtnClicked()
    local currentCameraId = Module.CollectionRoom.Field.mainPanelCurrentCameraId
    if currentCameraId > 1 then
        Module.CollectionRoom.Field.mainPanelCurrentCameraId = currentCameraId - 1
        self:_SetViewTargetToMainCamera()
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.MainPanelSwitchCamera)
    end
    if IsHD() then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
    end
end

function CollectionRoomPanel:_OnNextMainCameraBtnClicked()
    local currentCameraId = Module.CollectionRoom.Field.mainPanelCurrentCameraId
    if currentCameraId < #CollectionRoomConfig.CameraConfig.Main then
        Module.CollectionRoom.Field.mainPanelCurrentCameraId = currentCameraId + 1
        self:_SetViewTargetToMainCamera()
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.MainPanelSwitchCamera)
    end
    if IsHD() then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
    end
end

function CollectionRoomPanel:_OnCheckStateChanged(bChecked)
    if bChecked then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function CollectionRoomPanel:_ShowTips()
    if self._tipsHandle then
        return
    end
    local contents = {}
    -- 标题
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.CollectionRoomValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._totalValue, CurrencyHelperTool.EKMThousandsType.None)),
        useBigFont = true,
    }})
    -- tips内容
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.UpgradeCostValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[0], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.SeasonLimitValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[-1], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.DisplayCabinetName,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.Display], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.SpecialCabinetName,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.Special], CurrencyHelperTool.EKMThousandsType.None))
    }})
    for i = 1,self._diyCabinetNum do
        table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
            textTitle = CollectionRoomConfig.Loc.DIYCabinetName,
            textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.DIY][i], CurrencyHelperTool.EKMThousandsType.None)),
            hideBottomLine = i == self._diyCabinetNum,
        }})
    end
    self._tipsHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchor)
end

function CollectionRoomPanel:_HideTips()
    if self._tipsHandle then
        Module.CommonTips:RemoveAssembledTips(self._tipsHandle, self._wtTipsAnchor)
        self._tipsHandle = nil
    end
end

function CollectionRoomPanel:_OnUpgradeBtnClicked()
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if deviceData then
        Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
    end
end

function CollectionRoomPanel:_RegisterReddot()
    self._reddotIns =
    Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._fCheckReddot, nil, self._wtInteractionTip, {EReddotType.Normal})
end

function CollectionRoomPanel:_fCheckReddot()
    local currentCameraId = Module.CollectionRoom.Field.mainPanelCurrentCameraId
    if currentCameraId == 1 then
        return CollectionRoomLogic.JudgeHaveItemCanShevle(EShowCabinetType.Special)
    elseif currentCameraId == 2 then
        return CollectionRoomLogic.JudgeHaveItemCanShevle(EShowCabinetType.Display)
    else
        return false
    end
end

--region 手柄适配
function CollectionRoomPanel:_EnableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RegisterNavGroup()
    self:_BindDisplayAction()

    self._wtEnterCollectionRoomBtn:SetDisplayInputAction("CM_Enter", true, nil, true)
    -- lt/rt
    local WBP_GamepadKeyIconBox_Left = self:Wnd("WBP_CommonKeyIconBox_Left", HDKeyIconBox)
    local WBP_GamepadKeyIconBox_Right = self:Wnd("WBP_CommonKeyIconBox_Right", HDKeyIconBox)
    self.WBP_GamepadKeyIconBox_Left:SetOnlyDisplayOnGamepad(true)
    self.WBP_GamepadKeyIconBox_Right :SetOnlyDisplayOnGamepad(true)
    self.WBP_GamepadKeyIconBox_Left:InitByDisplayInputActionName("CM_LastPage", true, 0.0, false)
    self.WBP_GamepadKeyIconBox_Right :InitByDisplayInputActionName("CM_NextPage", true, 0.0, false)
    
end

function CollectionRoomPanel:_DisableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RemoveNavGroup()
    self:_UnBindDisplayAction()

end

function CollectionRoomPanel:_RegisterNavGroup()
    if not IsHD() then
        return
    end
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootPanel, self, "Hittest")
    end

    self._navGroup:AddNavWidgetToArray(self._wtInteractionTip)
    local _wtMusicPlayer = self._wtRootPanel:Wnd("WBP_CollectionHome_MusicPlayer", UIWidgetBase)
    self._navGroup:AddNavWidgetToArray(_wtMusicPlayer)

    self._navGroup:SetAutoUseRootGeometrySize(false)
    self._navGroup:SetNavSelectorWidgetVisibility(true)

    -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
end

function CollectionRoomPanel:_RemoveNavGroup()
    if not IsHD() then
        return 
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._navGroup = nil
end

function CollectionRoomPanel:_BindDisplayAction()
    if not IsHD() then
        return 
    end

    if self._displayInputHandles then
        self:_UnBindDisplayAction()
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    -- X键进入收藏室
    handle = inputMonitor:AddDisplayActionBinding("CM_Enter", EInputEvent.IE_Pressed, self._EnterCollectionRoomBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- RS提示
    handle = inputMonitor:AddDisplayActionBinding("CM_Tips", EInputEvent.IE_Pressed, self.FilpTips, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- LT切换至左侧页面
    handle = inputMonitor:AddDisplayActionBinding("CM_LastPage", EInputEvent.IE_Pressed, self._OnLastMainCameraBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- RT切换至右侧页面
    handle = inputMonitor:AddDisplayActionBinding("CM_NextPage", EInputEvent.IE_Pressed, self._OnNextMainCameraBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)

end

function CollectionRoomPanel:_UnBindDisplayAction()
    if not IsHD() then
        return 
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    if self._displayInputHandles then
        for i, handle in ipairs(self._displayInputHandles) do
            inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
        end
        self._displayInputHandles = {}
    end
end

-- 手柄功能接口
-- 切换tips
function CollectionRoomPanel:FilpTips()
    if self._tipsHandle then
        self:_HideTips()
    else
        self:_ShowTips()
    end
end

function CollectionRoomPanel:OnWidgetAnimationFinished(animation)
    if animation == self.WBP_CollectionHome_Main_in_1 or animation == self.WBP_CollectionHome_Main_in then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
    end
end

--endregion

return CollectionRoomPanel