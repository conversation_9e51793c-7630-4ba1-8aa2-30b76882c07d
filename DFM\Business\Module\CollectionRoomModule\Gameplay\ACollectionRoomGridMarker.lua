----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local UWidgetComponent = import "WidgetComponent"

---@class ACollectionRoomGridMarker : ACollectionRoomInteractorBase
local ACollectionRoomGridMarker = class("ACollectionRoomGridMarker", ACollectionRoomInteractorBase)

function ACollectionRoomGridMarker:Ctor()
    self._widgetUI = nil
end

function ACollectionRoomGridMarker:Imp_ReceiveBeginPlay()
    loginfo("ACollectionRoomGridMarker:Imp_ReceiveBeginPlay", self.CabinetId, self.GridId)
    local widgetComponent = self:GetComponentByClass(UWidgetComponent)
    if widgetComponent then
        self._widgetUI = widgetComponent:GetUserWidgetObject()
    end
    if self._widgetUI then
        local bFind = false
        if Module.CollectionRoom.Field.collectionDIYCabinetData[self.CabinetId] then
            for _, rackSlotInfoList in pairs(Module.CollectionRoom.Field.collectionDIYCabinetData[self.CabinetId]) do
                if bFind then
                    break
                end
                for _, rackSlotInfo in pairs(rackSlotInfoList) do
                    if bFind then
                        break
                    end
                    if rackSlotInfo.RackSlotID == self.GridId then
                        self._widgetUI.wtItemMarker:SetText(string.format("%d-%d", rackSlotInfo.DisplayID[1] or 0, rackSlotInfo.DisplayID[2] or 0))
                        bFind = true
                    end
                end
            end
        end
        if Module.CollectionRoom.Field.mainPanel then
            self._widgetUI:Collapsed()
        else
            self._widgetUI:SelfHitTestInvisible()
        end
        self._widgetUI:SetType(0)
        if not bFind then
            loginfo("ACollectionRoomGridMarker:Imp_ReceiveBeginPlay fail to find title", self.CabinetId, self.GridId)
        end
    else
        loginfo("ACollectionRoomGridMarker:Imp_ReceiveBeginPlay fail to find WidgetUI", self.CabinetId, self.GridId)
    end
    CollectionRoomConfig.Events.evtDIYCabinetGridHighLight:AddListener(self._OnDIYCabinetGridHighLight, self)
    CollectionRoomConfig.Events.evtShowOrHideAllGridMarker:AddListener(self._OnShowOrHideAllGridMarker, self)
end

function ACollectionRoomGridMarker:Imp_ReceiveEndPlay()
    self._widgetUI = nil
    CollectionRoomConfig.Events.evtDIYCabinetGridHighLight:RemoveListener(self._OnDIYCabinetGridHighLight, self)
    CollectionRoomConfig.Events.evtShowOrHideAllGridMarker:RemoveListener(self._OnShowOrHideAllGridMarker, self)
end

function ACollectionRoomGridMarker:_OnDIYCabinetGridHighLight(cabinetId, gridId, bHighLight, bValid)
    if not self._widgetUI then
        return
    end
    if cabinetId == self.CabinetId and gridId == self.GridId then
        if bHighLight then
            if bValid then
                self._widgetUI:SetType(1)
            else
                self._widgetUI:SetType(2)
            end
        else
            self._widgetUI:SetType(0)
        end
    else
        if bHighLight then
            self._widgetUI:SetType(0)
        end
    end
end

function ACollectionRoomGridMarker:_OnShowOrHideAllGridMarker(bShow)
    if self._widgetUI then
        if bShow then
            self._widgetUI:SelfHitTestInvisible()
        else
            self._widgetUI:Collapsed()
        end
    end
end

return ACollectionRoomGridMarker