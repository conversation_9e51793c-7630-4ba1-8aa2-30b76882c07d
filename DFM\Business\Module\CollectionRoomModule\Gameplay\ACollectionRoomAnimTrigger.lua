----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local ACollectionRoomInteractorBase = require "DFM.Business.Module.CollectionRoomModule.Gameplay.ACollectionRoomInteractorBase"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local UKismetSystemLibrary = import "KismetSystemLibrary"

---@class ACollectionRoomAnimTrigger : ACollectionRoomInteractorBase
local ACollectionRoomAnimTrigger = class("ACollectionRoomAnimTrigger", ACollectionRoomInteractorBase)

function ACollectionRoomAnimTrigger:Ctor()
    self:log("Ctor")
end

function ACollectionRoomAnimTrigger:TriggerStart()
    if Server.CollectionRoomServer:IsCabinetUnlocked(EShowCabinetType.Special) then
        if not Module.CollectionRoom.Field.bSpecialCabinetAnimHasTriggered and Module.CollectionRoom.Field.bFirstEnterAfterLogin then
            Module.CollectionRoom.Field.bSpecialCabinetAnimHasTriggered = true
            CollectionRoomLogic.PlayAllSpecialCabinetAnim()
        end
    end
end

function ACollectionRoomAnimTrigger:TriggerStop()

end

return ACollectionRoomAnimTrigger