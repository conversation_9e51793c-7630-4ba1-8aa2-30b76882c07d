---@class QuestDetailPanel : LuaUIBaseView

local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local QuestDetailPanel = ui("QuestDetailPanel")

function QuestDetailPanel:Ctor()
    
    self._wtQuestObjectiveBox = self:Wnd("wQuestObjectiveBox", UIScrollBox)

    self._wtTaskLabel = self:Wnd("WBP_TaskSystem_TaskLabel", UIWidgetBase)
    self._wtLabelText = self._wtTaskLabel:Wnd("DFTextBlock_QuestType", UITextBlock)

    self._wtGiveUpQuestBtn = self:Wnd("WBP_AbandonBtn", CommonButton)
    self._wtGiveUpQuestBtn:Event("OnClicked", self._OnClickGiveUpBtn, self)

    self._wtTaskName = self:Wnd("DFTextBlock_302", UITextBlock)

    self._questInfo = nil
    self._questTaskDetailItem = nil
end

function QuestDetailPanel:OnShowBegin()
    self:_AddEventListener()
    self:_EnableGamepad()
end

function QuestDetailPanel:OnHide()
    self:RemoveAllLuaEvent()
    self:_DisableGamepad()
end

function QuestDetailPanel:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtQuestObjectiveBox)
        self._questTaskDetailItem = nil
end

function QuestDetailPanel:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._UpdateGiveupBtn, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateObjectiveState, self._UpdateGiveupBtn, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._UpdateGiveupBtn, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateSeasonLevel, self._UpdateGiveupBtn, self)
end



function QuestDetailPanel:UpdateInfo(questInfo, isFact)
    self._questInfo = questInfo

    if isFact then
        self._wtLabelText:SetText(Module.Quest.Config.Loc.QuestSeasonContract)
        self._wtTaskLabel:SetColorType(3)
    else
        if self._questInfo.type == QuestType.Mission then
            self._wtLabelText:SetText(Module.Quest.Config.Loc.LineQuestsDroDownLoc_Mission)
            self._wtTaskLabel:SetColorType(0)
        elseif self._questInfo.type == QuestType.ImportantQuest then
            self._wtLabelText:SetText(Module.Quest.Config.Loc.LineQuestsDroDownLoc_Important)
            self._wtTaskLabel:SetColorType(2)
        elseif self._questInfo.type == QuestType.Branch then
            self._wtLabelText:SetText(Module.Quest.Config.Loc.LineQuestsDroDownLoc_Branch)
            self._wtTaskLabel:SetColorType(1)
        elseif self._questInfo.type == QuestType.SeasonQuest then
    
            local questlineInfo = Server.QuestServer:GetSeasonLineData(questInfo._seasonLineID)
            if questlineInfo then
                if questlineInfo:IsMainGroup(self._questInfo._seasonStageID, self._questInfo._seasonGroupID) then
                    self._wtLabelText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonMain)
                    self._wtTaskLabel:SetColorType(3)
                else
                    self._wtLabelText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranch)
                    self._wtTaskLabel:SetColorType(4)
                end
            end
        end
    end

    self._wtTaskName:SetText(self._questInfo.name)
    self._wtTaskName:PlayAnim_ComputeTextBlock()

    self:_UpdateQuestDetail()
    self:_UpdateGiveupBtn()

end

function QuestDetailPanel:_UpdateQuestDetail()
    if self._questTaskDetailItem then
        Module.Quest.Config.evtQuestTaskItemUpdate:Invoke(self._questInfo)
        return
    end
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
    self._questTaskDetailItem =
        Facade.UIManager:AddSubUI(
        self,
        UIName2ID.QuestTaskDetailItem,
        self._wtQuestObjectiveBox,
        nil,
        self._questInfo,
        idx
    )
end

function QuestDetailPanel:_UpdateGiveupBtn(questId)
    
    if questId and questId ~= self._questInfo.id then
        return
    end

    self._wtGiveUpQuestBtn:SetVisibility(ESlateVisibility.Collapsed)
    if self._questInfo.state == QuestState.Accepted then
        if not self._questInfo.bShouldAutoAccept then
            self._wtGiveUpQuestBtn:SetVisibility(ESlateVisibility.Visible)
        end
    end
end

function QuestDetailPanel:_OnClickGiveUpBtn()
    if self._questInfo == nil then
        return
    end

    if Server.MatchServer:GetIsMatching() then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.InMatching)
        return
    end

    local function fOnConfim()
        QuestLogic.DoGiveUpQuest(self._questInfo)
    end

    local function fOnCancel()
    end

    local text = Module.Quest.Config.Loc.GiveUpQuest
    if #self._questInfo:GetSubmitObjectives() > 0 then
        text = Module.Quest.Config.Loc.GiveUpSubmitQuest
    end
    Module.CommonTips:ShowConfirmWindow(text, fOnConfim, fOnCancel)
end

---------- Gamepad -------------

function QuestDetailPanel:_EnableGamepad()
    if IsHD() then
        self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtGiveUpQuestBtn, self, "Hittest")
        if self._wtNavGroup1 then
            self._wtNavGroup1:AddNavWidgetToArray(self._wtGiveUpQuestBtn)
        end
    end
end

function QuestDetailPanel:_DisableGamepad()
    if IsHD() then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup1 = nil
    end
end

return QuestDetailPanel