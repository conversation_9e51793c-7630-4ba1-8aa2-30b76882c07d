----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
-- END MODIFICATION


local CollectionsHangingPagePanel = ui("CollectionsHangingPagePanel")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CommonItemViewDropDownBox = require "DFM.Business.Module.CommonWidgetModule.UI.DropDown.CommonItemViewDropDownBox"
local CollectionConfig = Module.Collection.Config
local ItemDetailViewEquip = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailType3.ItemDetailViewEquip"
local CollectionMysticalSkinBtn = require "DFM.Business.Module.CollectionModule.UI.CollectionMysticalSkinBtn"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonWeaponSkinMissionProgress = require "DFM.Business.Module.CommonWidgetModule.UI.CommonWeaponSkinMissionProgress"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
function CollectionsHangingPagePanel:Ctor()
    self._wtMainPanel = self:Wnd("wtMainPanel", UIWidgetBase)
    self._wtFilterBtn = self:Wnd("wtFilterBtn", DFCommonButtonOnly)
    self._wtFilterBtn:Event("OnClicked",self._OnFilterBtnClick,self)
    self._wtMysticalHangingDropDown = self:Wnd("wtMysticalSkinDropDown", CommonItemViewDropDownBox)
    local data = {
        tabName = "",                                                               -- tab文本名字
        subUINavID = UIName2ID.CommonItemViewDropDownItem,                          -- 指定生成子ui
        caller = self,                                                              -- 方法持有者
        fOnGetPresetCount = self._OnGetMySticalItems,                     -- 子控件数量获取方法
        fOnPresetProcessTabItemWidget = self._OnProcessMysticalHangingWidget,    -- 子控件生成回调方法,
        fOnCheckedBoxStateChangedNative = self._OnCheckedBoxStateChangedNative,
    }
    self._wtMysticalHangingDropDown:InitDownBox(data)
    self._wtHangingScrollView = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinScrollView", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtInfoPanel = self:Wnd("wtInfoPanel", UIWidgetBase)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailViewEquip)
    -- self._wtItemDetailView:SetWeaponDetailIsHideBtn(true)
    -- self._wtItemDetailView:SetShowWeaponDetailCheckBox(false)
    self._wtFromMandelBrickBtn = self:Wnd("wtFromMandelBrickBtn", CollectionMysticalSkinBtn)
    self._wtFromMandelBrickBtn:Event("OnClicked", self._OnShowMandelBrickPage, self)
    self._wtFromAuctionBtn = self:Wnd("wtFromAuctionBtn", CollectionMysticalSkinBtn)
    self._wtFromAuctionBtn:Event("OnClicked", self._OnShowAuctionPage, self)
    self._wtFromMatrixWorkshopBtn = self:Wnd("wtFromMatrixWorkshopBtn", CollectionMysticalSkinBtn)
    self._wtFromMatrixWorkshopBtn:Event("OnClicked", self._OnShowMysticalWorkshopPage, self)
    self._wtMysticalSkinBtnPanel = self:Wnd("wtMysticalSkinBtnPanel", UIWidgetBase)
    self._wtApplyBtn = self:Wnd("wtApplyBtn", DFCommonButtonOnly)
    self._wtApplyBtn:Event("OnClicked", self._ApplyPendant, self)
    self._wtEmptyBg = self:Wnd("wtEmptyBg", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.NoItems)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._wtAlertHintBox = self:Wnd("wtAlertHintBox", UIWidgetBase) 
    self._wtAlertHintTxt = self:Wnd("wtAlertHintTxt", UIWidgetBase) 
    self._wtWeaponSkinMissionProgressPanel = self:Wnd("wtWeaponSkinMissionProgressPanel", CommonWeaponSkinMissionProgress)
    self._wtDetailBtn = self:Wnd("wtDetailBtn", DFCommonButtonOnly)
    self._wtDetailBtn:Event("OnClicked", self._ShowHangingDetailPage, self)
    self._wtDetailBtn_PC = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)
    self._wtDetailBtn_PC:Event("OnClicked", self._ShowHangingDetailPage, self)
    self._wtTradeBtn = self:Wnd("wtTradeBtn", DFCommonButtonOnly)
    self._wtTradeBtn:Event("OnClicked", self._JumpToTradePage, self)
    self._wtDownloadBtn = self:Wnd("wtDownloadBtn", DFCommonButtonOnly)
    self._wtDownloadBtn:Event("OnClicked", self._DownLoadResources, self)
    self._wtDownloadBtn:Collapsed()
    self._wtCollectionsHallBtn = self:Wnd("wtCollectionsHall", DFCommonButtonOnly)
    self._wtCollectionsHallBtn:Event("OnClicked", self._JumpToCollectionsHall, self)
    self._wtCollectionsHallBtn:Collapsed()
    -- if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("wtCommonDownload", LiteCommonDownload)
    -- end
    if self._wtCommonDownload then
        self._wtCommonDownload:Collapsed()
    end
    self._wtHideUIBtn = self:Wnd("wtHideUIBtn", UIButton)
    if not IsHD() then
        --self._wtHideUIBtn:Event("OnClicked", self._ToggleUI, self)
    end
    self._wtHideUIBtn:Collapsed()
    -- 截屏分享相关
    self._wtShareBtn = self:Wnd("wtShareBtn", DFCommonButtonOnly)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick,self)
    self._selectedPos = -1
    self._selectedMysticalPos = -1
    self._selectedCell = nil
    self._pendantItems = {}
    self._mysticalPendantItems = {}
    self._redDotInsMap = setmetatable({}, weakmeta_key)
    self._selectedPendant = {} -- [id] = item 的形式
    self._scrollStopHandle = nil
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function CollectionsHangingPagePanel:_RegisterDynamicNavConfig()  
    if not IsHD() then
        return
    end
    if self._wtApplyBtn:IsVisible() then 
        self:_DisableGamepadA()
    else 
        self:_EnableGamepadA()
    end
 end

 function CollectionsHangingPagePanel:_OnToggleTipsByPad()
    if not IsHD() then
        return
    end
    if self._wtItemDetailView:IsVisible() then
        --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
        self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
        if self._ItemDetailEquipWeaponMysticalSkin then
            self._ItemDetailEquipWeaponMysticalSkin:ToggleTips()
        end
    end
 end

 function CollectionsHangingPagePanel:OnInputTypeChanged(inputType)
    if not IsHD() or inputType ~= EGPInputType.Gamepad then
        self:_HideTips()
    end
    self._wtHangingScrollView:RefreshVisibleItems()
end

function CollectionsHangingPagePanel:_HideTips()
    if self._wtItemDetailView:IsVisible() then
        --self._wtItemDetailView是动态生成的，会根据初始化时传入的item生成不同类型的子UI
        self._ItemDetailEquipWeaponMysticalSkin =  self._wtItemDetailView._CollectionWeaponSkinPanel
        if self._ItemDetailEquipWeaponMysticalSkin then
            self._ItemDetailEquipWeaponMysticalSkin:OnBtnTipsUnhoverd()
        end
    end
end

 function CollectionsHangingPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtMysticalSkinBtnPanelNavGroup then
        self._wtMysticalSkinBtnPanelNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtMysticalSkinBtnPanel, self, "Hittest")
        if self._wtMysticalSkinBtnPanelNavGroup then
            self._wtMysticalSkinBtnPanelNavGroup:AddNavWidgetToArray(self._wtMysticalSkinBtnPanel) 
        end
    end 
    -- if not self._wtNavGroupFilterBtn then
    --     self._wtNavGroupFilterBtn = WidgetUtil.RegisterNavigationGroup(self._wtFilterBtn, self, "Hittest")
    --     if self._wtNavGroupFilterBtn then
    --         self._wtNavGroupFilterBtn:AddNavWidgetToArray(self._wtFilterBtn)
    --     end
    -- end
    if not self._wtNavGroup then 
        self._ScrollGridBox = self:Wnd("wtWeaponSkinScrollView", UIWidgetBase)
        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._ScrollGridBox, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self._ScrollGridBox)
            self._wtNavGroup:SetScrollRecipient(self._ScrollGridBox)
            self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    --添加长按A.应用外观
    self._wtApplyBtn:SetDisplayInputAction("Collection_Apply_Gamepad", true, nil, true)
    self._wtDetailBtn_PC:SetDisplayInputAction("GunSkin_ShowDetail", true, nil, true)    
    self._wtFilterBtn:SetDisplayInputAction("Collection_FilterLeft_Gamepad", true, nil, true) 
    self._wtMysticalHangingDropDown:EnableDropDownShortcutWithAction(true, "Collection_Style_Gamepad")
    if not self._wtDetailBtn_PC_handle then
	    self._wtDetailBtn_PC_handle = self:AddInputActionBinding("GunSkin_ShowDetail", EInputEvent.IE_Pressed, self._ShowHangingDetailPage, self, EDisplayInputActionPriority.UI_Stack)
    end
    if not self._wtFilterBtnHandle then
        self._wtFilterBtnHandle = self:AddInputActionBinding("Collection_FilterLeft_Gamepad", EInputEvent.IE_Pressed, self._OnFilterBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end  
    if not self._hNavigationChangedFocus then
        self._hNavigationChangedFocus = self._wtNavGroup.OnNavGroupFocusReceivedEvent:Add(self._DisableGamepadA, self)
    end
    -- if not self._hNavigationChangedFocus2 then
    --     self._hNavigationChangedFocus2 = self._wtNavGroupFilterBtn.OnNavGroupFocusReceivedEvent:Add(self._EnableGamepadA, self)
    -- end
    if not self._hNavigationChangedFocus3 then
        self._hNavigationChangedFocus3 = self._wtMysticalSkinBtnPanelNavGroup.OnNavGroupFocusReceivedEvent:Add(self._EnableGamepadA, self)
    end
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
end

function CollectionsHangingPagePanel:_SetDefaultGamepadFocus()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._wtNavGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
    end
end

function CollectionsHangingPagePanel:_AddInputActionForApplyBtn()
    if not IsHD() then
        return 
    end
    if not self._ApplyBtnUpgrade then
        self._ApplyBtnUpgrade = self:AddInputActionBinding("Collection_Apply_Gamepad", EInputEvent.IE_Pressed, self._ApplyPendant,self, EDisplayInputActionPriority.UI_Stack)
    end
end
function CollectionsHangingPagePanel:_RemoveInputActionForApplyBtn()
    if not IsHD() then
        return 
    end
    if  self._ApplyBtnUpgrade then
        self:RemoveInputActionBinding(self._ApplyBtnUpgrade)
        self._ApplyBtnUpgrade = nil 
    end
end

function CollectionsHangingPagePanel:_DisableGamepadA()
    if not IsHD() then
        return 
    end
    if self._NavConfigHandler then 
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function CollectionsHangingPagePanel:_EnableGamepadA()
    if not IsHD() then
        return 
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function CollectionsHangingPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_HideTips()
    if self._wtDetailBtn_PC_handle then
        self:RemoveInputActionBinding(self._wtDetailBtn_PC_handle)
        self._wtDetailBtn_PC_handle = nil 
    end
    if  self._ApplyBtnUpgrade then
        self:RemoveInputActionBinding(self._ApplyBtnUpgrade)
        self._ApplyBtnUpgrade = nil 
    end
    if self._wtFilterBtnHandle then
        self:RemoveInputActionBinding(self._wtFilterBtnHandle)
        self._wtFilterBtnHandle = nil
    end
    self._wtMysticalHangingDropDown:EnableDropDownShortcut(false)
    WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    self._NavConfigHandler = nil

    WidgetUtil.RemoveNavigationGroup(self)

    if self._hNavigationChangedFocus then
        self._wtNavGroup.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus)
        self._hNavigationChangedFocus = nil
    end
    if self._hNavigationChangedFocus2 then
        self._wtNavGroupFilterBtn.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus2)
        self._hNavigationChangedFocus2 = nil
    end
    if self._hNavigationChangedFocus3 then
        self._wtMysticalSkinBtnPanelNavGroup.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus3)
        self._hNavigationChangedFocus3 = nil
    end
    if self._wtNavGroup then
        self._wtNavGroup = nil 
    end
    if self._wtMysticalSkinBtnPanelNavGroup then
        self._wtMysticalSkinBtnPanelNavGroup = nil
    end
    -- if self._wtNavGroupFilterBtn then
    --     self._wtNavGroupFilterBtn = nil
    -- end
end

function CollectionsHangingPagePanel:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end
    if bOpen then
        self:_DisableGamepadA()
    else
        self:_EnableGamepadA()
    end
end
-- END MODIFICATION








function CollectionsHangingPagePanel:OnInitExtraData()
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionsHangingPagePanel:OnOpen()
    self:OrnamentsPadding()
    self._newCollectionPendantReddot = Module.ReddotTrie:RegisterStaticReddotDotWithConfig(self._wtCollectionsHallBtn,
        {{obType = EReddotTrieObserverType.Collection, key = "NewCollectionPendant"}}
    )
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionsHangingPagePanel:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.Hanging)
    if self._newCollectionPendantReddot ~= nil then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._newCollectionPendantReddot)
        self._newCollectionPendantReddot = nil
    end
end

function CollectionsHangingPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionsHangingPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionsHangingPagePanel:OnShow()
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtHangingScrollView, self)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsHangingPagePanel:OnHide()
    self._bDropDownBoxVisible = self._wtMysticalHangingDropDown:IsChecked()

    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionsHangingPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_GunTypeMain_in then
        if not hasdestroy(self._selectedCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        end
    end
end

function CollectionsHangingPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        if bFullReset == true then
            self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._RefreshDownloadBtn,self)
        end
        self:AddLuaEvent(CollectionConfig.Events.evtOnHangingFilterUpdated, self._OnHangingFilterUpdated, self)
        self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:AddLuaEvent(Module.Share.Config.Events.evtShareFlowFinish,self.OnShareFlowFinish, self)
        self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes,self._OnProcessevtCSWAssemblyApplySkinRes, self)
        self._wtMysticalHangingDropDown:SwitchCheckButtonState(self._bDropDownBoxVisible == true and ECheckButtonState.Checked or ECheckButtonState.Unchecked)
        -- LuaGlobalEvents.evtStageChangeEnter:AddListener(self.SubStageChangeEnter, self)
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    else
        if bFullReset == true then
            self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._RefreshDownloadBtn,self)
            self._selectedCell = nil
        end
        self:RemoveLuaEvent(CollectionConfig.Events.evtOnHangingFilterUpdated, self._OnHangingFilterUpdated, self)
        self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
        self:RemoveLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes,self._OnProcessevtCSWAssemblyApplySkinRes, self)
        -- LuaGlobalEvents.evtStageChangeEnter:RemoveListener(self.SubStageChangeEnter, self)
        self._bHideUI = nil
    end
end


function CollectionsHangingPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    self._mainTabIndex = mainTabIndex or self._mainTabIndex
    if bResetList then
        self._bDropDownBoxVisible = false
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.Hanging)
    end
    if bResetTab then 
        self:_ShowUI()
    end
    self:_OnRefreshHangingItems(bResetList)
end

function CollectionsHangingPagePanel:_OnCheckedBoxStateChangedNative(bChecked)
    if self._wtMysticalHangingDropDown:IsVisible() then 
        if bChecked then 
            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.Apply)
        else
            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.SetDefault)
        end
    end
end

function CollectionsHangingPagePanel:_OnRefreshHangingItems(bResetList)
    local filterData = CollectionLogic.GetLastHangingFilterData()
    local selectedSkinGroups = filterData.selectedSkinGroups
    local skinGroup = table.nums(selectedSkinGroups) > 1 and 0 or table.keys(selectedSkinGroups)[1]
    local selectedSkinTypes = filterData.selectedSkinTypes
    local skinType = table.nums(selectedSkinTypes) > 1 and 0 or table.keys(selectedSkinTypes)[1]
    local selectedQualityIDs = filterData.selectedQualityIDs
    local selectedSeason= filterData.selectedSeason
    self._pendantItems = CollectionLogic.GetPendants(skinGroup, skinType,nil,nil,nil,selectedSeason)
    self._equipPendants = CollectionLogic.getEquipPendants()
    local ownedHangingItems = Server.CollectionServer:GetOwnedHangings()
    local filterHangings = {}
	-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then 
    	self:_RegisterDynamicNavConfig()    
    end
    -- END MODIFICATION
    if #self._pendantItems > 0 then
        for index, hangingItem in ipairs(self._pendantItems) do
            if filterHangings[hangingItem.id] == nil then
                if ItemHelperTool.IsMysticalPendant(hangingItem.id) then
                    if Server.CollectionServer:IsOwnedHanging(hangingItem.id) then
                        local instanceItems = CollectionLogic.GetMysticalPendantById(hangingItem.id)
                        local selectedItem = instanceItems[1]
                        for k,v in pairs(instanceItems) do 
                            if self._equipPendants[hangingItem.id] == v.gid then 
                                selectedItem = v
                                break
                            end
                        end
                        declare_if_nil(filterHangings, hangingItem.id, selectedItem)
                    else
                        declare_if_nil(filterHangings, hangingItem.id, hangingItem)
                    end
                else
                    declare_if_nil(filterHangings, hangingItem.id, hangingItem)
                end
            end
        end
    end
    self._pendantItems = table.values(filterHangings)
    if table.nums(selectedQualityIDs) > 0 then
        local filterHangings = {}
        for index, skinItem in ipairs(self._pendantItems) do
            if selectedQualityIDs[skinItem.quality] == true then
                table.insert(filterHangings, skinItem)
            end
        end
        self._pendantItems = filterHangings
    end
    table.sort(self._pendantItems, CollectionLogic.HangingsDefaultSort)
    if bResetList then
        self._selectedCell = nil
        self._selectedPos = -1
        self._wtFromMandelBrickBtn:SetInfo(string.format(CollectionConfig.Loc.MandelDraw, tostring(0)), "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'")
        self._wtFromAuctionBtn:SetInfo(CollectionConfig.Loc.FromMarket, "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_De_Buy.CommonHall_De_Buy'")
        self._wtFromMatrixWorkshopBtn:SetInfo(CollectionConfig.Loc.FromMatrixWorkshop, "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Warehouse_Icon_0106.CommonHall_Warehouse_Icon_0106'")
        if #self._pendantItems > 0 then
            self._selectedPos = 0
            self._selectedMysticalPos = 0
            local item = self._pendantItems[self._selectedPos+1]
            local items = nil
            table.empty(self._mysticalPendantItems)
            if item then
                if ItemHelperTool.IsMysticalPendant(item.id) == true then
                    self._mysticalPendantItems = CollectionLogic.GetMysticalPendantById(item.id)
                    for k, v in ipairs(self._mysticalPendantItems) do
                        if self._equipPendants[v.id] == v.gid then 
                            self._selectedMysticalPos = k - 1
                            break
                        end
                    end
                end
            end
            if #self._mysticalPendantItems > 0 then
                items = self._mysticalPendantItems
            elseif item then
                items = {item}
            end
            if items and #items > 0 then
                Timer.DelayCall(1, function ()
                    CollectionLogic.RemoveRedDots(items)
                end)
            end 
        end
        self._wtHangingScrollView:RefreshAllItems()
    else
        if #self._pendantItems > 0 then
            local item = self._pendantItems[self._selectedPos+1]
            if item then
                if ItemHelperTool.IsMysticalPendant(item.id) == true then
                    self._mysticalPendantItems = CollectionLogic.GetMysticalPendantById(item.id)
                end
            end
        end
        self._wtHangingScrollView:RefreshVisibleItems()
    end
    if #self._pendantItems == 0 then
        self._wtEmptyBg:SelfHitTestInvisible()
        self._wtEmptySlot:SelfHitTestInvisible()
        self:_SetDefaultGamepadFocus()
    else
        self._wtEmptyBg:Collapsed()
        self._wtEmptySlot:Collapsed()
    end
    self:_RefreshItemUI(not bResetList)
end

function CollectionsHangingPagePanel:_OnGetTabItemCount()
    return #self._dropDownDataList
end

function CollectionsHangingPagePanel:_OnGetItemsCount()
    return #self._pendantItems
end

function CollectionsHangingPagePanel:_OnProcessItemWidget(position, itemWidget)
    local sizeBox = itemWidget:Wnd("SizeBox_0", UIWidgetBase)
    sizeBox:SetCppValue("WidthOverride",548) 
    sizeBox:SetCppValue("HeightOverride",254) 
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    local fClickCb = CreateCallBack(self._OnHangingItemClick, self,itemWidget, position)
    itemWidget:BindClickCallback(fClickCb)
    local item = self._pendantItems[position]
    if item ~= nil then
        local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
            return Server.CollectionServer:IsPropWithRedDot(item.id)
        end,self)
        if isvalid(self._redDotInsMap[itemWidget]) then
            Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.Hanging, self._redDotInsMap[itemWidget], fCheckFunc, nil)
        else
            self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.Hanging, fCheckFunc, nil ,itemWidget, {EReddotType.Normal})
        end
        local owned = Server.CollectionServer:GetHangingIfOwned(item.id,item.gid)
        itemWidget:InitCollectionHangingItem(item,owned,ItemHelperTool.IsMysticalPendant(item.id) and Server.CollectionServer:GetHangingInstanceNum(item.id) or nil)
    end
    if self._selectedPos == position-1 then
        self._selectedCell = itemWidget
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    end
    itemWidget:SetSelected(nil, self._selectedPos == position-1)
end

function CollectionsHangingPagePanel:_OnGetMySticalItems()
    return #self._mysticalPendantItems or 0
end
function CollectionsHangingPagePanel:_OnProcessMysticalHangingWidget(position, itemWidget)
    --AssemblyPresetItem
    local index = position + 1
    local item = self._mysticalPendantItems[index]
    if item then
        local fItemOnClicked = CreateCallBack(self._OnMysticalHangingItemClick, self)
        itemWidget:InitCollectionPendantData(position, item,  CollectionLogic.getWeaponlistByEquipPendant(item.id, item.gid), fItemOnClicked)
        itemWidget:SetSelected(self._selectedMysticalPos == position)
    end
    if self._selectedMysticalPos == position then
        self._selectedMysticalCell = itemWidget
    end
end

function CollectionsHangingPagePanel:_OnHangingItemClick(itemCell, position)
    Module.Collection.Config.Events.evtHangingPageItemClicked:Invoke(position)
    if self._selectedCell then
        local lastItem = self._pendantItems[self._selectedPos+1]
        if ItemHelperTool.IsMysticalPendant(lastItem.id) then
            if Server.CollectionServer:IsOwnedHanging(lastItem.id) then
                local instanceItems = CollectionLogic.GetMysticalPendantById(lastItem.id)
                local selectedItem = instanceItems[1]
                for k,v in pairs(instanceItems) do 
                    if next(CollectionLogic.getWeaponlistByEquipPendant(v.id, v.gid)) then
                        lastItem = v
                        break
                    end
                end
            end
        end
        local weaponDesc = self._selectedCell.item:GetRawDescObj()
        local pendantInfo = weaponDesc:GetPendantInfo()
        if lastItem.id == self._selectedCell.item.id or pendantInfo.PendantId == lastItem.id then 
            self._selectedCell:InitCollectionHangingItem(lastItem,Server.CollectionServer:GetHangingIfOwned(lastItem.id,lastItem.gid),ItemHelperTool.IsMysticalPendant(lastItem.id) and Server.CollectionServer:GetHangingInstanceNum(lastItem.id) or nil)
        end
    end
    if self._selectedPos ~= position-1 then
        local item = self._pendantItems[position]
        if self._selectedCell and self._selectedPos > -1 then
            self._selectedCell:SetSelected(nil, false)
        end
        table.empty(self._mysticalPendantItems)
        local items = nil
        if item then
            if ItemHelperTool.IsMysticalPendant(item.id) == true then
                self._mysticalPendantItems = CollectionLogic.GetMysticalPendantById(item.id)
            end
        end
        if #self._mysticalPendantItems > 0 then
            items = self._mysticalPendantItems
        elseif item then
            items = {item}
        end
        if items and #items > 0 then
            if self._selectedPos == -1 then
                Timer.DelayCall(1, function ()
                    CollectionLogic.RemoveRedDots(items)
                end)
            else
                CollectionLogic.RemoveRedDots(items)
            end
        end 
        self._selectedCell = itemCell
        self._selectedCell:SetSelected(nil, true)
    end
    if self._selectedPos ~= position-1 or self._wtMysticalHangingDropDown:IsChecked() == true then
        self._selectedPos = position-1
        self:_RefreshItemUI()
    end
end

function CollectionsHangingPagePanel:_OnMysticalHangingItemClick(itemCell, position)
    if self._selectedMysticalPos ~= position then
        if isvalid(self._selectedMysticalCell) then
            if self._selectedMysticalPos ~= position then
                self._selectedMysticalCell:SetSelected(false)
            end
        end
    end
    local index = position + 1
    local item = self._mysticalPendantItems[index]
    self._selectedMysticalCell = itemCell
    if self._selectedMysticalPos ~= position then
        itemCell:SetSelected(true)
    end
    self._selectedMysticalPos = position
    if self._wtMysticalHangingDropDown:IsChecked() == true then
        self._wtApplyBtn:SetIsEnabledStyle(true)
        -- self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
        self._wtApplyBtn:SelfHitTestInvisible()
        -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
        if IsHD() then
            self:_RegisterDynamicNavConfig()
        end
        -- END MODIFICATION
        if CollectionLogic.checkIfSkinAppliedOnWeapon(item.id, item.gid) then
            -- self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
            self._wtApplyBtn:SetIsEnabledStyle(false)
        else
            -- self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
        end
    end
    if isvalid(item) then
        self._selectedPendant[item.id] = item.gid
        self._wtItemDetailView:UpdateItem(item)
        self._wtItemDetailView:SetShowkillCnt(false)
        self._wtInfoPanel:SelfHitTestInvisible()
        self:OnRefreshModel(ESubStage.CollectionHanging)
    end
    if self._selectedCell then
        self._selectedCell:InitCollectionHangingItem(item,Server.CollectionServer:GetHangingIfOwned(item.id,item.gid),ItemHelperTool.IsMysticalPendant(item.id) and Server.CollectionServer:GetHangingInstanceNum(item.id) or nil)
    end
end

function CollectionsHangingPagePanel:_RefreshItemUI(bUpdateInfo)
    local item = self._pendantItems[self._selectedPos+1]
    self._equipPendants = CollectionLogic.getEquipPendants()
    if not bUpdateInfo or #self._pendantItems == 0 then
        self._wtApplyBtn:Collapsed()
		-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    	if IsHD() then
        	self:_RegisterDynamicNavConfig()
    	end
    	-- END MODIFICATION
        self._wtApplyBtn:SetIsEnabledStyle(true)
        self._wtMysticalSkinBtnPanel:Collapsed()
        self._wtMysticalHangingDropDown:Collapsed()
        self._wtWeaponSkinMissionProgressPanel:Collapsed()
        self._wtDetailBtn:Collapsed()
        self._wtDetailBtn_PC:Collapsed()
        self._wtTradeBtn:Collapsed()
        self._wtShareBtn:Collapsed()
        self._wtHideUIBtn:Collapsed()
        self._wtAlertHintBox:Collapsed()
        self._wtInfoPanel:Collapsed()
        self._wtCollectionsHallBtn:Collapsed()
        self._shotcutList = {}
        self._selectedMysticalPos = -1
        self._selectedMysticalCell = nil
        if isvalid(item) then
            self._wtApplyBtn:SetIsEnabled(true)
            self._wtApplyBtn:SetIsEnabledStyle(true)
            if IsHD() then
                table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
                table.insert(self._shotcutList, {actionName = "Pendant_ShowHall",func = self._JumpToCollectionsHall, caller = self ,bUIOnly = false, bHideIcon = false, reddot = {
                    {
                        obType = EReddotTrieObserverType.Collection,
                        key = "NewCollectionPendant"
                    },
			    }})
                self._wtDetailBtn_PC:Visible()
				-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
            	self:_AddInputActionForApplyBtn()
            	-- END MODIFICATION
            else
                self._wtDetailBtn:Visible()
                self._wtHideUIBtn:Visible()
 			end
			-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
        	--添加手柄操作
        	-- if IsHD() then
            -- 	if ItemHelperTool.IsMysticalPendant(item.id) == true then
            --     	table.insert(self._shotcutList, {actionName = "Common_ToggleTip",func = self._OnToggleTipsByPad, caller = self ,bUIOnly = false, bHideIcon = false})
            -- 	end
        	-- end
        	-- END MODIFICATION
            if Server.CollectionServer:IsOwnedHanging(item.id) == true then
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.SetDefault)
                if ItemHelperTool.IsMysticalPendant(item.id) == true then
                    if self._wtMysticalHangingDropDown:IsChecked() == true then
                        self._wtMysticalHangingDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
                    end
                    self._wtMysticalHangingDropDown:SelfHitTestInvisible()
                    self._wtMysticalHangingDropDown:SetMainTabText(string.format(CollectionConfig.Loc.MysticalSkinType, tostring(#self._mysticalPendantItems)))
                    if #self._mysticalPendantItems > 0 then
                        self._selectedMysticalPos = 0
                        for k, v in ipairs(self._mysticalPendantItems) do
                            if self._equipPendants[v.id] == v.gid then
                                self._selectedMysticalPos = k - 1
                                break
                            end
                        end
                    end
                end
                self._wtMysticalHangingDropDown:RefreshTab()
                if IsHD() then
                    if Server.MarketServer:CheckIsInSaleList(item.id) then
                        table.insert(self._shotcutList, {actionName = "Pendant_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                    end
                else
                    if Server.MarketServer:CheckIsInSaleList(item.id) then
                        self._wtTradeBtn:Visible()
                    end
                    -- Module.Share:FuncPointUnLock(SwitchSystemID.SubShareMetaphysicalGunSkin, self._wtShareBtn)
                end
                self._wtApplyBtn:SelfHitTestInvisible()
				-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
            	if IsHD() then
                	self:_RegisterDynamicNavConfig()
            	end            
            	-- END MODIFICATION
            else
                if ItemHelperTool.IsMysticalPendant(item.id) == true then
                    local mandelBrickId = CollectionLogic.GetMandelBrickIdByPendantId(item.id)
                    local mandelBrickItem = Server.CollectionServer:GetCollectionItemById(mandelBrickId)
                    self._wtFromMandelBrickBtn:SetInfo(string.format(CollectionConfig.Loc.PendantMandelDraw,tostring(mandelBrickItem ~= nil and mandelBrickItem.num or 0)), "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0202.CommonHud_MapMarker_Icon_0202'")
                    self._wtMysticalSkinBtnPanel:SelfHitTestInvisible()
                    if  Server.MarketServer:CheckIsInSaleList(item.id) then 
                        self._wtFromAuctionBtn:SelfHitTestInvisible()
                    else
                        self._wtFromAuctionBtn:Collapsed()
                    end
                else
                    local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
                    if itemConfigRow then
                        if (itemConfigRow.JumpID == nil or itemConfigRow.JumpID == 0) and CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        elseif itemConfigRow.ButtonDescription ~= nil and itemConfigRow.ButtonDescription ~= "" then
                            self._wtApplyBtn:SetMainTitle(itemConfigRow.ButtonDescription)
                        else
                            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                        end
                    else
                        if CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        else
                            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
                            self._wtApplyBtn:SetIsEnabledStyle(false)
							-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
                            self:_RemoveInputActionForApplyBtn()
                        	-- END MODIFICATION
						end
                    end
                    self._wtApplyBtn:SelfHitTestInvisible()
                end
            end
            local adapterFeature = item:GetFeature(EFeatureType.Adapter)
            if adapterFeature and adapterFeature:IsPendant() then
                if IsHD() then
                    -- table.insert(self._shotcutList, {actionName = "GunSkin_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
                else
                    self._wtCollectionsHallBtn:Visible()
                end
            end
        end
    else
        local selectedMysticalPendantItem = self._mysticalPendantItems[self._selectedMysticalPos+1]
        if isvalid(self._selectedMysticalCell) and isvalid(selectedMysticalPendantItem) then
            local fItemOnClicked = CreateCallBack(self._OnMysticalHangingItemClick, self)
            self._selectedMysticalCell:InitCollectionPendantData(self._selectedMysticalPos, selectedMysticalPendantItem,  CollectionLogic.getWeaponlistByEquipPendant(selectedMysticalPendantItem.id, selectedMysticalPendantItem.gid), fItemOnClicked)
        end
    end
    if isvalid(self._mysticalPendantItems[self._selectedMysticalPos + 1] or item) then
        self._wtItemDetailView:UpdateItem(self._mysticalPendantItems[self._selectedMysticalPos + 1] or item)
        self._wtItemDetailView:SetShowkillCnt(false)
        self._wtInfoPanel:SelfHitTestInvisible()
    end
    CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
    self:OnRefreshModel(ESubStage.CollectionHanging)
    self:UpdateBackground()
end

function CollectionsHangingPagePanel:OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.CollectionHanging then
        local item = nil
        if self._selectedMysticalPos ~= -1 then
            item = self._mysticalPendantItems[self._selectedMysticalPos+1]
        else
            item = self._pendantItems[self._selectedPos+1] 
        end
        if isvalid(item) then
            -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayWeapon", item:GetRawDescObj(), item.id, 
            -- false, false)
            --Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6, EOffsetType.ZOffset)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetEnableTrans",false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetIsAdapter", true)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayWeapon", item:GetRawDescObj(), item.id, 
            false, false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "DestroyWatch")
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetDisplayItem")
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetWeapon")
        end
        if IsHD() then 
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main")
        else
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main_Mobile")
        end
    end
end

-- function CollectionsHangingPagePanel:_OnWeaponSkinMissionStatusLoaded(skinId, bCanRetrieve)
--     local item = self._pendantItems[self._selectedPos+1] 
--     if item and item.id == skinId then
--         self._bCanRetrieveSkinId = nil
--         if bCanRetrieve == true then
--             self._bCanRetrieveSkinId = skinId
--         end
--         self._wtApplyBtn:SetMainTitle(bCanRetrieve == true and CollectionConfig.Loc.RetrieveWeaponSkin or CollectionConfig.Loc.NotUnlocked)
--         self._wtApplyBtn:SetIsEnabledStyle(true)
--         self._wtApplyBtn:SelfHitTestInvisible()
--     end
-- end

function CollectionsHangingPagePanel:_OnShowMandelBrickPage()
    if self._selectedPos >= 0 then
        local pendantId = self._pendantItems[self._selectedPos+1].id
        local manderBrick = CollectionLogic.GetMandelBrickIdByPendantId(pendantId)
        CollectionLogic.ShowMandelBrickPage(manderBrick,1,EMandelOpenSource.Collection)--CollectionLogic.GetMandelBrickIdBySkinId(skinId))
    end
end

function CollectionsHangingPagePanel:_OnShowAuctionPage()
    if self._selectedPos >= 0 then
        local item = self._pendantItems[self._selectedPos + 1]
        if item ~= nil then
            CollectionLogic.ShowMarketPage(EMarketSubPageType.MysticalPendant)
        end
    end
end

function CollectionsHangingPagePanel:_OnShowMysticalWorkshopPage()
    local tabInfo = Module.Collection.Field:GetTabInfo()
    for index, mainTabInfo in ipairs(tabInfo) do
        if mainTabInfo.mainTabId == 6 then
            Module.CommonBar:CheckTopTabGroup(index, true, 2)
            return
        end
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMysticalWorkshopPage, nil, nil)
end

function CollectionsHangingPagePanel:_JumpToTradePage()
    if self._selectedMysticalPos ~= -1 then
        Module.Market:JumpToMysticalSkinSellPopWindow(self._mysticalPendantItems[self._selectedMysticalPos+1])
    else
        Module.Market:JumpToMysticalSkinSellPopWindow(self._pendantItems[self._selectedPos+1])
    end
end

function CollectionsHangingPagePanel:_JumpToCollectionsHall()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionsHallMain)
end
    
function CollectionsHangingPagePanel:_DownLoadResources()
    if self._wtCommonDownload then
        self._wtCommonDownload:Visible()
    end
end

function CollectionsHangingPagePanel:_RefreshDownloadBtn()
    -- if self._wtCommonDownload then
    --     self._wtCommonDownload:Collapsed()
    --     local bDownloaded = self._wtCommonDownload:InitModuleKey("WeaponSkin")
    --     if not bDownloaded then
    --         self._wtDownloadBtn:Visible()
    --     else
    --         self._wtDownloadBtn:Collapsed()
    --     end
    -- else
    --     self._wtDownloadBtn:Collapsed()
    -- end
end

function CollectionsHangingPagePanel:_ToggleUI()
    if self._bHideUI == true then
        self._bHideUI = false
        self:_ShowUI()
    else
        self._bHideUI = true
        self:_HideUI()
    end
end

function CollectionsHangingPagePanel:_HideUI()
    self:HideUI(true)
    if IsHD() then
        self._shotcutList = {}
        table.insert(self._shotcutList, {actionName = "CollectionShowUI",func = self._ShowUI, caller = self ,bUIOnly = false, bHideIcon = false})
        CollectionLogic.RegStackUIInputSummary(self._shotcutList, true)
    else
        Module.CommonBar:SetTopBarVisible(false)
    end
end

function CollectionsHangingPagePanel:_ShowUI()
    self:HideUI(false)
    local item = self._pendantItems[self._selectedPos+1]
    if isvalid(item) then
        if IsHD() then
            self._shotcutList = {}
            table.insert(self._shotcutList, {actionName = "CollectionHideUI",func = self._HideUI, caller = self ,bUIOnly = false, bHideIcon = false})
            table.insert(self._shotcutList, {actionName = "Pendant_ShowHall",func = self._JumpToCollectionsHall, caller = self ,bUIOnly = false, bHideIcon = false, reddot = {
                    {
                        obType = EReddotTrieObserverType.Collection,
                        key = "NewCollectionPendant"
                    },
			    }})
            if Server.CollectionServer:IsOwnedHanging(item.id) == true and ItemHelperTool.IsMysticalPendant(item.id) == true then
                table.insert(self._shotcutList, {actionName = "Pendant_Trade",func = self._JumpToTradePage, caller = self ,bUIOnly = false, bHideIcon = false})
            end
            CollectionLogic.RegStackUIInputSummary(self._shotcutList, false)
        else
            Module.CommonBar:SetTopBarVisible(true)
        end
    end
end

function CollectionsHangingPagePanel:_ShowHangingDetailPage()
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalPendantItems[self._selectedMysticalPos+1]
    else
        item = self._pendantItems[self._selectedPos+1] 
    end
    if item ~= nil then 
        --Module.Invite:InviteByQQ(Server.AccountServer:GetPlayerId())
        --Module.Invite:InviteByQQFromClient()
        --Module.Invite:InviteByWeChat(Server.AccountServer:GetPlayerId())
        --Module.Jump:JumpByID(********)
        --Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, {item, ItemBase:New(***********), ItemBase:New(***********),ItemBase:New(***********),  ItemBase:New(***********),ItemBase:New(***********), ItemBase:New(***********)}, nil, nil, nil, true)
        --Module.Reward:OpenGestureUnlockPanel(***********)
        --Server.TeamServer:ApplyJoinFromMiniProgram(5348)
        --Module.Reward:OpenPendantGainPop(ItemBase:New(***********))
        --Module.Reward:OpenHeroStuffUnlockPanel(***********)
        --local testnum = tonumber("xixix34343")
        --Module.Reward:OpenSafehouseLevelUpRewardPanel(1001)
        --Module.Reward:OpenVehicleSkinGainPage({ItemBase:New(***********)})
        --[[
        local playerInfoList = {}
        local SOLRankScore = Server.RankingServer:GetRankScore()
        local SOLAttended = Server.RankingServer:GetHasAttended()
        local MPRankScore = Server.TournamentServer:GetRankScore()
        local MPAttended = Server.TournamentServer:GetHasAttended()
        for i = 1, 5, 1 do
            table.insert(playerInfoList, 
            {
                player_id = Server.AccountServer:GetPlayerId(),
                nick_name = Server.RoleInfoServer.nickName or "",
                pic_url = Server.RoleInfoServer.picUrl,
                level = Server.RoleInfoServer.accountLevel,
                season_lvl = Server.RoleInfoServer.seasonLevel,
                gender = Server.RoleInfoServer.gender,
                safehouse_degree = 0,
                sol_rank_score = SOLRankScore,
                sol_rank_attended = SOLAttended,
                mp_rank_score = MPRankScore,
                mp_rank_attended = MPAttended
            }
            )
        end
        Module.Social:ShowRecommendAddFriendPop(playerInfoList, 12)
        --]]
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
        Module.Collection:ShowHangingDetailPage(item)
    end
end


function CollectionsHangingPagePanel:_ApplyPendant()
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalPendantItems[self._selectedMysticalPos+1]
    else
        item = self._pendantItems[self._selectedPos+1] 
    end
    if self._wtMysticalHangingDropDown:IsChecked() then 
        local weaponList = CollectionLogic.getWeaponlistByEquipPendant(item.id,nil,true)
        local list = {}
        for _,weaponItem in pairs(weaponList) do 
            list[weaponItem.id] = weaponItem
        end
        if next(list) then 
            Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.EquipedDefault)
            CollectionLogic.EquipPendantAllModeByWeaponList(list,item.id,item.gid)
            self._wtMysticalHangingDropDown:RefreshTab()
        else
            if Server.CollectionServer:GetHangingIfOwned(item.id,item.gid) then 
                Facade.UIManager:AsyncShowUI(UIName2ID.CollectionHangingDefaultPanel, nil, self,item.id,item.gid)
            end
        end
        self._wtMysticalHangingDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
    else
        
        if item ~= nil then
            if Server.CollectionServer:GetHangingIfOwned(item.id,item.gid) then 
            -- if Server.CollectionServer:GetHangingIfOwned(item.id, item.gid) == true then
                Facade.UIManager:AsyncShowUI(UIName2ID.CollectionHangingDefaultPanel, nil, self,item.id,item.gid)
            -- elseif self._bCanRetrieveSkinId == item.id then
            --         Server.CollectionServer:RetrieveWeaponSkin(item.id)
            else
                self:_PurchasePendant()
            end
        end
    end
end

function CollectionsHangingPagePanel:_OnHangingFilterUpdated()
    self:_OnRefreshHangingItems(true)
end

function CollectionsHangingPagePanel:_PurchasePendant()
    local item = nil
    if self._selectedPos ~= #self._pendantItems then
        item = self._pendantItems[self._selectedPos+1]
        if item ~= nil then 
            local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
            if itemConfigRow ~= nil then
                local bHasTipInConfig = itemConfigRow.UnlockTip ~= nil and itemConfigRow.UnlockTip ~= ""
                if itemConfigRow.JumpID ~= nil and itemConfigRow.JumpID ~= 0 then
                    local bCanJump = Module.Jump:CheckCanJumpByID(itemConfigRow.JumpID)
                    if bCanJump then
                        Module.Jump:JumpByID(itemConfigRow.JumpID)
                    elseif bHasTipInConfig then
                        Module.CommonTips:ShowSimpleTip(itemConfigRow.UnlockTip)
                    else
                        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
                    end
                elseif CollectionLogic.IsItemInStore(item.id) == true then
                    Module.CommonTips:ShowConfirmWindow(
                        string.format(CollectionConfig.Loc.ConfirmPurchase, item.name),
                        function()
                            CollectionLogic.PurchaseItem(item.id)
                        end,
                        function()
            
                        end,
                        CollectionConfig.Loc.Cancel,
                        CollectionConfig.Loc.Confirm
                    )
                elseif bHasTipInConfig then
                    Module.CommonTips:ShowSimpleTip(itemConfigRow.UnlockTip)
                else
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)   
                end
            else
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.UnlockTip)
            end
        end
    end
end

function CollectionsHangingPagePanel:_OnFilterBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionHangingFilterPanel, nil, self)
end  

function CollectionsHangingPagePanel:ClosePopup()
end

function CollectionsHangingPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionsHangingPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end


function CollectionsHangingPagePanel:OnHandleMouseButtonUpEvent(mouseEvent)
    local absolutePoint = mouseEvent:GetScreenSpacePosition()
    if self._wtMysticalHangingDropDown:IsChecked() == true then
        local bInsideDropDown = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalHangingDropDown, absolutePoint)
        local bInsideDropDownBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalHangingDropDown._wtScrollGridBox, absolutePoint)
        local bInsideApplyBtn = UIUtil.CheckAbsolutePointInsideWidget(self._wtApplyBtn, absolutePoint)
        local bInsideItemDetailView = UIUtil.CheckAbsolutePointInsideWidget(self._wtItemDetailView, absolutePoint)
        local bInsideDetailBtn = UIUtil.CheckAbsolutePointInsideWidget(self._wtDetailBtn, absolutePoint)
        local bInsideDetailBtnPC = UIUtil.CheckAbsolutePointInsideWidget(self._wtDetailBtn_PC, absolutePoint)
        local bInsideTradeBtn = UIUtil.CheckAbsolutePointInsideWidget(self._wtTradeBtn, absolutePoint)
        local bInsidebottomBarShowDetailBtn = false
        local bottomBarShowDetailBtn = Module.CommonBar:GetBottomBarItemByActionName("Pendant_ShowDetail")
        if bottomBarShowDetailBtn then
            bInsidebottomBarShowDetailBtn = UIUtil.CheckAbsolutePointInsideWidget(bottomBarShowDetailBtn, absolutePoint)
        end
        local bInsidebottomBarTradeBtn = false
        local bottomBarTradeBtn = Module.CommonBar:GetBottomBarItemByActionName("Pendant_Trade")
        if bottomBarTradeBtn then
            bInsidebottomBarTradeBtn = UIUtil.CheckAbsolutePointInsideWidget(bottomBarTradeBtn, absolutePoint)
        end
        if not bInsideDropDown and not bInsideDropDownBox and not bInsideApplyBtn and not bInsideItemDetailView and not bInsideDetailBtn and not bInsideDetailBtnPC and not bInsideTradeBtn and not bInsidebottomBarShowDetailBtn and not bInsidebottomBarTradeBtn then
            local currentStackUI = Facade.UIManager:GetCurrentStackUI()
            if currentStackUI and currentStackUI.UINavID == UIName2ID.CollectionMainPanel then
                -- self._wtMysticalHangingDropDown:SwitchCheckButtonState(ECheckButtonState.Unchecked)
                self:_RefreshItemUI(false)
                if self._selectedCell then 
                    local item = nil
                    if self._selectedMysticalPos ~= -1 then
                        item = self._mysticalPendantItems[self._selectedMysticalPos+1]
                    else
                        item = self._pendantItems[self._selectedPos+1] 
                    end
                    local owned = Server.CollectionServer:GetHangingIfOwned(item.id,item.gid)
                    self._selectedCell:InitCollectionHangingItem(item,owned,ItemHelperTool.IsMysticalPendant(item.id) and Server.CollectionServer:GetHangingInstanceNum(item.id) or nil)
                end
            end
        end
    elseif self._wtMysticalHangingDropDown:IsVisible() == true then
        local bInsideDropDown = UIUtil.CheckAbsolutePointInsideWidget(self._wtMysticalHangingDropDown, absolutePoint)
        if bInsideDropDown == true then
            local item = self._mysticalPendantItems[self._selectedMysticalPos + 1]
            if isvalid(item) then
                self._wtApplyBtn:SetIsEnabledStyle(true)
                self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
                self._wtApplyBtn:SelfHitTestInvisible()
                if Server.CollectionServer:GetHangingIfOwned(item.id, item.gid) then
                    -- if CollectionLogic.checkIfSkinAppliedOnWeapon(item.id, item.gid) then
                    --     self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.AppearanceApplied)
                    --     self._wtApplyBtn:SetIsEnabledStyle(false)
                    -- else
                    --     self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ApplyAppearance)
                    -- end
                    self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.SetDefault)
                else
                    local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
                    if itemConfigRow then
                        if (itemConfigRow.JumpID == nil or itemConfigRow.JumpID == 0) and CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        elseif itemConfigRow.ButtonDescription ~= nil and itemConfigRow.ButtonDescription ~= "" then
                            self._wtApplyBtn:SetMainTitle(itemConfigRow.ButtonDescription)
                        else
                            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.ProceedToUnlock)
                        end
                    else
                        if CollectionLogic.IsItemInStore(item.id) == true then
                            local priceStr = CollectionLogic.GePriceStrByItemId(item.id)
                            self._wtApplyBtn:SetMainTitle(priceStr)
                        else
                            self._wtApplyBtn:SetMainTitle(CollectionConfig.Loc.NotUnlocked)
                            self._wtApplyBtn:SetIsEnabledStyle(false)
                        end
                    end
                end 
            end       
        end
    end
end

function CollectionsHangingPagePanel:PreScreenshotShare()
    Module.CommonBar:SetTopBarVisible(false)
    self._wtMainPanel:Hidden()
end

function CollectionsHangingPagePanel:AfterScreenshotShare()
    Module.CommonBar:SetTopBarVisible(true)
end

function CollectionsHangingPagePanel:OnShareFlowFinish()
    self._wtMainPanel:SelfHitTestInvisible()
end

function CollectionsHangingPagePanel:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end,self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end,self)
    local item = nil
    if self._selectedMysticalPos ~= -1 then
        item = self._mysticalPendantItems[self._selectedMysticalPos+1]
    else
        item = self._pendantItems[self._selectedPos+1] 
    end
    if item ~= nil then 
        -- Module.Share:ReqShareWeapon(item.id, item, "CollectionWeaponSkinDetailPage", PreScreenshotShare, AfterScreenshotShare)
    end
end

function CollectionsHangingPagePanel:SubStageChangeEnter(curSubStageType)
    if curSubStageType == ESubStage.CollectionHanging then 
        self:OnRefreshModel(curSubStageType)
    end
end

function CollectionsHangingPagePanel:_OnProcessevtCSWAssemblyApplySkinRes()
    self._wtMysticalHangingDropDown:RefreshTab()
end

return CollectionsHangingPagePanel
