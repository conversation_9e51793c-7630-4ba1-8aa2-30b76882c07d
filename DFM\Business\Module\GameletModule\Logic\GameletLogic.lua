----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGamelet)
----- LOG FUNCTION AUTO GENERATE END -----------



local function log(...)
    logerror("[GameletLogic]", ...)
end

local GameletLogic = {}
local UGamelet = import "DFMGamelet"
local UGameletSettings = import "GameletSettings"
local this = GameletLogic
local UGameVersionUtils = import "GameVersionUtils"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local Json = JsonFactory.createJson()
local UAppSetting = import "AppSetting"
local UGameFriend = import "DFMGameFriend"
local GameFriendMgr = UGameFriend.Get(GetWorld())
local LastShareAppID = ""
local LastShareType = ""
local ULuaExtension = import "LuaExtension"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"

local function GetLanguage()
    return LocalizeTool.GetCurrentCulture()
end

-- 潘多拉通知展示入口
local function PandoraShowEntrance(proto)
    Module.Gamelet.Field:AddActiveApp(proto.appId, proto)
    Module.Gamelet.Config.evtPandoraShowEntrance:Invoke(proto)
    --if proto.appName == "TestDemoPixui" then --潘多拉验收测试弹窗
    --    Timer.DelayCall(2, function ()    --避免登录后收到sdk消息过早挂载窗口被清除
    --        GameletLogic.OpenApp(proto.appId)
    --    end)
    --end
    --GameletLogic.ShowEntrance()
end

-- 红点显隐
local function PandoraShowRedpoint(proto)
    logerror("PandoraShowRedpoint", proto)
    if proto then
        local bRed = proto.content == "1"
        logerror("PandoraShowRedpoint app", proto.appId, "red", bRed)
        if bRed then
            Module.Gamelet.Field:AddAppIDRedDot(proto.appId)
        else
            Module.Gamelet.Field:DelAppIDRedDot(proto.appId)
        end
    end
    Module.Gamelet.Config.evtPandoraShowRedpoint:Invoke(proto)
end

-- 展示文字，飘字
local function PandoraShowTextTip(proto)
    local msg = proto.content
    Module.CommonTips:ShowSimpleTip(msg)
end

-- 显示正在加载的动画
local function PandoraShowLoading(proto)
    local bShow = tonumber(proto.content) == 1
    if bShow then
        Module.Preparation:ShowLoadingUI()
    else 
        Module.Preparation:CloseLoadingUI()
    end
end

-- 展示获得道具的弹窗
local function PandoraShowReceivedItem(proto)
    local itemListStr = proto.content
    local item_data = string.split(itemListStr, ",")
    local itemList = {}
    if not item_data then
        log("道具格式错误"..itemListStr)
        return 
    end
    local char_split = "|"
    for _, itemStr in pairs(item_data) do
        if not string.isempty(itemStr) then
            local tItem = string.split(itemStr, char_split)
            if not table.isempty(tItem) then
                local itemId = tonumber(tItem[1])
                local itemNum = tonumber(tItem[2])
                if itemNum > 0 then
                    local item = ItemBase:New(itemId, itemNum)
                    table.insert(itemList, item)
                end
            end
        end
    end
    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)
    -- Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, itemList)
end

-- 打开网页
local function PandoraOpenUrl(proto)
    local url = proto.content
    local screenType = proto.screenType
    local isFullScreen = proto.isFullScreen
    local isUseURLEncode = proto.isUseURLEncode
    local extraJson = proto.extraJson
    local isBrowser = proto.isBrowser
    local webBrowserData = proto.webBrowserData
    -- Module.GCloudSDK:OpenUrl(url, 1, true, false, nil, true)
    Module.GCloudSDK:OpenUrl(url, screenType, isFullScreen, isUseURLEncode, extraJson, isBrowser, webBrowserData)
end

-- 打开游戏内面板
local function PandoraGoSystem(proto)
    local jumpID = tonumber(proto.jumpID)
    local args = proto.args
    local arr = string.split(args, ",")
    Module.Jump:JumpByID(jumpID, arr)
end

-- 打开另外一个潘多拉活动
local function PandoraGoPandora(proto)
    local json_args = {
        appPage = proto.targetAppPage,
    }
    local sJson = Json.encode(json_args)
    GameletLogic.ShowGameletStackNoCB(proto.targetAppId, sJson)
end

-- 获取玩家信息
local function PandoraGetUserInfo(proto)
    local playerListCount = #proto.playerIDList
    local playerNowCount = 0
    
    local info = {}
    
    local playerID = ULuautils.GetUInt64StrToInt64(proto.playerIDList[1])
    local playerIDStr = proto.playerIDList[1]

    local function fCallback(res)
        table.insert(info, {
            playerID = playerIDStr,
            name = res.nick_name,
            avatar = res.pic_url
        })

        playerNowCount = playerNowCount + 1

        if playerListCount == playerNowCount then
            local json_info = {
                type = "getUserInfoResult",
                appId = proto.appId,
                info = info
            }
            local json_args = Json.encode(json_info)

            GameletLogic.SendMessageToApp(proto.appId, json_args)
        else
            playerID = ULuautils.GetUInt64StrToInt64(proto.playerIDList[playerNowCount + 1])
            playerIDStr = proto.playerIDList[playerNowCount + 1]
            Timer.DelayCall(1, function()
                Server.AccountServer:ReqGetPlayerProfile(playerID, fCallback)
            end)
        end
    end

    Server.AccountServer:ReqGetPlayerProfile(playerID, fCallback)
end

local function AppendAppIDToJson(sJson, sAppID, type) 
    local tb = Json.decode(sJson)
    if tb == nil then
        return "{}"
    end
    tb.DFMAppID = sAppID
    tb.DFMType = type
    local rt = Json.encode(tb)
    return rt
end

local function GetChannel()
    local channelId = Server.SDKInfoServer:GetChannel()
    return Module.Share.Config.EChannelMapShareChannelStr[channelId]
end

-- 国内MSDK分享（V5）发消息给好友
local function PandoraSendMessage(proto)
    local appId = proto.appId

    local extraJson = ""
    if proto.extraParam and proto.extraParam ~= "" then
        extraJson = AppendAppIDToJson(proto.extraJson, appId, "sendMessageResult")
    end
    UGameFriend.SendMessageLua(GetChannel(), tonumber(tostring(proto.friendReqType)), proto.user, proto.title, proto.desc, proto.link, proto.thumbPath, proto.mediaPath, extraJson)
    
    LastShareAppID = appId
    LastShareType = "sendMessageResult"
end

-- 国内MSDK分享（V5）分享到空间/朋友圈
local function PandoraShare(proto)
    local appId = proto.appId

    local extraJson = ""
    if proto.extraParam and proto.extraParam ~= "" then
        extraJson = AppendAppIDToJson(proto.extraJson, appId, "shareResult")
    end
    UGameFriend.ShareLua(GetChannel(), tonumber(tostring(proto.friendReqType)), proto.user, proto.title, proto.desc, proto.link, proto.thumbPath, proto.mediaPath, extraJson, "")
    
    LastShareAppID = appId
    LastShareType = "shareResult"
end

-- 国内MSDK分享（V5） ARK分享 游戏需要接入msdk此分享功能才可以使用
local function PandoraShareArkMessageToQQInSilence(proto)
    local appId = proto.appId

    local json_info = {
        type = "shareArkMessageToQQInSilenceResult",
        content = "use pandoraSendMessage"
    }
    local json_args = Json.encode(json_info)

    GameletLogic.SendMessageToApp(appId,json_args)
end

local function PandoraActCenterReady(proto)
    local appId = proto.appId
    Module.Gamelet.Field:AddActivityAppTable(appId, proto)
    Module.Gamelet.Config.evtPandoraActCenterReady:Invoke(proto)
end

local function PandoraCopy2Clipboard(proto)
    ULuaExtension.RoleInfoCopy2Clipboard(proto.content)
    Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.CopySuccessTips)
end

local function PandoraShowItemTip(proto)
    local args = proto.content
    local arr = string.split(args, ",")
    
    local ID = tonumber(arr[1])
    local width = tonumber(arr[2])
    local height = tonumber(arr[3])
    local left = tonumber(arr[4])
    local top = tonumber(arr[5])
    local right = tonumber(arr[6])
    local bottom = tonumber(arr[7])

    local item = ItemBase:New(ID, 1)
    local fOnLoadCallback = function(detailIns)
        detailIns:UpdateDetailViewPositionByWidgetInfo(width, height, left, top)
    end
    Module.ItemDetail:OpenItemDetailPanel(item, nil, nil, false,nil,nil, fOnLoadCallback)
end

local function PandoraShowSpecialReward(proto)
    local args = proto.content
    local arr = string.split(args, ",")

    local tItemList = {}
    for index, reward in pairs(arr) do
        local tID2Count = string.split(reward, "|")
        local id = tID2Count[1]
        local count = tID2Count[2]
        if id and count then
            local item = ItemBase:New(id, count)
            if item.itemMainType == EItemType.WeaponSkin then
                local itemWeapon = nil
                itemWeapon = Server.CollectionServer:GetWeaponSkinIfExists(id)
                if itemWeapon == nil then
                    local propInfo = {
                        id = id,
                        gid = 0,
                        num = count
                    }
                    itemWeapon = ItemHelperTool.CreateItemByPropInfo(propInfo)
                end
                table.insert(tItemList, itemWeapon)
            else
                table.insert(tItemList, item)
            end
        end
    end

    Module.Reward:OpenSpecialRewardPanel(tItemList)
end

local function PandoraActivityWheelBanArea(proto)
    local tAreaList = {}
    local arr = string.split(proto.area, ",")
    for index, area in pairs(arr) do
        local tArea = {}
        local tArgs = string.split(area, "|")
        if tArgs[1] then
            tArea["left"] = tonumber(tArgs[1])
        end
        if tArgs[2] then
            tArea["top"] = tonumber(tArgs[2])
        end
        if tArgs[3] then
            tArea["width"] = tonumber(tArgs[3])
        end
        if tArgs[4] then
            tArea["height"] = tonumber(tArgs[4])
        end

        if tArgs[1] and tArgs[3] then
            tArea["right"] = tonumber(tArgs[1]) + tonumber(tArgs[3])
        end
        if tArgs[2] and tArgs[4] then
            tArea["bottom"] = tonumber(tArgs[2]) + tonumber(tArgs[4])
        end
        table.insert(tAreaList, tArea)
    end
    Module.Gamelet.Field:SetActivityAppWheelBanArea(proto.appId, tAreaList)
end

local function pandoraShowPlayerInfo(proto)
    Module.RoleInfo:ShowMainPanel(ULuautils.GetUInt64StrToInt64(proto.playerID))
end

local function pandoraTaskFinishStatus(proto)
    logerror("pandoraTaskFinishStatus", proto)
    if proto then
        local bCond = tostring(proto.isTaskFinish) == "1"
        logerror("pandoraTaskFinishStatus app", proto.appId, "bCond", bCond)
        if bCond then
            Module.Gamelet.Field:AddAppIDTaskFinish(proto.appId)
        else
            Module.Gamelet.Field:DelAppIDTaskFinish(proto.appId)
        end
    end
    Module.Gamelet.Config.evtPandoraShowTaskFinish:Invoke(proto)
end

local function pandoraAddFriend(proto)
    local playerID = ULuautils.GetUInt64StrToInt64(proto.playerID)
    if Module.Friend:CheckIsGameFriend(playerID) then
        logerror("[GameletLogic] pandoraAddFriend, player ", playerID, "not friend")
        return
    end
    Module.Friend:AddFriend(playerID, FriendApplySource.OtherApply)
end

local function pandoraInviteTeam(proto)
    local playerID = ULuautils.GetUInt64StrToInt64(proto.playerID)
   
    local playerInfo = {
        player_id = playerID,
        team_id = 0,
        member_num = 0,
        state = 0,
        mode_info = 0,
        nick_name = 0,
    }
    
    local function fCallbackReqPlayerState(newStateList)
        for _,_playerInfo in pairs(newStateList) do
            local id = _playerInfo.player_id
            if id == playerID then
                playerInfo.team_id = _playerInfo.team_id
                playerInfo.member_num = _playerInfo.member_num
                playerInfo.state = _playerInfo.state
                playerInfo.mode_info = _playerInfo.mode_info

                local playerState = Module.Social:GetOnlinePlayerStateFromStateCode(playerInfo.state)
                if playerState == GlobalPlayerStateEnums.EPlayerState_Offline or
                        (playerInfo.team_id ~= 0 and playerInfo.team_id == Server.TeamServer:GetTeamID()) then
                    return
                end

                if Server.MatchServer:GetIsMatching() == true then
                    Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotInviteDuringMatching)
                else
                    Module.Social:TeamInvite(playerInfo, TeamInviteSource.FromOther)
                end
            end
        end
    end
    
    local function fCallbackReqGetPlayerProfile(res)
        playerInfo.nick_name = res.nick_name
        
        local playerList = {playerID}
        Server.SocialServer:ReqPlayerState(playerList, fCallbackReqPlayerState)
    end

    Server.AccountServer:ReqGetPlayerProfile(playerID, fCallbackReqGetPlayerProfile)
end

local function isUserInTeamReq(proto)
    local playerID = ULuautils.GetUInt64StrToInt64(proto.playerID)
    
    local function fCallback(newStateList)
        local inTeam = 0
        for _,playerInfo in pairs(newStateList) do
            local id = playerInfo.player_id
            if id == playerID then
                if (playerInfo.state & GlobalPlayerStateEnums.EPlayerState_InTeam) ~= 0 then
                    inTeam = 1
                end
            end
        end

        local json_info = {
            type = "isUserInTeamRsp",
            appId = proto.appId,
            playerID = proto.playerID,
            isInTeam = inTeam
        }

        local json_args = Json.encode(json_info)
        GameletLogic.SendMessageToApp(proto.appId,json_args)
    end
    
    local playerList = {playerID}
    Server.SocialServer:ReqPlayerState(playerList, fCallback)
end

local function isUserFriendReq(proto)
    local iIsFriend = 0
    if Module.Friend:CheckIsGameFriend(ULuautils.GetUInt64StrToInt64(proto.playerID)) then
        iIsFriend = 1
    else
        iIsFriend = 0
    end
    
    local json_info = {
        type = "isUserFriendRsp",
        appId = proto.appId,
        playerID = proto.playerID,
        isFriend = iIsFriend
    }

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local function GetGameInfoReq(proto)
    local json_info = {
        type = "GetGameInfoRsp",
        appId = proto.appId,
        language = GetLanguage() 
    }
    
    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local function GetFromClipboardReq(proto)
    local content = ULuaExtension.GetFromClipboard()
    
    local json_info = {
        type = "GetFromClipboardRsp",
        appId = proto.appId,
        content = content
    }

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local function pandoraGoRecharge(proto)
    Module.Store:ShowStoreRechargeMainPanle()
end

local function pandoraGoPreview(proto)
    local itemID = proto.itemID
    
    local itemData = ItemBase:NewIns(itemID)
    if itemData == nil then
        logerror("GameletLogic pandoraGoWeaponPreview item", itemID, "itemData nil")
        return
    end

    if itemData.itemMainType == EItemType.WeaponSkin then
        local itemWeapon = nil
        itemWeapon = Server.CollectionServer:GetWeaponSkinIfExists(itemID)
        if itemWeapon == nil then
            local propInfo = {
                id = itemID,
                gid = 0,
                num = 1
            }
            itemWeapon = ItemHelperTool.CreateItemByPropInfo(propInfo)
        end
        Module.Collection:ShowWeaponSkinDetailPage(itemWeapon)
    elseif itemData.itemMainType == EItemType.Adapter and itemData.itemSubType == ItemConfig.EAdapterItemType.Pendant then
        local itemHanging = nil
        itemHanging = Server.CollectionServer:GetWeaponSkinIfExists(itemID)
        if itemHanging == nil then
            local propInfo = {
                id = itemID,
                gid = 0,
                num = 1
            }
            itemHanging = ItemHelperTool.CreateItemByPropInfo(propInfo)
        end
        Module.Collection:ShowHangingDetailPage(itemHanging)
    else
        logerror("GameletLogic pandoraGoWeaponPreview item", itemID, "is not right type")
        return
    end
end

local function pandoraShowStackCurrency(proto)
    if proto.appId == nil or proto.currencyIDList == nil then
        logerror("GameletLogic pandoraShowStackCurrency, appId", proto.appId, "currencIDList", proto.currencyIDList)
        return
    end
    
    Module.Gamelet.Config.evtPandoraShowStackCurrency:Invoke(proto.appId, proto.currencyIDList)
end

local function pandoraGetCurrencyCountReq(proto)
    local json_info = {
        type = "pandoraGetCurrencyCountRes",
        appId = proto.appId,
        currencyCountList = {}
    }

    for i, currencyID in pairs(proto.currencyIDList) do
        local currencyItemNum = Module.Currency:GetNumByItemId(currencyID) 
        table.insert(json_info.currencyCountList, {ID = currencyID, Count = currencyItemNum}) 
    end

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local function pandoraGetInputTypeReq(proto)
    local json_info = {
        type = "pandoraGetInputTypeRes",
        appId = proto.appId,
    }

    json_info.inputType = WidgetUtil.GetCurrentInputType()
    json_info.gamepadInputType = WidgetUtil.GetCurrentGamepadInputType()

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local function pandoraShowItemScene(proto)
    Module.Gamelet.Config.evtPandoraShowItemScene:Invoke(proto.appId, proto.itemID, proto.typeFunc, proto.extraParam)
end

local function pandoraPlayUIAudioEvent(proto)
    if proto.resType == 1 then
        if proto.action == 1 then
            Facade.SoundManager:PlayUIAudioEvent(proto.event)
        elseif proto.action == 2 then
            Facade.SoundManager:StopUIAudioEvent(proto.event)
        end
    elseif proto.resType == 2 then
        if proto.action == 1 then
            Facade.SoundManager:PlayMusicByName(proto.event)
        elseif proto.action == 2 then
            local transitionMs = 0
            if proto.transitionMs then
                transitionMs = proto.transitionMs
            end
            Facade.SoundManager:StopMusicByName(proto.event, transitionMs)
        end
    end
end

local function pandoraGetPayInfoReq(proto)
    local json_info = {
        type = "pandoraGetPayInfoRes",
        appId = proto.appId,
    }

    json_info.payInfo = Server.PayServer:GetPayToken()

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(proto.appId,json_args)
end

local lastPayAppID = nil
local function pandoraPayReq(proto)
    Module.Pay:Pay(proto.goodsTokenUrl)
    lastPayAppID = proto.appId
end

local function ProcessSDKProto(proto_json)
    log("收到潘多拉SDK消息通知:"..proto_json)
    local proto = Json.decode(proto_json)
    if proto then
        local ret_type = proto.type
        if ret_type == "pandoraShowEntrance" then
            PandoraShowEntrance(proto)
        elseif ret_type == "pandoraShowRedpoint" then
            PandoraShowRedpoint(proto)
        elseif ret_type == "pandoraShowTextTip" then
            PandoraShowTextTip(proto)
        elseif ret_type == "pandoraShowLoading" then
            PandoraShowLoading(proto)
        elseif ret_type == "pandoraShowReceivedItem" then
            PandoraShowReceivedItem(proto)
        elseif ret_type == "pandoraOpenUrl" then
            PandoraOpenUrl(proto)
        elseif ret_type == "pandoraGoSystem" then
            PandoraGoSystem(proto)
        elseif ret_type == "pandoraGoPandora" then
            PandoraGoPandora(proto)
        elseif ret_type == "pandoraGetUserInfo" then
            PandoraGetUserInfo(proto)
        elseif ret_type == "pandoraSendMessage" then
            PandoraSendMessage(proto)
        elseif ret_type == "pandoraShare" then
            PandoraShare(proto)
        elseif ret_type == "pandoraShareArkMessageToQQInSilence" then
            PandoraShareArkMessageToQQInSilence(proto)
        elseif ret_type == "pandoraActCenterReady" then
            PandoraActCenterReady(proto)
        elseif ret_type == "pandoraCopy2Clipboard" then
            PandoraCopy2Clipboard(proto)
        elseif ret_type == "pandoraShowItemTip" then
            PandoraShowItemTip(proto)
        elseif ret_type == "pandoraShowSpecialReward" then
            PandoraShowSpecialReward(proto)
        elseif ret_type == "pandoraActivityWheelBanArea" then
            PandoraActivityWheelBanArea(proto)
        elseif ret_type == "pandoraShowPlayerInfo" then
            pandoraShowPlayerInfo(proto)
        elseif ret_type == "pandoraTaskFinishStatus" then
            pandoraTaskFinishStatus(proto)
        elseif ret_type == "pandoraAddFriend" then
            pandoraAddFriend(proto)
        elseif ret_type == "pandoraInviteTeam" then
            pandoraInviteTeam(proto)
        elseif ret_type == "isUserInTeamReq" then
            isUserInTeamReq(proto)
        elseif ret_type == "isUserFriendReq" then
            isUserFriendReq(proto)
        elseif ret_type == "GetGameInfoReq" then
            GetGameInfoReq(proto)
        elseif ret_type == "GetFromClipboardReq" then
            GetFromClipboardReq(proto)
        elseif ret_type == "pandoraGoRecharge" then
            pandoraGoRecharge(proto)
        elseif ret_type == "pandoraGoPreview" then
            pandoraGoPreview(proto)
        elseif ret_type == "pandoraShowStackCurrency" then
            pandoraShowStackCurrency(proto)
        elseif ret_type == "pandoraGetCurrencyCountReq" then
            pandoraGetCurrencyCountReq(proto)
        elseif ret_type == "pandoraGetInputTypeReq" then
            pandoraGetInputTypeReq(proto)
        elseif ret_type == "pandoraShowItemScene" then
            pandoraShowItemScene(proto)
        elseif ret_type == "pandoraPlayUIAudioEvent" then
            pandoraPlayUIAudioEvent(proto)
        elseif ret_type == "pandoraGetPayInfoReq" then
            pandoraGetPayInfoReq(proto)
        elseif ret_type == "pandoraPayReq" then
            pandoraPayReq(proto)
        end
    end
end

function GameletLogic.SendInputTypeChangeEvent(appID)
    local inputType = tostring(WidgetUtil.GetCurrentInputType())
    local gamepadInputType = tostring(WidgetUtil.GetCurrentGamepadInputType())
    
    local eventArgs = inputType .. ";" .. gamepadInputType
    
    GameletLogic.SendPandoraGameEvent(appID, "InputTypeChange", eventArgs)
end

function GameletLogic.SendPandoraPayRsp(result)
    local json_info = {
        type = "pandoraPayRsp",
        result = result,
    }

    if lastPayAppID then
        json_info.appId = lastPayAppID
        lastPayAppID = nil
    else
        return
    end

    local json_args = Json.encode(json_info)
    GameletLogic.SendMessageToApp(json_info.appId,json_args)
end

local function GetPlatId()
    local platId = "2" --pc
    --todo PLATFORM_OPENHARMONY
    if PLATFORM_ANDROID or _WITH_EDITOR == 1 then
        platId = "1"
    elseif PLATFORM_IOS then
        platId = "0"
    elseif PLATFORM_OPENHARMONY then
        platId = "12"
    elseif PLATFORM_XSX == 1 then
        platId = "4"
    elseif PLATFORM_PS5 == 1 then
        platId = "3"
    end
    return platId
end

local function GetServerPlatID()
    local platId = "2" --pc
    --todo PLATFORM_OPENHARMONY
    if PLATFORM_ANDROID or _WITH_EDITOR == 1 then
        platId = "1"
    elseif PLATFORM_IOS then
        platId = "0"
    elseif PLATFORM_OPENHARMONY then
        platId = "1"
    end
    return platId
end

local function GetGameAppId()
    if IsBuildRegionCN() then
        if VersionUtil.IsGameChannelWeGame() then
            return UAppSetting.Get().WeGameId 
        end
        local channelId = Server.SDKInfoServer:GetChannel()
        if channelId == EChannelType.kChannelQQ then
            return UAppSetting.Get().QQAppId
        elseif channelId == EChannelType.kChannelWechat then
            return UAppSetting.Get().WeixinAppId
        elseif channelId == EChannelType.kChannelSteam then
            return UAppSetting.Get().SteamAppId
        end
    elseif IsBuildRegionGlobal() then
        return UAppSetting.Get().SdkGameId
    elseif IsBuildRegionGA() then
        return UAppSetting.Get().SdkGameId
    end
    return UAppSetting.Get().SdkGameId
end

local function GetAccountType()
    local channelId = Server.SDKInfoServer:GetChannel()
    logerror("channel id", channelId)
    
    -- wegame
    if IsBuildRegionCN() then
        if VersionUtil.IsGameChannelWeGame() then
            if BUILD_REGION_CN then
                return "wegame"
            elseif BUILD_REGION_CN_EXPER then
                return "wegamety"
            elseif BUILD_REGION_CN_MATCH then
                return "wegamesh"
            else
                return "wegame"
            end
        end
    end
    
    if channelId == EChannelType.kChannelNone then
        return "None"
    elseif channelId == EChannelType.kChannelQQ then
        if BUILD_REGION_CN_EXPER then
            return "tyqq"
        else
            return "qq"
        end
    elseif channelId == EChannelType.kChannelWechat then
        if BUILD_REGION_CN_EXPER then
            return "tywx"
        else
            return "wx"
        end
    elseif channelId == EChannelType.kChannelGuest then
        return "ttpp"
    elseif channelId == EChannelType.kChannelFacebook then
        return "Facebook"
    elseif channelId == EChannelType.kChannelGameCenter then
        return "GameCenter"
    elseif channelId == EChannelType.kChannelGooglePlay then
        return "Google"
    elseif channelId == EChannelType.kChannelIEGPassport then
        return "IEGPassport"
    elseif channelId == EChannelType.kChannelFirebase then
        return "Firebase"
    elseif channelId == EChannelType.kChannelTwitter then
        return "Twitter"
    elseif channelId == EChannelType.kChannelGarena then
        return "Garena"
    elseif channelId == EChannelType.kChannelCustomAccount then
        return "CustomAccount"
    elseif channelId == EChannelType.kChannelEGame then
        return "EGame"
    elseif channelId == EChannelType.kChannelSwitch then
        return "Switch"
    elseif channelId == EChannelType.kChannelLine then
        return "Line"
    elseif channelId == EChannelType.kChannelApple then
        return "Apple"
    elseif channelId == EChannelType.kChannelVK then
        return "VK"
    elseif channelId == EChannelType.kChannelXbox then
        return "XBox"
    elseif channelId == EChannelType.kChannelSteam then
        return "Steam"
    elseif channelId == EChannelType.kChannelPS4 then
        return "PS4"
    elseif channelId == EChannelType.kChannelEpic then
        return "Epic"
    elseif channelId == EChannelType.kChannelDiscord then
        return "Discord"
    elseif channelId == EChannelType.kChannelPS5 then
        return "PS5"
    elseif channelId == EChannelType.kChannelDmm then
        return "Dmm"
    elseif channelId == EChannelType.kChannelSquareEnix then
        return "SquareEnix"
    elseif channelId == EChannelType.kChannelSupercell then
        return "Supercell"
    elseif channelId == EChannelType.kChannelAppsFlyer then
        return "AppsFlyer"
    elseif channelId == EChannelType.kChannelKaKao then
        return "KaKao"
    elseif channelId == EChannelType.kChannelUbisoft then
        return "Ubisoft"
    elseif channelId == EChannelType.kChannelVNG then
        return "VNG"
    elseif channelId == EChannelType.kChannelGooglePGS then
        return "GooglePGS"
    elseif channelId == EChannelType.kChannelEA then
        return "EA"
    elseif channelId == EChannelType.kChannelNintendo then
        return "Nintendo"
    elseif channelId == EChannelType.kChannelLevelInfinite then
        return "Infinite"
    end
    return "Other"
end

local function GetChannelID()
    local channelId = Server.SDKInfoServer:GetChannel()
    logerror("channel id", channelId)
    return channelId
end

local function GetArea()
    return Server.SDKInfoServer:GetZoneId()
end

local function GetRegion()
    return VersionUtil.GetGameBuildRegion()
end

local function GetExtCgiAttrs()
    return '{"attr1":"","attr2":"","attr3":""}'
end

local function GetIntlSdkParam()
    local os = GetPlatId()
    local gameid = UE.INTLSDKAPI.GetConfig("GAME_ID", "", "INTL")
    local channelid = GetChannelID()
    local ts = tostring(Facade.ClockManager:GetLocalTimestamp())
    local source = 0

    local params = "os=" .. os
            .. "&gameid=" .. gameid
            .. "&channelid=" .. channelid
            .. "&ts=" .. ts
            .. "&source=" .. source

    return params
end

local function printTable(t, indent)
    indent = indent or 0
    local prefix = string.rep("  ", indent)
    if type(t) ~= "table" then
        logerror("printTable", prefix .. tostring(t))
        return
    end
    logerror("printTable", prefix .. "{")
    for k, v in pairs(t) do
        local key = tostring(k)
        if type(v) == "table" then
            logerror("printTable", prefix .. "  " .. key .. " = ")
            printTable(v, indent + 1)
        else
            logerror("printTable", prefix .. "  " .. key .. " = " .. tostring(v))
        end
    end
    logerror("printTable", prefix .. "}")
end

local function GetUserData()
    local openId = Server.SDKInfoServer:GetOpenIdStr()
    logerror("openid", openId)
    local roleId = Server.SDKInfoServer:GetUserName()
    local appId = GetGameAppId()
    local userData = {}
    
    logerror("GetAccountType", GetAccountType())

    if IsBuildRegionCN() then
        userData = {
            sOpenId = openId,     -- 玩家唯一标识符，wegame平台请传railid
            sAppId = appId,  -- 游戏app唯一标识，即登录相关的appid，qq wx不同，如果接入wegame请填wegame的gameid            
            sRoleId = openId,         -- 游戏角色id
            sPlatID = GetPlatId(),     -- 平台id: android = 1, ios = 0, windows = 2（如果没有pc平台，建议WindowsEditor中设置为1用来测试）
            --sServerPlatID = GetServerPlatID(),
            sAccountType = GetAccountType(),    -- 玩家账户类型，国内："qq" or "wx"，ttpp（代表游客）
            sArea = GetArea(),           -- 大区id
            sPartition = GetArea(),           -- 大区id
            sAccessToken = Server.SDKInfoServer:GetToken(),    -- 从MSDK获取票据进行设置，国内端游：端游我们统一要求游戏把st票据传入到accesstoken；接入wegame请传wegame的accesstoken
            sGameVer = UGameVersionUtils.GetAppVersion(),
            sServiceType = "dfm",   --潘多拉业务英文代码，与ams业务代码一致
            sLanguage = GetLanguage(),
        }
    elseif IsBuildRegionGlobal() then
        userData = {
            sOpenId = openId,     -- 玩家唯一标识符，wegame平台请传railid
            sAppId = appId,  -- 游戏app唯一标识，即登录相关的appid，qq wx不同，如果接入wegame请填wegame的gameid            
            sRoleId = openId,         -- 游戏角色id
            sPlatID = GetPlatId(),     -- 平台id: android = 1, ios = 0, windows = 2（如果没有pc平台，建议WindowsEditor中设置为1用来测试）
            --sServerPlatID = GetServerPlatID(),
            sAccountType = GetAccountType(),    -- 玩家账户类型，国内："qq" or "wx"，ttpp（代表游客）
            sArea = GetArea(),           -- 大区id
            sAccessToken = Server.SDKInfoServer:GetToken(),    -- 从MSDK获取票据进行设置，国内端游：端游我们统一要求游戏把st票据传入到accesstoken；接入wegame请传wegame的accesstoken
            sGameVer = UGameVersionUtils.GetAppVersion(),
            sServiceType = "ProjectD",   --潘多拉业务英文代码，与ams业务代码一致
            sPartition = 0,
            sRegion = GetRegion(),
            sLanguage = GetLanguage(),
            sExtCgiAttrs = GetExtCgiAttrs(),
            sIntlSdkParam = GetIntlSdkParam(),
        }
    elseif IsBuildRegionGA() then
        userData = {
            sOpenId = openId,     -- 玩家唯一标识符，wegame平台请传railid
            sAppId = appId,  -- 游戏app唯一标识，即登录相关的appid，qq wx不同，如果接入wegame请填wegame的gameid            
            sRoleId = openId,         -- 游戏角色id
            sPlatID = GetPlatId(),     -- 平台id: android = 1, ios = 0, windows = 2（如果没有pc平台，建议WindowsEditor中设置为1用来测试）
            sAccountType = GetAccountType(),    -- 玩家账户类型，国内："qq" or "wx"，ttpp（代表游客）
            sArea = GetArea(),           -- 大区id
            sAccessToken = Server.SDKInfoServer:GetToken(),    -- 从MSDK获取票据进行设置，国内端游：端游我们统一要求游戏把st票据传入到accesstoken；接入wegame请传wegame的accesstoken
            sGameVer = UGameVersionUtils.GetAppVersion(),
            sServiceType = "dfgarena",   --潘多拉业务英文代码，与ams业务代码一致
            sLanguage = GetLanguage(),
            sPartition = 0,
            sRegion = GetRegion(),
            sExtCgiAttrs = GetExtCgiAttrs(),
            sIntlSdkParam = GetIntlSdkParam(),
        }
    end
    
    logerror("platid", GetPlatId())
    printTable(userData)
    return userData
end

GameletLogic.CallGameletFunc = function(funcName, ...)
    log("GameletLogic.CallGameletFunc:"..funcName)
    GameletLogic.CallGameletFuncPure(funcName, ...)
end

GameletLogic.CallGameletFuncPure = function(funcName, ...)
    local gameletIns = UGamelet.Get(GetGameInstance())
    -- log(gameletIns)
    if gameletIns then
        local func = gameletIns[funcName]
        if func then
            func(gameletIns, ...)
        else
            log(funcName, "找不到方法")
        end
    else
        log("GameletModule:Gamelet Error Nogamelet")
    end
end

function GameletLogic.AddListener()
    ----bug=119579549 【海外】【PC】【必现】性能 纯场景，FPixUIModule存在tick，需关闭
    if Module.Gamelet.Field:IsPandoraOpen() then
         Module.Login.Config.Events.evtOnLoginSuccess:AddListener(this.OpenSDK)
         GameletLogic.InitSDK() 
    end
end

function GameletLogic.RemoveListener()
    Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(this.OpenSDK)
end

--初始化设置一次回调
function GameletLogic.InitDelegate()
    local gameletSettingsIns = UGameletSettings.Get()
    if gameletSettingsIns then
        logerror("GameletLogic.InitDelegate")
        gameletSettingsIns.OnSDKMessage:Clear()
        gameletSettingsIns.OnSDKMessage:Bind(this._OnSDKMsg)
        gameletSettingsIns.OnRefreshUserdata:Clear()
        gameletSettingsIns.OnRefreshUserdata:Bind(this._OnRefreshUserData)
        gameletSettingsIns.OnViewCreated:Clear()
        gameletSettingsIns.OnViewCreated:Bind(this._OnViewCreated)
        gameletSettingsIns.OnViewAboutToDestroy:Clear()
        gameletSettingsIns.OnViewAboutToDestroy:Bind(this._OnViewAboutToDestroy)
        gameletSettingsIns.OnReportData:Clear()
        gameletSettingsIns.OnReportData:Bind(this._OnReportData)
    end
end

--玩家登录拿到用户信息后调用，调用后会启动执行SDK逻辑(环境，玩家数据信息) 0测试环境 1正式环境
function GameletLogic.OpenSDK()
    local env = 0
    local userData = GetUserData()
    if string.isempty(userData.sAppId) then
        log("appid 为空")
        return
    end

    --- enum EGameletEnvironment
    -- 根据情况传入环境参数
    if IsBuildRegionCN() then
        if VersionUtil.IsShipping() then
            env = 1
        else
            env = 0
        end
    elseif IsBuildRegionGlobal() then
        if VersionUtil.IsShipping() then
            env = 3
        else
            env = 2
        end
    elseif IsBuildRegionGA() then
        if VersionUtil.IsShipping() then
            env = 5
        else
            env = 4
        end
    end

    logerror("GameletLogic.OpenSDK() env", env)

    GameletLogic.CallGameletFunc("OpenSDK",userData,env)
    
    -- 注册监听事件
    GameFriendMgr.OnBaseResultNotifyEvent:Add(CreateCPlusCallBack(Module.Gamelet.OnBaseResultNotifyEvent, Module.Gamelet))
end

function GameletLogic.InitSDK()
    if not Module.Gamelet.Field:GetIsInit() then
        Module.Gamelet.Field:SetIsInit(true)
        GameletLogic.InitDelegate()
        local gamelet = UGamelet.Get(GetGameInstance())
        if gamelet then
            local ret = gamelet:InitSDK()
            if ret == 0 then
                log("小应用sdk初始化成功")
                GameletLogic.CallGameletFunc("SetFont","FZLTHJW_Font","Font'/Game/UI/Fonts/FZLTHJW_Font.FZLTHJW_Font'")
                GameletLogic.CallGameletFunc("SetFont","FZLTZHJW_ZH_Font","Font'/Game/UI/Fonts/FZLTZHJW_ZH_Font.FZLTZHJW_ZH_Font'")
                --GameletLogic.CallGameletFunc("SetPixUIMaxLayerId", 800)
            end
        end
    end
end

--玩家退出到登录界面时调用，游戏内断线重连不调用
function GameletLogic.CloseSDK()
    if not Module.Gamelet.Field:GetIsInit() then return end
    GameletLogic.CallGameletFunc("CloseSDK")

    -- 取消注册监听事件
    GameFriendMgr.OnBaseResultNotifyEvent:Clear()
end

function GameletLogic.OpenApp(appId,openArgs)
    local sAppId = appId
    if openArgs == nil then
        openArgs = "{}"
    end
    logerror("[GameletLogic] OpenApp, appID:", appId, "openArgs:", openArgs)
    if not sAppId then
        log("appid 为空")
        return 
    end
    GameletLogic.CallGameletFunc("OpenApp", sAppId, openArgs)
end

function GameletLogic.CloseApp(appId)
    local sAppId = appId
    if not sAppId then return end
    GameletLogic.CallGameletFunc("CloseApp",sAppId)
end

function GameletLogic.SendMessageToApp(appId, json_msg)
    GameletLogic.CallGameletFunc("SendMessageToApp",appId,json_msg)
end

-----------sdk回调-----------
--响应处理SDK消息
function GameletLogic._OnSDKMsg(msg)
    log("GameletLogic.OnSDKMsg:"..msg)
    ProcessSDKProto(msg)
end

--响应处理SDK请求刷新Userdata数据
function GameletLogic._OnRefreshUserData()
    log("GameletLogic.OnRefreshUserData")
    local userData = GetUserData()
    GameletLogic.CallGameletFunc("RefreshUserdata",userData)
end

--处理SDK创建面板事件
function GameletLogic._OnViewCreated(widget,appInfo)
    log("GameletLogic.OnViewCreated"..appInfo)
    local app_info = Json.decode(appInfo)
    if app_info and app_info.windowConfig and app_info.windowConfig.parameter and app_info.windowConfig.parameter.containerType == EUILayer.Pop then
        -- Pop 特殊处理
        Facade.UIManager:AsyncShowUI(UIName2ID.PandoraPop, nil,nil, {iAppID = app_info.belongToApp, tAppInfo = app_info, tWidget = widget})
    else
        Module.Gamelet.Config.evtPandoraShowAppPage:Invoke(widget,app_info)
    end
end

--处理SDK关闭面板事件
function GameletLogic._OnViewAboutToDestroy(widget,appInfo)
    log("GameletLogic.OnViewAboutToDestroy"..appInfo)
    local app_info = Json.decode(appInfo)
    Module.Gamelet.Config.evtPandoraHideAppPage:Invoke(widget,app_info)
end

--潘多拉SDK数据上报委托
function GameletLogic._OnReportData(eventName,data)
    log("GameletLogic.OnReportData: eventName:"..eventName.."数据json串:"..data)
end

----------------接口--------------------------
GameletLogic.ShowEntrance = function()
    local onEntranceCreated = function(uiIns)
        Module.Gamelet.Field:SetEntranceUIins(uiIns)
    end
    Timer.DelayCall(1, function ()    
        Facade.UIManager:AsyncShowUI(UIName2ID.GameletEntrance,onEntranceCreated,nil)
    end)
end

-- GameletLogic.HideEntrance = function()
--     local entranceUI = Module.Gamelet.Field:GetEntranceUIins()
--     if entranceUI then
--         Facade.UIManager:CloseUI(entranceUI)
--     end
-- end

function GameletLogic.ShowGameletStack(appId, openArgs, nameID)
    local id = appId
    local appActive = Module.Gamelet.Field:GetActiveApp(id)
    if not appActive then
        log("该app 未准备好"..id)
        return -1
    end
    local app = Module.Gamelet.Field:IsUIReadyAppTable(id)
    if not app then
        Facade.UIManager:AsyncShowUI(nameID, nil, nil, {iAppID = id, tOpenArgs = openArgs})
    else
        log("该app 已打开"..id)
        return -2
    end

    return 0
end

function GameletLogic.Tick(fDeltaTime)
    GameletLogic.CallGameletFuncPure("Tick", fDeltaTime)
end

function GameletLogic.SendShareResult(result)
    local json_info = {
        type = LastShareType,
        content = LastShareAppID,
        result = result
    }
    local json_args = Json.encode(json_info)

    GameletLogic.SendMessageToApp(LastShareAppID, json_args)
end

function GameletLogic.SendAppShow(appID)
    local json_info = {
        type = "pandoraAppShow",
    }
    local json_args = Json.encode(json_info)

    GameletLogic.SendMessageToApp(appID, json_args)
end

function GameletLogic.SendAllActivePandoraGameEvent(eventName, eventArgs)
    local tTable = Module.Gamelet.Field:GetUIReadAppTable()
    for appID in pairs(tTable) do
        GameletLogic.SendPandoraGameEvent(appID, eventName, eventArgs)
    end
end

function GameletLogic.SendPandoraGameEvent(appID, eventName, eventArgs)
    local json_info = {
        type = "pandoraGameEvent",
        appId = appID,
        eventName = eventName,
        eventArgs = eventArgs,
    }
    local json_args = Json.encode(json_info)

    GameletLogic.SendMessageToApp(appID, json_args)
end

--------------------------------------------------------------------------
--- Loading加载逻辑
--------------------------------------------------------------------------
function GameletLogic.OnLoadingLogin2FrontendProcess()
    GameletLogic.PreLoad()
end

function GameletLogic.OnLoadingGame2FrontendProcess()
    GameletLogic.PreLoad()
end

function GameletLogic.OnLoadingFrontend2GameProcess()
end

function GameletLogic.PreLoad()
    --- 预加载一些主要打开卡顿的蓝图
    local loadingUIIdList = {
        UIName2ID.PandoraStackNoCB,
    }
    logerror("GameletLogic.PreLoad()")
    Facade.UIManager:PreloadLoadingUIAssetList(loadingUIIdList)
end

function GameletLogic.ProcessSDKProtoLogic(proto)
    ProcessSDKProto(proto)
end

--------------------------------------------------------------------------
--- 场景相关逻辑
--------------------------------------------------------------------------

function GameletLogic.ShowReward(ui, itemID, type, extraParam)
    local itemData = ItemBase:NewIns(itemID)
    if not itemData then
        return
    end

    -- 重置
    if type == Module.Gamelet.Config.ESceneType.StoreHotRecommend then
        Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCanInteractState", false)
        Module.Hero:ShowOperatorWatch("None")--删除手表
        local wtImageRoot = ui:Wnd("DFScaleBox_0", UIWidgetBase)
        if wtImageRoot then
            wtImageRoot:Collapsed()
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end

    if itemData.itemMainType == EItemType.WeaponSkin then  -- 武器皮肤
        GameletLogic.ShowWeaponSkin(ui, itemData, type, extraParam)
    elseif itemData.itemMainType == EItemType.Adapter then -- 配件
        GameletLogic.ShowAdapter(ui, itemData, type, extraParam)
    elseif itemData.itemMainType == EItemType.SocialAppearance and itemData.itemSubType == ESocialAppearanceType.SocialAvatarTab then  -- 头像
        GameletLogic.ShowSocialAvatarTab(ui, itemData, type, extraParam)
    elseif itemData.itemMainType == EItemType.SocialAppearance and itemData.itemSubType == ESocialAppearanceType.SocialMilitaryTab then  -- 军牌
        GameletLogic.ShowSocialMilitaryTab(ui, itemData, type, extraParam)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Card then -- 名片
        GameletLogic.ShowCard(ui, itemData, type, extraParam)
    elseif itemData.itemMainType == EItemType.CollectionProp or itemData.id == ECurrencyItemId.Diamond then
        GameletLogic.ShowDiamond(ui, itemData, type, extraParam)
    else
        GameletLogic.ShowOther(ui, itemData, type, extraParam)
    end
end

function GameletLogic.ShowWeaponSkin(ui, itemData, type, extraParam)
    local fAllLevelFinishCallback = function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)

        if extraParam and extraParam.tabID then
            Facade.HallSceneManager:SetDisplayBackground(extraParam.tabID, true)
        end
        Facade.HallSceneManager:SetBackgroundScale("WeaponShowBG")
        
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponAutoBoundAdapter", itemData:GetRawDescObj(), false, false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCanInteractState", true)
    end

    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallMall, true, nil, fAllLevelFinishCallback, false, 30)
end

function GameletLogic.ShowAdapter(ui, itemData, type, extraParam)
    local fAllLevelFinishCallback = function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)

        if extraParam and extraParam.tabID then
            Facade.HallSceneManager:SetDisplayBackground(extraParam.tabID, true)
        end
        Facade.HallSceneManager:SetBackgroundScale("WeaponShowBG")

        Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6, EOffsetType.ZOffset)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", itemData:GetRawDescObj(), itemData.id,
                false, true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCanInteractState", true)
    end

    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallMall, true, nil, fAllLevelFinishCallback, false, 30)
end

function GameletLogic.ShowSocialAvatarTab(ui, itemData, type, extraParam)
    GameletLogic.ShowIcon(ui, itemData, type, extraParam, 1)
end

function GameletLogic.ShowSocialMilitaryTab(ui, itemData, type, extraParam)
    GameletLogic.ShowIcon(ui, itemData, type, extraParam, 3)
end

function GameletLogic.ShowOther(ui, itemData, type, extraParam)
    GameletLogic.ShowIcon(ui, itemData, type, extraParam, 1)
end

function GameletLogic.ShowCard(ui, itemData, type, extraParam)
    GameletLogic.ShowIcon(ui, itemData, type, extraParam, 4)
end

function GameletLogic.ShowDiamond(ui, itemData, type, extraParam)
    if IsHD() then
        GameletLogic.ShowIcon(ui, itemData, type, extraParam, 1)
    else
        GameletLogic.ShowIcon(ui, itemData, type, extraParam, 5)
    end
end

function GameletLogic.ShowIcon(ui, itemData, type, extraParam, size)
    local fAllLevelFinishCallback = function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)

        if extraParam and extraParam.tabID then
            Facade.HallSceneManager:SetDisplayBackground(extraParam.tabID, true)
        end
        Facade.HallSceneManager:SetBackgroundScale("WeaponShowBG")

        local wtImageRoot = ui:Wnd("DFScaleBox_0", UIWidgetBase)
        wtImageRoot:SelfHitTestInvisible()
        ui:SetSize(size)
        GameletLogic.SetIcon(ui, itemData, type, extraParam)
    end

    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallMall, true, nil, fAllLevelFinishCallback, false, 30)
end

function GameletLogic.SetIcon(ui, itemData, type, extraParam)
    local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
    -- 静态图集路径
    local iconPath = ""
    if itemAsset ~= nil then
        iconPath = itemAsset.ItemIconPath
    end

    local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
    if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
        iconPath = gameItemRow.MallItemIcon
    end
    
    local wtImageIcon = ui:Wnd("DFImage_191", UIImage)
    if iconPath ~= "" then
        wtImageIcon:AsyncSetImagePath(iconPath, true)
    end
end

return GameletLogic
